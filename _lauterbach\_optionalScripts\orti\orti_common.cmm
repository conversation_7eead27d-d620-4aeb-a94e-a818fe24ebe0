;/**********************************************************************************************************************
;  AUTHOR IDENTITY
; ----------------------------------------------------------------------------------------------------------------------
;  Name                          Initials      Company
;  ----------------------------  ------------  -------------------------------------------------------------------------
;  <PERSON>               v<PERSON>        Vector Informatik GmbH
;  Joachim Wenzel                visjwo        Vector Informatik GmbH
;  Emanuel Schnierle             visese        Vector Informatik GmbH
;  Hahn Andreas                  vishan        Vector Informatik GmbH
;  Derick Beng Yuh               visydg        Vector Informatik GmbH
;-----------------------------------------------------------------------------------------------------------------------
;  REVISION HISTORY
; ----------------------------------------------------------------------------------------------------------------------
;  Version   Date        Author  Description
;  --------  ----------  ------  ---------------------------------------------------------------------------------------
;  01.00.00  2017-08-01  visscs  Initial creation
;**********************************************************************************************************************/

;--------------------------------------------------------
; load the ORTI file if it exists  
;--------------------------------------------------------
  LOCAL &path &ORTI
;concatenate the TRACE32 system directory with the subfolder path
&path=OS.PWD()+"\..\GenData\"                     ;path to generated files (current working directory + tcb)
&ORTI=OS.FIRSTFILE("&path")                ;get the first file name


WHILE "&ORTI"!=""
(
   IF STRING.SCAN("STRING.UPR(OS.FILE.EXTENSION(&ORTI))", ".ORT", 0)!=-1
   (
      TASK.ORTI &path&ORTI                     ; load the ORTI file
      PRINT "ORTI Loaded"
   )
   &ORTI=OS.NEXTFILE()                   ;get next file name
)


;========================================================================
; setup window page "ORTI" 

 WinPAGE.Create ORTI
 WinCLEAR
 
 if (&UseHighResWindows==1.)
 (
 
   WinPOS 180.43 0.0 88. 2. 0. 1. W007
   WinTABS 11. 18. 13. 23. 23.
   TASK.Dvs_OS_CONFIG
   
   WinPOS 0.0 0.0 175. 2. 0. 1. W000
   WinTABS 11. 15. 16. 42. 22. 36. 18. 16. 25.
   TASK.DOS
   
   WinPOS 0.14286 68.5 141. 33. 33. 1. W001
   task.STACK.VIEW /HUMANREADABLE 
   
   WinPOS 147.0 68.5 82. 33. 0. 1. W005
   WinTABS 23. 11. 29. 32. 9.
   TASK.Dvs_ISR
   
   WinPOS 235.0 68.5 73. 33. 0. 1. W006
   WinTABS 62. 11.
   TASK.Dvs_EVENT
   
   WinPOS 0.28571 7.0833 237. 24. 0. 1. W004
   WinTABS 57. 12. 19. 16. 19. 16. 86. 12.
   TASK.DALARM
   
   WinPOS 0.28571 36.333 178. 27. 0. 1. W002
   WinTABS 29. 12. 10. 19. 6. 36. 5. 9. 11. 11. 12. 17. 32.
   TASK.DTASK
   
   WinPOS 184.14 36.333 124. 27. 0. 1. W003
   WinTABS 35. 18. 20. 16. 19.
   TASK.DSTACK
 )
 ELSE
 (
   WinPOS 180.43 0.0 88. 2. 0. 1. W007
   WinTABS 11. 18. 13. 23. 23.
   TASK.Dvs_OS_CONFIG
   
   WinPOS 0.0 0.0 175. 2. 0. 1. W000
   WinTABS 11. 15. 16. 42. 22. 36. 18. 16. 25.
   TASK.DOS
   
   WinPOS 0.14286 7.0 48. 25. 0. 1. W005
   WinTABS 23. 11. 29. 32. 9.
   TASK.Dvs_ISR
   
   WinPOS 54.0 7.0833 64. 25. 0. 1. W003
   WinTABS 35. 18. 20. 16. 19.
   TASK.DSTACK
   
   WinPOS 124.14 7.0833 65. 25. 0. 1. W004
   WinTABS 57. 12. 19. 16. 19. 16. 86. 12.
   TASK.DALARM
   
   WinPOS 195.14 7.0833 73. 25. 0. 1. W006
   WinTABS 62. 11.
   TASK.Dvs_EVENT
   
   WinPOS 139.86 37.583 128. 29. 1. 1. W002
   WinTABS 29. 12. 10. 19. 6. 36. 5. 9. 11. 11. 12. 17. 32.
   TASK.DTASK
   
   WinPOS 0.0 37.583 134. 29. 50. 1. W001
   WinTABS 50. 11. 11. 5. 11. 11. 5. 50.
   task.STACK.VIEW /HUMANREADABLE 
 )
 
 WinPAGE.select ORTI