/**********************************************************************************************************************
 *  COPYRIGHT
 *  -------------------------------------------------------------------------------------------------------------------
 *  \verbatim
 *
 *                This software is copyright protected and proprietary to Vector Informatik GmbH.
 *                Vector Informatik GmbH grants to you only those rights as set out in the license conditions.
 *                All other rights remain with Vector Informatik GmbH.
 *  \endverbatim
 *  -------------------------------------------------------------------------------------------------------------------
 *  FILE DESCRIPTION
 *  -------------------------------------------------------------------------------------------------------------------
 *             File:  Rte_Hook.h
 *           Config:  Demo.dpa
 *      ECU-Project:  Demo
 *
 *        Generator:  MICROSAR RTE Generator Version 4.22.1
 *                    RTE Core Version 1.22.1
 *          License:  CBD2000456
 *
 *      Description:  Header file containing definitions for VFB tracing
 *********************************************************************************************************************/


/**********************************************************************************************************************
 * Names of available VFB-Trace-Hooks
 **********************************************************************************************************************
 *
 * Not configured:
 *
 *  Rte_CallHook_Dcm_DataServices_Data_Boot_Software_Read_ConditionCheckRead_Return
 *  Rte_CallHook_Dcm_DataServices_Data_Boot_Software_Read_ConditionCheckRead_Start
 *  Rte_CallHook_Dcm_DataServices_Data_Boot_Software_Read_ReadData_Return
 *  Rte_CallHook_Dcm_DataServices_Data_Boot_Software_Read_ReadData_Start
 *  Rte_CallHook_Dcm_DataServices_Data_Boot_Software_Read_WriteData_Return
 *  Rte_CallHook_Dcm_DataServices_Data_Boot_Software_Read_WriteData_Start
 *  Rte_CallHook_Dcm_DataServices_Data_DID_0xF410_Read_ConditionCheckRead_Return
 *  Rte_CallHook_Dcm_DataServices_Data_DID_0xF410_Read_ConditionCheckRead_Start
 *  Rte_CallHook_Dcm_DataServices_Data_DID_0xF410_Read_ReadData_Return
 *  Rte_CallHook_Dcm_DataServices_Data_DID_0xF410_Read_ReadData_Start
 *  Rte_CallHook_Dcm_DataServices_Data_DID_0xF412_Read_ConditionCheckRead_Return
 *  Rte_CallHook_Dcm_DataServices_Data_DID_0xF412_Read_ConditionCheckRead_Start
 *  Rte_CallHook_Dcm_DataServices_Data_DID_0xF412_Read_ReadData_Return
 *  Rte_CallHook_Dcm_DataServices_Data_DID_0xF412_Read_ReadData_Start
 *  Rte_CallHook_Dcm_DataServices_Data_DID_0xF413_Read_ConditionCheckRead_Return
 *  Rte_CallHook_Dcm_DataServices_Data_DID_0xF413_Read_ConditionCheckRead_Start
 *  Rte_CallHook_Dcm_DataServices_Data_DID_0xF413_Read_ReadData_Return
 *  Rte_CallHook_Dcm_DataServices_Data_DID_0xF413_Read_ReadData_Start
 *  Rte_CallHook_Dcm_DataServices_Data_DID_StartApplication_ConditionCheckRead_Return
 *  Rte_CallHook_Dcm_DataServices_Data_DID_StartApplication_ConditionCheckRead_Start
 *  Rte_CallHook_Dcm_DataServices_Data_DID_StartApplication_ReadData_Return
 *  Rte_CallHook_Dcm_DataServices_Data_DID_StartApplication_ReadData_Start
 *  Rte_CallHook_Dcm_DataServices_Data_DID_StartApplication_WriteData_Return
 *  Rte_CallHook_Dcm_DataServices_Data_DID_StartApplication_WriteData_Start
 *  Rte_CallHook_Dcm_DataServices_Data_DataDiagnosticIdentifier_DID_Read_ConditionCheckRead_Return
 *  Rte_CallHook_Dcm_DataServices_Data_DataDiagnosticIdentifier_DID_Read_ConditionCheckRead_Start
 *  Rte_CallHook_Dcm_DataServices_Data_DataDiagnosticIdentifier_DID_Read_ReadData_Return
 *  Rte_CallHook_Dcm_DataServices_Data_DataDiagnosticIdentifier_DID_Read_ReadData_Start
 *  Rte_CallHook_Dcm_DataServices_Data_DataDiagnosticIdentifier_DID_Read_WriteData_Return
 *  Rte_CallHook_Dcm_DataServices_Data_DataDiagnosticIdentifier_DID_Read_WriteData_Start
 *  Rte_CallHook_Dcm_DataServices_Data_DevelopmentData_Read_ConditionCheckRead_Return
 *  Rte_CallHook_Dcm_DataServices_Data_DevelopmentData_Read_ConditionCheckRead_Start
 *  Rte_CallHook_Dcm_DataServices_Data_DevelopmentData_Read_ReadData_Return
 *  Rte_CallHook_Dcm_DataServices_Data_DevelopmentData_Read_ReadData_Start
 *  Rte_CallHook_Dcm_DataServices_Data_DevelopmentData_Read_WriteData_Return
 *  Rte_CallHook_Dcm_DataServices_Data_DevelopmentData_Read_WriteData_Start
 *  Rte_CallHook_Dcm_DataServices_Data_EcuIdentification_Read_ConditionCheckRead_Return
 *  Rte_CallHook_Dcm_DataServices_Data_EcuIdentification_Read_ConditionCheckRead_Start
 *  Rte_CallHook_Dcm_DataServices_Data_EcuIdentification_Read_ReadData_Return
 *  Rte_CallHook_Dcm_DataServices_Data_EcuIdentification_Read_ReadData_Start
 *  Rte_CallHook_Dcm_DataServices_Data_EcuIdentification_Read_WriteData_Return
 *  Rte_CallHook_Dcm_DataServices_Data_EcuIdentification_Read_WriteData_Start
 *  Rte_CallHook_Dcm_DataServices_Data_Example_ReadOnlyDID_Read_ConditionCheckRead_Return
 *  Rte_CallHook_Dcm_DataServices_Data_Example_ReadOnlyDID_Read_ConditionCheckRead_Start
 *  Rte_CallHook_Dcm_DataServices_Data_Example_ReadOnlyDID_Read_ReadData_Return
 *  Rte_CallHook_Dcm_DataServices_Data_Example_ReadOnlyDID_Read_ReadData_Start
 *  Rte_CallHook_Dcm_DataServices_Data_Example_ReadWriteData_Read_ConditionCheckRead_Return
 *  Rte_CallHook_Dcm_DataServices_Data_Example_ReadWriteData_Read_ConditionCheckRead_Start
 *  Rte_CallHook_Dcm_DataServices_Data_Example_ReadWriteData_Read_ReadData_Return
 *  Rte_CallHook_Dcm_DataServices_Data_Example_ReadWriteData_Read_ReadData_Start
 *  Rte_CallHook_Dcm_DataServices_Data_Example_ReadWriteData_Read_WriteData_Return
 *  Rte_CallHook_Dcm_DataServices_Data_Example_ReadWriteData_Read_WriteData_Start
 *  Rte_CallHook_Dcm_DataServices_Data_Example_WriteOnlyDID_Write_WriteData_Return
 *  Rte_CallHook_Dcm_DataServices_Data_Example_WriteOnlyDID_Write_WriteData_Start
 *  Rte_CallHook_Dcm_DataServices_Data_Hardware_Version_Read_ConditionCheckRead_Return
 *  Rte_CallHook_Dcm_DataServices_Data_Hardware_Version_Read_ConditionCheckRead_Start
 *  Rte_CallHook_Dcm_DataServices_Data_Hardware_Version_Read_ReadData_Return
 *  Rte_CallHook_Dcm_DataServices_Data_Hardware_Version_Read_ReadData_Start
 *  Rte_CallHook_Dcm_DataServices_Data_Hardware_Version_Read_WriteData_Return
 *  Rte_CallHook_Dcm_DataServices_Data_Hardware_Version_Read_WriteData_Start
 *  Rte_CallHook_Dcm_DataServices_Data_PeriodicDataSample_Send_once_ConditionCheckRead_Return
 *  Rte_CallHook_Dcm_DataServices_Data_PeriodicDataSample_Send_once_ConditionCheckRead_Start
 *  Rte_CallHook_Dcm_DataServices_Data_PeriodicDataSample_Send_once_ReadData_Return
 *  Rte_CallHook_Dcm_DataServices_Data_PeriodicDataSample_Send_once_ReadData_Start
 *  Rte_CallHook_Dcm_DataServices_Data_SampleIoControl_Read_FreezeCurrentState_Return
 *  Rte_CallHook_Dcm_DataServices_Data_SampleIoControl_Read_FreezeCurrentState_Start
 *  Rte_CallHook_Dcm_DataServices_Data_SampleIoControl_Read_ReadData_Return
 *  Rte_CallHook_Dcm_DataServices_Data_SampleIoControl_Read_ReadData_Start
 *  Rte_CallHook_Dcm_DataServices_Data_SampleIoControl_Read_ResetToDefault_Return
 *  Rte_CallHook_Dcm_DataServices_Data_SampleIoControl_Read_ResetToDefault_Start
 *  Rte_CallHook_Dcm_DataServices_Data_SampleIoControl_Read_ReturnControlToECU_Return
 *  Rte_CallHook_Dcm_DataServices_Data_SampleIoControl_Read_ReturnControlToECU_Start
 *  Rte_CallHook_Dcm_DataServices_Data_SampleIoControl_Read_ShortTermAdjustment_Return
 *  Rte_CallHook_Dcm_DataServices_Data_SampleIoControl_Read_ShortTermAdjustment_Start
 *  Rte_CallHook_Dcm_DataServices_Data_SerialNumber_Read_ConditionCheckRead_Return
 *  Rte_CallHook_Dcm_DataServices_Data_SerialNumber_Read_ConditionCheckRead_Start
 *  Rte_CallHook_Dcm_DataServices_Data_SerialNumber_Read_ReadData_Return
 *  Rte_CallHook_Dcm_DataServices_Data_SerialNumber_Read_ReadData_Start
 *  Rte_CallHook_Dcm_DataServices_Data_SerialNumber_Read_WriteData_Return
 *  Rte_CallHook_Dcm_DataServices_Data_SerialNumber_Read_WriteData_Start
 *  Rte_CallHook_Dcm_DataServices_Data_Spare_Part_Number_Read_ConditionCheckRead_Return
 *  Rte_CallHook_Dcm_DataServices_Data_Spare_Part_Number_Read_ConditionCheckRead_Start
 *  Rte_CallHook_Dcm_DataServices_Data_Spare_Part_Number_Read_ReadData_Return
 *  Rte_CallHook_Dcm_DataServices_Data_Spare_Part_Number_Read_ReadData_Start
 *  Rte_CallHook_Dcm_DataServices_Data_Spare_Part_Number_Read_WriteData_Return
 *  Rte_CallHook_Dcm_DataServices_Data_Spare_Part_Number_Read_WriteData_Start
 *  Rte_CallHook_Dcm_DataServices_Data_Vehicle_Identification_Read_ConditionCheckRead_Return
 *  Rte_CallHook_Dcm_DataServices_Data_Vehicle_Identification_Read_ConditionCheckRead_Start
 *  Rte_CallHook_Dcm_DataServices_Data_Vehicle_Identification_Read_ReadData_Return
 *  Rte_CallHook_Dcm_DataServices_Data_Vehicle_Identification_Read_ReadData_Start
 *  Rte_CallHook_Dcm_DataServices_Data_Vehicle_Identification_Read_WriteData_Return
 *  Rte_CallHook_Dcm_DataServices_Data_Vehicle_Identification_Read_WriteData_Start
 *  Rte_CallHook_Dcm_RoutineServices_Routine_CheckProgrammingPreconditions_Start_Start_Return
 *  Rte_CallHook_Dcm_RoutineServices_Routine_CheckProgrammingPreconditions_Start_Start_Start
 *  Rte_CallHook_Dcm_RoutineServices_Routine_ForceBootMode_Start_Start_Return
 *  Rte_CallHook_Dcm_RoutineServices_Routine_ForceBootMode_Start_Start_Start
 *  Rte_CallHook_Dcm_RoutineServices_Routine_SampleRoutineControl_StartOnly_Start_RequestResults_Return
 *  Rte_CallHook_Dcm_RoutineServices_Routine_SampleRoutineControl_StartOnly_Start_RequestResults_Start
 *  Rte_CallHook_Dcm_RoutineServices_Routine_SampleRoutineControl_StartOnly_Start_Start_Return
 *  Rte_CallHook_Dcm_RoutineServices_Routine_SampleRoutineControl_StartOnly_Start_Start_Start
 *  Rte_CallHook_Dcm_RoutineServices_Routine_SampleRoutineControl_Start_RequestResults_Return
 *  Rte_CallHook_Dcm_RoutineServices_Routine_SampleRoutineControl_Start_RequestResults_Start
 *  Rte_CallHook_Dcm_RoutineServices_Routine_SampleRoutineControl_Start_Start_Return
 *  Rte_CallHook_Dcm_RoutineServices_Routine_SampleRoutineControl_Start_Start_Start
 *  Rte_CallHook_Dcm_RoutineServices_Routine_SampleRoutineControl_Start_Stop_Return
 *  Rte_CallHook_Dcm_RoutineServices_Routine_SampleRoutineControl_Start_Stop_Start
 *  Rte_CallHook_Dcm_SecurityAccess_Level_3_CompareKey_Return
 *  Rte_CallHook_Dcm_SecurityAccess_Level_3_CompareKey_Start
 *  Rte_CallHook_Dcm_SecurityAccess_Level_3_GetSeed_Return
 *  Rte_CallHook_Dcm_SecurityAccess_Level_3_GetSeed_Start
 *  Rte_CallHook_Dcm_SecurityAccess_UnlockedL1_CompareKey_Return
 *  Rte_CallHook_Dcm_SecurityAccess_UnlockedL1_CompareKey_Start
 *  Rte_CallHook_Dcm_SecurityAccess_UnlockedL1_GetSeed_Return
 *  Rte_CallHook_Dcm_SecurityAccess_UnlockedL1_GetSeed_Start
 *  Rte_CallHook_DemMaster_0_CBInitEvt_DTC_0x000002_InitMonitorForEvent_Return
 *  Rte_CallHook_DemMaster_0_CBInitEvt_DTC_0x000002_InitMonitorForEvent_Start
 *  Rte_CallHook_DemMaster_0_CBReadData_Data_DID_0x1_DID_Data_GlobalTime_ReadData_Return
 *  Rte_CallHook_DemMaster_0_CBReadData_Data_DID_0x1_DID_Data_GlobalTime_ReadData_Start
 *  Rte_CallHook_DemMaster_0_CBReadData_Data_PID_0x12_DID_Data_ReadData_Return
 *  Rte_CallHook_DemMaster_0_CBReadData_Data_PID_0x12_DID_Data_ReadData_Start
 *  Rte_CallHook_DemMaster_0_CBReadData_Data_PID_0x13_DID_Data_ReadData_Return
 *  Rte_CallHook_DemMaster_0_CBReadData_Data_PID_0x13_DID_Data_ReadData_Start
 *  Rte_CallHook_DemMaster_0_CBReadData_DemDataElementClass_StartApplication_ReadData_Return
 *  Rte_CallHook_DemMaster_0_CBReadData_DemDataElementClass_StartApplication_ReadData_Start
 *  Rte_CallHook_DemSatellite_0_CBReadData_Data_DID_0x1_DID_Data_GlobalTime_ReadData_Return
 *  Rte_CallHook_DemSatellite_0_CBReadData_Data_DID_0x1_DID_Data_GlobalTime_ReadData_Start
 *  Rte_CallHook_DemSatellite_0_CBReadData_Data_PID_0x12_DID_Data_ReadData_Return
 *  Rte_CallHook_DemSatellite_0_CBReadData_Data_PID_0x12_DID_Data_ReadData_Start
 *  Rte_CallHook_DemSatellite_0_CBReadData_Data_PID_0x13_DID_Data_ReadData_Return
 *  Rte_CallHook_DemSatellite_0_CBReadData_Data_PID_0x13_DID_Data_ReadData_Start
 *  Rte_CallHook_DemSatellite_0_CBReadData_DemDataElementClass_StartApplication_ReadData_Return
 *  Rte_CallHook_DemSatellite_0_CBReadData_DemDataElementClass_StartApplication_ReadData_Start
 *  Rte_CallHook_NvM_NvM_RpNotifyJobEnd_StartApplication_NvMBlock1_JobFinished_Return
 *  Rte_CallHook_NvM_NvM_RpNotifyJobEnd_StartApplication_NvMBlock1_JobFinished_Start
 *  Rte_CallHook_NvM_NvM_RpNotifyJobEnd_StartApplication_NvMBlock2_JobFinished_Return
 *  Rte_CallHook_NvM_NvM_RpNotifyJobEnd_StartApplication_NvMBlock2_JobFinished_Start
 *  Rte_CallHook_StartApplication_PpDemOpCycle_PowerCycle_SetOperationCycleState_Return
 *  Rte_CallHook_StartApplication_PpDemOpCycle_PowerCycle_SetOperationCycleState_Start
 *  Rte_CallHook_StartApplication_PpDiagnosticMonitor_DEM_EVENT_StartApplication_SetEventStatus_Return
 *  Rte_CallHook_StartApplication_PpDiagnosticMonitor_DEM_EVENT_StartApplication_SetEventStatus_Start
 *  Rte_CallHook_StartApplication_PpPS_StartApplication_NvMBlock1_ReadBlock_Return
 *  Rte_CallHook_StartApplication_PpPS_StartApplication_NvMBlock1_ReadBlock_Start
 *  Rte_CallHook_StartApplication_PpPS_StartApplication_NvMBlock1_WriteBlock_Return
 *  Rte_CallHook_StartApplication_PpPS_StartApplication_NvMBlock1_WriteBlock_Start
 *  Rte_CallHook_StartApplication_PpPS_StartApplication_NvMBlock2_ReadBlock_Return
 *  Rte_CallHook_StartApplication_PpPS_StartApplication_NvMBlock2_ReadBlock_Start
 *  Rte_CallHook_StartApplication_PpPS_StartApplication_NvMBlock2_WriteBlock_Return
 *  Rte_CallHook_StartApplication_PpPS_StartApplication_NvMBlock2_WriteBlock_Start
 *  Rte_CallHook_StartApplication_UR_CN_CAN_52ce3533_GetCurrentComMode_Return
 *  Rte_CallHook_StartApplication_UR_CN_CAN_52ce3533_GetCurrentComMode_Start
 *  Rte_CallHook_StartApplication_UR_CN_CAN_52ce3533_RequestComMode_Return
 *  Rte_CallHook_StartApplication_UR_CN_CAN_52ce3533_RequestComMode_Start
 *  Rte_CallHook_StartApplication_UR_CN_FlexRay_oChannel_A_8b187a93_GetCurrentComMode_Return
 *  Rte_CallHook_StartApplication_UR_CN_FlexRay_oChannel_A_8b187a93_GetCurrentComMode_Start
 *  Rte_CallHook_StartApplication_UR_CN_FlexRay_oChannel_A_8b187a93_RequestComMode_Return
 *  Rte_CallHook_StartApplication_UR_CN_FlexRay_oChannel_A_8b187a93_RequestComMode_Start
 *  Rte_CallHook_StartApplication_UR_CN_LIN00_0a7bdc9c_GetCurrentComMode_Return
 *  Rte_CallHook_StartApplication_UR_CN_LIN00_0a7bdc9c_GetCurrentComMode_Start
 *  Rte_CallHook_StartApplication_UR_CN_LIN00_0a7bdc9c_RequestComMode_Return
 *  Rte_CallHook_StartApplication_UR_CN_LIN00_0a7bdc9c_RequestComMode_Start
 *  Rte_ComHookRx_SG_RgRx_Prof1C_RxCycle_omsg_RxCycle_E2eProf1C_500_10_oCAN_430c5af9_Rx
 *  Rte_ComHookRx_Signal_Rx20bit_Cyclic_omsg_RxCycle500_20_oCAN_983b15aa_Rx
 *  Rte_ComHook_SigRx_Prof1C_CRC_omsg_RxCycle_E2eProf1C_500_10_oCAN_d6a2f34d_Rx_SigRx
 *  Rte_ComHook_SigRx_Prof1C_DataId_HiByte_LoNib_omsg_RxCycle_E2eProf1C_500_10_oCAN_bdeb2c98_Rx_SigRx
 *  Rte_ComHook_SigRx_Prof1C_SQ_omsg_RxCycle_E2eProf1C_500_10_oCAN_263a4149_Rx_SigRx
 *  Rte_ComHook_SigRx_Prof1C_Sig32Bit_omsg_RxCycle_E2eProf1C_500_10_oCAN_f0dd3f4d_Rx_SigRx
 *  Rte_ComHook_SigRx_Prof1C_Sig3Bit_omsg_RxCycle_E2eProf1C_500_10_oCAN_95ddcbc9_Rx_SigRx
 *  Rte_ComHook_SigRx_Prof1C_Sig4Bit_omsg_RxCycle_E2eProf1C_500_10_oCAN_a713ffd7_Rx_SigRx
 *  Rte_ComHook_Sig_StartAppl_LinData_Rx_oFrame_LinTr_RearECU_oLIN00_182ca40c_Rx_SigRx
 *  Rte_ComHook_Sig_StartAppl_LinData_Tx_oFrame_LinTr_MyECU_oLIN00_d383dc6b_Tx_SigTx
 *  Rte_ComHook_Signal_Rx20bit_Cyclic_omsg_RxCycle500_20_oCAN_983b15aa_Rx_SigRx
 *  Rte_ComHook_Signal_SomeTxSignal_oPDU_Transmit_MyECU_763437bb_Tx_SigTx
 *  Rte_ComHook_Signal_Tx10bit_Cyclic_omsg_TxCycle1000_10_oCAN_9539efc7_Tx_SigTx
 *  Rte_ComHook_Signal_Tx24bit_Cyclic_omsg_TxCycle10_0_oCAN_393dd5fe_Tx_SigTx
 *  Rte_ComHook_StartAppl_Fr_BothECU_RX_Ctrl_oPDU_Fr_StartAppl_BothECU_RX_cb3aeb40_Rx_SigRx
 *  Rte_IrvReadHook_StartApplication_StartApplication_Cyclic250ms_IrvOccuranceCounterDid_Return
 *  Rte_IrvReadHook_StartApplication_StartApplication_Cyclic250ms_IrvOccuranceCounterDid_Start
 *  Rte_IrvReadHook_StartApplication_StartApplication_DIAG_DcmReadData_IrvOccuranceCounterDid_Return
 *  Rte_IrvReadHook_StartApplication_StartApplication_DIAG_DcmReadData_IrvOccuranceCounterDid_Start
 *  Rte_IrvReadHook_StartApplication_StartApplication_DIAG_DemReadData_IrvOccuranceCounterDid_Return
 *  Rte_IrvReadHook_StartApplication_StartApplication_DIAG_DemReadData_IrvOccuranceCounterDid_Start
 *  Rte_IrvReadHook_StartApplication_StartApplication_OnDataRec_RxData_IrvOccuranceCounterDid_Return
 *  Rte_IrvReadHook_StartApplication_StartApplication_OnDataRec_RxData_IrvOccuranceCounterDid_Start
 *  Rte_IrvWriteHook_StartApplication_StartApplication_DIAG_DcmWriteData_IrvOccuranceCounterDid_Return
 *  Rte_IrvWriteHook_StartApplication_StartApplication_DIAG_DcmWriteData_IrvOccuranceCounterDid_Start
 *  Rte_IrvWriteHook_StartApplication_StartApplication_OnDataRec_RxData_IrvOccuranceCounterDid_Return
 *  Rte_IrvWriteHook_StartApplication_StartApplication_OnDataRec_RxData_IrvOccuranceCounterDid_Start
 *  Rte_ReadHook_BswM_Request_ESH_PostRunRequest_0_requestedMode_Return
 *  Rte_ReadHook_BswM_Request_ESH_PostRunRequest_0_requestedMode_Start
 *  Rte_ReadHook_BswM_Request_ESH_PostRunRequest_1_requestedMode_Return
 *  Rte_ReadHook_BswM_Request_ESH_PostRunRequest_1_requestedMode_Start
 *  Rte_ReadHook_BswM_Request_ESH_RunRequest_0_requestedMode_Return
 *  Rte_ReadHook_BswM_Request_ESH_RunRequest_0_requestedMode_Start
 *  Rte_ReadHook_BswM_Request_ESH_RunRequest_1_requestedMode_Return
 *  Rte_ReadHook_BswM_Request_ESH_RunRequest_1_requestedMode_Start
 *  Rte_ReadHook_StartApplication_PpSG_RgRx_Prof1C_RxCycle_omsg_RxCycle_E2eProf1C_500_10_oCAN_430c5af9_Rx_DeSG_RgRx_Prof1C_RxCycle_omsg_RxCycle_E2eProf1C_500_10_oCAN_430c5af9_Rx_Return
 *  Rte_ReadHook_StartApplication_PpSG_RgRx_Prof1C_RxCycle_omsg_RxCycle_E2eProf1C_500_10_oCAN_430c5af9_Rx_DeSG_RgRx_Prof1C_RxCycle_omsg_RxCycle_E2eProf1C_500_10_oCAN_430c5af9_Rx_Start
 *  Rte_ReadHook_StartApplication_PpSig_StartAppl_LinData_Rx_oFrame_LinTr_RearECU_oLIN00_182ca40c_Rx_DeSig_StartAppl_LinData_Rx_oFrame_LinTr_RearECU_oLIN00_182ca40c_Rx_Return
 *  Rte_ReadHook_StartApplication_PpSig_StartAppl_LinData_Rx_oFrame_LinTr_RearECU_oLIN00_182ca40c_Rx_DeSig_StartAppl_LinData_Rx_oFrame_LinTr_RearECU_oLIN00_182ca40c_Rx_Start
 *  Rte_ReadHook_StartApplication_PpSignal_Rx20bit_Cyclic_omsg_RxCycle500_20_oCAN_983b15aa_Rx_DeSignal_Rx20bit_Cyclic_omsg_RxCycle500_20_oCAN_983b15aa_Rx_Return
 *  Rte_ReadHook_StartApplication_PpSignal_Rx20bit_Cyclic_omsg_RxCycle500_20_oCAN_983b15aa_Rx_DeSignal_Rx20bit_Cyclic_omsg_RxCycle500_20_oCAN_983b15aa_Rx_Start
 *  Rte_ReadHook_StartApplication_PpStartAppl_Fr_BothECU_RX_Ctrl_oPDU_Fr_StartAppl_BothECU_RX_cb3aeb40_Rx_DeStartAppl_Fr_BothECU_RX_Ctrl_oPDU_Fr_StartAppl_BothECU_RX_cb3aeb40_Rx_Return
 *  Rte_ReadHook_StartApplication_PpStartAppl_Fr_BothECU_RX_Ctrl_oPDU_Fr_StartAppl_BothECU_RX_cb3aeb40_Rx_DeStartAppl_Fr_BothECU_RX_Ctrl_oPDU_Fr_StartAppl_BothECU_RX_cb3aeb40_Rx_Start
 *  Rte_Runnable_BswM_BswM_MainFunction_Return
 *  Rte_Runnable_BswM_BswM_MainFunction_Start
 *  Rte_Runnable_ComM_ComM_MainFunction_0_Return
 *  Rte_Runnable_ComM_ComM_MainFunction_0_Start
 *  Rte_Runnable_ComM_ComM_MainFunction_1_Return
 *  Rte_Runnable_ComM_ComM_MainFunction_1_Start
 *  Rte_Runnable_ComM_ComM_MainFunction_2_Return
 *  Rte_Runnable_ComM_ComM_MainFunction_2_Start
 *  Rte_Runnable_ComM_GetCurrentComMode_Return
 *  Rte_Runnable_ComM_GetCurrentComMode_Start
 *  Rte_Runnable_ComM_GetInhibitionStatus_Return
 *  Rte_Runnable_ComM_GetInhibitionStatus_Start
 *  Rte_Runnable_ComM_GetMaxComMode_Return
 *  Rte_Runnable_ComM_GetMaxComMode_Start
 *  Rte_Runnable_ComM_GetRequestedComMode_Return
 *  Rte_Runnable_ComM_GetRequestedComMode_Start
 *  Rte_Runnable_ComM_LimitChannelToNoComMode_Return
 *  Rte_Runnable_ComM_LimitChannelToNoComMode_Start
 *  Rte_Runnable_ComM_LimitECUToNoComMode_Return
 *  Rte_Runnable_ComM_LimitECUToNoComMode_Start
 *  Rte_Runnable_ComM_PreventWakeUp_Return
 *  Rte_Runnable_ComM_PreventWakeUp_Start
 *  Rte_Runnable_ComM_ReadInhibitCounter_Return
 *  Rte_Runnable_ComM_ReadInhibitCounter_Start
 *  Rte_Runnable_ComM_RequestComMode_Return
 *  Rte_Runnable_ComM_RequestComMode_Start
 *  Rte_Runnable_ComM_ResetInhibitCounter_Return
 *  Rte_Runnable_ComM_ResetInhibitCounter_Start
 *  Rte_Runnable_ComM_SetECUGroupClassification_Return
 *  Rte_Runnable_ComM_SetECUGroupClassification_Start
 *  Rte_Runnable_Dcm_Dcm_MainFunction_Return
 *  Rte_Runnable_Dcm_Dcm_MainFunction_Start
 *  Rte_Runnable_Dcm_GetActiveProtocol_Return
 *  Rte_Runnable_Dcm_GetActiveProtocol_Start
 *  Rte_Runnable_Dcm_GetRequestKind_Return
 *  Rte_Runnable_Dcm_GetRequestKind_Start
 *  Rte_Runnable_Dcm_GetSecurityLevel_Return
 *  Rte_Runnable_Dcm_GetSecurityLevel_Start
 *  Rte_Runnable_Dcm_GetSesCtrlType_Return
 *  Rte_Runnable_Dcm_GetSesCtrlType_Start
 *  Rte_Runnable_Dcm_ResetToDefaultSession_Return
 *  Rte_Runnable_Dcm_ResetToDefaultSession_Start
 *  Rte_Runnable_Dcm_SetActiveDiagnostic_Return
 *  Rte_Runnable_Dcm_SetActiveDiagnostic_Start
 *  Rte_Runnable_DemMaster_0_ClearDTC_Return
 *  Rte_Runnable_DemMaster_0_ClearDTC_Start
 *  Rte_Runnable_DemMaster_0_Dem_MasterMainFunction_Return
 *  Rte_Runnable_DemMaster_0_Dem_MasterMainFunction_Start
 *  Rte_Runnable_DemMaster_0_GetDTCOfEvent_Return
 *  Rte_Runnable_DemMaster_0_GetDTCOfEvent_Start
 *  Rte_Runnable_DemMaster_0_GetDTCStatusAvailabilityMask_Return
 *  Rte_Runnable_DemMaster_0_GetDTCStatusAvailabilityMask_Start
 *  Rte_Runnable_DemMaster_0_GetDebouncingOfEvent_Return
 *  Rte_Runnable_DemMaster_0_GetDebouncingOfEvent_Start
 *  Rte_Runnable_DemMaster_0_GetEventEnableCondition_Return
 *  Rte_Runnable_DemMaster_0_GetEventEnableCondition_Start
 *  Rte_Runnable_DemMaster_0_GetEventExtendedDataRecordEx_Return
 *  Rte_Runnable_DemMaster_0_GetEventExtendedDataRecordEx_Start
 *  Rte_Runnable_DemMaster_0_GetEventFailed_Return
 *  Rte_Runnable_DemMaster_0_GetEventFailed_Start
 *  Rte_Runnable_DemMaster_0_GetEventFreezeFrameDataEx_Return
 *  Rte_Runnable_DemMaster_0_GetEventFreezeFrameDataEx_Start
 *  Rte_Runnable_DemMaster_0_GetEventMemoryOverflow_Return
 *  Rte_Runnable_DemMaster_0_GetEventMemoryOverflow_Start
 *  Rte_Runnable_DemMaster_0_GetEventStatus_Return
 *  Rte_Runnable_DemMaster_0_GetEventStatus_Start
 *  Rte_Runnable_DemMaster_0_GetEventTested_Return
 *  Rte_Runnable_DemMaster_0_GetEventTested_Start
 *  Rte_Runnable_DemMaster_0_GetEventUdsStatus_Return
 *  Rte_Runnable_DemMaster_0_GetEventUdsStatus_Start
 *  Rte_Runnable_DemMaster_0_GetFaultDetectionCounter_Return
 *  Rte_Runnable_DemMaster_0_GetFaultDetectionCounter_Start
 *  Rte_Runnable_DemMaster_0_GetIndicatorStatus_Return
 *  Rte_Runnable_DemMaster_0_GetIndicatorStatus_Start
 *  Rte_Runnable_DemMaster_0_GetMonitorStatus_Return
 *  Rte_Runnable_DemMaster_0_GetMonitorStatus_Start
 *  Rte_Runnable_DemMaster_0_GetNumberOfEventMemoryEntries_Return
 *  Rte_Runnable_DemMaster_0_GetNumberOfEventMemoryEntries_Start
 *  Rte_Runnable_DemMaster_0_GetOperationCycleState_Return
 *  Rte_Runnable_DemMaster_0_GetOperationCycleState_Start
 *  Rte_Runnable_DemMaster_0_PostRunRequested_Return
 *  Rte_Runnable_DemMaster_0_PostRunRequested_Start
 *  Rte_Runnable_DemMaster_0_SelectDTC_Return
 *  Rte_Runnable_DemMaster_0_SelectDTC_Start
 *  Rte_Runnable_DemMaster_0_SetOperationCycleState_Return
 *  Rte_Runnable_DemMaster_0_SetOperationCycleState_Start
 *  Rte_Runnable_DemSatellite_0_Dem_SatelliteMainFunction_Return
 *  Rte_Runnable_DemSatellite_0_Dem_SatelliteMainFunction_Start
 *  Rte_Runnable_DemSatellite_0_GetDTCOfEvent_Return
 *  Rte_Runnable_DemSatellite_0_GetDTCOfEvent_Start
 *  Rte_Runnable_DemSatellite_0_GetDebouncingOfEvent_Return
 *  Rte_Runnable_DemSatellite_0_GetDebouncingOfEvent_Start
 *  Rte_Runnable_DemSatellite_0_GetEventEnableCondition_Return
 *  Rte_Runnable_DemSatellite_0_GetEventEnableCondition_Start
 *  Rte_Runnable_DemSatellite_0_GetEventExtendedDataRecordEx_Return
 *  Rte_Runnable_DemSatellite_0_GetEventExtendedDataRecordEx_Start
 *  Rte_Runnable_DemSatellite_0_GetEventFailed_Return
 *  Rte_Runnable_DemSatellite_0_GetEventFailed_Start
 *  Rte_Runnable_DemSatellite_0_GetEventFreezeFrameDataEx_Return
 *  Rte_Runnable_DemSatellite_0_GetEventFreezeFrameDataEx_Start
 *  Rte_Runnable_DemSatellite_0_GetEventStatus_Return
 *  Rte_Runnable_DemSatellite_0_GetEventStatus_Start
 *  Rte_Runnable_DemSatellite_0_GetEventTested_Return
 *  Rte_Runnable_DemSatellite_0_GetEventTested_Start
 *  Rte_Runnable_DemSatellite_0_GetEventUdsStatus_Return
 *  Rte_Runnable_DemSatellite_0_GetEventUdsStatus_Start
 *  Rte_Runnable_DemSatellite_0_GetFaultDetectionCounter_Return
 *  Rte_Runnable_DemSatellite_0_GetFaultDetectionCounter_Start
 *  Rte_Runnable_DemSatellite_0_GetMonitorStatus_Return
 *  Rte_Runnable_DemSatellite_0_GetMonitorStatus_Start
 *  Rte_Runnable_DemSatellite_0_ResetEventDebounceStatus_Return
 *  Rte_Runnable_DemSatellite_0_ResetEventDebounceStatus_Start
 *  Rte_Runnable_DemSatellite_0_ResetEventStatus_Return
 *  Rte_Runnable_DemSatellite_0_ResetEventStatus_Start
 *  Rte_Runnable_DemSatellite_0_SetEventStatus_Return
 *  Rte_Runnable_DemSatellite_0_SetEventStatus_Start
 *  Rte_Runnable_Det_ReportError_Return
 *  Rte_Runnable_Det_ReportError_Start
 *  Rte_Runnable_EcuM_EcuM_MainFunction_Return
 *  Rte_Runnable_EcuM_EcuM_MainFunction_Start
 *  Rte_Runnable_EcuM_GetBootTarget_Return
 *  Rte_Runnable_EcuM_GetBootTarget_Start
 *  Rte_Runnable_EcuM_GetLastShutdownTarget_Return
 *  Rte_Runnable_EcuM_GetLastShutdownTarget_Start
 *  Rte_Runnable_EcuM_GetShutdownCause_Return
 *  Rte_Runnable_EcuM_GetShutdownCause_Start
 *  Rte_Runnable_EcuM_GetShutdownTarget_Return
 *  Rte_Runnable_EcuM_GetShutdownTarget_Start
 *  Rte_Runnable_EcuM_SelectBootTarget_Return
 *  Rte_Runnable_EcuM_SelectBootTarget_Start
 *  Rte_Runnable_EcuM_SelectShutdownCause_Return
 *  Rte_Runnable_EcuM_SelectShutdownCause_Start
 *  Rte_Runnable_EcuM_SelectShutdownTarget_Return
 *  Rte_Runnable_EcuM_SelectShutdownTarget_Start
 *  Rte_Runnable_NvM_EraseBlock_Return
 *  Rte_Runnable_NvM_EraseBlock_Start
 *  Rte_Runnable_NvM_GetErrorStatus_Return
 *  Rte_Runnable_NvM_GetErrorStatus_Start
 *  Rte_Runnable_NvM_InvalidateNvBlock_Return
 *  Rte_Runnable_NvM_InvalidateNvBlock_Start
 *  Rte_Runnable_NvM_NvM_MainFunction_Return
 *  Rte_Runnable_NvM_NvM_MainFunction_Start
 *  Rte_Runnable_NvM_ReadBlock_Return
 *  Rte_Runnable_NvM_ReadBlock_Start
 *  Rte_Runnable_NvM_SetBlockProtection_Return
 *  Rte_Runnable_NvM_SetBlockProtection_Start
 *  Rte_Runnable_NvM_SetRamBlockStatus_Return
 *  Rte_Runnable_NvM_SetRamBlockStatus_Start
 *  Rte_Runnable_NvM_WriteBlock_Return
 *  Rte_Runnable_NvM_WriteBlock_Start
 *  Rte_Runnable_Os_OsCore0_swc_GetCounterValue_Return
 *  Rte_Runnable_Os_OsCore0_swc_GetCounterValue_Start
 *  Rte_Runnable_Os_OsCore0_swc_GetElapsedValue_Return
 *  Rte_Runnable_Os_OsCore0_swc_GetElapsedValue_Start
 *  Rte_Runnable_StartApplication_StartApplication_Cyclic1000ms_Return
 *  Rte_Runnable_StartApplication_StartApplication_Cyclic1000ms_Start
 *  Rte_Runnable_StartApplication_StartApplication_Cyclic10ms_Return
 *  Rte_Runnable_StartApplication_StartApplication_Cyclic10ms_Start
 *  Rte_Runnable_StartApplication_StartApplication_Cyclic1ms_Return
 *  Rte_Runnable_StartApplication_StartApplication_Cyclic1ms_Start
 *  Rte_Runnable_StartApplication_StartApplication_Cyclic250ms_Return
 *  Rte_Runnable_StartApplication_StartApplication_Cyclic250ms_Start
 *  Rte_Runnable_StartApplication_StartApplication_DIAG_ConditionCheckRead_Return
 *  Rte_Runnable_StartApplication_StartApplication_DIAG_ConditionCheckRead_Start
 *  Rte_Runnable_StartApplication_StartApplication_DIAG_DcmReadData_Return
 *  Rte_Runnable_StartApplication_StartApplication_DIAG_DcmReadData_Start
 *  Rte_Runnable_StartApplication_StartApplication_DIAG_DcmWriteData_Return
 *  Rte_Runnable_StartApplication_StartApplication_DIAG_DcmWriteData_Start
 *  Rte_Runnable_StartApplication_StartApplication_DIAG_DemReadData_Return
 *  Rte_Runnable_StartApplication_StartApplication_DIAG_DemReadData_Start
 *  Rte_Runnable_StartApplication_StartApplication_Init_Return
 *  Rte_Runnable_StartApplication_StartApplication_Init_Start
 *  Rte_Runnable_StartApplication_StartApplication_MEM_JobFinished_StartApplication_NvMBlock1_Return
 *  Rte_Runnable_StartApplication_StartApplication_MEM_JobFinished_StartApplication_NvMBlock1_Start
 *  Rte_Runnable_StartApplication_StartApplication_MEM_JobFinished_StartApplication_NvMBlock2_Return
 *  Rte_Runnable_StartApplication_StartApplication_MEM_JobFinished_StartApplication_NvMBlock2_Start
 *  Rte_Runnable_StartApplication_StartApplication_OnDataRec_RxCtrl_Return
 *  Rte_Runnable_StartApplication_StartApplication_OnDataRec_RxCtrl_Start
 *  Rte_Runnable_StartApplication_StartApplication_OnDataRec_RxData_Return
 *  Rte_Runnable_StartApplication_StartApplication_OnDataRec_RxData_Start
 *  Rte_SwitchAckHook_Dcm_DcmEcuReset_DcmEcuReset_Return
 *  Rte_SwitchAckHook_Dcm_DcmEcuReset_DcmEcuReset_Start
 *  Rte_SwitchHook_BswM_Switch_ESH_ModeSwitch_BswM_MDGP_ESH_Mode_Return
 *  Rte_SwitchHook_BswM_Switch_ESH_ModeSwitch_BswM_MDGP_ESH_Mode_Start
 *  Rte_SwitchHook_Dcm_DcmCommunicationControl_ComMConf_ComMChannel_CN_CAN_fe6ecc87_DcmCommunicationControl_ComMConf_ComMChannel_CN_CAN_fe6ecc87_Return
 *  Rte_SwitchHook_Dcm_DcmCommunicationControl_ComMConf_ComMChannel_CN_CAN_fe6ecc87_DcmCommunicationControl_ComMConf_ComMChannel_CN_CAN_fe6ecc87_Start
 *  Rte_SwitchHook_Dcm_DcmControlDtcSetting_DcmControlDtcSetting_Return
 *  Rte_SwitchHook_Dcm_DcmControlDtcSetting_DcmControlDtcSetting_Start
 *  Rte_SwitchHook_Dcm_DcmDiagnosticSessionControl_DcmDiagnosticSessionControl_Return
 *  Rte_SwitchHook_Dcm_DcmDiagnosticSessionControl_DcmDiagnosticSessionControl_Start
 *  Rte_SwitchHook_Dcm_DcmEcuReset_DcmEcuReset_Return
 *  Rte_SwitchHook_Dcm_DcmEcuReset_DcmEcuReset_Start
 *  Rte_Task_Activate
 *  Rte_Task_Dispatch
 *  Rte_Task_SetEvent
 *  Rte_Task_Terminate
 *  Rte_Task_WaitEvent
 *  Rte_Task_WaitEventRet
 *  Rte_WriteHook_StartApplication_PpSig_StartAppl_LinData_Tx_oFrame_LinTr_MyECU_oLIN00_d383dc6b_Tx_DeSig_StartAppl_LinData_Tx_oFrame_LinTr_MyECU_oLIN00_d383dc6b_Tx_Return
 *  Rte_WriteHook_StartApplication_PpSig_StartAppl_LinData_Tx_oFrame_LinTr_MyECU_oLIN00_d383dc6b_Tx_DeSig_StartAppl_LinData_Tx_oFrame_LinTr_MyECU_oLIN00_d383dc6b_Tx_Start
 *  Rte_WriteHook_StartApplication_PpSignal_SomeTxSignal_oPDU_Transmit_MyECU_763437bb_Tx_DeSignal_SomeTxSignal_oPDU_Transmit_MyECU_763437bb_Tx_Return
 *  Rte_WriteHook_StartApplication_PpSignal_SomeTxSignal_oPDU_Transmit_MyECU_763437bb_Tx_DeSignal_SomeTxSignal_oPDU_Transmit_MyECU_763437bb_Tx_Start
 *  Rte_WriteHook_StartApplication_PpSignal_Tx10bit_Cyclic_omsg_TxCycle1000_10_oCAN_9539efc7_Tx_DeSignal_Tx10bit_Cyclic_omsg_TxCycle1000_10_oCAN_9539efc7_Tx_Return
 *  Rte_WriteHook_StartApplication_PpSignal_Tx10bit_Cyclic_omsg_TxCycle1000_10_oCAN_9539efc7_Tx_DeSignal_Tx10bit_Cyclic_omsg_TxCycle1000_10_oCAN_9539efc7_Tx_Start
 *  Rte_WriteHook_StartApplication_PpSignal_Tx24bit_Cyclic_omsg_TxCycle10_0_oCAN_393dd5fe_Tx_DeSignal_Tx24bit_Cyclic_omsg_TxCycle10_0_oCAN_393dd5fe_Tx_Return
 *  Rte_WriteHook_StartApplication_PpSignal_Tx24bit_Cyclic_omsg_TxCycle10_0_oCAN_393dd5fe_Tx_DeSignal_Tx24bit_Cyclic_omsg_TxCycle10_0_oCAN_393dd5fe_Tx_Start
 *  SchM_EnterHook_BswM_BSWM_EXCLUSIVE_AREA_0_Return
 *  SchM_EnterHook_BswM_BSWM_EXCLUSIVE_AREA_0_Start
 *  SchM_EnterHook_CanIf_CANIF_EXCLUSIVE_AREA_0_Return
 *  SchM_EnterHook_CanIf_CANIF_EXCLUSIVE_AREA_0_Start
 *  SchM_EnterHook_CanIf_CANIF_EXCLUSIVE_AREA_1_Return
 *  SchM_EnterHook_CanIf_CANIF_EXCLUSIVE_AREA_1_Start
 *  SchM_EnterHook_CanIf_CANIF_EXCLUSIVE_AREA_2_Return
 *  SchM_EnterHook_CanIf_CANIF_EXCLUSIVE_AREA_2_Start
 *  SchM_EnterHook_CanIf_CANIF_EXCLUSIVE_AREA_3_Return
 *  SchM_EnterHook_CanIf_CANIF_EXCLUSIVE_AREA_3_Start
 *  SchM_EnterHook_CanIf_CANIF_EXCLUSIVE_AREA_4_Return
 *  SchM_EnterHook_CanIf_CANIF_EXCLUSIVE_AREA_4_Start
 *  SchM_EnterHook_CanIf_CANIF_EXCLUSIVE_AREA_5_Return
 *  SchM_EnterHook_CanIf_CANIF_EXCLUSIVE_AREA_5_Start
 *  SchM_EnterHook_CanIf_CANIF_EXCLUSIVE_AREA_6_Return
 *  SchM_EnterHook_CanIf_CANIF_EXCLUSIVE_AREA_6_Start
 *  SchM_EnterHook_CanIf_CANIF_EXCLUSIVE_AREA_7_Return
 *  SchM_EnterHook_CanIf_CANIF_EXCLUSIVE_AREA_7_Start
 *  SchM_EnterHook_CanNm_CANNM_EXCLUSIVE_AREA_0_Return
 *  SchM_EnterHook_CanNm_CANNM_EXCLUSIVE_AREA_0_Start
 *  SchM_EnterHook_CanNm_CANNM_EXCLUSIVE_AREA_1_Return
 *  SchM_EnterHook_CanNm_CANNM_EXCLUSIVE_AREA_1_Start
 *  SchM_EnterHook_CanNm_CANNM_EXCLUSIVE_AREA_2_Return
 *  SchM_EnterHook_CanNm_CANNM_EXCLUSIVE_AREA_2_Start
 *  SchM_EnterHook_CanNm_CANNM_EXCLUSIVE_AREA_3_Return
 *  SchM_EnterHook_CanNm_CANNM_EXCLUSIVE_AREA_3_Start
 *  SchM_EnterHook_CanNm_CANNM_EXCLUSIVE_AREA_4_Return
 *  SchM_EnterHook_CanNm_CANNM_EXCLUSIVE_AREA_4_Start
 *  SchM_EnterHook_CanNm_CANNM_EXCLUSIVE_AREA_5_Return
 *  SchM_EnterHook_CanNm_CANNM_EXCLUSIVE_AREA_5_Start
 *  SchM_EnterHook_CanNm_CANNM_EXCLUSIVE_AREA_6_Return
 *  SchM_EnterHook_CanNm_CANNM_EXCLUSIVE_AREA_6_Start
 *  SchM_EnterHook_CanSM_CANSM_EXCLUSIVE_AREA_1_Return
 *  SchM_EnterHook_CanSM_CANSM_EXCLUSIVE_AREA_1_Start
 *  SchM_EnterHook_CanSM_CANSM_EXCLUSIVE_AREA_2_Return
 *  SchM_EnterHook_CanSM_CANSM_EXCLUSIVE_AREA_2_Start
 *  SchM_EnterHook_CanSM_CANSM_EXCLUSIVE_AREA_3_Return
 *  SchM_EnterHook_CanSM_CANSM_EXCLUSIVE_AREA_3_Start
 *  SchM_EnterHook_CanSM_CANSM_EXCLUSIVE_AREA_4_Return
 *  SchM_EnterHook_CanSM_CANSM_EXCLUSIVE_AREA_4_Start
 *  SchM_EnterHook_CanSM_CANSM_EXCLUSIVE_AREA_5_Return
 *  SchM_EnterHook_CanSM_CANSM_EXCLUSIVE_AREA_5_Start
 *  SchM_EnterHook_CanSM_CANSM_EXCLUSIVE_AREA_6_Return
 *  SchM_EnterHook_CanSM_CANSM_EXCLUSIVE_AREA_6_Start
 *  SchM_EnterHook_CanTp_CANTP_EXCLUSIVE_AREA_0_Return
 *  SchM_EnterHook_CanTp_CANTP_EXCLUSIVE_AREA_0_Start
 *  SchM_EnterHook_Can_CAN_EXCLUSIVE_AREA_0_Return
 *  SchM_EnterHook_Can_CAN_EXCLUSIVE_AREA_0_Start
 *  SchM_EnterHook_Can_CAN_EXCLUSIVE_AREA_1_Return
 *  SchM_EnterHook_Can_CAN_EXCLUSIVE_AREA_1_Start
 *  SchM_EnterHook_Can_CAN_EXCLUSIVE_AREA_2_Return
 *  SchM_EnterHook_Can_CAN_EXCLUSIVE_AREA_2_Start
 *  SchM_EnterHook_Can_CAN_EXCLUSIVE_AREA_3_Return
 *  SchM_EnterHook_Can_CAN_EXCLUSIVE_AREA_3_Start
 *  SchM_EnterHook_Can_CAN_EXCLUSIVE_AREA_4_Return
 *  SchM_EnterHook_Can_CAN_EXCLUSIVE_AREA_4_Start
 *  SchM_EnterHook_Can_CAN_EXCLUSIVE_AREA_5_Return
 *  SchM_EnterHook_Can_CAN_EXCLUSIVE_AREA_5_Start
 *  SchM_EnterHook_Can_CAN_EXCLUSIVE_AREA_6_Return
 *  SchM_EnterHook_Can_CAN_EXCLUSIVE_AREA_6_Start
 *  SchM_EnterHook_Can_CAN_EXCLUSIVE_AREA_7_Return
 *  SchM_EnterHook_Can_CAN_EXCLUSIVE_AREA_7_Start
 *  SchM_EnterHook_ComM_COMM_EXCLUSIVE_AREA_0_Return
 *  SchM_EnterHook_ComM_COMM_EXCLUSIVE_AREA_0_Start
 *  SchM_EnterHook_ComM_COMM_EXCLUSIVE_AREA_1_Return
 *  SchM_EnterHook_ComM_COMM_EXCLUSIVE_AREA_1_Start
 *  SchM_EnterHook_Com_COM_EXCLUSIVE_AREA_BOTH_Return
 *  SchM_EnterHook_Com_COM_EXCLUSIVE_AREA_BOTH_Start
 *  SchM_EnterHook_Com_COM_EXCLUSIVE_AREA_RX_Return
 *  SchM_EnterHook_Com_COM_EXCLUSIVE_AREA_RX_Start
 *  SchM_EnterHook_Com_COM_EXCLUSIVE_AREA_TX_Return
 *  SchM_EnterHook_Com_COM_EXCLUSIVE_AREA_TX_Start
 *  SchM_EnterHook_Dcm_DCM_EXCLUSIVE_AREA_0_Return
 *  SchM_EnterHook_Dcm_DCM_EXCLUSIVE_AREA_0_Start
 *  SchM_EnterHook_Dem_DEM_EXCLUSIVE_AREA_0_Return
 *  SchM_EnterHook_Dem_DEM_EXCLUSIVE_AREA_0_Start
 *  SchM_EnterHook_Dem_DEM_EXCLUSIVE_AREA_1_Return
 *  SchM_EnterHook_Dem_DEM_EXCLUSIVE_AREA_1_Start
 *  SchM_EnterHook_Dem_DEM_EXCLUSIVE_AREA_2_Return
 *  SchM_EnterHook_Dem_DEM_EXCLUSIVE_AREA_2_Start
 *  SchM_EnterHook_Dem_DEM_EXCLUSIVE_AREA_3_Return
 *  SchM_EnterHook_Dem_DEM_EXCLUSIVE_AREA_3_Start
 *  SchM_EnterHook_Dem_DEM_EXCLUSIVE_AREA_4_Return
 *  SchM_EnterHook_Dem_DEM_EXCLUSIVE_AREA_4_Start
 *  SchM_EnterHook_Det_DET_EXCLUSIVE_AREA_0_Return
 *  SchM_EnterHook_Det_DET_EXCLUSIVE_AREA_0_Start
 *  SchM_EnterHook_EcuM_ECUM_EXCLUSIVE_AREA_0_Return
 *  SchM_EnterHook_EcuM_ECUM_EXCLUSIVE_AREA_0_Start
 *  SchM_EnterHook_EcuM_ECUM_EXCLUSIVE_AREA_1_Return
 *  SchM_EnterHook_EcuM_ECUM_EXCLUSIVE_AREA_1_Start
 *  SchM_EnterHook_EcuM_ECUM_EXCLUSIVE_AREA_2_Return
 *  SchM_EnterHook_EcuM_ECUM_EXCLUSIVE_AREA_2_Start
 *  SchM_EnterHook_Fls_17_Dmu_Erase_Return
 *  SchM_EnterHook_Fls_17_Dmu_Erase_Start
 *  SchM_EnterHook_Fls_17_Dmu_Init_Return
 *  SchM_EnterHook_Fls_17_Dmu_Init_Start
 *  SchM_EnterHook_Fls_17_Dmu_Main_Return
 *  SchM_EnterHook_Fls_17_Dmu_Main_Start
 *  SchM_EnterHook_Fls_17_Dmu_ResumeErase_Return
 *  SchM_EnterHook_Fls_17_Dmu_ResumeErase_Start
 *  SchM_EnterHook_Fls_17_Dmu_UserContentCount_Return
 *  SchM_EnterHook_Fls_17_Dmu_UserContentCount_Start
 *  SchM_EnterHook_Fls_17_Dmu_Write_Return
 *  SchM_EnterHook_Fls_17_Dmu_Write_Start
 *  SchM_EnterHook_FrIf_FRIF_EXCLUSIVE_AREA_0_Return
 *  SchM_EnterHook_FrIf_FRIF_EXCLUSIVE_AREA_0_Start
 *  SchM_EnterHook_FrIf_FRIF_EXCLUSIVE_AREA_1_Return
 *  SchM_EnterHook_FrIf_FRIF_EXCLUSIVE_AREA_1_Start
 *  SchM_EnterHook_FrIf_FRIF_EXCLUSIVE_AREA_2_Return
 *  SchM_EnterHook_FrIf_FRIF_EXCLUSIVE_AREA_2_Start
 *  SchM_EnterHook_FrNm_FRNM_EXCLUSIVE_AREA_0_Return
 *  SchM_EnterHook_FrNm_FRNM_EXCLUSIVE_AREA_0_Start
 *  SchM_EnterHook_FrNm_FRNM_EXCLUSIVE_AREA_1_Return
 *  SchM_EnterHook_FrNm_FRNM_EXCLUSIVE_AREA_1_Start
 *  SchM_EnterHook_FrNm_FRNM_EXCLUSIVE_AREA_2_Return
 *  SchM_EnterHook_FrNm_FRNM_EXCLUSIVE_AREA_2_Start
 *  SchM_EnterHook_FrNm_FRNM_EXCLUSIVE_AREA_3_Return
 *  SchM_EnterHook_FrNm_FRNM_EXCLUSIVE_AREA_3_Start
 *  SchM_EnterHook_FrNm_FRNM_EXCLUSIVE_AREA_4_Return
 *  SchM_EnterHook_FrNm_FRNM_EXCLUSIVE_AREA_4_Start
 *  SchM_EnterHook_FrNm_FRNM_EXCLUSIVE_AREA_5_Return
 *  SchM_EnterHook_FrNm_FRNM_EXCLUSIVE_AREA_5_Start
 *  SchM_EnterHook_FrNm_FRNM_EXCLUSIVE_AREA_6_Return
 *  SchM_EnterHook_FrNm_FRNM_EXCLUSIVE_AREA_6_Start
 *  SchM_EnterHook_FrSM_FRSM_EXCLUSIVE_AREA_0_Return
 *  SchM_EnterHook_FrSM_FRSM_EXCLUSIVE_AREA_0_Start
 *  SchM_EnterHook_FrSM_FRSM_EXCLUSIVE_AREA_1_Return
 *  SchM_EnterHook_FrSM_FRSM_EXCLUSIVE_AREA_1_Start
 *  SchM_EnterHook_FrSM_FRSM_EXCLUSIVE_AREA_2_Return
 *  SchM_EnterHook_FrSM_FRSM_EXCLUSIVE_AREA_2_Start
 *  SchM_EnterHook_FrSM_FRSM_EXCLUSIVE_AREA_3_Return
 *  SchM_EnterHook_FrSM_FRSM_EXCLUSIVE_AREA_3_Start
 *  SchM_EnterHook_Fr_FR_EXCLUSIVE_AREA_0_Return
 *  SchM_EnterHook_Fr_FR_EXCLUSIVE_AREA_0_Start
 *  SchM_EnterHook_Fr_FR_EXCLUSIVE_AREA_1_Return
 *  SchM_EnterHook_Fr_FR_EXCLUSIVE_AREA_1_Start
 *  SchM_EnterHook_LinIf_LINIF_EXCLUSIVE_AREA_0_Return
 *  SchM_EnterHook_LinIf_LINIF_EXCLUSIVE_AREA_0_Start
 *  SchM_EnterHook_LinIf_LINIF_EXCLUSIVE_AREA_1_Return
 *  SchM_EnterHook_LinIf_LINIF_EXCLUSIVE_AREA_1_Start
 *  SchM_EnterHook_LinIf_LINIF_EXCLUSIVE_AREA_2_Return
 *  SchM_EnterHook_LinIf_LINIF_EXCLUSIVE_AREA_2_Start
 *  SchM_EnterHook_LinNm_LINNM_EXCLUSIVE_AREA_0_Return
 *  SchM_EnterHook_LinNm_LINNM_EXCLUSIVE_AREA_0_Start
 *  SchM_EnterHook_LinSM_LINSM_EXCLUSIVE_AREA_0_Return
 *  SchM_EnterHook_LinSM_LINSM_EXCLUSIVE_AREA_0_Start
 *  SchM_EnterHook_LinSM_LINSM_EXCLUSIVE_AREA_1_Return
 *  SchM_EnterHook_LinSM_LINSM_EXCLUSIVE_AREA_1_Start
 *  SchM_EnterHook_LinSM_LINSM_EXCLUSIVE_AREA_2_Return
 *  SchM_EnterHook_LinSM_LINSM_EXCLUSIVE_AREA_2_Start
 *  SchM_EnterHook_LinSM_LINSM_EXCLUSIVE_AREA_3_Return
 *  SchM_EnterHook_LinSM_LINSM_EXCLUSIVE_AREA_3_Start
 *  SchM_EnterHook_LinSM_LINSM_EXCLUSIVE_AREA_4_Return
 *  SchM_EnterHook_LinSM_LINSM_EXCLUSIVE_AREA_4_Start
 *  SchM_EnterHook_McalLib_CpuEndInit_Return
 *  SchM_EnterHook_McalLib_CpuEndInit_Start
 *  SchM_EnterHook_McalLib_PeripheralEndInit_Return
 *  SchM_EnterHook_McalLib_PeripheralEndInit_Start
 *  SchM_EnterHook_McalLib_SafetyEndInit_Return
 *  SchM_EnterHook_McalLib_SafetyEndInit_Start
 *  SchM_EnterHook_McalLib_StmTimerResolution_Return
 *  SchM_EnterHook_McalLib_StmTimerResolution_Start
 *  SchM_EnterHook_Mcu_AtomAgcReg_Return
 *  SchM_EnterHook_Mcu_AtomAgcReg_Start
 *  SchM_EnterHook_Mcu_TomTgcReg_Return
 *  SchM_EnterHook_Mcu_TomTgcReg_Start
 *  SchM_EnterHook_Nm_NM_EXCLUSIVE_AREA_0_Return
 *  SchM_EnterHook_Nm_NM_EXCLUSIVE_AREA_0_Start
 *  SchM_EnterHook_Nm_NM_EXCLUSIVE_AREA_1_Return
 *  SchM_EnterHook_Nm_NM_EXCLUSIVE_AREA_1_Start
 *  SchM_EnterHook_NvM_NVM_EXCLUSIVE_AREA_0_Return
 *  SchM_EnterHook_NvM_NVM_EXCLUSIVE_AREA_0_Start
 *  SchM_EnterHook_PduR_PDUR_EXCLUSIVE_AREA_0_Return
 *  SchM_EnterHook_PduR_PDUR_EXCLUSIVE_AREA_0_Start
 *  SchM_ExitHook_BswM_BSWM_EXCLUSIVE_AREA_0_Return
 *  SchM_ExitHook_BswM_BSWM_EXCLUSIVE_AREA_0_Start
 *  SchM_ExitHook_CanIf_CANIF_EXCLUSIVE_AREA_0_Return
 *  SchM_ExitHook_CanIf_CANIF_EXCLUSIVE_AREA_0_Start
 *  SchM_ExitHook_CanIf_CANIF_EXCLUSIVE_AREA_1_Return
 *  SchM_ExitHook_CanIf_CANIF_EXCLUSIVE_AREA_1_Start
 *  SchM_ExitHook_CanIf_CANIF_EXCLUSIVE_AREA_2_Return
 *  SchM_ExitHook_CanIf_CANIF_EXCLUSIVE_AREA_2_Start
 *  SchM_ExitHook_CanIf_CANIF_EXCLUSIVE_AREA_3_Return
 *  SchM_ExitHook_CanIf_CANIF_EXCLUSIVE_AREA_3_Start
 *  SchM_ExitHook_CanIf_CANIF_EXCLUSIVE_AREA_4_Return
 *  SchM_ExitHook_CanIf_CANIF_EXCLUSIVE_AREA_4_Start
 *  SchM_ExitHook_CanIf_CANIF_EXCLUSIVE_AREA_5_Return
 *  SchM_ExitHook_CanIf_CANIF_EXCLUSIVE_AREA_5_Start
 *  SchM_ExitHook_CanIf_CANIF_EXCLUSIVE_AREA_6_Return
 *  SchM_ExitHook_CanIf_CANIF_EXCLUSIVE_AREA_6_Start
 *  SchM_ExitHook_CanIf_CANIF_EXCLUSIVE_AREA_7_Return
 *  SchM_ExitHook_CanIf_CANIF_EXCLUSIVE_AREA_7_Start
 *  SchM_ExitHook_CanNm_CANNM_EXCLUSIVE_AREA_0_Return
 *  SchM_ExitHook_CanNm_CANNM_EXCLUSIVE_AREA_0_Start
 *  SchM_ExitHook_CanNm_CANNM_EXCLUSIVE_AREA_1_Return
 *  SchM_ExitHook_CanNm_CANNM_EXCLUSIVE_AREA_1_Start
 *  SchM_ExitHook_CanNm_CANNM_EXCLUSIVE_AREA_2_Return
 *  SchM_ExitHook_CanNm_CANNM_EXCLUSIVE_AREA_2_Start
 *  SchM_ExitHook_CanNm_CANNM_EXCLUSIVE_AREA_3_Return
 *  SchM_ExitHook_CanNm_CANNM_EXCLUSIVE_AREA_3_Start
 *  SchM_ExitHook_CanNm_CANNM_EXCLUSIVE_AREA_4_Return
 *  SchM_ExitHook_CanNm_CANNM_EXCLUSIVE_AREA_4_Start
 *  SchM_ExitHook_CanNm_CANNM_EXCLUSIVE_AREA_5_Return
 *  SchM_ExitHook_CanNm_CANNM_EXCLUSIVE_AREA_5_Start
 *  SchM_ExitHook_CanNm_CANNM_EXCLUSIVE_AREA_6_Return
 *  SchM_ExitHook_CanNm_CANNM_EXCLUSIVE_AREA_6_Start
 *  SchM_ExitHook_CanSM_CANSM_EXCLUSIVE_AREA_1_Return
 *  SchM_ExitHook_CanSM_CANSM_EXCLUSIVE_AREA_1_Start
 *  SchM_ExitHook_CanSM_CANSM_EXCLUSIVE_AREA_2_Return
 *  SchM_ExitHook_CanSM_CANSM_EXCLUSIVE_AREA_2_Start
 *  SchM_ExitHook_CanSM_CANSM_EXCLUSIVE_AREA_3_Return
 *  SchM_ExitHook_CanSM_CANSM_EXCLUSIVE_AREA_3_Start
 *  SchM_ExitHook_CanSM_CANSM_EXCLUSIVE_AREA_4_Return
 *  SchM_ExitHook_CanSM_CANSM_EXCLUSIVE_AREA_4_Start
 *  SchM_ExitHook_CanSM_CANSM_EXCLUSIVE_AREA_5_Return
 *  SchM_ExitHook_CanSM_CANSM_EXCLUSIVE_AREA_5_Start
 *  SchM_ExitHook_CanSM_CANSM_EXCLUSIVE_AREA_6_Return
 *  SchM_ExitHook_CanSM_CANSM_EXCLUSIVE_AREA_6_Start
 *  SchM_ExitHook_CanTp_CANTP_EXCLUSIVE_AREA_0_Return
 *  SchM_ExitHook_CanTp_CANTP_EXCLUSIVE_AREA_0_Start
 *  SchM_ExitHook_Can_CAN_EXCLUSIVE_AREA_0_Return
 *  SchM_ExitHook_Can_CAN_EXCLUSIVE_AREA_0_Start
 *  SchM_ExitHook_Can_CAN_EXCLUSIVE_AREA_1_Return
 *  SchM_ExitHook_Can_CAN_EXCLUSIVE_AREA_1_Start
 *  SchM_ExitHook_Can_CAN_EXCLUSIVE_AREA_2_Return
 *  SchM_ExitHook_Can_CAN_EXCLUSIVE_AREA_2_Start
 *  SchM_ExitHook_Can_CAN_EXCLUSIVE_AREA_3_Return
 *  SchM_ExitHook_Can_CAN_EXCLUSIVE_AREA_3_Start
 *  SchM_ExitHook_Can_CAN_EXCLUSIVE_AREA_4_Return
 *  SchM_ExitHook_Can_CAN_EXCLUSIVE_AREA_4_Start
 *  SchM_ExitHook_Can_CAN_EXCLUSIVE_AREA_5_Return
 *  SchM_ExitHook_Can_CAN_EXCLUSIVE_AREA_5_Start
 *  SchM_ExitHook_Can_CAN_EXCLUSIVE_AREA_6_Return
 *  SchM_ExitHook_Can_CAN_EXCLUSIVE_AREA_6_Start
 *  SchM_ExitHook_Can_CAN_EXCLUSIVE_AREA_7_Return
 *  SchM_ExitHook_Can_CAN_EXCLUSIVE_AREA_7_Start
 *  SchM_ExitHook_ComM_COMM_EXCLUSIVE_AREA_0_Return
 *  SchM_ExitHook_ComM_COMM_EXCLUSIVE_AREA_0_Start
 *  SchM_ExitHook_ComM_COMM_EXCLUSIVE_AREA_1_Return
 *  SchM_ExitHook_ComM_COMM_EXCLUSIVE_AREA_1_Start
 *  SchM_ExitHook_Com_COM_EXCLUSIVE_AREA_BOTH_Return
 *  SchM_ExitHook_Com_COM_EXCLUSIVE_AREA_BOTH_Start
 *  SchM_ExitHook_Com_COM_EXCLUSIVE_AREA_RX_Return
 *  SchM_ExitHook_Com_COM_EXCLUSIVE_AREA_RX_Start
 *  SchM_ExitHook_Com_COM_EXCLUSIVE_AREA_TX_Return
 *  SchM_ExitHook_Com_COM_EXCLUSIVE_AREA_TX_Start
 *  SchM_ExitHook_Dcm_DCM_EXCLUSIVE_AREA_0_Return
 *  SchM_ExitHook_Dcm_DCM_EXCLUSIVE_AREA_0_Start
 *  SchM_ExitHook_Dem_DEM_EXCLUSIVE_AREA_0_Return
 *  SchM_ExitHook_Dem_DEM_EXCLUSIVE_AREA_0_Start
 *  SchM_ExitHook_Dem_DEM_EXCLUSIVE_AREA_1_Return
 *  SchM_ExitHook_Dem_DEM_EXCLUSIVE_AREA_1_Start
 *  SchM_ExitHook_Dem_DEM_EXCLUSIVE_AREA_2_Return
 *  SchM_ExitHook_Dem_DEM_EXCLUSIVE_AREA_2_Start
 *  SchM_ExitHook_Dem_DEM_EXCLUSIVE_AREA_3_Return
 *  SchM_ExitHook_Dem_DEM_EXCLUSIVE_AREA_3_Start
 *  SchM_ExitHook_Dem_DEM_EXCLUSIVE_AREA_4_Return
 *  SchM_ExitHook_Dem_DEM_EXCLUSIVE_AREA_4_Start
 *  SchM_ExitHook_Det_DET_EXCLUSIVE_AREA_0_Return
 *  SchM_ExitHook_Det_DET_EXCLUSIVE_AREA_0_Start
 *  SchM_ExitHook_EcuM_ECUM_EXCLUSIVE_AREA_0_Return
 *  SchM_ExitHook_EcuM_ECUM_EXCLUSIVE_AREA_0_Start
 *  SchM_ExitHook_EcuM_ECUM_EXCLUSIVE_AREA_1_Return
 *  SchM_ExitHook_EcuM_ECUM_EXCLUSIVE_AREA_1_Start
 *  SchM_ExitHook_EcuM_ECUM_EXCLUSIVE_AREA_2_Return
 *  SchM_ExitHook_EcuM_ECUM_EXCLUSIVE_AREA_2_Start
 *  SchM_ExitHook_Fls_17_Dmu_Erase_Return
 *  SchM_ExitHook_Fls_17_Dmu_Erase_Start
 *  SchM_ExitHook_Fls_17_Dmu_Init_Return
 *  SchM_ExitHook_Fls_17_Dmu_Init_Start
 *  SchM_ExitHook_Fls_17_Dmu_Main_Return
 *  SchM_ExitHook_Fls_17_Dmu_Main_Start
 *  SchM_ExitHook_Fls_17_Dmu_ResumeErase_Return
 *  SchM_ExitHook_Fls_17_Dmu_ResumeErase_Start
 *  SchM_ExitHook_Fls_17_Dmu_UserContentCount_Return
 *  SchM_ExitHook_Fls_17_Dmu_UserContentCount_Start
 *  SchM_ExitHook_Fls_17_Dmu_Write_Return
 *  SchM_ExitHook_Fls_17_Dmu_Write_Start
 *  SchM_ExitHook_FrIf_FRIF_EXCLUSIVE_AREA_0_Return
 *  SchM_ExitHook_FrIf_FRIF_EXCLUSIVE_AREA_0_Start
 *  SchM_ExitHook_FrIf_FRIF_EXCLUSIVE_AREA_1_Return
 *  SchM_ExitHook_FrIf_FRIF_EXCLUSIVE_AREA_1_Start
 *  SchM_ExitHook_FrIf_FRIF_EXCLUSIVE_AREA_2_Return
 *  SchM_ExitHook_FrIf_FRIF_EXCLUSIVE_AREA_2_Start
 *  SchM_ExitHook_FrNm_FRNM_EXCLUSIVE_AREA_0_Return
 *  SchM_ExitHook_FrNm_FRNM_EXCLUSIVE_AREA_0_Start
 *  SchM_ExitHook_FrNm_FRNM_EXCLUSIVE_AREA_1_Return
 *  SchM_ExitHook_FrNm_FRNM_EXCLUSIVE_AREA_1_Start
 *  SchM_ExitHook_FrNm_FRNM_EXCLUSIVE_AREA_2_Return
 *  SchM_ExitHook_FrNm_FRNM_EXCLUSIVE_AREA_2_Start
 *  SchM_ExitHook_FrNm_FRNM_EXCLUSIVE_AREA_3_Return
 *  SchM_ExitHook_FrNm_FRNM_EXCLUSIVE_AREA_3_Start
 *  SchM_ExitHook_FrNm_FRNM_EXCLUSIVE_AREA_4_Return
 *  SchM_ExitHook_FrNm_FRNM_EXCLUSIVE_AREA_4_Start
 *  SchM_ExitHook_FrNm_FRNM_EXCLUSIVE_AREA_5_Return
 *  SchM_ExitHook_FrNm_FRNM_EXCLUSIVE_AREA_5_Start
 *  SchM_ExitHook_FrNm_FRNM_EXCLUSIVE_AREA_6_Return
 *  SchM_ExitHook_FrNm_FRNM_EXCLUSIVE_AREA_6_Start
 *  SchM_ExitHook_FrSM_FRSM_EXCLUSIVE_AREA_0_Return
 *  SchM_ExitHook_FrSM_FRSM_EXCLUSIVE_AREA_0_Start
 *  SchM_ExitHook_FrSM_FRSM_EXCLUSIVE_AREA_1_Return
 *  SchM_ExitHook_FrSM_FRSM_EXCLUSIVE_AREA_1_Start
 *  SchM_ExitHook_FrSM_FRSM_EXCLUSIVE_AREA_2_Return
 *  SchM_ExitHook_FrSM_FRSM_EXCLUSIVE_AREA_2_Start
 *  SchM_ExitHook_FrSM_FRSM_EXCLUSIVE_AREA_3_Return
 *  SchM_ExitHook_FrSM_FRSM_EXCLUSIVE_AREA_3_Start
 *  SchM_ExitHook_Fr_FR_EXCLUSIVE_AREA_0_Return
 *  SchM_ExitHook_Fr_FR_EXCLUSIVE_AREA_0_Start
 *  SchM_ExitHook_Fr_FR_EXCLUSIVE_AREA_1_Return
 *  SchM_ExitHook_Fr_FR_EXCLUSIVE_AREA_1_Start
 *  SchM_ExitHook_LinIf_LINIF_EXCLUSIVE_AREA_0_Return
 *  SchM_ExitHook_LinIf_LINIF_EXCLUSIVE_AREA_0_Start
 *  SchM_ExitHook_LinIf_LINIF_EXCLUSIVE_AREA_1_Return
 *  SchM_ExitHook_LinIf_LINIF_EXCLUSIVE_AREA_1_Start
 *  SchM_ExitHook_LinIf_LINIF_EXCLUSIVE_AREA_2_Return
 *  SchM_ExitHook_LinIf_LINIF_EXCLUSIVE_AREA_2_Start
 *  SchM_ExitHook_LinNm_LINNM_EXCLUSIVE_AREA_0_Return
 *  SchM_ExitHook_LinNm_LINNM_EXCLUSIVE_AREA_0_Start
 *  SchM_ExitHook_LinSM_LINSM_EXCLUSIVE_AREA_0_Return
 *  SchM_ExitHook_LinSM_LINSM_EXCLUSIVE_AREA_0_Start
 *  SchM_ExitHook_LinSM_LINSM_EXCLUSIVE_AREA_1_Return
 *  SchM_ExitHook_LinSM_LINSM_EXCLUSIVE_AREA_1_Start
 *  SchM_ExitHook_LinSM_LINSM_EXCLUSIVE_AREA_2_Return
 *  SchM_ExitHook_LinSM_LINSM_EXCLUSIVE_AREA_2_Start
 *  SchM_ExitHook_LinSM_LINSM_EXCLUSIVE_AREA_3_Return
 *  SchM_ExitHook_LinSM_LINSM_EXCLUSIVE_AREA_3_Start
 *  SchM_ExitHook_LinSM_LINSM_EXCLUSIVE_AREA_4_Return
 *  SchM_ExitHook_LinSM_LINSM_EXCLUSIVE_AREA_4_Start
 *  SchM_ExitHook_McalLib_CpuEndInit_Return
 *  SchM_ExitHook_McalLib_CpuEndInit_Start
 *  SchM_ExitHook_McalLib_PeripheralEndInit_Return
 *  SchM_ExitHook_McalLib_PeripheralEndInit_Start
 *  SchM_ExitHook_McalLib_SafetyEndInit_Return
 *  SchM_ExitHook_McalLib_SafetyEndInit_Start
 *  SchM_ExitHook_McalLib_StmTimerResolution_Return
 *  SchM_ExitHook_McalLib_StmTimerResolution_Start
 *  SchM_ExitHook_Mcu_AtomAgcReg_Return
 *  SchM_ExitHook_Mcu_AtomAgcReg_Start
 *  SchM_ExitHook_Mcu_TomTgcReg_Return
 *  SchM_ExitHook_Mcu_TomTgcReg_Start
 *  SchM_ExitHook_Nm_NM_EXCLUSIVE_AREA_0_Return
 *  SchM_ExitHook_Nm_NM_EXCLUSIVE_AREA_0_Start
 *  SchM_ExitHook_Nm_NM_EXCLUSIVE_AREA_1_Return
 *  SchM_ExitHook_Nm_NM_EXCLUSIVE_AREA_1_Start
 *  SchM_ExitHook_NvM_NVM_EXCLUSIVE_AREA_0_Return
 *  SchM_ExitHook_NvM_NVM_EXCLUSIVE_AREA_0_Start
 *  SchM_ExitHook_PduR_PDUR_EXCLUSIVE_AREA_0_Return
 *  SchM_ExitHook_PduR_PDUR_EXCLUSIVE_AREA_0_Start
 *  SchM_Schedulable_BswM_BswM_MainFunction_Return
 *  SchM_Schedulable_BswM_BswM_MainFunction_Start
 *  SchM_Schedulable_CanNm_CanNm_MainFunction_Return
 *  SchM_Schedulable_CanNm_CanNm_MainFunction_Start
 *  SchM_Schedulable_CanSM_CanSM_MainFunction_Return
 *  SchM_Schedulable_CanSM_CanSM_MainFunction_Start
 *  SchM_Schedulable_CanTp_CanTp_MainFunction_Return
 *  SchM_Schedulable_CanTp_CanTp_MainFunction_Start
 *  SchM_Schedulable_Can_Can_MainFunction_BusOff_Return
 *  SchM_Schedulable_Can_Can_MainFunction_BusOff_Start
 *  SchM_Schedulable_Can_Can_MainFunction_Mode_Return
 *  SchM_Schedulable_Can_Can_MainFunction_Mode_Start
 *  SchM_Schedulable_Can_Can_MainFunction_Wakeup_Return
 *  SchM_Schedulable_Can_Can_MainFunction_Wakeup_Start
 *  SchM_Schedulable_ComM_ComM_MainFunction_0_Return
 *  SchM_Schedulable_ComM_ComM_MainFunction_0_Start
 *  SchM_Schedulable_ComM_ComM_MainFunction_1_Return
 *  SchM_Schedulable_ComM_ComM_MainFunction_1_Start
 *  SchM_Schedulable_ComM_ComM_MainFunction_2_Return
 *  SchM_Schedulable_ComM_ComM_MainFunction_2_Start
 *  SchM_Schedulable_Com_Com_MainFunctionRx_Return
 *  SchM_Schedulable_Com_Com_MainFunctionRx_Start
 *  SchM_Schedulable_Com_Com_MainFunctionTx_Return
 *  SchM_Schedulable_Com_Com_MainFunctionTx_Start
 *  SchM_Schedulable_Dcm_Dcm_MainFunction_Return
 *  SchM_Schedulable_Dcm_Dcm_MainFunction_Start
 *  SchM_Schedulable_Dem_Dem_MasterMainFunction_Return
 *  SchM_Schedulable_Dem_Dem_MasterMainFunction_Start
 *  SchM_Schedulable_Dem_Dem_SatelliteMainFunction_Return
 *  SchM_Schedulable_Dem_Dem_SatelliteMainFunction_Start
 *  SchM_Schedulable_EcuM_EcuM_MainFunction_Return
 *  SchM_Schedulable_EcuM_EcuM_MainFunction_Start
 *  SchM_Schedulable_Fee_Fee_MainFunction_Return
 *  SchM_Schedulable_Fee_Fee_MainFunction_Start
 *  SchM_Schedulable_Fls_17_Dmu_Fls_17_Dmu_MainFunction_Return
 *  SchM_Schedulable_Fls_17_Dmu_Fls_17_Dmu_MainFunction_Start
 *  SchM_Schedulable_FrIf_FrIf_MainFunction_0_Return
 *  SchM_Schedulable_FrIf_FrIf_MainFunction_0_Start
 *  SchM_Schedulable_FrNm_FrNm_MainFunction_0_Return
 *  SchM_Schedulable_FrNm_FrNm_MainFunction_0_Start
 *  SchM_Schedulable_FrSM_FrSM_MainFunction_0_Return
 *  SchM_Schedulable_FrSM_FrSM_MainFunction_0_Start
 *  SchM_Schedulable_LinIf_LinIf_MainFunction_Return
 *  SchM_Schedulable_LinIf_LinIf_MainFunction_Start
 *  SchM_Schedulable_LinSM_LinSM_MainFunction_Return
 *  SchM_Schedulable_LinSM_LinSM_MainFunction_Start
 *  SchM_Schedulable_NvM_NvM_MainFunction_Return
 *  SchM_Schedulable_NvM_NvM_MainFunction_Start
 *  SchM_Schedulable_Rte_Rte_ComSendSignalProxyPeriodic_Return
 *  SchM_Schedulable_Rte_Rte_ComSendSignalProxyPeriodic_Start
 *
 *********************************************************************************************************************/

/* double include prevention */
#ifndef RTE_HOOK_H
# define RTE_HOOK_H

# include "Os.h" /* PRQA S 0828, 0883 */ /* MD_MSR_Dir1.1, MD_Rte_Os */

# include "Rte_Type.h"
# include "Rte_Cfg.h"

# ifndef RTE_VFB_TRACE
#  define RTE_VFB_TRACE (0)
# endif

#endif /* RTE_HOOK_H */

/**********************************************************************************************************************
 MISRA 2012 violations and justifications
 *********************************************************************************************************************/

/* module specific MISRA deviations:
   MD_Rte_Os:
     Reason:     This justification is used as summary justification for all deviations caused by the MICROSAR OS
                 which is for testing of the RTE. Those deviations are no issues in the RTE code.
     Risk:       No functional risk.
     Prevention: Not required.

*/
