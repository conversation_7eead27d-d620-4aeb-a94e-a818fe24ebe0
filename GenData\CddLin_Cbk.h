/**********************************************************************************************************************
 *  COPYRIGHT
 *  -------------------------------------------------------------------------------------------------------------------
 *  \verbatim
 *
 *                 This software is copyright protected and proprietary to Vector Informatik GmbH.
 *                 Vector Informatik GmbH grants to you only those rights as set out in the license conditions.
 *                 All other rights remain with Vector Informatik GmbH.
 *  \endverbatim
 *  -------------------------------------------------------------------------------------------------------------------
 *  LICENSE
 *  -------------------------------------------------------------------------------------------------------------------
 *            Module: Cdd
 *           Program: MSR_Vector_SLP4
 *          Customer: Infineon Technologies AG
 *       Expiry Date: Not restricted
 *  Ordered Derivat.: Aurix 2G/TC377
 *    License Scope : The usage is restricted to CBD2000456_D00
 *
 *  -------------------------------------------------------------------------------------------------------------------
 *  FILE DESCRIPTION
 *  -------------------------------------------------------------------------------------------------------------------
 *              File: CddLin_Cbk.h
 *   Generation Time: 2020-08-07 11:09:59
 *           Project: Demo - Version 1.0
 *          Delivery: CBD2000456_D00
 *      Tool Version: DaVinci Configurator (beta) 5.21.40 SP2
 *
 *
 *********************************************************************************************************************/

/**********************************************************************************************************************
 ! BETA VERSION                                                                                                       !
 !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
 ! This version of DaVinci Configurator Pro and/or the related Basic Software Package is BETA software.               !
 ! BETA Software is basically operable, but not sufficiently tested, verified and/or qualified for use in series      !
 ! production and/or in vehicles operating on public or non-public roads.                                             !
 ! In particular, without limitation, BETA Software may cause unpredictable ECU behavior, may not provide all         !
 ! functions necessary for use in series production and/or may not comply with quality requirements which are         !
 ! necessary according to the state of the art. BETA Software must not be used in series production.                  !
 !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
**********************************************************************************************************************/



#if !defined (CDDLIN_CBK_H)
# define CDDLIN_CBK_H

/**********************************************************************************************************************
  INCLUDES
**********************************************************************************************************************/
# include "ComStack_Types.h"



/**********************************************************************************************************************
  GLOBAL CONSTANT MACROS
**********************************************************************************************************************/
#ifndef CDDLIN_USE_DUMMY_STATEMENT
#define CDDLIN_USE_DUMMY_STATEMENT STD_OFF /* /MICROSAR/vSet/vSetGeneral/vSetDummyStatementKind */
#endif
#ifndef CDDLIN_DUMMY_STATEMENT
#define CDDLIN_DUMMY_STATEMENT(v)  /* PRQA S 3453 */ /* MD_MSR_FctLikeMacro */  /* /MICROSAR/vSet/vSetGeneral/vSetDummyStatementKind */
#endif
#ifndef CDDLIN_DUMMY_STATEMENT_CONST
#define CDDLIN_DUMMY_STATEMENT_CONST(v)  /* PRQA S 3453 */ /* MD_MSR_FctLikeMacro */  /* /MICROSAR/vSet/vSetGeneral/vSetDummyStatementKind */
#endif
#ifndef CDDLIN_ATOMIC_BIT_ACCESS_IN_BITFIELD
#define CDDLIN_ATOMIC_BIT_ACCESS_IN_BITFIELD STD_OFF /* /MICROSAR/EcuC/EcucGeneral/AtomicBitAccessInBitfield */
#endif
#ifndef CDDLIN_ATOMIC_VARIABLE_ACCESS
#define CDDLIN_ATOMIC_VARIABLE_ACCESS 32u /* /MICROSAR/EcuC/EcucGeneral/AtomicVariableAccess */
#endif
#ifndef CDDLIN_PROCESSOR_TC377T
#define CDDLIN_PROCESSOR_TC377T
#endif
#ifndef CDDLIN_COMP_TASKING
#define CDDLIN_COMP_TASKING
#endif
#ifndef CDDLIN_GEN_GENERATOR_MSR
#define CDDLIN_GEN_GENERATOR_MSR
#endif
#ifndef CDDLIN_CPUTYPE_BITORDER_LSB2MSB
#define CDDLIN_CPUTYPE_BITORDER_LSB2MSB /* /MICROSAR/vSet/vSetPlatform/vSetBitOrder */
#endif
#ifndef CDDLIN_CONFIGURATION_VARIANT_PRECOMPILE
#define CDDLIN_CONFIGURATION_VARIANT_PRECOMPILE 1
#endif
#ifndef CDDLIN_CONFIGURATION_VARIANT_LINKTIME
#define CDDLIN_CONFIGURATION_VARIANT_LINKTIME 2
#endif
#ifndef CDDLIN_CONFIGURATION_VARIANT_POSTBUILD_LOADABLE
#define CDDLIN_CONFIGURATION_VARIANT_POSTBUILD_LOADABLE 3
#endif
#ifndef CDDLIN_CONFIGURATION_VARIANT
#define CDDLIN_CONFIGURATION_VARIANT CDDLIN_CONFIGURATION_VARIANT_PRECOMPILE
#endif
#ifndef CDDLIN_POSTBUILD_VARIANT_SUPPORT
#define CDDLIN_POSTBUILD_VARIANT_SUPPORT STD_OFF
#endif


#define CDDLIN_COMIF  STD_OFF
#define CDDLIN_COMIF_RX  STD_OFF
#define CDDLIN_COMIF_TX  STD_OFF
#define CDDLIN_COMIF_TRIGGERTRANSMIT  STD_OFF

#define CDDLIN_PDUR_UL_COMIF  STD_OFF
#define CDDLIN_PDUR_UL_COMIF_TRIGGERTRANSMIT  STD_OFF

#define CDDLIN_PDUR_UL_COMTP  STD_ON

#define CDDLIN_PDUR_LL_COMIF  STD_OFF
#define CDDLIN_PDUR_LL_COMIF_TRIGGERTRANSMIT  STD_OFF

#define CDDLIN_PDUR_LL_COMTP  STD_OFF

#define CDDLIN_SOADUL_COMIF_RX  STD_OFF
#define CDDLIN_SOADUL_COMIF_TRIGGERTRANSMIT  STD_OFF
#define CDDLIN_SOADUL_COMIF_TXCONFIRMATION   STD_OFF

#define CDDLIN_SOADUL_COMTP_RX  STD_OFF
#define CDDLIN_SOADUL_COMTP_TX  STD_OFF





/**
 * \defgroup CddLinHandleIdsPduRUpperLayerRx Handle IDs of handle space PduRUpperLayerRx.
 * \brief CddPduRUpperLayerContribution Rx PDUs
 * \{
 */

/* Handle IDs active in all predefined variants (the application has not to take the active variant into account) */
/*      Symbolic Name                                                 Value   Active in predefined variant(s) */
#define CddLinConf_CddPduRUpperLayerRxPdu_SlaveResp_RearECU_oLIN00_c13bc529_Rx 0u
#define CddLinConf_CddPduRUpperLayerRxPdu_SlaveResp_Slave3_oLIN00_97811d8d_Rx 1u
/**\} */

/**
 * \defgroup CddLinHandleIdsPduRUpperLayerTx Handle IDs of handle space PduRUpperLayerTx.
 * \brief CddPduRUpperLayerContribution Tx PDUs
 * \{
 */

/* Handle IDs active in all predefined variants (the application has not to take the active variant into account) */
/*      Symbolic Name                                                 Value   Active in predefined variant(s) */
#define CddLinConf_CddPduRUpperLayerTxPdu_MasterReq_RearECU_oLIN00_8ed7799b_Tx 1u
#define CddLinConf_CddPduRUpperLayerTxPdu_MasterReq_Slave3_oLIN00_a4cffd2e_Tx 0u
#define CddLinConf_CddPduRUpperLayerTxPdu_MasterReq_oLIN00_4a2bb011_Tx 2u
/**\} */

/**********************************************************************************************************************
  GLOBAL FUNCTION MACROS
**********************************************************************************************************************/

/**********************************************************************************************************************
  GLOBAL DATA TYPES AND STRUCTURES
**********************************************************************************************************************/

/**********************************************************************************************************************
  GLOBAL DATA PROTOTYPES
**********************************************************************************************************************/

/**********************************************************************************************************************
  GLOBAL FUNCTION PROTOTYPES
**********************************************************************************************************************/
# define CDDLIN_START_SEC_CODE
/*lint -save -esym(961, 19.1) */
# include "MemMap.h"    /* PRQA S 5087 */       /* MD_MSR_MemMap */
/*lint -restore */





/*! \defgroup ProvidedCddCddLinTransportProtocol Provided CddLin transport protocol interface to PduR
    \brief    These services have to be provided by the CddLin if the CDD is an upper layer for the PduR. */
/*\{*/

/**********************************************************************************************************************
  CddLin_StartOfReception
**********************************************************************************************************************/
/*! \brief       The function call indicates the reception start of a segmented PDU.
    \param[in]   id             id of the TP CddPduRUpperLayerRxPdu.
    \param[in]   info           Pointer to a PduInfoType structure containing the payload data
    \param[in]   TpSduLength    length of the entire the TP SDU that will be received.
    \param[out]  bufferSizePtr  length of the available receive buffer in CddLin.\n
                                This parameter is used e.g. in CanTp to calculate the Block Size (BS).
    \return      a BufReq_ReturnType constant of ComStackTypes.h.
    \pre         The CddLin is initialized and active.
    \context     This function can be called on interrupt and task level and has not to be interrupted by other\n
                 CddLin_StartOfReception calls for the same id.
    \note        The function is called by the PduR.
**********************************************************************************************************************/
FUNC(BufReq_ReturnType, CDDLIN_CODE) CddLin_StartOfReception(PduIdType id, P2VAR(PduInfoType, AUTOMATIC, CDDLIN_APPL_DATA) info, PduLengthType TpSduLength, P2VAR(PduLengthType, AUTOMATIC, CDDLIN_APPL_DATA) bufferSizePtr);

/**********************************************************************************************************************
  CddLin_CopyRxData
**********************************************************************************************************************/
/*! \brief       This function is called to trigger the copy process of a segmented PDU.\n
                 The function can be called several times and\n
                 each call to this function copies parts of the received data.\n
    \param[in]   id             id of the TP CddPduRUpperLayerRxPdu.
    \param[in]   info           a PduInfoType pointing to the data to be copied in the CddLin data buffer.
    \param[out]  bufferSizePtr  available receive buffer after data has been copied.
    \return      a BufReq_ReturnType constant of ComStackTypes.h.      
    \pre         The CddLin is initialized and active.
    \context     This function can be called on interrupt and task level and has not to be interrupted by other\n
                 CddLin_CopyRxData calls for the same id.
    \note        The function is called by the PduR.
**********************************************************************************************************************/
FUNC(BufReq_ReturnType, CDDLIN_CODE) CddLin_CopyRxData(PduIdType id, P2VAR(PduInfoType, AUTOMATIC, CDDLIN_APPL_DATA) info, P2VAR(PduLengthType, AUTOMATIC, CDDLIN_APPL_DATA) bufferSizePtr);

/**********************************************************************************************************************
  CddLin_TpRxIndication
**********************************************************************************************************************/
/*! \brief       The function is called to indicate the complete receiption of a CddLin TP SDU
                 or to report an error that occurred during reception.
    \param[in]   id             id of the TP CddPduRUpperLayerRxPdu.
    \param[in]   result         a Std_ReturnType to indicate the result of the reception.
    \return      none
    \pre         The CddLin is initialized and active.
    \context     This function can be called on interrupt and task level and has not to be interrupted by other\n
                 CddLin_TpRxIndication calls for the same id.
    \note        The function is called by the PduR.
**********************************************************************************************************************/
FUNC(void, CDDLIN_CODE) CddLin_TpRxIndication(PduIdType id, Std_ReturnType result);

/**********************************************************************************************************************
  CddLin_CopyTxData
**********************************************************************************************************************/
/*! \brief       This function is called to request transmit data of a TP CddPduRUpperLayerTxPdu\n
                 The function can be called several times and\n
                 each call to this function copies the next part of the data to be transmitted.\n
    \param[in]   id             id of the TP CddPduRUpperLayerTxPdu.
    \param[in]   info           a PduInfoType pointing to the destination buffer.
    \param[in]   retry          NULL_PTR to indicate a successful copy process\n
                                or a RetryInfoType containing a TpDataStateType constant of ComStackTypes.h.
    \param       availableDataPtr   Indicates the remaining number of bytes that are available in the TX buffer.\n
                                availableDataPtr can be used by TP modules that support dynamic payload lengths\n
                                (e.g. Iso FrTp) to determine the size of the following CFs.
    \return      a BufReq_ReturnType constant of ComStackTypes.h.
    \pre         The CddLin is initialized and active.
    \context     This function can be called on interrupt and task level and has not to be interrupted by other\n
                 CddLin_CopyTxData calls for the same id.
    \note        The function is called by the PduR.
**********************************************************************************************************************/
FUNC(BufReq_ReturnType, CDDLIN_CODE) CddLin_CopyTxData(PduIdType id, P2VAR(PduInfoType, AUTOMATIC, CDDLIN_APPL_DATA) info, P2VAR(RetryInfoType, AUTOMATIC, CDDLIN_APPL_DATA) retry, P2VAR(PduLengthType, AUTOMATIC, CDDLIN_APPL_DATA) availableDataPtr);

/**********************************************************************************************************************
  CddLin_TpTxConfirmation
**********************************************************************************************************************/
/*! \brief       The function is called to confirm a successful transmission of a TP CddPduRUpperLayerTxPdu\n
                 or to report an error that occurred during transmission.
    \param[in]   id             id of the TP CddPduRUpperLayerTxPdu.
    \param[in]   result         a Std_ReturnType to indicate the result of the transmission.
    \return      none
    \pre         The CddLin is initialized and active.
    \context     This function can be called on interrupt and task level and has not to be interrupted by other\n
                 CddLin_TpTxConfirmation calls for the same id.
    \note        The function is called by the PduR.
**********************************************************************************************************************/
FUNC(void, CDDLIN_CODE) CddLin_TpTxConfirmation(PduIdType id, Std_ReturnType result);

/*\}*/


# define CDDLIN_STOP_SEC_CODE
/*lint -save -esym(961, 19.1) */
# include "MemMap.h"    /* PRQA S 5087 */       /* MD_MSR_MemMap */
/*lint -restore */

#endif  /* CDDLIN_CBK_H */
/**********************************************************************************************************************
  END OF FILE: CddLin_Cbk.h
**********************************************************************************************************************/


