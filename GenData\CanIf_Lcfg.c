/**********************************************************************************************************************
 *  COPYRIGHT
 *  -------------------------------------------------------------------------------------------------------------------
 *  \verbatim
 *
 *                 This software is copyright protected and proprietary to Vector Informatik GmbH.
 *                 Vector Informatik GmbH grants to you only those rights as set out in the license conditions.
 *                 All other rights remain with Vector Informatik GmbH.
 *  \endverbatim
 *  -------------------------------------------------------------------------------------------------------------------
 *  LICENSE
 *  -------------------------------------------------------------------------------------------------------------------
 *            Module: CanIf
 *           Program: MSR_Vector_SLP4
 *          Customer: AUTOSAR 4 Evaluation Bundle
 *       Expiry Date: Not restricted
 *  Ordered Derivat.: Aurix 2G/TC377
 *    License Scope : The usage is restricted to CBD2000456_D02
 *
 *  -------------------------------------------------------------------------------------------------------------------
 *  FILE DESCRIPTION
 *  -------------------------------------------------------------------------------------------------------------------
 *              File: CanIf_Lcfg.c
 *   Generation Time: 2025-08-05 10:37:17
 *           Project: Demo - Version 1.0
 *          Delivery: CBD2000456_D02
 *      Tool Version: DaVinci Configurator (beta) 5.21.40 SP2
 *
 *
 *********************************************************************************************************************/

/**********************************************************************************************************************
 ! BETA VERSION                                                                                                       !
 !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
 ! This version of DaVinci Configurator Pro and/or the related Basic Software Package is BETA software.               !
 ! BETA Software is basically operable, but not sufficiently tested, verified and/or qualified for use in series      !
 ! production and/or in vehicles operating on public or non-public roads.                                             !
 ! In particular, without limitation, BETA Software may cause unpredictable ECU behavior, may not provide all         !
 ! functions necessary for use in series production and/or may not comply with quality requirements which are         !
 ! necessary according to the state of the art. BETA Software must not be used in series production.                  !
 !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
**********************************************************************************************************************/

#define CANIF_LCFG_SOURCE

/**********************************************************************************************************************
  \file  Includes
**********************************************************************************************************************/
/** 
  \brief  Required external files.
*/ 

#include "CanIf_Cfg.h"

 /*  CanNm Header Files  */ 
#include "CanNm_Cfg.h"
#include "CanNm_Cbk.h"
 /*  CanTp Header Files  */ 
#include "CanTp_Cfg.h"
#include "CanTp_Cbk.h"
 /*  PduR Header Files  */ 
#include "PduR_Cfg.h"
#include "PduR_CanIf.h"
 /*  CanSM Header Files  */ 
#include "CanSM_Cbk.h"

#define CANIF_START_SEC_APPL_CODE

#include "MemMap.h" /* PRQA S 5087 */  /* MD_MSR_MemMap */

/**********************************************************************************************************************
  \var  Prototypes of callback functions
**********************************************************************************************************************/

/** 
  \brief  Tx confirmation functions.
*/



/** 
  \brief  Rx indication functions.
*/





#define CANIF_STOP_SEC_APPL_CODE

#include "MemMap.h" /* PRQA S 5087 */  /* MD_MSR_MemMap */

/**********************************************************************************************************************
  ComStackLib
**********************************************************************************************************************/
/**********************************************************************************************************************
  LOCAL DATA PROTOTYPES
**********************************************************************************************************************/
/**********************************************************************************************************************
  CONFIGURATION CLASS: PRE_COMPILE
  SECTION: LOCAL DATA TYPES AND STRUCTURES
**********************************************************************************************************************/

/**********************************************************************************************************************
  CONFIGURATION CLASS: LINK
  SECTION: LOCAL DATA TYPES AND STRUCTURES
**********************************************************************************************************************/


/**********************************************************************************************************************
  LOCAL DATA
**********************************************************************************************************************/
/**********************************************************************************************************************
  CONFIGURATION CLASS: PRE_COMPILE
  SECTION: LOCAL DATA
**********************************************************************************************************************/

/**********************************************************************************************************************
  CONFIGURATION CLASS: LINK
  SECTION: LOCAL DATA
**********************************************************************************************************************/


/**********************************************************************************************************************
  GLOBAL DATA
**********************************************************************************************************************/
/**********************************************************************************************************************
  CONFIGURATION CLASS: PRE_COMPILE
  SECTION: GLOBAL DATA
**********************************************************************************************************************/
/**********************************************************************************************************************
  CanIf_BusOffNotificationFctPtr
**********************************************************************************************************************/
#define CANIF_START_SEC_CONST_UNSPECIFIED
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */
CONST(CanIf_BusOffNotificationFctType, CANIF_CONST) CanIf_BusOffNotificationFctPtr = CanSM_ControllerBusOff;  /* PRQA S 1514, 1533 */  /* MD_CSL_ObjectOnlyAccessedOnce */
#define CANIF_STOP_SEC_CONST_UNSPECIFIED
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */

/**********************************************************************************************************************
  CanIf_CanIfCtrlId2MappedTxBuffersConfig
**********************************************************************************************************************/
/** 
  \var    CanIf_CanIfCtrlId2MappedTxBuffersConfig
  \brief  CAN controller configuration - mapped Tx-buffer(s).
  \details
  Element                          Description
  MappedTxBuffersConfigEndIdx      the end index of the 1:n relation pointing to CanIf_MappedTxBuffersConfig
  MappedTxBuffersConfigStartIdx    the start index of the 1:n relation pointing to CanIf_MappedTxBuffersConfig
*/ 
#define CANIF_START_SEC_CONST_UNSPECIFIED
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */
CONST(CanIf_CanIfCtrlId2MappedTxBuffersConfigType, CANIF_CONST) CanIf_CanIfCtrlId2MappedTxBuffersConfig[1] = {  /* PRQA S 1514, 1533 */  /* MD_CSL_ObjectOnlyAccessedOnce */
    /* Index    MappedTxBuffersConfigEndIdx                                                                    MappedTxBuffersConfigStartIdx                                                                          Referable Keys */
  { /*     0 */                          1u  /* /ActiveEcuC/CanIf/CanIfCtrlDrvCfg_7d254554/CT_CAN_d43e43a4 */,                            0u  /* /ActiveEcuC/CanIf/CanIfCtrlDrvCfg_7d254554/CT_CAN_d43e43a4 */ }   /* [/ActiveEcuC/CanIf/CanIfCtrlDrvCfg_7d254554/CT_CAN_d43e43a4] */
};
#define CANIF_STOP_SEC_CONST_UNSPECIFIED
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */

/**********************************************************************************************************************
  CanIf_CtrlModeIndicationFctPtr
**********************************************************************************************************************/
#define CANIF_START_SEC_CONST_UNSPECIFIED
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */
CONST(CanIf_CtrlModeIndicationFctType, CANIF_CONST) CanIf_CtrlModeIndicationFctPtr = CanSM_ControllerModeIndication;  /* PRQA S 1514, 1533 */  /* MD_CSL_ObjectOnlyAccessedOnce */
#define CANIF_STOP_SEC_CONST_UNSPECIFIED
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */

/**********************************************************************************************************************
  CanIf_MailBoxConfig
**********************************************************************************************************************/
/** 
  \var    CanIf_MailBoxConfig
  \brief  Mailbox table.
  \details
  Element                 Description
  CtrlStatesIdx           the index of the 1:1 relation pointing to CanIf_CtrlStates
  PduIdFirst              "First" PDU mapped to mailbox.
  PduIdLast               "Last" PDU mapped to mailbox.
  TxBufferCfgIdx          the index of the 0:1 relation pointing to CanIf_TxBufferPrioByCanIdByteQueueConfig
  TxBufferHandlingType
  MailBoxType             Type of mailbox: Rx-/Tx- BasicCAN/FullCAN/unused.
*/ 
#define CANIF_START_SEC_CONST_UNSPECIFIED
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */
CONST(CanIf_MailBoxConfigType, CANIF_CONST) CanIf_MailBoxConfig[2] = {  /* PRQA S 1514, 1533 */  /* MD_CSL_ObjectOnlyAccessedOnce */
    /* Index    CtrlStatesIdx                                                                    PduIdFirst                           PduIdLast                           TxBufferCfgIdx                                                                              TxBufferHandlingType                     MailBoxType                    Referable Keys */
  { /*     0 */            0u  /* /ActiveEcuC/CanIf/CanIfCtrlDrvCfg_7d254554/CT_CAN_d43e43a4 */,         0u  /* Unused, TxPduId 0 */,        0u  /* Unused, TxPduId 7 */,                                     0u  /* /ActiveEcuC/CanIf/CanIfInitCfg/CHNL_346f2748 */, CANIF_TXBUFFER_HANDLINGTYPE_PRIOBYCANID, CANIF_TxBasicCANMailbox },  /* [/ActiveEcuC/Can/CanConfigSet/CN_CAN_fe6ecc87_Tx] */
  { /*     1 */            0u  /* /ActiveEcuC/CanIf/CanIfCtrlDrvCfg_7d254554/CT_CAN_d43e43a4 */,         1u  /* RxPduId */          ,        7u  /* RxPduId  */         , CANIF_NO_TXBUFFERCFGIDXOFMAILBOXCONFIG  /* unusedIndex1 */                                , CANIF_TXBUFFER_HANDLINGTYPE_NONE       , CANIF_RxBasicCANMailbox }   /* [/ActiveEcuC/Can/CanConfigSet/CN_CAN_23287e84_Rx] */
};
#define CANIF_STOP_SEC_CONST_UNSPECIFIED
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */

/**********************************************************************************************************************
  CanIf_MappedTxBuffersConfig
**********************************************************************************************************************/
/** 
  \var    CanIf_MappedTxBuffersConfig
  \brief  Mapped Tx-buffer(s)
  \details
  Element             Description
  MailBoxConfigIdx    the index of the 1:1 relation pointing to CanIf_MailBoxConfig
*/ 
#define CANIF_START_SEC_CONST_UNSPECIFIED
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */
CONST(CanIf_MappedTxBuffersConfigType, CANIF_CONST) CanIf_MappedTxBuffersConfig[1] = {  /* PRQA S 1514, 1533 */  /* MD_CSL_ObjectOnlyAccessedOnce */
    /* Index    MailBoxConfigIdx                                                               Referable Keys */
  { /*     0 */               0u  /* /ActiveEcuC/Can/CanConfigSet/CN_CAN_fe6ecc87_Tx */ }   /* [/ActiveEcuC/CanIf/CanIfCtrlDrvCfg_7d254554/CT_CAN_d43e43a4] */
};
#define CANIF_STOP_SEC_CONST_UNSPECIFIED
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */

/**********************************************************************************************************************
  CanIf_RxIndicationFctList
**********************************************************************************************************************/
/** 
  \var    CanIf_RxIndicationFctList
  \brief  Rx indication functions table.
  \details
  Element               Description
  RxIndicationFct       Rx indication function.
  RxIndicationLayout    Layout of Rx indication function.
*/ 
#define CANIF_START_SEC_CONST_UNSPECIFIED
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */
CONST(CanIf_RxIndicationFctListType, CANIF_CONST) CanIf_RxIndicationFctList[4] = {  /* PRQA S 1514, 1533 */  /* MD_CSL_ObjectOnlyAccessedOnce */
    /* Index    RxIndicationFct                                               RxIndicationLayout                                                                 Referable Keys */
  { /*     0 */  { (CanIf_SimpleRxIndicationFctType)NULL_PTR }              , CanIf_SimpleRxIndicationLayout    /* PRQA S 0313 */ /* MD_CanIf_Rule11.1 */ },  /* [NULL_PTR] */
  { /*     1 */  { (CanIf_SimpleRxIndicationFctType)CanNm_RxIndication }    , CanIf_AdvancedRxIndicationLayout  /* PRQA S 0313 */ /* MD_CanIf_Rule11.1 */ },  /* [CanNm_RxIndication] */
  { /*     2 */  { (CanIf_SimpleRxIndicationFctType)CanTp_RxIndication }    , CanIf_AdvancedRxIndicationLayout  /* PRQA S 0313 */ /* MD_CanIf_Rule11.1 */ },  /* [CanTp_RxIndication] */
  { /*     3 */  { (CanIf_SimpleRxIndicationFctType)PduR_CanIfRxIndication }, CanIf_AdvancedRxIndicationLayout  /* PRQA S 0313 */ /* MD_CanIf_Rule11.1 */ }   /* [PduR_CanIfRxIndication] */
};
#define CANIF_STOP_SEC_CONST_UNSPECIFIED
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */

/**********************************************************************************************************************
  CanIf_RxPduConfig
**********************************************************************************************************************/
/** 
  \var    CanIf_RxPduConfig
  \brief  Rx-PDU configuration table.
  \details
  Element                   Description
  RxPduCanId                Rx-PDU: CAN identifier.
  RxPduMask                 Rx-PDU: CAN identifier mask.
  UpperPduId                PDU ID defined by upper layer.
  RxIndicationFctListIdx    the index of the 1:1 relation pointing to CanIf_RxIndicationFctList
  RxPduDlc                  Rx-PDU length (DLC).
  MsgType                   Type of CAN message: *CAN (both 2.0 or FD), *FD_CAN (only FD), *NO_FD_CAN (only 2.0).
*/ 
#define CANIF_START_SEC_CONST_UNSPECIFIED
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */
CONST(CanIf_RxPduConfigType, CANIF_CONST) CanIf_RxPduConfig[8] = {  /* PRQA S 1514, 1533 */  /* MD_CSL_ObjectOnlyAccessedOnce */
    /* Index    RxPduCanId                                                                       RxPduMask                                                       UpperPduId                                  RxIndicationFctListIdx                                RxPduDlc                              MsgType                         Comment                                                                        Referable Keys */
  { /*     0 */    0x0400u  /* Lower CanId of range: MyECU_aa57c804_Rx, 2.0- or FD-PDU */      ,   0x643Fu  /* Upper CanId of range: MyECU_aa57c804_Rx */      , CanNmConf_CanNmRxPdu_CAN_5f8bc0cc_Rx      ,                     1u  /* CanNm_RxIndication */    ,       8u  /* DLC-Check is enabled */, CANIF_MSG_TYPE_CAN       },  /* [PDU: MyECU_aa57c804_Rx, Lower CanId of range: 0x400]              */  /* [CanIfConf_CanIfRxPduCfg_MyECU_aa57c804_Rx] */
  { /*     1 */    0x0614u  /* msg_diag_RequestGlobal_Tp_oCAN_37844e48_Rx, only 2.0-PDU */     ,   0x07FFu  /* msg_diag_RequestGlobal_Tp_oCAN_37844e48_Rx */   , CanTpConf_CanTpRxNPdu_CanTpRxNPdu_e872022a,                     2u  /* CanTp_RxIndication */    ,       8u  /* DLC-Check is enabled */, CANIF_MSG_TYPE_NO_FD_CAN },  /* [PDU: msg_diag_RequestGlobal_Tp_oCAN_37844e48_Rx, CanId: 0x614]    */  /* [CanIfConf_CanIfRxPduCfg_msg_diag_RequestGlobal_Tp_oCAN_37844e48_Rx] */
  { /*     2 */    0x0610u  /* msg_diag_Request_MyECU_Tp_oCAN_0b2d11a0_Rx, only 2.0-PDU */     ,   0x07FFu  /* msg_diag_Request_MyECU_Tp_oCAN_0b2d11a0_Rx */   , CanTpConf_CanTpRxNPdu_CanTpRxNPdu_29945216,                     2u  /* CanTp_RxIndication */    ,       8u  /* DLC-Check is enabled */, CANIF_MSG_TYPE_NO_FD_CAN },  /* [PDU: msg_diag_Request_MyECU_Tp_oCAN_0b2d11a0_Rx, CanId: 0x610]    */  /* [CanIfConf_CanIfRxPduCfg_msg_diag_Request_MyECU_Tp_oCAN_0b2d11a0_Rx] */
  { /*     3 */    0x0501u  /* msg_StartAppl_Rx_MyECU_oCAN_2369e80d_Rx, 2.0- or FD-PDU */      ,   0x07FFu  /* msg_StartAppl_Rx_MyECU_oCAN_2369e80d_Rx */      , PduRConf_PduRSrcPdu_PduRSrcPdu_7a86d966   ,                     3u  /* PduR_CanIfRxIndication */,      64u  /* DLC-Check is enabled */, CANIF_MSG_TYPE_CAN       },  /* [PDU: msg_StartAppl_Rx_MyECU_oCAN_2369e80d_Rx, CanId: 0x501]       */  /* [CanIfConf_CanIfRxPduCfg_msg_StartAppl_Rx_MyECU_oCAN_2369e80d_Rx] */
  { /*     4 */    0x0310u  /* msg_RxEvent_20_oCAN_8771683b_Rx, 2.0- or FD-PDU */              ,   0x07FFu  /* msg_RxEvent_20_oCAN_8771683b_Rx */              , PduRConf_PduRSrcPdu_PduRSrcPdu_37e6280b   ,                     3u  /* PduR_CanIfRxIndication */,       4u  /* DLC-Check is enabled */, CANIF_MSG_TYPE_CAN       },  /* [PDU: msg_RxEvent_20_oCAN_8771683b_Rx, CanId: 0x310]               */  /* [CanIfConf_CanIfRxPduCfg_msg_RxEvent_20_oCAN_8771683b_Rx] */
  { /*     5 */    0x0210u  /* msg_RxCycle100_0_oCAN_e369bba0_Rx, 2.0- or FD-PDU */            ,   0x07FFu  /* msg_RxCycle100_0_oCAN_e369bba0_Rx */            , PduRConf_PduRSrcPdu_PduRSrcPdu_9e00b2d3   ,                     3u  /* PduR_CanIfRxIndication */,       1u  /* DLC-Check is enabled */, CANIF_MSG_TYPE_CAN       },  /* [PDU: msg_RxCycle100_0_oCAN_e369bba0_Rx, CanId: 0x210]             */  /* [CanIfConf_CanIfRxPduCfg_msg_RxCycle100_0_oCAN_e369bba0_Rx] */
  { /*     6 */    0x0120u  /* msg_RxCycle_E2eProf1C_500_10_oCAN_223145ae_Rx, 2.0- or FD-PDU */,   0x07FFu  /* msg_RxCycle_E2eProf1C_500_10_oCAN_223145ae_Rx */, PduRConf_PduRSrcPdu_PduRSrcPdu_01c8980f   ,                     3u  /* PduR_CanIfRxIndication */,       8u  /* DLC-Check is enabled */, CANIF_MSG_TYPE_CAN       },  /* [PDU: msg_RxCycle_E2eProf1C_500_10_oCAN_223145ae_Rx, CanId: 0x120] */  /* [CanIfConf_CanIfRxPduCfg_msg_RxCycle_E2eProf1C_500_10_oCAN_223145ae_Rx] */
  { /*     7 */    0x0110u  /* msg_RxCycle500_20_oCAN_a62cdda0_Rx, 2.0- or FD-PDU */           ,   0x07FFu  /* msg_RxCycle500_20_oCAN_a62cdda0_Rx */           , PduRConf_PduRSrcPdu_PduRSrcPdu_b1d1dd8a   ,                     3u  /* PduR_CanIfRxIndication */,       6u  /* DLC-Check is enabled */, CANIF_MSG_TYPE_CAN       }   /* [PDU: msg_RxCycle500_20_oCAN_a62cdda0_Rx, CanId: 0x110]            */  /* [CanIfConf_CanIfRxPduCfg_msg_RxCycle500_20_oCAN_a62cdda0_Rx] */
};
#define CANIF_STOP_SEC_CONST_UNSPECIFIED
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */

/**********************************************************************************************************************
  CanIf_TxBufferPrioByCanIdByteQueueConfig
**********************************************************************************************************************/
/** 
  \var    CanIf_TxBufferPrioByCanIdByteQueueConfig
  \brief  Tx-buffer: PRIO_BY_CANID as BYTE_QUEUE
  \details
  Element                                             Description
  TxBufferPrioByCanIdBaseIdx                          the index of the 1:1 relation pointing to CanIf_TxBufferPrioByCanIdBase
  TxBufferPrioByCanIdByteQueueMappedTxPdusEndIdx      the end index of the 1:n relation pointing to CanIf_TxBufferPrioByCanIdByteQueueMappedTxPdus
  TxBufferPrioByCanIdByteQueueMappedTxPdusLength      the number of relations pointing to CanIf_TxBufferPrioByCanIdByteQueueMappedTxPdus
  TxBufferPrioByCanIdByteQueueMappedTxPdusStartIdx    the start index of the 1:n relation pointing to CanIf_TxBufferPrioByCanIdByteQueueMappedTxPdus
*/ 
#define CANIF_START_SEC_CONST_UNSPECIFIED
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */
CONST(CanIf_TxBufferPrioByCanIdByteQueueConfigType, CANIF_CONST) CanIf_TxBufferPrioByCanIdByteQueueConfig[1] = {  /* PRQA S 1514, 1533 */  /* MD_CSL_ObjectOnlyAccessedOnce */
    /* Index    TxBufferPrioByCanIdBaseIdx                                                      TxBufferPrioByCanIdByteQueueMappedTxPdusEndIdx                                                      TxBufferPrioByCanIdByteQueueMappedTxPdusLength                                                      TxBufferPrioByCanIdByteQueueMappedTxPdusStartIdx                                                            Referable Keys */
  { /*     0 */                         0u  /* /ActiveEcuC/CanIf/CanIfInitCfg/CHNL_346f2748 */,                                             8u  /* /ActiveEcuC/CanIf/CanIfInitCfg/CHNL_346f2748 */,                                             8u  /* /ActiveEcuC/CanIf/CanIfInitCfg/CHNL_346f2748 */,                                               0u  /* /ActiveEcuC/CanIf/CanIfInitCfg/CHNL_346f2748 */ }   /* [/ActiveEcuC/CanIf/CanIfInitCfg/CHNL_346f2748] */
};
#define CANIF_STOP_SEC_CONST_UNSPECIFIED
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */

/**********************************************************************************************************************
  CanIf_TxBufferPrioByCanIdByteQueueMappedTxPdus
**********************************************************************************************************************/
/** 
  \var    CanIf_TxBufferPrioByCanIdByteQueueMappedTxPdus
  \brief  Tx-buffer: PRIO_BY_CANID as BYTE_QUEUE: Mapped Tx-PDUs
  \details
  Element           Description
  TxPduConfigIdx    the index of the 1:1 relation pointing to CanIf_TxPduConfig
*/ 
#define CANIF_START_SEC_CONST_UNSPECIFIED
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */
CONST(CanIf_TxBufferPrioByCanIdByteQueueMappedTxPdusType, CANIF_CONST) CanIf_TxBufferPrioByCanIdByteQueueMappedTxPdus[8] = {  /* PRQA S 1514, 1533 */  /* MD_CSL_ObjectOnlyAccessedOnce */
    /* Index    TxPduConfigIdx                                                                                            Referable Keys */
  { /*     0 */             0u  /* /ActiveEcuC/CanIf/CanIfInitCfg/msg_diag_Uudt_Response_MyECU_oCAN_6917a2df_Tx */ },  /* [/ActiveEcuC/CanIf/CanIfInitCfg/CHNL_346f2748] */
  { /*     1 */             1u  /* /ActiveEcuC/CanIf/CanIfInitCfg/msg_diag_Response_MyECU_Tp_oCAN_ee3092c6_Tx */   },  /* [/ActiveEcuC/CanIf/CanIfInitCfg/CHNL_346f2748] */
  { /*     2 */             2u  /* /ActiveEcuC/CanIf/CanIfInitCfg/msg_StartAppl_Tx_MyECU_oCAN_b6f80cd2_Tx */       },  /* [/ActiveEcuC/CanIf/CanIfInitCfg/CHNL_346f2748] */
  { /*     3 */             3u  /* /ActiveEcuC/CanIf/CanIfInitCfg/msg_nm_MyECU_oCAN_6fd77547_Tx */                 },  /* [/ActiveEcuC/CanIf/CanIfInitCfg/CHNL_346f2748] */
  { /*     4 */             4u  /* /ActiveEcuC/CanIf/CanIfInitCfg/msg_TxCycle_E2eProf1C_500_30_oCAN_f209e413_Tx */ },  /* [/ActiveEcuC/CanIf/CanIfInitCfg/CHNL_346f2748] */
  { /*     5 */             5u  /* /ActiveEcuC/CanIf/CanIfInitCfg/msg_TxCycle1000_10_oCAN_fd90faa0_Tx */           },  /* [/ActiveEcuC/CanIf/CanIfInitCfg/CHNL_346f2748] */
  { /*     6 */             6u  /* /ActiveEcuC/CanIf/CanIfInitCfg/msg_TxCycle10_0_oCAN_9c370e88_Tx */              },  /* [/ActiveEcuC/CanIf/CanIfInitCfg/CHNL_346f2748] */
  { /*     7 */             7u  /* /ActiveEcuC/CanIf/CanIfInitCfg/msg_TxEvent_10_oCAN_b963731b_Tx */               }   /* [/ActiveEcuC/CanIf/CanIfInitCfg/CHNL_346f2748] */
};
#define CANIF_STOP_SEC_CONST_UNSPECIFIED
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */

/**********************************************************************************************************************
  CanIf_TxConfirmationFctList
**********************************************************************************************************************/
/** 
  \var    CanIf_TxConfirmationFctList
  \brief  Tx confirmation functions table.
*/ 
#define CANIF_START_SEC_CONST_UNSPECIFIED
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */
CONST(CanIf_TxConfirmationFctType, CANIF_CONST) CanIf_TxConfirmationFctList[4] = {  /* PRQA S 1514, 1533 */  /* MD_CSL_ObjectOnlyAccessedOnce */
  /* Index     TxConfirmationFctList                      Referable Keys */
  /*     0 */ (CanIf_TxConfirmationFctType)NULL_PTR ,  /* [NULL_PTR] */
  /*     1 */ CanNm_TxConfirmation                  ,  /* [CanNm_TxConfirmation] */
  /*     2 */ CanTp_TxConfirmation                  ,  /* [CanTp_TxConfirmation] */
  /*     3 */ PduR_CanIfTxConfirmation                 /* [PduR_CanIfTxConfirmation] */
};
#define CANIF_STOP_SEC_CONST_UNSPECIFIED
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */

/**********************************************************************************************************************
  CanIf_TxPduConfig
**********************************************************************************************************************/
/** 
  \var    CanIf_TxPduConfig
  \brief  Tx-PDUs - configuration.
  \details
  Element                     Description
  CanId                       CAN identifier (16bit / 32bit).
  UpperLayerTxPduId           Upper layer handle ID (8bit / 16bit).
  IsTxPduTruncation           TRUE: Truncation of Tx-PDU is enabled, FALSE: Truncation of Tx-PDU is disabled
  CtrlStatesIdx               the index of the 1:1 relation pointing to CanIf_CtrlStates
  MailBoxConfigIdx            the index of the 1:1 relation pointing to CanIf_MailBoxConfig
  TxConfirmationFctListIdx    the index of the 1:1 relation pointing to CanIf_TxConfirmationFctList
  TxPduLength                 Tx-PDU length.
*/ 
#define CANIF_START_SEC_CONST_UNSPECIFIED
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */
CONST(CanIf_TxPduConfigType, CANIF_CONST) CanIf_TxPduConfig[8] = {  /* PRQA S 1514, 1533 */  /* MD_CSL_ObjectOnlyAccessedOnce */
    /* Index    CanId    UpperLayerTxPduId                                                   IsTxPduTruncation  CtrlStatesIdx                                                                    MailBoxConfigIdx                                                         TxConfirmationFctListIdx                                  TxPduLength        Comment                                                          Referable Keys */
  { /*     0 */ 0x0616u, PduRConf_PduRDestPdu_msg_diag_Uudt_Response_MyECU_oCAN_e0fa8c3a_Tx, TRUE             ,            0u  /* /ActiveEcuC/CanIf/CanIfCtrlDrvCfg_7d254554/CT_CAN_d43e43a4 */,               0u  /* /ActiveEcuC/Can/CanConfigSet/CN_CAN_fe6ecc87_Tx */,                       3u  /* PduR_CanIfTxConfirmation */,          8u },  /* [PDU: msg_diag_Uudt_Response_MyECU_oCAN_6917a2df_Tx] */  /* [/ActiveEcuC/CanIf/CanIfInitCfg/msg_diag_Uudt_Response_MyECU_oCAN_6917a2df_Tx] */
  { /*     1 */ 0x0612u, CanTpConf_CanTpTxFcNPdu_CanTpTxFcNPdu_29945216                    , TRUE             ,            0u  /* /ActiveEcuC/CanIf/CanIfCtrlDrvCfg_7d254554/CT_CAN_d43e43a4 */,               0u  /* /ActiveEcuC/Can/CanConfigSet/CN_CAN_fe6ecc87_Tx */,                       2u  /* CanTp_TxConfirmation */    ,          8u },  /* [PDU: msg_diag_Response_MyECU_Tp_oCAN_ee3092c6_Tx]   */  /* [/ActiveEcuC/CanIf/CanIfInitCfg/msg_diag_Response_MyECU_Tp_oCAN_ee3092c6_Tx] */
  { /*     2 */ 0x4500u, PduRConf_PduRDestPdu_msg_StartAppl_Tx_MyECU_oCAN_c14d78df_Tx      , TRUE             ,            0u  /* /ActiveEcuC/CanIf/CanIfCtrlDrvCfg_7d254554/CT_CAN_d43e43a4 */,               0u  /* /ActiveEcuC/Can/CanConfigSet/CN_CAN_fe6ecc87_Tx */,                       0u  /* NULL_PTR */                ,         64u },  /* [PDU: msg_StartAppl_Tx_MyECU_oCAN_b6f80cd2_Tx]       */  /* [/ActiveEcuC/CanIf/CanIfInitCfg/msg_StartAppl_Tx_MyECU_oCAN_b6f80cd2_Tx] */
  { /*     3 */ 0x0400u, CanNmConf_CanNmTxPdu_msg_nm_MyECU_oCAN_9090bd79_Tx                , TRUE             ,            0u  /* /ActiveEcuC/CanIf/CanIfCtrlDrvCfg_7d254554/CT_CAN_d43e43a4 */,               0u  /* /ActiveEcuC/Can/CanConfigSet/CN_CAN_fe6ecc87_Tx */,                       1u  /* CanNm_TxConfirmation */    ,          8u },  /* [PDU: msg_nm_MyECU_oCAN_6fd77547_Tx]                 */  /* [/ActiveEcuC/CanIf/CanIfInitCfg/msg_nm_MyECU_oCAN_6fd77547_Tx] */
  { /*     4 */ 0x4378u, PduRConf_PduRDestPdu_msg_TxCycle_E2eProf1C_500_30_oCAN_995018f6_Tx, TRUE             ,            0u  /* /ActiveEcuC/CanIf/CanIfCtrlDrvCfg_7d254554/CT_CAN_d43e43a4 */,               0u  /* /ActiveEcuC/Can/CanConfigSet/CN_CAN_fe6ecc87_Tx */,                       0u  /* NULL_PTR */                ,          8u },  /* [PDU: msg_TxCycle_E2eProf1C_500_30_oCAN_f209e413_Tx] */  /* [/ActiveEcuC/CanIf/CanIfInitCfg/msg_TxCycle_E2eProf1C_500_30_oCAN_f209e413_Tx] */
  { /*     5 */ 0x4368u, PduRConf_PduRDestPdu_msg_TxCycle1000_10_oCAN_6dd6f284_Tx          , TRUE             ,            0u  /* /ActiveEcuC/CanIf/CanIfCtrlDrvCfg_7d254554/CT_CAN_d43e43a4 */,               0u  /* /ActiveEcuC/Can/CanConfigSet/CN_CAN_fe6ecc87_Tx */,                       0u  /* NULL_PTR */                ,          2u },  /* [PDU: msg_TxCycle1000_10_oCAN_fd90faa0_Tx]           */  /* [/ActiveEcuC/CanIf/CanIfInitCfg/msg_TxCycle1000_10_oCAN_fd90faa0_Tx] */
  { /*     6 */ 0x4268u, PduRConf_PduRDestPdu_msg_TxCycle10_0_oCAN_2d7b6a87_Tx             , TRUE             ,            0u  /* /ActiveEcuC/CanIf/CanIfCtrlDrvCfg_7d254554/CT_CAN_d43e43a4 */,               0u  /* /ActiveEcuC/Can/CanConfigSet/CN_CAN_fe6ecc87_Tx */,                       0u  /* NULL_PTR */                ,          7u },  /* [PDU: msg_TxCycle10_0_oCAN_9c370e88_Tx]              */  /* [/ActiveEcuC/CanIf/CanIfInitCfg/msg_TxCycle10_0_oCAN_9c370e88_Tx] */
  { /*     7 */ 0x4168u, PduRConf_PduRDestPdu_msg_TxEvent_10_oCAN_b9443fef_Tx              , TRUE             ,            0u  /* /ActiveEcuC/CanIf/CanIfCtrlDrvCfg_7d254554/CT_CAN_d43e43a4 */,               0u  /* /ActiveEcuC/Can/CanConfigSet/CN_CAN_fe6ecc87_Tx */,                       0u  /* NULL_PTR */                ,          7u }   /* [PDU: msg_TxEvent_10_oCAN_b963731b_Tx]               */  /* [/ActiveEcuC/CanIf/CanIfInitCfg/msg_TxEvent_10_oCAN_b963731b_Tx] */
};
#define CANIF_STOP_SEC_CONST_UNSPECIFIED
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */

/**********************************************************************************************************************
  CanIf_TxPduQueueIndex
**********************************************************************************************************************/
/** 
  \var    CanIf_TxPduQueueIndex
  \brief  Indirection table: Tx-PDU handle ID to corresponding Tx buffer handle ID. NOTE: Only BasicCAN Tx-PDUs have a valid indirection into the Tx buffer.
  \details
  Element                          Description
  TxQueueIdx                       the index of the 0:1 relation pointing to CanIf_TxQueue
  TxQueueIndex2DataStartStopIdx    the index of the 0:1 relation pointing to CanIf_TxQueueIndex2DataStartStop
*/ 
#define CANIF_START_SEC_CONST_UNSPECIFIED
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */
CONST(CanIf_TxPduQueueIndexType, CANIF_CONST) CanIf_TxPduQueueIndex[8] = {  /* PRQA S 1514, 1533 */  /* MD_CSL_ObjectOnlyAccessedOnce */
    /* Index    TxQueueIdx                                                                                      TxQueueIndex2DataStartStopIdx                                                                                            Comment                                                                                    Referable Keys */
  { /*     0 */         0u  /* /ActiveEcuC/CanIf/CanIfInitCfg/msg_diag_Uudt_Response_MyECU_oCAN_6917a2df_Tx */,                            0u  /* /ActiveEcuC/CanIf/CanIfInitCfg/msg_diag_Uudt_Response_MyECU_oCAN_6917a2df_Tx */ },  /* [msg_diag_Uudt_Response_MyECU_oCAN_6917a2df_Tx, BasicCAN TxPdu with Tx-buffer] */  /* [/ActiveEcuC/CanIf/CanIfInitCfg/msg_diag_Uudt_Response_MyECU_oCAN_6917a2df_Tx, /ActiveEcuC/CanIf/CanIfCtrlDrvCfg_7d254554/CT_CAN_d43e43a4] */
  { /*     1 */         1u  /* /ActiveEcuC/CanIf/CanIfInitCfg/msg_diag_Response_MyECU_Tp_oCAN_ee3092c6_Tx */  ,                            1u  /* /ActiveEcuC/CanIf/CanIfInitCfg/msg_diag_Response_MyECU_Tp_oCAN_ee3092c6_Tx */   },  /* [msg_diag_Response_MyECU_Tp_oCAN_ee3092c6_Tx, BasicCAN TxPdu with Tx-buffer]   */  /* [/ActiveEcuC/CanIf/CanIfInitCfg/msg_diag_Response_MyECU_Tp_oCAN_ee3092c6_Tx, /ActiveEcuC/CanIf/CanIfCtrlDrvCfg_7d254554/CT_CAN_d43e43a4] */
  { /*     2 */         2u  /* /ActiveEcuC/CanIf/CanIfInitCfg/msg_StartAppl_Tx_MyECU_oCAN_b6f80cd2_Tx */      ,                            2u  /* /ActiveEcuC/CanIf/CanIfInitCfg/msg_StartAppl_Tx_MyECU_oCAN_b6f80cd2_Tx */       },  /* [msg_StartAppl_Tx_MyECU_oCAN_b6f80cd2_Tx, BasicCAN TxPdu with Tx-buffer]       */  /* [/ActiveEcuC/CanIf/CanIfInitCfg/msg_StartAppl_Tx_MyECU_oCAN_b6f80cd2_Tx, /ActiveEcuC/CanIf/CanIfCtrlDrvCfg_7d254554/CT_CAN_d43e43a4] */
  { /*     3 */         3u  /* /ActiveEcuC/CanIf/CanIfInitCfg/msg_nm_MyECU_oCAN_6fd77547_Tx */                ,                            3u  /* /ActiveEcuC/CanIf/CanIfInitCfg/msg_nm_MyECU_oCAN_6fd77547_Tx */                 },  /* [msg_nm_MyECU_oCAN_6fd77547_Tx, BasicCAN TxPdu with Tx-buffer]                 */  /* [/ActiveEcuC/CanIf/CanIfInitCfg/msg_nm_MyECU_oCAN_6fd77547_Tx, /ActiveEcuC/CanIf/CanIfCtrlDrvCfg_7d254554/CT_CAN_d43e43a4] */
  { /*     4 */         4u  /* /ActiveEcuC/CanIf/CanIfInitCfg/msg_TxCycle_E2eProf1C_500_30_oCAN_f209e413_Tx */,                            4u  /* /ActiveEcuC/CanIf/CanIfInitCfg/msg_TxCycle_E2eProf1C_500_30_oCAN_f209e413_Tx */ },  /* [msg_TxCycle_E2eProf1C_500_30_oCAN_f209e413_Tx, BasicCAN TxPdu with Tx-buffer] */  /* [/ActiveEcuC/CanIf/CanIfInitCfg/msg_TxCycle_E2eProf1C_500_30_oCAN_f209e413_Tx, /ActiveEcuC/CanIf/CanIfCtrlDrvCfg_7d254554/CT_CAN_d43e43a4] */
  { /*     5 */         5u  /* /ActiveEcuC/CanIf/CanIfInitCfg/msg_TxCycle1000_10_oCAN_fd90faa0_Tx */          ,                            5u  /* /ActiveEcuC/CanIf/CanIfInitCfg/msg_TxCycle1000_10_oCAN_fd90faa0_Tx */           },  /* [msg_TxCycle1000_10_oCAN_fd90faa0_Tx, BasicCAN TxPdu with Tx-buffer]           */  /* [/ActiveEcuC/CanIf/CanIfInitCfg/msg_TxCycle1000_10_oCAN_fd90faa0_Tx, /ActiveEcuC/CanIf/CanIfCtrlDrvCfg_7d254554/CT_CAN_d43e43a4] */
  { /*     6 */         6u  /* /ActiveEcuC/CanIf/CanIfInitCfg/msg_TxCycle10_0_oCAN_9c370e88_Tx */             ,                            6u  /* /ActiveEcuC/CanIf/CanIfInitCfg/msg_TxCycle10_0_oCAN_9c370e88_Tx */              },  /* [msg_TxCycle10_0_oCAN_9c370e88_Tx, BasicCAN TxPdu with Tx-buffer]              */  /* [/ActiveEcuC/CanIf/CanIfInitCfg/msg_TxCycle10_0_oCAN_9c370e88_Tx, /ActiveEcuC/CanIf/CanIfCtrlDrvCfg_7d254554/CT_CAN_d43e43a4] */
  { /*     7 */         7u  /* /ActiveEcuC/CanIf/CanIfInitCfg/msg_TxEvent_10_oCAN_b963731b_Tx */              ,                            7u  /* /ActiveEcuC/CanIf/CanIfInitCfg/msg_TxEvent_10_oCAN_b963731b_Tx */               }   /* [msg_TxEvent_10_oCAN_b963731b_Tx, BasicCAN TxPdu with Tx-buffer]               */  /* [/ActiveEcuC/CanIf/CanIfInitCfg/msg_TxEvent_10_oCAN_b963731b_Tx, /ActiveEcuC/CanIf/CanIfCtrlDrvCfg_7d254554/CT_CAN_d43e43a4] */
};
#define CANIF_STOP_SEC_CONST_UNSPECIFIED
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */

/**********************************************************************************************************************
  CanIf_TxQueueIndex2DataStartStop
**********************************************************************************************************************/
/** 
  \var    CanIf_TxQueueIndex2DataStartStop
  \details
  Element                Description
  TxQueueDataEndIdx      the end index of the 0:n relation pointing to CanIf_TxQueueData
  TxQueueDataLength      the number of relations pointing to CanIf_TxQueueData
  TxQueueDataStartIdx    the start index of the 0:n relation pointing to CanIf_TxQueueData
*/ 
#define CANIF_START_SEC_CONST_UNSPECIFIED
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */
CONST(CanIf_TxQueueIndex2DataStartStopType, CANIF_CONST) CanIf_TxQueueIndex2DataStartStop[8] = {  /* PRQA S 1514, 1533 */  /* MD_CSL_ObjectOnlyAccessedOnce */
    /* Index    TxQueueDataEndIdx                                                                                      TxQueueDataLength                                                                                      TxQueueDataStartIdx                                                                                            Comment                                                             Referable Keys */
  { /*     0 */                8u  /* /ActiveEcuC/CanIf/CanIfInitCfg/msg_diag_Uudt_Response_MyECU_oCAN_6917a2df_Tx */,                8u  /* /ActiveEcuC/CanIf/CanIfInitCfg/msg_diag_Uudt_Response_MyECU_oCAN_6917a2df_Tx */,                  0u  /* /ActiveEcuC/CanIf/CanIfInitCfg/msg_diag_Uudt_Response_MyECU_oCAN_6917a2df_Tx */ },  /* [Tx-PDU: msg_diag_Uudt_Response_MyECU_oCAN_6917a2df_Tx] */  /* [/ActiveEcuC/CanIf/CanIfInitCfg/msg_diag_Uudt_Response_MyECU_oCAN_6917a2df_Tx] */
  { /*     1 */               16u  /* /ActiveEcuC/CanIf/CanIfInitCfg/msg_diag_Response_MyECU_Tp_oCAN_ee3092c6_Tx */  ,                8u  /* /ActiveEcuC/CanIf/CanIfInitCfg/msg_diag_Response_MyECU_Tp_oCAN_ee3092c6_Tx */  ,                  8u  /* /ActiveEcuC/CanIf/CanIfInitCfg/msg_diag_Response_MyECU_Tp_oCAN_ee3092c6_Tx */   },  /* [Tx-PDU: msg_diag_Response_MyECU_Tp_oCAN_ee3092c6_Tx]   */  /* [/ActiveEcuC/CanIf/CanIfInitCfg/msg_diag_Response_MyECU_Tp_oCAN_ee3092c6_Tx] */
  { /*     2 */               80u  /* /ActiveEcuC/CanIf/CanIfInitCfg/msg_StartAppl_Tx_MyECU_oCAN_b6f80cd2_Tx */      ,               64u  /* /ActiveEcuC/CanIf/CanIfInitCfg/msg_StartAppl_Tx_MyECU_oCAN_b6f80cd2_Tx */      ,                 16u  /* /ActiveEcuC/CanIf/CanIfInitCfg/msg_StartAppl_Tx_MyECU_oCAN_b6f80cd2_Tx */       },  /* [Tx-PDU: msg_StartAppl_Tx_MyECU_oCAN_b6f80cd2_Tx]       */  /* [/ActiveEcuC/CanIf/CanIfInitCfg/msg_StartAppl_Tx_MyECU_oCAN_b6f80cd2_Tx] */
  { /*     3 */               88u  /* /ActiveEcuC/CanIf/CanIfInitCfg/msg_nm_MyECU_oCAN_6fd77547_Tx */                ,                8u  /* /ActiveEcuC/CanIf/CanIfInitCfg/msg_nm_MyECU_oCAN_6fd77547_Tx */                ,                 80u  /* /ActiveEcuC/CanIf/CanIfInitCfg/msg_nm_MyECU_oCAN_6fd77547_Tx */                 },  /* [Tx-PDU: msg_nm_MyECU_oCAN_6fd77547_Tx]                 */  /* [/ActiveEcuC/CanIf/CanIfInitCfg/msg_nm_MyECU_oCAN_6fd77547_Tx] */
  { /*     4 */               96u  /* /ActiveEcuC/CanIf/CanIfInitCfg/msg_TxCycle_E2eProf1C_500_30_oCAN_f209e413_Tx */,                8u  /* /ActiveEcuC/CanIf/CanIfInitCfg/msg_TxCycle_E2eProf1C_500_30_oCAN_f209e413_Tx */,                 88u  /* /ActiveEcuC/CanIf/CanIfInitCfg/msg_TxCycle_E2eProf1C_500_30_oCAN_f209e413_Tx */ },  /* [Tx-PDU: msg_TxCycle_E2eProf1C_500_30_oCAN_f209e413_Tx] */  /* [/ActiveEcuC/CanIf/CanIfInitCfg/msg_TxCycle_E2eProf1C_500_30_oCAN_f209e413_Tx] */
  { /*     5 */               98u  /* /ActiveEcuC/CanIf/CanIfInitCfg/msg_TxCycle1000_10_oCAN_fd90faa0_Tx */          ,                2u  /* /ActiveEcuC/CanIf/CanIfInitCfg/msg_TxCycle1000_10_oCAN_fd90faa0_Tx */          ,                 96u  /* /ActiveEcuC/CanIf/CanIfInitCfg/msg_TxCycle1000_10_oCAN_fd90faa0_Tx */           },  /* [Tx-PDU: msg_TxCycle1000_10_oCAN_fd90faa0_Tx]           */  /* [/ActiveEcuC/CanIf/CanIfInitCfg/msg_TxCycle1000_10_oCAN_fd90faa0_Tx] */
  { /*     6 */              105u  /* /ActiveEcuC/CanIf/CanIfInitCfg/msg_TxCycle10_0_oCAN_9c370e88_Tx */             ,                7u  /* /ActiveEcuC/CanIf/CanIfInitCfg/msg_TxCycle10_0_oCAN_9c370e88_Tx */             ,                 98u  /* /ActiveEcuC/CanIf/CanIfInitCfg/msg_TxCycle10_0_oCAN_9c370e88_Tx */              },  /* [Tx-PDU: msg_TxCycle10_0_oCAN_9c370e88_Tx]              */  /* [/ActiveEcuC/CanIf/CanIfInitCfg/msg_TxCycle10_0_oCAN_9c370e88_Tx] */
  { /*     7 */              112u  /* /ActiveEcuC/CanIf/CanIfInitCfg/msg_TxEvent_10_oCAN_b963731b_Tx */              ,                7u  /* /ActiveEcuC/CanIf/CanIfInitCfg/msg_TxEvent_10_oCAN_b963731b_Tx */              ,                105u  /* /ActiveEcuC/CanIf/CanIfInitCfg/msg_TxEvent_10_oCAN_b963731b_Tx */               }   /* [Tx-PDU: msg_TxEvent_10_oCAN_b963731b_Tx]               */  /* [/ActiveEcuC/CanIf/CanIfInitCfg/msg_TxEvent_10_oCAN_b963731b_Tx] */
};
#define CANIF_STOP_SEC_CONST_UNSPECIFIED
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */

/**********************************************************************************************************************
  CanIf_CtrlStates
**********************************************************************************************************************/
/** 
  \var    CanIf_CtrlStates
  \details
  Element     Description
  CtrlMode    Controller mode.
  PduMode     PDU mode state.
*/ 
#define CANIF_START_SEC_VAR_NOINIT_UNSPECIFIED
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */
VAR(CanIf_CtrlStatesUType, CANIF_VAR_NOINIT) CanIf_CtrlStates;  /* PRQA S 0759, 1514, 1533 */  /* MD_CSL_Union, MD_CSL_ObjectOnlyAccessedOnce, MD_CSL_ObjectOnlyAccessedOnce */
  /* Index        Referable Keys */
  /*     0 */  /* [/ActiveEcuC/CanIf/CanIfCtrlDrvCfg_7d254554/CT_CAN_d43e43a4] */

#define CANIF_STOP_SEC_VAR_NOINIT_UNSPECIFIED
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */

/**********************************************************************************************************************
  CanIf_TxBufferPrioByCanIdBase
**********************************************************************************************************************/
/** 
  \var    CanIf_TxBufferPrioByCanIdBase
  \brief  Variable declaration - Tx-buffer: PRIO_BY_CANID as byte/bit-queue. Stores at least the QueueCounter.
*/ 
#define CANIF_START_SEC_VAR_NOINIT_UNSPECIFIED
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */
VAR(CanIf_TxBufferPrioByCanIdBaseUType, CANIF_VAR_NOINIT) CanIf_TxBufferPrioByCanIdBase;  /* PRQA S 0759, 1514, 1533 */  /* MD_CSL_Union, MD_CSL_ObjectOnlyAccessedOnce, MD_CSL_ObjectOnlyAccessedOnce */
  /* Index        Referable Keys */
  /*     0 */  /* [/ActiveEcuC/CanIf/CanIfInitCfg/CHNL_346f2748] */

#define CANIF_STOP_SEC_VAR_NOINIT_UNSPECIFIED
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */

/**********************************************************************************************************************
  CanIf_TxQueue
**********************************************************************************************************************/
/** 
  \var    CanIf_TxQueue
  \brief  Variable declaration - Tx byte queue.
*/ 
#define CANIF_START_SEC_VAR_NOINIT_UNSPECIFIED
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */
VAR(CanIf_TxQueueUType, CANIF_VAR_NOINIT) CanIf_TxQueue;  /* PRQA S 0759, 1514, 1533 */  /* MD_CSL_Union, MD_CSL_ObjectOnlyAccessedOnce, MD_CSL_ObjectOnlyAccessedOnce */
  /* Index        Referable Keys */
  /*     0 */  /* [/ActiveEcuC/CanIf/CanIfInitCfg/msg_diag_Uudt_Response_MyECU_oCAN_6917a2df_Tx] */
  /*     1 */  /* [/ActiveEcuC/CanIf/CanIfInitCfg/msg_diag_Response_MyECU_Tp_oCAN_ee3092c6_Tx] */
  /*     2 */  /* [/ActiveEcuC/CanIf/CanIfInitCfg/msg_StartAppl_Tx_MyECU_oCAN_b6f80cd2_Tx] */
  /*     3 */  /* [/ActiveEcuC/CanIf/CanIfInitCfg/msg_nm_MyECU_oCAN_6fd77547_Tx] */
  /*     4 */  /* [/ActiveEcuC/CanIf/CanIfInitCfg/msg_TxCycle_E2eProf1C_500_30_oCAN_f209e413_Tx] */
  /*     5 */  /* [/ActiveEcuC/CanIf/CanIfInitCfg/msg_TxCycle1000_10_oCAN_fd90faa0_Tx] */
  /*     6 */  /* [/ActiveEcuC/CanIf/CanIfInitCfg/msg_TxCycle10_0_oCAN_9c370e88_Tx] */
  /*     7 */  /* [/ActiveEcuC/CanIf/CanIfInitCfg/msg_TxEvent_10_oCAN_b963731b_Tx] */

#define CANIF_STOP_SEC_VAR_NOINIT_UNSPECIFIED
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */

/**********************************************************************************************************************
  CanIf_TxQueueData
**********************************************************************************************************************/
/** 
  \var    CanIf_TxQueueData
  \brief  Variable declaration - Tx queue data.
*/ 
#define CANIF_START_SEC_VAR_FAST_NOINIT_8BIT
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */
VAR(CanIf_TxQueueDataUType, CANIF_VAR_NOINIT_FAST) CanIf_TxQueueData;  /* PRQA S 0759, 1514, 1533 */  /* MD_CSL_Union, MD_CSL_ObjectOnlyAccessedOnce, MD_CSL_ObjectOnlyAccessedOnce */
  /* Index        Referable Keys */
  /*     0 */  /* [/ActiveEcuC/CanIf/CanIfInitCfg/msg_diag_Uudt_Response_MyECU_oCAN_6917a2df_Tx] */
  /*   ... */  /* [/ActiveEcuC/CanIf/CanIfInitCfg/msg_diag_Uudt_Response_MyECU_oCAN_6917a2df_Tx] */
  /*     7 */  /* [/ActiveEcuC/CanIf/CanIfInitCfg/msg_diag_Uudt_Response_MyECU_oCAN_6917a2df_Tx] */
  /*     8 */  /* [/ActiveEcuC/CanIf/CanIfInitCfg/msg_diag_Response_MyECU_Tp_oCAN_ee3092c6_Tx] */
  /*   ... */  /* [/ActiveEcuC/CanIf/CanIfInitCfg/msg_diag_Response_MyECU_Tp_oCAN_ee3092c6_Tx] */
  /*    15 */  /* [/ActiveEcuC/CanIf/CanIfInitCfg/msg_diag_Response_MyECU_Tp_oCAN_ee3092c6_Tx] */
  /*    16 */  /* [/ActiveEcuC/CanIf/CanIfInitCfg/msg_StartAppl_Tx_MyECU_oCAN_b6f80cd2_Tx] */
  /*   ... */  /* [/ActiveEcuC/CanIf/CanIfInitCfg/msg_StartAppl_Tx_MyECU_oCAN_b6f80cd2_Tx] */
  /*    79 */  /* [/ActiveEcuC/CanIf/CanIfInitCfg/msg_StartAppl_Tx_MyECU_oCAN_b6f80cd2_Tx] */
  /*    80 */  /* [/ActiveEcuC/CanIf/CanIfInitCfg/msg_nm_MyECU_oCAN_6fd77547_Tx] */
  /*   ... */  /* [/ActiveEcuC/CanIf/CanIfInitCfg/msg_nm_MyECU_oCAN_6fd77547_Tx] */
  /*    87 */  /* [/ActiveEcuC/CanIf/CanIfInitCfg/msg_nm_MyECU_oCAN_6fd77547_Tx] */
  /*    88 */  /* [/ActiveEcuC/CanIf/CanIfInitCfg/msg_TxCycle_E2eProf1C_500_30_oCAN_f209e413_Tx] */
  /*   ... */  /* [/ActiveEcuC/CanIf/CanIfInitCfg/msg_TxCycle_E2eProf1C_500_30_oCAN_f209e413_Tx] */
  /*    95 */  /* [/ActiveEcuC/CanIf/CanIfInitCfg/msg_TxCycle_E2eProf1C_500_30_oCAN_f209e413_Tx] */
  /*    96 */  /* [/ActiveEcuC/CanIf/CanIfInitCfg/msg_TxCycle1000_10_oCAN_fd90faa0_Tx] */
  /*    97 */  /* [/ActiveEcuC/CanIf/CanIfInitCfg/msg_TxCycle1000_10_oCAN_fd90faa0_Tx] */
  /*    98 */  /* [/ActiveEcuC/CanIf/CanIfInitCfg/msg_TxCycle10_0_oCAN_9c370e88_Tx] */
  /*   ... */  /* [/ActiveEcuC/CanIf/CanIfInitCfg/msg_TxCycle10_0_oCAN_9c370e88_Tx] */
  /*   104 */  /* [/ActiveEcuC/CanIf/CanIfInitCfg/msg_TxCycle10_0_oCAN_9c370e88_Tx] */
  /*   105 */  /* [/ActiveEcuC/CanIf/CanIfInitCfg/msg_TxEvent_10_oCAN_b963731b_Tx] */
  /*   ... */  /* [/ActiveEcuC/CanIf/CanIfInitCfg/msg_TxEvent_10_oCAN_b963731b_Tx] */
  /*   111 */  /* [/ActiveEcuC/CanIf/CanIfInitCfg/msg_TxEvent_10_oCAN_b963731b_Tx] */

#define CANIF_STOP_SEC_VAR_FAST_NOINIT_8BIT
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */


/**********************************************************************************************************************
  CONFIGURATION CLASS: LINK
  SECTION: GLOBAL DATA
**********************************************************************************************************************/



