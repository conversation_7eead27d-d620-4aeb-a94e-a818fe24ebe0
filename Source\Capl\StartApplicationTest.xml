<?xml version="1.0" encoding="iso-8859-1" standalone="yes"?>
<testmodule xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://www.vector-informatik.de/CANoe/TestModule/1.8" xsi:schemaLocation="http://www.vector-informatik.de/CANoe/TestModule/1.8 testmodule.xsd" title="StartApplication" version="1.0">
    <miscinfo title="Please note">
        <info>
            <description>EXAMPLE CODE ONLY

                This Example Code is only intended for illustrating an example of a possible BSW integration and BSW
                configuration. The Example Code has not passed any quality control measures and may be incomplete. The
                Example Code is neither intended nor qualified for use in series production. The Example Code as well
                as any of its modifications and/or implementations must be tested with diligent care and must comply
                with all quality requirements which are necessary according to the state of the art before their use.
            </description>
        </info>
    </miscinfo>
    <templates>
        <!-- ************************************************************************* -->
        <!-- ***                    COM Use Case Templates                         *** -->
        <!-- ************************************************************************* -->
        <testcasetemplate name="DataTransmission template" version="2.0">
            <paramdef name="parDescription" />
            <paramdef name="parComSignalPairSelector" />
            <paramdef name="parCANoeVersion" default="80200" />
            <description>$parDescription$</description>
            <preparation>
                <set title="Clear error log">
                    <sysvar name="StartApplication::ErrorLog"></sysvar>
                </set>
                <statechange title="Activate COM Tx Only Use Case" wait="1200">
                    <in>
                        <sysvar name="StartApplication::UseCaseActivator">3</sysvar>
                    </in>
                    <expected>
                        <sysvar name="StartApplication::UseCaseActivator">3</sysvar>
                    </expected>
                </statechange>
                <awaitvaluematch title="Verify that the current CANoe version is suitable for the Start Application" timeout="10" joincondition="all">
                    <sysvar name="StartApplication::CANoeVersionInfo">
                        <ge>$parCANoeVersion$</ge>
                    </sysvar>
                </awaitvaluematch>
                <set title="Initialize variables">
                    <sysvar name="StartApplication::ComSignalPairSelector">$parComSignalPairSelector$</sysvar>
                    <sysvar name="StartApplication::ComSendCtrl">2</sysvar>
                </set>
                <set title="Initialize ComInput">
                    <sysvar name="StartApplication::ComInput">0</sysvar>
                </set>
                <awaitvaluematch title="Wait for rx data processing on ECU side." timeout="10000" joincondition="all">
                    <sysvar namespace="StartApplication" name="ComCmpOutputs">
                        <eq>0</eq>
                    </sysvar>
                    <sysvar namespace="StartApplication" name="ComActualOutput">
                        <eq>0</eq>
                    </sysvar>
                    <sysvar namespace="StartApplication" name="ComExpectedOutput">
                        <eq>0</eq>
                    </sysvar>
                </awaitvaluematch>
            </preparation>
            <set title="Set sensor value to 254, this will be scaled to the maximum value of the Rx signal">
                <sysvar namespace="StartApplication" name="ComInput">254</sysvar>
            </set>
            <awaitvaluematch title="Wait until there is a difference between actual and expected output because the Rx signal has been transmitted on the bus but the ECU has not updated the Tx signal yet." timeout="10000" joincondition="all">
                <sysvar namespace="StartApplication" name="ComCmpOutputs">
                    <ne>0</ne>
                </sysvar>
            </awaitvaluematch>
            <awaitvaluematch title="Wait until there is no difference between actual and expected output because the ECU has updated the Tx signal" timeout="10000" joincondition="all">
                <sysvar namespace="StartApplication" name="ComCmpOutputs">
                    <eq>0</eq>
                </sysvar>
            </awaitvaluematch>
            <set title="Set sensor value to 128, this will be scaled to the median value of the Rx signal">
                <sysvar namespace="StartApplication" name="ComInput">128</sysvar>
            </set>
            <awaitvaluematch title="Wait until there is a difference between actual and expected output because the Rx signal has been transmitted on the bus but the ECU has not updated the Tx signal yet." timeout="10000" joincondition="all">
                <sysvar namespace="StartApplication" name="ComCmpOutputs">
                    <ne>0</ne>
                </sysvar>
            </awaitvaluematch>
            <awaitvaluematch title="Wait until there is no difference between actual and expected output because the ECU has updated the Tx signal" timeout="10000" joincondition="all">
                <sysvar namespace="StartApplication" name="ComCmpOutputs">
                    <eq>0</eq>
                </sysvar>
            </awaitvaluematch>
            <set title="Set sensor value to 0, this will be scaled to the minimum value of the Rx signal">
                <sysvar namespace="StartApplication" name="ComInput">0</sysvar>
            </set>
            <awaitvaluematch title="Wait until there is a difference between actual and expected output because the Rx signal has been transmitted on the bus but the ECU has not updated the Tx signal yet." timeout="10000" joincondition="all">
                <sysvar namespace="StartApplication" name="ComCmpOutputs">
                    <ne>0</ne>
                </sysvar>
            </awaitvaluematch>
            <awaitvaluematch title="Wait until there is no difference between actual and expected output because the ECU has updated the Tx signal" timeout="10000" joincondition="all">
                <sysvar namespace="StartApplication" name="ComCmpOutputs">
                    <eq>0</eq>
                </sysvar>
            </awaitvaluematch>
            <completion>
                <set title="Switch back to Com Rx/Tx use case">
                    <sysvar name="StartApplication::UseCaseActivator">0</sysvar>
                </set>
                <valuecomment>
                    <description></description>
                    <sysvar name="StartApplication::ErrorLog"/>
                </valuecomment>
            </completion>
        </testcasetemplate>

        <testcasetemplate name="DataReception template" version="2.0">
            <paramdef name="parDescription" />
            <paramdef name="parComSignalPairSelector" />
            <paramdef name="parCANoeVersion" default="80200" />
            <description>$parDescription$</description>
            <preparation>
                <set title="Clear error log">
                    <sysvar name="StartApplication::ErrorLog"></sysvar>
                </set>
                <statechange title="Activate COM Use Case" wait="1200">
                    <in>
                        <sysvar name="StartApplication::UseCaseActivator">0</sysvar>
                    </in>
                    <expected>
                        <sysvar name="StartApplication::UseCaseActivator">0</sysvar>
                    </expected>
                </statechange>
                <awaitvaluematch title="Verify that the current CANoe version is suitable for the Start Application" timeout="10" joincondition="all">
                    <sysvar name="StartApplication::CANoeVersionInfo">
                        <ge>$parCANoeVersion$</ge>
                    </sysvar>
                </awaitvaluematch>
                <set title="Initialize variables">
                    <sysvar name="StartApplication::ComSignalPairSelector">$parComSignalPairSelector$</sysvar>
                    <sysvar name="StartApplication::ComSendCtrl">2</sysvar>
                </set>
                <set title="Initialize ComInput">
                    <sysvar name="StartApplication::ComInput">0</sysvar>
                </set>
                <awaitvaluematch title="Wait for rx data processing on ECU side." timeout="10000" joincondition="all">
                    <sysvar namespace="StartApplication" name="ComCmpOutputs">
                        <eq>0</eq>
                    </sysvar>
                    <sysvar namespace="StartApplication" name="ComActualOutput">
                        <eq>0</eq>
                    </sysvar>
                    <sysvar namespace="StartApplication" name="ComExpectedOutput">
                        <eq>0</eq>
                    </sysvar>
                </awaitvaluematch>
            </preparation>
            <set title="Set sensor value to 254, this will be scaled to the maximum value of the Rx signal">
                <sysvar namespace="StartApplication" name="ComInput">254</sysvar>
            </set>
            <awaitvaluematch title="Wait until there is a difference between actual and expected output because the Rx signal has been transmitted on the bus but the ECU has not updated the Tx signal yet." timeout="10000" joincondition="all">
                <sysvar namespace="StartApplication" name="ComCmpOutputs">
                    <ne>0</ne>
                </sysvar>
            </awaitvaluematch>
            <awaitvaluematch title="Wait until there is no difference between actual and expected output because the ECU has updated the Tx signal" timeout="10000" joincondition="all">
                <sysvar namespace="StartApplication" name="ComCmpOutputs">
                    <eq>0</eq>
                </sysvar>
            </awaitvaluematch>
            <set title="Set sensor value to 128, this will be scaled to the median value of the Rx signal">
                <sysvar namespace="StartApplication" name="ComInput">128</sysvar>
            </set>
            <awaitvaluematch title="Wait until there is a difference between actual and expected output because the Rx signal has been transmitted on the bus but the ECU has not updated the Tx signal yet." timeout="10000" joincondition="all">
                <sysvar namespace="StartApplication" name="ComCmpOutputs">
                    <ne>0</ne>
                </sysvar>
            </awaitvaluematch>
            <awaitvaluematch title="Wait until there is no difference between actual and expected output because the ECU has updated the Tx signal" timeout="10000" joincondition="all">
                <sysvar namespace="StartApplication" name="ComCmpOutputs">
                    <eq>0</eq>
                </sysvar>
            </awaitvaluematch>
            <set title="Set sensor value to 0, this will be scaled to the minimum value of the Rx signal">
                <sysvar namespace="StartApplication" name="ComInput">0</sysvar>
            </set>
            <awaitvaluematch title="Wait until there is a difference between actual and expected output because the Rx signal has been transmitted on the bus but the ECU has not updated the Tx signal yet." timeout="10000" joincondition="all">
                <sysvar namespace="StartApplication" name="ComCmpOutputs">
                    <ne>0</ne>
                </sysvar>
            </awaitvaluematch>
            <awaitvaluematch title="Wait until there is no difference between actual and expected output because the ECU has updated the Tx signal" timeout="10000" joincondition="all">
                <sysvar namespace="StartApplication" name="ComCmpOutputs">
                    <eq>0</eq>
                </sysvar>
            </awaitvaluematch>
            <completion>
                <valuecomment>
                    <description></description>
                    <sysvar name="StartApplication::ErrorLog"/>
                </valuecomment>
            </completion>
        </testcasetemplate>
        <!-- ************************************************************************* -->
        <!-- ***                    DIAG Use Case Templates                        *** -->
        <!-- ************************************************************************* -->
        <testcasetemplate name="BasicRequestResponseViaUDS template" version="2.0">
            <paramdef name="parDescription" />
            <paramdef name="parDiagChannel" />
            <paramdef name="parCANoeVersion" default="80200" />
            <description>$parDescription$</description>
            <preparation>
                <set title="Clear error log">
                    <sysvar name="StartApplication::ErrorLog"></sysvar>
                </set>
                <statechange title="Activate DIAG Use Case" wait="1200">
                    <in>
                        <sysvar name="StartApplication::UseCaseActivator">2</sysvar>
                    </in>
                    <expected>
                        <sysvar name="StartApplication::UseCaseActivator">2</sysvar>
                    </expected>
                </statechange>
                <awaitvaluematch title="Verify that the current CANoe version is suitable for the Start Application" timeout="10" joincondition="all">
                    <sysvar name="StartApplication::CANoeVersionInfo">
                        <ge>$parCANoeVersion$</ge>
                    </sysvar>
                </awaitvaluematch>
                <set title="Initialize variables">
                    <sysvar name="StartApplication::DiagChannel">$parDiagChannel$</sysvar>
                    <sysvar name="StartApplication::DiagActivateDefaultSession">0</sysvar>
                </set>
                <wait title="Wait to allow System Variables to change" time="10"/>
                <awaitvaluematch title="Wait until there is no diagnostic request active and no further requests are queued." timeout="30000" joincondition="all">
                    <sysvar namespace="StartApplication" name="DiagRequestState">
                        <eq>0</eq>
                    </sysvar>
                </awaitvaluematch>
                <statechange title="Reset response code" wait="10">
                    <in>
                        <sysvar name="StartApplication::DiagResponseCode">0</sysvar>
                    </in>
                    <expected>
                        <sysvar name="StartApplication::DiagResponseCode">0</sysvar>
                    </expected>
                </statechange>
            </preparation>
            <statechange title="Try to activate default session via DSC" wait="1000">
                <in>
                    <sysvar name="StartApplication::DiagActivateDefaultSession">1</sysvar>
                </in>
                <expected>
                    <sysvar name="StartApplication::DiagActivateDefaultSession">1</sysvar>
                </expected>
            </statechange>
            <awaitvaluematch title="Verify that a response can be received: either positive response (0x50) or negative response (0x7F)" timeout="5000" joincondition="any">
                <sysvar name="StartApplication::DiagResponseCode">
                    <eq>0x50</eq>
                </sysvar>
                <sysvar name="StartApplication::DiagResponseCode">
                    <eq>0x7F</eq>
                </sysvar>
            </awaitvaluematch>
            <completion>
                <valuecomment>
                    <description></description>
                    <sysvar name="StartApplication::ErrorLog"/>
                </valuecomment>
            </completion>
        </testcasetemplate>

        <testcasetemplate name="ReadDiagnosticDataViaUDS template" version="2.0">
            <paramdef name="parDescription" />
            <paramdef name="parDiagChannel" />
            <paramdef name="parCANoeVersion" default="80200" />
            <description>$parDescription$</description>
            <preparation>
                <set title="Clear error log">
                    <sysvar name="StartApplication::ErrorLog"></sysvar>
                </set>
                <statechange title="Activate DIAG Use Case" wait="1200">
                    <in>
                        <sysvar name="StartApplication::UseCaseActivator">2</sysvar>
                    </in>
                    <expected>
                        <sysvar name="StartApplication::UseCaseActivator">2</sysvar>
                    </expected>
                </statechange>
                <awaitvaluematch title="Verify that the current CANoe version is suitable for the Start Application" timeout="10" joincondition="all">
                    <sysvar name="StartApplication::CANoeVersionInfo">
                        <ge>$parCANoeVersion$</ge>
                    </sysvar>
                </awaitvaluematch>
                <set title="Initialize variables">
                    <sysvar name="StartApplication::DiagChannel">$parDiagChannel$</sysvar>
                    <sysvar name="StartApplication::DiagSetEventStatusPassed">0</sysvar>
                    <sysvar name="StartApplication::DiagGetCounter">0</sysvar>
                    <sysvar name="StartApplication::DiagResetCounterWithRxData">0</sysvar>
                    <sysvar name="StartApplication::DiagCounterValue">0</sysvar>
                </set>
                <wait title="Wait to allow System Variables to change" time="10"/>
                <awaitvaluematch title="Wait until there is no diagnostic request active and no further requests are queued." timeout="30000" joincondition="all">
                    <sysvar namespace="StartApplication" name="DiagRequestState">
                        <eq>0</eq>
                    </sysvar>
                </awaitvaluematch>
                <statechange title="Reset response code" wait="10">
                    <in>
                        <sysvar name="StartApplication::DiagResponseCode">0</sysvar>
                    </in>
                    <expected>
                        <sysvar name="StartApplication::DiagResponseCode">0</sysvar>
                    </expected>
                </statechange>
            </preparation>
            <statechange title="Initialize: Reset DID value to 0 via RxData" wait="100" resettime="100">
                <in>
                    <sysvar name="StartApplication::DiagResetCounterWithRxData">1</sysvar>
                </in>
                <expected>
                    <sysvar name="StartApplication::DiagResetCounterWithRxData">1</sysvar>
                </expected>
            </statechange>
            <wait title="Wait to allow for transmission of control signals" time="1200"/>
            <statechange title="Get DID value via RDBI and verify that it was reset to 0x0000" wait="1000" resettime="100">
                <in>
                    <sysvar name="StartApplication::DiagGetCounter">1</sysvar>
                </in>
                <expected>
                    <sysvar name="StartApplication::DiagCounterValue">0x0000</sysvar>
                    <sysvar name="StartApplication::DiagResponseCode">0x62</sysvar>
                </expected>
            </statechange>
            <statechange title="Set event status to passed to increment the DID value by 1" wait="100" resettime="100">
                <in>
                    <sysvar name="StartApplication::DiagSetEventStatusPassed">1</sysvar>
                </in>
                <expected>
                    <sysvar name="StartApplication::DiagSetEventStatusPassed">1</sysvar>
                </expected>
            </statechange>
            <statechange title="Get DID value via RDBI and verify that it was set to 0x0001" wait="1000" resettime="100">
                <in>
                    <sysvar name="StartApplication::DiagGetCounter">1</sysvar>
                </in>
                <expected>
                    <sysvar name="StartApplication::DiagCounterValue">0x0001</sysvar>
                    <sysvar name="StartApplication::DiagResponseCode">0x62</sysvar>
                </expected>
            </statechange>
            <completion>
                <valuecomment>
                    <description></description>
                    <sysvar name="StartApplication::ErrorLog"/>
                </valuecomment>
            </completion>
        </testcasetemplate>

        <testcasetemplate name="WriteDiagnosticDataViaUDS template" version="2.0">
            <paramdef name="parDescription" />
            <paramdef name="parDiagChannel" />
            <paramdef name="parCANoeVersion" default="80200" />
            <description>$parDescription$</description>
            <preparation>
                <set title="Clear error log">
                    <sysvar name="StartApplication::ErrorLog"></sysvar>
                </set>
                <statechange title="Activate DIAG Use Case" wait="1200">
                    <in>
                        <sysvar name="StartApplication::UseCaseActivator">2</sysvar>
                    </in>
                    <expected>
                        <sysvar name="StartApplication::UseCaseActivator">2</sysvar>
                    </expected>
                </statechange>
                <awaitvaluematch title="Verify that the current CANoe version is suitable for the Start Application" timeout="10" joincondition="all">
                    <sysvar name="StartApplication::CANoeVersionInfo">
                        <ge>$parCANoeVersion$</ge>
                    </sysvar>
                </awaitvaluematch>
                <set title="Initialize variables">
                    <sysvar name="StartApplication::DiagRequestState">0</sysvar>
                    <sysvar name="StartApplication::DiagChannel">$parDiagChannel$</sysvar>
                    <sysvar name="StartApplication::DiagSetCounter">0</sysvar>
                    <sysvar name="StartApplication::DiagCounterValueFromTxData">0</sysvar>
                    <sysvar name="StartApplication::DiagResetCounterWithRxData">0</sysvar>
                </set>
                <wait title="Wait to allow System Variables to change" time="10"/>
                <awaitvaluematch title="Wait until there is no diagnostic request active and no further requests are queued." timeout="30000" joincondition="all">
                    <sysvar namespace="StartApplication" name="DiagRequestState">
                        <eq>0</eq>
                    </sysvar>
                </awaitvaluematch>
                <statechange title="Reset response code" wait="10">
                    <in>
                        <sysvar name="StartApplication::DiagResponseCode">0</sysvar>
                    </in>
                    <expected>
                        <sysvar name="StartApplication::DiagResponseCode">0</sysvar>
                    </expected>
                </statechange>
            </preparation>
            <statechange title="Initialize: Reset DID value to 0 via RxData" wait="100" resettime="100">
                <in>
                    <sysvar name="StartApplication::DiagResetCounterWithRxData">0</sysvar>
                </in>
                <expected>
                    <sysvar name="StartApplication::DiagResetCounterWithRxData">0</sysvar>
                </expected>
            </statechange>
            <wait title="Wait to allow for transmission of control signals" time="1200"/>
            <awaitvaluematch title="Get DID value via TxData and verify that it was reset to 0x0000" timeout="10000" joincondition="all">
                <sysvar name="StartApplication::DiagCounterValueFromTxData">
                    <eq>0</eq>
                </sysvar>
            </awaitvaluematch>
            <statechange title="Set DID value to 0x0005 via WDBI" wait="1000">
                <in>
                    <sysvar name="StartApplication::DiagSetCounter">0x0005</sysvar>
                </in>
                <expected>
                    <sysvar name="StartApplication::DiagResponseCode">0x6E</sysvar>
                </expected>
            </statechange>
            <awaitvaluematch title="Get DID value via TxData and verify that it was set to 0x0005" timeout="10000" joincondition="all">
                <sysvar name="StartApplication::DiagCounterValueFromTxData">
                    <eq>5</eq>
                </sysvar>
            </awaitvaluematch>
            <completion>
                <valuecomment>
                    <description></description>
                    <sysvar name="StartApplication::ErrorLog"/>
                </valuecomment>
            </completion>
        </testcasetemplate>

        <testcasetemplate name="DiagnosticEventHandlingWithFreezeFrameAndWithoutDebouncing template" version="2.0">
            <paramdef name="parDescription" />
            <paramdef name="parDiagChannel" />
            <paramdef name="parCANoeVersion" default="80200" />
            <description>$parDescription$</description>
            <preparation>
                <set title="Clear error log">
                    <sysvar name="StartApplication::ErrorLog"></sysvar>
                </set>
                <statechange title="Activate DIAG Use Case" wait="1200">
                    <in>
                        <sysvar name="StartApplication::UseCaseActivator">2</sysvar>
                    </in>
                    <expected>
                        <sysvar name="StartApplication::UseCaseActivator">2</sysvar>
                    </expected>
                </statechange>
                <awaitvaluematch title="Verify that the current CANoe version is suitable for the Start Application" timeout="10" joincondition="all">
                    <sysvar name="StartApplication::CANoeVersionInfo">
                        <ge>$parCANoeVersion$</ge>
                    </sysvar>
                </awaitvaluematch>
                <set title="Initialize variables">
                    <sysvar name="StartApplication::DiagChannel">$parDiagChannel$</sysvar>
                    <sysvar name="StartApplication::DiagSetEventStatusPassed">0</sysvar>
                    <sysvar name="StartApplication::DiagGetCounter">0</sysvar>
                    <sysvar name="StartApplication::DiagGetDTCSnapshot">0</sysvar>
                    <sysvar name="StartApplication::DiagResetCounterWithRxData">0</sysvar>
                    <sysvar name="StartApplication::DiagClearDTC">0</sysvar>
                    <sysvar name="StartApplication::DiagCounterValue">0</sysvar>
                    <sysvar name="StartApplication::DiagSnapshotDataValue">0</sysvar>
                </set>
                <wait title="Wait to allow System Variables to change" time="10"/>
                <awaitvaluematch title="Wait until there is no diagnostic request active and no further requests are queued." timeout="30000" joincondition="all">
                    <sysvar namespace="StartApplication" name="DiagRequestState">
                        <eq>0</eq>
                    </sysvar>
                </awaitvaluematch>
                <statechange title="Reset response code" wait="10">
                    <in>
                        <sysvar name="StartApplication::DiagResponseCode">0</sysvar>
                    </in>
                    <expected>
                        <sysvar name="StartApplication::DiagResponseCode">0</sysvar>
                    </expected>
                </statechange>
            </preparation>
            <statechange title="Initialize: Reset event counter to 0 via RxData" wait="100" resettime="100">
                <in>
                    <sysvar name="StartApplication::DiagResetCounterWithRxData">1</sysvar>
                </in>
                <expected>
                    <sysvar name="StartApplication::DiagResetCounterWithRxData">1</sysvar>
                </expected>
            </statechange>
            <wait title="Wait to allow for transmission of control signals" time="1200"/>
            <statechange title="Initialize: Clear DTC" wait="1000" resettime="100">
                <in>
                    <sysvar name="StartApplication::DiagClearDTC">1</sysvar>
                </in>
                <expected>
                    <sysvar name="StartApplication::DiagResponseCode">0x54</sysvar>
                </expected>
            </statechange>
            <statechange title="Get DTC snapshot and verify that no snapshot record is returned (i.e. -1)" wait="1000" resettime="100">
                <in>
                    <sysvar name="StartApplication::DiagGetDTCSnapshot">1</sysvar>
                </in>
                <expected>
                    <sysvar name="StartApplication::DiagSnapshotDataValue">-1</sysvar>
                    <sysvar name="StartApplication::DiagResponseCode">0x59</sysvar>
                </expected>
            </statechange>
            <statechange title="Set event status to passed" wait="100" resettime="100">
                <in>
                    <sysvar name="StartApplication::DiagSetEventStatusPassed">1</sysvar>
                </in>
                <expected>
                    <sysvar name="StartApplication::DiagSetEventStatusPassed">1</sysvar>
                </expected>
            </statechange>
            <statechange title="Get DTC snapshot and verify that still no snapshot record is returned (i.e. -1)" wait="1000" resettime="100">
                <in>
                    <sysvar name="StartApplication::DiagGetDTCSnapshot">1</sysvar>
                </in>
                <expected>
                    <sysvar name="StartApplication::DiagSnapshotDataValue">-1</sysvar>
                    <sysvar name="StartApplication::DiagResponseCode">0x59</sysvar>
                </expected>
            </statechange>
            <statechange title="Set event status to failed" wait="100" resettime="100">
                <in>
                    <sysvar name="StartApplication::DiagSetEventStatusFailed">1</sysvar>
                </in>
                <expected>
                    <sysvar name="StartApplication::DiagSetEventStatusFailed">1</sysvar>
                </expected>
            </statechange>
            <statechange title="Get DTC snapshot and verify that a snapshot record with the value 2 is returned" wait="1000" resettime="100">
                <in>
                    <sysvar name="StartApplication::DiagGetDTCSnapshot">1</sysvar>
                </in>
                <expected>
                    <sysvar name="StartApplication::DiagSnapshotDataValue">2</sysvar>
                    <sysvar name="StartApplication::DiagResponseCode">0x59</sysvar>
                </expected>
            </statechange>
            <statechange title="Set event status to passed" wait="100" resettime="100">
                <in>
                    <sysvar name="StartApplication::DiagSetEventStatusPassed">1</sysvar>
                </in>
                <expected>
                    <sysvar name="StartApplication::DiagSetEventStatusPassed">1</sysvar>
                </expected>
            </statechange>
            <statechange title="Get DTC snapshot and verify that still a snapshot record with the value 2 is returned" wait="1000" resettime="100">
                <in>
                    <sysvar name="StartApplication::DiagGetDTCSnapshot">1</sysvar>
                </in>
                <expected>
                    <sysvar name="StartApplication::DiagSnapshotDataValue">2</sysvar>
                    <sysvar name="StartApplication::DiagResponseCode">0x59</sysvar>
                </expected>
            </statechange>
            <statechange title="Set event status to failed" wait="100" resettime="100">
                <in>
                    <sysvar name="StartApplication::DiagSetEventStatusFailed">1</sysvar>
                </in>
                <expected>
                    <sysvar name="StartApplication::DiagSetEventStatusFailed">1</sysvar>
                </expected>
            </statechange>
            <statechange title="Get DTC snapshot and verify that still a snapshot record with the value 2 is returned" wait="1000" resettime="100">
                <in>
                    <sysvar name="StartApplication::DiagGetDTCSnapshot">1</sysvar>
                </in>
                <expected>
                    <sysvar name="StartApplication::DiagSnapshotDataValue">2</sysvar>
                    <sysvar name="StartApplication::DiagResponseCode">0x59</sysvar>
                </expected>
            </statechange>
            <statechange title="Clear DTC" wait="1000" resettime="100">
                <in>
                    <sysvar name="StartApplication::DiagClearDTC">1</sysvar>
                </in>
                <expected>
                    <sysvar name="StartApplication::DiagResponseCode">0x54</sysvar>
                </expected>
            </statechange>
            <statechange title="Get DTC snapshot and verify that no snapshot record is returned (i.e. -1)" wait="1000" resettime="100">
                <in>
                    <sysvar name="StartApplication::DiagGetDTCSnapshot">1</sysvar>
                </in>
                <expected>
                    <sysvar name="StartApplication::DiagSnapshotDataValue">-1</sysvar>
                    <sysvar name="StartApplication::DiagResponseCode">0x59</sysvar>
                </expected>
            </statechange>
            <statechange title="Set event status to failed" wait="100" resettime="100">
                <in>
                    <sysvar name="StartApplication::DiagSetEventStatusFailed">1</sysvar>
                </in>
                <expected>
                    <sysvar name="StartApplication::DiagSetEventStatusFailed">1</sysvar>
                </expected>
            </statechange>
            <statechange title="Get DTC snapshot and verify that a snapshot record with the value 5 is returned" wait="1000" resettime="100">
                <in>
                    <sysvar name="StartApplication::DiagGetDTCSnapshot">1</sysvar>
                </in>
                <expected>
                    <sysvar name="StartApplication::DiagSnapshotDataValue">5</sysvar>
                    <sysvar name="StartApplication::DiagResponseCode">0x59</sysvar>
                </expected>
            </statechange>
            <completion>
                <valuecomment>
                    <description></description>
                    <sysvar name="StartApplication::ErrorLog"/>
                </valuecomment>
            </completion>
        </testcasetemplate>
    <!-- ************************************************************************* -->
    <!-- ***                        MEM Use Case Templates                     *** -->
    <!-- ************************************************************************* -->
        <testcasetemplate name="Write and Read NV block template" version="2.0">
            <paramdef name="parDescription" default="Verify that the application can write and read non-volatile data."/>
            <paramdef name="parBlockSelector"/>
            <description>$parDescription$</description>
            <preparation>
                <vardef title="The value which is written" name="valueToWrite" type="int">0</vardef>
                <set title="Clear error log">
                    <sysvar name="StartApplication::ErrorLog"></sysvar>
                </set>
                <statechange title="Activate MEM Use Case" wait="1200">
                    <in>
                        <sysvar name="StartApplication::UseCaseActivator">1</sysvar>
                    </in>
                    <expected>
                        <sysvar name="StartApplication::UseCaseActivator">1</sysvar>
                    </expected>
                </statechange>
                <statechange title="Set active block" wait="500">
                    <in>
                        <sysvar name="StartApplication::MemNvBlockSelector">$parBlockSelector$</sysvar>
                    </in>
                    <expected>
                        <sysvar name="StartApplication::MemNvBlockSelector">$parBlockSelector$</sysvar>
                    </expected>
                </statechange>
                <set title="Initialize variables">
                    <sysvar name="StartApplication::MemNvStore">0</sysvar>
                    <sysvar name="StartApplication::MemNvStoreValue">0</sysvar>
                    <sysvar name="StartApplication::MemNvReadCurrValue">0</sysvar>
                    <sysvar name="StartApplication::MemNvPendingExtended">0</sysvar>
                </set>
                <awaitvaluematch title="Wait for initial read operation after use case activation to complete" timeout="10000" joincondition="any">
                    <sysvar name="StartApplication::MemNvPendingExtended">
                        <eq>4</eq>
                    </sysvar>
                    <sysvar name="StartApplication::MemNvPendingExtended">
                        <eq>6</eq>
                    </sysvar>
                </awaitvaluematch>
            </preparation>
            <for_loop title="Repeat writing and reading of the NV block with the values 0, 15 and 30" loopvar="valueToWrite" startvalue="0" stopvalue="31" increment="15">
                <set title="Set the new value to write">
                    <sysvar name="StartApplication::MemNvStoreValue">
                        <var name="valueToWrite"/>
                    </sysvar>
                </set>
                <statechange title="Trigger writing of the value. After the write is complete the read operation is started automatically" wait="100" resettime="100">
                    <in>
                        <sysvar name="StartApplication::MemNvStore">1</sysvar>
                    </in>
                    <expected>
                        <sysvar name="StartApplication::MemNvStore">1</sysvar>
                    </expected>
                </statechange>
                <wait title="Wait to allow for transmission of control signals" time="1000"/>
                <awaitvaluematch title="Wait for the read operation to complete and verify that the received value is the one which was previously set to be written" timeout="10000" joincondition="all">
                    <sysvar name="StartApplication::MemNvReadCurrValue">
                        <eq>
                            <var name="valueToWrite"/>
                        </eq>
                    </sysvar>
                    <sysvar name="StartApplication::MemNvPendingExtended">
                        <eq>4</eq>
                    </sysvar>
                </awaitvaluematch>
            </for_loop>
            <completion>
                <valuecomment>
                    <description></description>
                    <sysvar name="StartApplication::ErrorLog"/>
                </valuecomment>
            </completion>
        </testcasetemplate>
    </templates>

    <!-- ************************************************************************* -->
    <!-- ***                    Template Instances                             *** -->
    <!-- ************************************************************************* -->
        <testgroup title="Usecase COM">
            <testgroup title="CAN Data Transmission and Reception">
                <templateinstances template="DataTransmission template">
                    <testcaseinstance ident="TCASE-394602" title="CAN-FD Data Transmission ( Vita_CAN0 )">
                        <paramvalue name="parDescription">Verify that the application can transmit data on CAN_FD using signals or signal groups.
                        Ecu Rx group signal: 'SigRx_Prof1C_Sig32Bit_omsg_RxCycle_E2eProf1C_500_10_oCAN_f0dd3f4d_Rx',
                        Ecu Tx signal: 'Signal_Tx24bit_Cyclic_omsg_TxCycle10_0_oCAN_393dd5fe_Tx'.</paramvalue>
                        <paramvalue name="parComSignalPairSelector">0</paramvalue>
                    </testcaseinstance>
                </templateinstances>
                <templateinstances template="DataReception template">
                    <testcaseinstance ident="TCASE-375706" title="CAN Data Reception ( Vita_CAN0 )">
                        <paramvalue name="parDescription">Verify that the application can receive data on CAN using signals or signal groups.
                        Ecu Rx group signal: 'SigRx_Prof1C_Sig32Bit_omsg_RxCycle_E2eProf1C_500_10_oCAN_f0dd3f4d_Rx',
                        Ecu Tx signal: 'Signal_Tx24bit_Cyclic_omsg_TxCycle10_0_oCAN_393dd5fe_Tx'.</paramvalue>
                        <paramvalue name="parComSignalPairSelector">0</paramvalue>
                    </testcaseinstance>
                </templateinstances>
            </testgroup>
            <testgroup title="LIN Data Transmission and Reception">
                <templateinstances template="DataTransmission template">
                    <testcaseinstance ident="TCASE-375709" title="LIN Data Transmission ( Vita_LIN0 )">
                        <paramvalue name="parDescription">Verify that the application can transmit data on LIN using signals.
                        Ecu Rx group signal: 'SigRx_Prof1C_Sig32Bit_omsg_RxCycle_E2eProf1C_500_10_oCAN_f0dd3f4d_Rx',
                        Ecu Tx signal: 'Sig_StartAppl_LinData_Tx_oFrame_LinTr_MyECU_oLIN00_d383dc6b_Tx'.</paramvalue>
                        <paramvalue name="parComSignalPairSelector">1</paramvalue>
                    </testcaseinstance>
                </templateinstances>
                <templateinstances template="DataReception template">
                    <testcaseinstance ident="TCASE-375710" title="LIN Data Reception ( Vita_LIN0 )">
                        <paramvalue name="parDescription">Verify that the application can receive data on LIN using signals.
                        Ecu Rx signal: 'Sig_StartAppl_LinData_Rx_oFrame_LinTr_RearECU_oLIN00_182ca40c_Rx',
                        Ecu Tx signal: 'Sig_StartAppl_LinData_Tx_oFrame_LinTr_MyECU_oLIN00_d383dc6b_Tx'.</paramvalue>
                        <paramvalue name="parComSignalPairSelector">1</paramvalue>
                    </testcaseinstance>
                </templateinstances>
            </testgroup>
            <testgroup title="FlexRay Data Transmission and Reception">
                <templateinstances template="DataTransmission template">
                    <testcaseinstance ident="TCASE-375707" title="FlexRay Data Transmission ( Vita_FR0 )">
                        <paramvalue name="parDescription">Verify that the application can transmit data on FlexRay using signals or signal groups.
                        Ecu Rx group signal: 'SigRx_Prof1C_Sig32Bit_omsg_RxCycle_E2eProf1C_500_10_oCAN_f0dd3f4d_Rx',
                        Ecu Tx signal: 'Signal_SomeTxSignal_oPDU_Transmit_MyECU_763437bb_Tx'.</paramvalue>
                        <paramvalue name="parComSignalPairSelector">2</paramvalue>
                    </testcaseinstance>
                </templateinstances>
                <templateinstances template="DataReception template">
                    <testcaseinstance ident="TCASE-375708" title="FlexRay Data Reception ( Vita_FR0 )">
                        <paramvalue name="parDescription">Verify that the application can receive data on FlexRay using signals or signal groups.
                        Ecu Rx signal: 'StartAppl_Fr_BothECU_RX_Ctrl_oPDU_Fr_StartAppl_BothECU_RX_cb3aeb40_Rx',
                        Ecu Tx signal: 'Signal_SomeTxSignal_oPDU_Transmit_MyECU_763437bb_Tx'.</paramvalue>
                        <paramvalue name="parComSignalPairSelector">2</paramvalue>
                    </testcaseinstance>
                </templateinstances>
            </testgroup>
        </testgroup>
        <testgroup title="Usecase DIAG">
            <testgroup title="Diagnostics over CAN">
                <templateinstances template="BasicRequestResponseViaUDS template">
                    <testcaseinstance ident="TCASE-478915" title="Basic Request/Response via UDS over CAN ( Vita_CAN0 )">
                        <paramvalue name="parDescription">Verify that a DCM request can be sent and a response can be received via UDS over CAN</paramvalue>
                        <paramvalue name="parDiagChannel">0</paramvalue>
                    </testcaseinstance>
                </templateinstances>
                <templateinstances template="ReadDiagnosticDataViaUDS template">
                    <testcaseinstance ident="TCASE-481982" title="Read Diagnostic Data via UDS over CAN ( Vita_CAN0 )">
                        <paramvalue name="parDescription">Verify that a diagnostic data record (DID) can be read via UDS over CAN</paramvalue>
                        <paramvalue name="parDiagChannel">0</paramvalue>
                    </testcaseinstance>
                </templateinstances>
                <templateinstances template="WriteDiagnosticDataViaUDS template">
                    <testcaseinstance ident="TCASE-483095" title="Write Diagnostic Data via UDS over CAN ( Vita_CAN0 )">
                        <paramvalue name="parDescription">Verify that a diagnostic data record (DID) can be written via UDS over CAN</paramvalue>
                        <paramvalue name="parDiagChannel">0</paramvalue>
                    </testcaseinstance>
                </templateinstances>
            </testgroup>
            <testgroup title="Diagnostic Event Handling">
                <templateinstances template="DiagnosticEventHandlingWithFreezeFrameAndWithoutDebouncing template">
                    <testcaseinstance ident="TCASE-379331" title="Diagnostic Event Handling with FreezeFrame and without Debouncing ( Vita_CAN0 )">
                        <paramvalue name="parDescription">Verify that a diagnostic data record (DID) can be written and read via UDS over CAN</paramvalue>
                        <paramvalue name="parDiagChannel">0</paramvalue>
                    </testcaseinstance>
                </templateinstances>
            </testgroup>
        </testgroup>
        <testgroup title="Usecase MEM">
            <testgroup title="Write and Read Nonvolatile Memory">
                <templateinstances template="Write and Read NV block template">
                    <testcaseinstance ident="TCASE-379342" title="Write and Read NV block (Block 1, FEE)">
                        <paramvalue name="parDescription">Verify that the application can write and read non-volatile data.</paramvalue>
                        <paramvalue name="parBlockSelector">0</paramvalue>
                    </testcaseinstance>
                    <testcaseinstance ident="TCASE-379342" title="Write and Read NV block (Block 2, FEE)">
                        <paramvalue name="parDescription">Verify that the application can write and read non-volatile data.</paramvalue>
                        <paramvalue name="parBlockSelector">1</paramvalue>
                    </testcaseinstance>
                </templateinstances>
            </testgroup>
        </testgroup>
</testmodule>

