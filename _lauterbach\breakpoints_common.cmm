;/**********************************************************************************************************************
;  AUTHOR IDENTITY
; ----------------------------------------------------------------------------------------------------------------------
;  Name                          Initials      Company
;  ----------------------------  ------------  -------------------------------------------------------------------------
;  <PERSON>               v<PERSON>        Vector Informatik GmbH
;  Joachim Wenzel                visjwo        Vector Informatik GmbH
;  Emanuel Schnierle             visese        Vector Informatik GmbH
;  Hahn Andreas                  vishan        Vector Informatik GmbH
;  Derick Beng Yuh               visydg        Vector Informatik GmbH
;-----------------------------------------------------------------------------------------------------------------------
;  REVISION HISTORY
; ----------------------------------------------------------------------------------------------------------------------
;  Version   Date        Author  Description
;  --------  ----------  ------  ---------------------------------------------------------------------------------------
;  01.00.00  2017-08-01  visscs  Initial creation
;**********************************************************************************************************************/

;========================================================================
;Load Breakpoints

  ;________________________________________________________________________
  ;Enable error handler for Breakpoints
  ERROR.RESET
  ON ERROR GOSUB
  (
    ;possible erros:  
    ;##sym_notfnd => Symbol not found   
    IF ERROR.ID()=="##sym_notfnd"
    (
      DIALOG.OK "Breakpoints: symbol not found!" "Continue loading breakpoints!"
    )  
    ;Shows the error id if no previous error ids matches
    ELSE IF ERROR.ID()!=""
    (
      ;DIALOG.OK "Error occurred : " ERROR.ID()
    )
    
    RETURN
  )
  ;________________________________________________________________________
    
    if (os.file(.\breakpoints_user.cmm))
    (
     do ".\breakpoints_user.cmm"
    )
    
    if (os.file(.\breakpoints_det.cmm))
    (
     do ".\breakpoints_det.cmm"
    )    
    
    if (os.file(.\breakpoints_sys.cmm))
    (
     do ".\breakpoints_sys.cmm"
    )    
    
  ;________________________________________________________________________
  ; Restore previous error handler
  ON ERROR inherit
  ;________________________________________________________________________
      
