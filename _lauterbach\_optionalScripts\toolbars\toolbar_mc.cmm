;/**********************************************************************************************************************
;  AUTHOR IDENTITY
; ----------------------------------------------------------------------------------------------------------------------
;  Name                          Initials      Company
;  ----------------------------  ------------  -------------------------------------------------------------------------
;  <PERSON>               v<PERSON>        Vector Informatik GmbH
;  Joachim Wenzel                visjwo        Vector Informatik GmbH
;  Emanuel Schnierle             visese        Vector Informatik GmbH
;  Hahn Andreas                  vishan        Vector Informatik GmbH
;  Derick Beng Yuh               visydg        Vector Informatik GmbH
;-----------------------------------------------------------------------------------------------------------------------
;  REVISION HISTORY
; ----------------------------------------------------------------------------------------------------------------------
;  Version   Date        Author  Description
;  --------  ----------  ------  ---------------------------------------------------------------------------------------
;  01.00.00  2017-08-01  visscs  Initial creation
;**********************************************************************************************************************/

;&NumberOfCores is a GLOBAL variable defined in the user section in _start.cmm

 TOOLBAR ON
 STATUSBAR ON


;========================================================================
; setup Menu Buttons for 1 to 6 Cores

if (&NumberOfCores==1.)
(	 
;no action necessary
)
else if (&NumberOfCores==2.)
(	 
  menu.rp
  (
    add
    toolbar
    (	 
       separator
  	 
         toolitem "Core 0" "C0,R" 
         (
           CORE.SELECT 0
         )  	  
         toolitem "Core 1" "C1,G" 
         (
           CORE.SELECT 1
         )
       separator
    )
  )
)
else if (&NumberOfCores==3.)
(	 
  menu.rp
  (
    add
    toolbar
    (	 
       separator
  	 
         toolitem "Core 0" "C0,R" 
         (
           CORE.SELECT 0
         )  	  
         toolitem "Core 1" "C1,G" 
         (
           CORE.SELECT 1
         )
         toolitem "Core 2" "C2,B" 
         (     
           CORE.SELECT 2
         )
       separator
    )
  )
)
else if (&NumberOfCores==4.)
(	 
  menu.rp
  (
    add
    toolbar
    (	 
       separator
  	 
         toolitem "Core 0" "C0,R" 
         (
           CORE.SELECT 0
         )  	  
         toolitem "Core 1" "C1,G" 
         (
           CORE.SELECT 1
         )
         toolitem "Core 2" "C2,B" 
         (     
           CORE.SELECT 2
         )
         toolitem "Core 3" "C3,Y" 
         (     
           CORE.SELECT 3
         )
       separator
    )
  )
)
else if (&NumberOfCores==5.)
(	 
  menu.rp
  (
    add
    toolbar
    (	 
       separator
  	 
         toolitem "Core 0" "C0,R" 
         (
           CORE.SELECT 0
         )  	  
         toolitem "Core 1" "C1,G" 
         (
           CORE.SELECT 1
         )
         toolitem "Core 2" "C2,B" 
         (     
           CORE.SELECT 2
         )
         toolitem "Core 3" "C3,Y" 
         (     
           CORE.SELECT 3
         )
         toolitem "Core 4" "C4,C" 
         (     
           CORE.SELECT 4
         )
       separator
    )
  )
)
else if (&NumberOfCores==6.)
(	 
  menu.rp
  (
    add
    toolbar
    (	 
       separator
  	 
         toolitem "Core 0" "C0,R" 
         (
           CORE.SELECT 0
         )  	  
         toolitem "Core 1" "C1,G" 
         (
           CORE.SELECT 1
         )
         toolitem "Core 2" "C2,B" 
         (     
           CORE.SELECT 2
         )
         toolitem "Core 3" "C3,Y" 
         (     
           CORE.SELECT 3
         )
         toolitem "Core 4" "C4,C" 
         (     
           CORE.SELECT 4
         )
         toolitem "Core 5" "C5,O" 
         (     
           CORE.SELECT 5
         )	
       separator
    )
  )
)
	
	
	
	