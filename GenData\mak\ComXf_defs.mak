# *********************************************************************************************************************
#   COPYRIGHT
#   -------------------------------------------------------------------------------------------------------------------
#   \verbatim
# 
#                 This software is copyright protected and proprietary to Vector Informatik GmbH.
#                 Vector Informatik GmbH grants to you only those rights as set out in the license conditions.
#                 All other rights remain with Vector Informatik GmbH.
#   \endverbatim
#   -------------------------------------------------------------------------------------------------------------------
#   FILE DESCRIPTION
#   -------------------------------------------------------------------------------------------------------------------
#              File:  ComXf_defs.mak
#            Config:  Demo.dpa
#       ECU-Project:  Demo
# 
#         Generator:  MICROSAR ComXf Generator Version 1.14.0
#                     RTE Core Version 1.22.1
#           License:  CBD2000456
# 
#       Description:  GNU MAKEFILE (defines)
# *********************************************************************************************************************


COMXF_CORE_PATH        =
COMXF_OUTPUT_PATH      =

#----------------------------------------------------------------------------------------------------------------------
# MakeSupport usually includes all header-files which were in the same
# directory as the source-files automatically, but to ensure that the
# Asr-Makefiles will also work with other Basic-Make-Packages,
# it is necessary to define all include directories for this Module
#----------------------------------------------------------------------------------------------------------------------
CC_INCLUDE_PATH     +=
