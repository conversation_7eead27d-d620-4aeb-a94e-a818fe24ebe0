########################################################################################################################
# File Name  : Makefile_ECO.config
# Description: This makefile contains the settings that are needed to perform a code coverage analysis with
#              preprocessed sources with the help of the ECO-tool.
#
#              This file has to be included by the project local "Makefile".
#
#              This file works together with MakeSupport 4.1
#
#              Example (see the project local "Makefile"):
#              include $(MAKESUPPORT_DIR)/Global.Makefile.target.$(VERSION).mk
#          --> -include Makefile_ECO.config
#              -include $(MAKESUPPORT_DIR_U)/quality/Makefile_ECO.mk
#
#-----------------------------------------------------------------------------------------------------------------------
# COPYRIGHT                                                                                                           
#-----------------------------------------------------------------------------------------------------------------------
# Copyright (c) 2019 by Vector Informatik GmbH.                                                  All rights reserved. 
#                                                                                                                     
#-----------------------------------------------------------------------------------------------------------------------
# AUTHOR IDENTITY                                                                                                     
#-----------------------------------------------------------------------------------------------------------------------
# Name                          Initials      Company                                                                 
# ----------------------------  ------------  --------------------------------------------------------------------------
# Clemens von Mann              vircvo        Vector Informatik GmbH
# Claudia Buhl                  vircbl        Vector Informatik GmbH
#-----------------------------------------------------------------------------------------------------------------------
#               R E V I S I O N   H I S T O R Y
#-----------------------------------------------------------------------------------------------------------------------
# Version   Date        Author  Description                                                                           
# --------  ----------  ------  ----------------------------------------------------------------------------------------
# 04.00.04  2018-11-07  vircvo  Initial creation
# 04.00.05  2018-11-19  vircvo  Several modifications and fixes
# 04.01.10  2019-04-02  vircbl  Added support of ARM6 compiler; added support of configurable ECO output folders
# 04.01.25  2019-09-24  vircbl  Updated comment sections
########################################################################################################################

########################################################################################################################
#    EXAMPLE CODE ONLY
#    -------------------------------------------------------------------------------------------------------------------
#    This Example Code is only intended for illustrating an example of a possible BSW integration and BSW configuration.
#    The Example Code has not passed any quality control measures and may be incomplete. The Example Code is neither
#    intended nor qualified for use in series production. The Example Code as well as any of its modifications and/or
#    implementations must be tested with diligent care and must comply with all quality requirements which are necessary
#    according to the state of the art before their use.
########################################################################################################################


###############################################################################
# Section that contains "ECO project" specific settings.
###############################################################################

#------------------------------------------------------------------------------
#----------------------------- MUST be filled out -----------------------------
# Specify the ECO target platform.
# Remark: The ECO target platform "EricFpga" is currently not supported by the
# ECO MakeSupport.
#
# Choose one of the following values:
#   EcoWindows
#   EcoEmbedded
#   EricFpga
#
# Example:
#   ECO_TARGET_PLATFORM = EcoWindows
#------------------------------------------------------------------------------
ECO_TARGET_PLATFORM =

#------------------------------------------------------------------------------
#----------------------------- MUST be filled out -----------------------------
# Specify the kind of code coverage that has to be performed.
# Remark: The coverage type "All" does not include the "MCDC" coverage type
# due to the additional memory consumption on the ECO coverage target.
#
# Choose any combination of the following values:
#   FunctionCoverage
#   DecisionCoverage
#   ConditionDecisionCoverage
#   MinimalMultipleConditionCoverage
#   MCDC
#   StatementCoverage
#   LineCoverage
#   DefineCoverage
#   All (FunctionCoverage, DecisionCoverage, ConditionDecisionCoverage,
#        MinimalMultipleConditionCoverage, StatementCoverage, LineCoverage,
#        DefineCoverage)
#
# Example:
#   ECO_COVERAGE_TYPE += FunctionCoverage
#   ECO_COVERAGE_TYPE += DecisionCoverage
#------------------------------------------------------------------------------
ECO_COVERAGE_TYPE +=

#------------------------------------------------------------------------------
#----------------------------- MUST be filled out -----------------------------
# Specify all source files that have to be instrumented by ECO.
# Use relative paths.
#
# Example:
#   ECO_FILES_THAT_NEED_INSTRUMENTATION += ..\..\..\external\BSW\NvM\NvM.c
#   ECO_FILES_THAT_NEED_INSTRUMENTATION += ..\..\..\external\BSW\Det\Det.c
#------------------------------------------------------------------------------
ECO_FILES_THAT_NEED_INSTRUMENTATION +=

#------------------------------------------------------------------------------
#----------------------------- MUST be filled out -----------------------------
#  Enable compiler or write a new compiler configuration file using
#  Makefile_ECO.config_template
#------------------------------------------------------------------------------
#include $(MAKESUPPORT_DIR_U)/quality/Makefile_ECO.config_arm6
#include $(MAKESUPPORT_DIR_U)/quality/Makefile_ECO.config_diab
#include $(MAKESUPPORT_DIR_U)/quality/Makefile_ECO.config_gcc
#include $(MAKESUPPORT_DIR_U)/quality/Makefile_ECO.config_ghs
#include $(MAKESUPPORT_DIR_U)/quality/Makefile_ECO.config_iar
#include $(MAKESUPPORT_DIR_U)/quality/Makefile_ECO.config_mingw
#include $(MAKESUPPORT_DIR_U)/quality/Makefile_ECO.config_tasking
#include $(MAKESUPPORT_DIR_U)/quality/Makefile_ECO.config_vc

#------------------------------------------------------------------------------
#------------------------ May be filled out (optional) ------------------------
# Add additional ECO-specific options for C preprocessor and compiler below
#------------------------------------------------------------------------------
ECO_CPPFLAGS += 
ECO_CFLAGS   += 

#------------------------------------------------------------------------------
#------------------------ May be filled out (optional) ------------------------
# Specify the name of the ECO project file.
#
# Default value:
#   CoverageProject.xml
#
# Example:
#   ECO_PROJECT_FILENAME = CoverageProject_CanCanFr.xml
#------------------------------------------------------------------------------
ECO_PROJECT_FILENAME =

#------------------------------------------------------------------------------
#------------------------ May be filled out (optional) ------------------------
# Specify all of your "function blacklist" CSV-files here if you have functions
# that need to be ignored by ECO.
# Use relative paths.
#
# The content of a "function blacklist" CSV-file has the following form:
# SourceFileName_1.c;FunctionName_1;Justification_1
# ...
# SourceFileName_N.c;FunctionName_N;Justification_N
#
# Lines that start with a "#" character in a "function blacklist" CSV-file
# are interpreted as comments.
#
# Example:
#   ECO_FUNCTION_BLACKLIST_FILES += FunctionBlacklist1.csv
#   ECO_FUNCTION_BLACKLIST_FILES += FunctionBlacklist2.csv
#------------------------------------------------------------------------------
ECO_FUNCTION_BLACKLIST_FILES +=

#------------------------------------------------------------------------------
#------------------------ May be filled out (optional) ------------------------
# Specify if the ECO "BitVectorSupport" should be enabled.
# If the ECO "BitVectorSupport" is enabled then the RAM consumption for an
# instrumentation point can be reduced from 2 bytes to 1 bit.
# Remark: This feature is currently not supported for the ECO target platform
# "EricFPGA".
#
# Choose one of the following values:
#   0 (disabled)
#   1 (enabled)
#
# Default value:
#   0 (disabled)
#
# Example:
#   ECO_USE_BITVECTORSUPPORT = 1
#------------------------------------------------------------------------------
ECO_USE_BITVECTORSUPPORT =

#------------------------------------------------------------------------------
#------------------------ May be filled out (optional) ------------------------
# Specify if ECO should remove duplicate line directives from "preprocessed"
# source files prior to parsing them.
# Use this option if you encounter errors during the calculation of the
# coverage results.
#
# Choose one of the following values:
#   0 (disabled)
#   1 (enabled)
#
# Default value:
#   0 (disabled)
#
# Example:
#   ECO_ELIMINATE_DUPLICATE_LINE_DIRECTIVES = 1
#------------------------------------------------------------------------------
ECO_ELIMINATE_DUPLICATE_LINE_DIRECTIVES =

#------------------------------------------------------------------------------
#------------------------ May be filled out (optional) ------------------------
# Specify if ECO should handle "static inline functions" in header files
# of "preprocessed" source files.
#
# Choose one of the following values:
#   0 (disabled)
#   1 (enabled)
#
# Default value:
#   0 (disabled)
#
# Example:
#   ECO_HANDLE_STATIC_INLINE_FUNCTIONS_IN_HEADER_FILES = 1
#------------------------------------------------------------------------------
ECO_HANDLE_STATIC_INLINE_FUNCTIONS_IN_HEADER_FILES =

#------------------------------------------------------------------------------
#------------------------ May be filled out (optional) ------------------------
# Specify the names of header files for which code coverage information should
# be determined.
# Remark: This option is only relevant in case when ECO should handle
# "static inline functions" in header files of "preprocessed" source files.
#
# Example:
#   ECO_HEADER_FILES += EthTrcv_30_Tja1100_Int.h
#------------------------------------------------------------------------------
ECO_HEADER_FILES +=

#------------------------------------------------------------------------------
#------------------------ May be filled out (optional) ------------------------
# Specify the syntax of the inline directive of the used compiler if it differs
# from "inline".
# Remark 1: This option is only relevant in case when ECO should handle
# "static inline functions" in header files of "preprocessed" source files.
# Remark 2: This option is only taken into account if the "ECO_HEADER_FILES"
# option does not specify any files and is used to search for header files that
# contain "static inline functions" automatically. This option should be used
# in exceptional cases only. The standard approach is to specify all relevant
# header files with the help of the "ECO_HEADER_FILES" option.
#
# Default value:
#   inline
#
# Example:
#   ECO_INLINE_DIRECTIVE_SYNTAX = __inline
#------------------------------------------------------------------------------
ECO_INLINE_DIRECTIVE_SYNTAX =

#------------------------------------------------------------------------------
#------------------------ May be filled out (optional) ------------------------
# Specify all files that contain your ECO code coverage measurement results in
# the "BIN" format (ECO dump files).
# Use relative paths.
#
# Default value:
#   ..\Canoe\ECO_DUMP.hex
#
# Examples:
#   ECO_MEASUREMENT_REPORT_HEX_FILES += ..\Canoe\ECO_DUMP1.hex
#   ECO_MEASUREMENT_REPORT_HEX_FILES += ..\Canoe\ECO_DUMP2.bin
#------------------------------------------------------------------------------
ECO_MEASUREMENT_REPORT_HEX_FILES +=

#------------------------------------------------------------------------------
#------------------------ May be filled out (optional) ------------------------
# Specify all files that contain your ECO MC/DC code coverage measurement
# results in the "BIN" format (ECO MC/DC dump files).
# Use relative paths.
#
# Default value:
#   ..\Canoe\ECO_MCDC_DUMP.hex
#
# Example:
#   ECO_MCDC_MEASUREMENT_REPORT_HEX_FILES += ..\Canoe\ECO_MCDC_DUMP1.hex
#   ECO_MCDC_MEASUREMENT_REPORT_HEX_FILES += ..\Canoe\ECO_MCDC_DUMP2.hex
#------------------------------------------------------------------------------
ECO_MCDC_MEASUREMENT_REPORT_HEX_FILES +=

#------------------------ May be filled out (optional) ------------------------
# Specify whether a "log file" should be generated during the
# "binary report file" to "XML report file" conversion. The "log file" will be
# generated in the folder where the "binary report file" is located with the
# following name:
# <binary report file>_BinaryData.log
#
# Choose one of the following values:
#   0 (disabled)
#   1 (enabled)
#
# Default value:
#   0 (disabled)
#
# Example:
#   ECO_GENERATE_LOGFILE_DURING_MEASUREMENT_REPORT_HEX_FILE_CONVERSION = 1
#------------------------------------------------------------------------------
ECO_GENERATE_LOGFILE_DURING_MEASUREMENT_REPORT_HEX_FILE_CONVERSION =

#------------------------------------------------------------------------------
#------------------------ May be filled out (optional) ------------------------
# Specify the name of the ECO summary report file (shortened code coverage
# report without code sections).
#
# Default value:
#   SummaryReport.xml
#
# Example:
#   ECO_SUMMARY_REPORT_FILENAME = SummaryReport_CanCanFr.xml
#------------------------------------------------------------------------------
ECO_SUMMARY_REPORT_FILENAME =

#------------------------------------------------------------------------------
#------------------------ May be filled out (optional) ------------------------
# Specify the name of the ECO MCDC report file. 
#
# Default value:
#   McdcReport.csv
#
# Example:
#   ECO_MCDC_REPORT_FILENAME = McdcReport_CanCanFr.csv
#------------------------------------------------------------------------------
ECO_MCDC_REPORT_FILENAME =


###############################################################################
# Section that contains "ECO global report" specific settings
###############################################################################

#------------------------------------------------------------------------------
#------------------------ May be filled out (optional) ------------------------
# Specify all of your ECO project files here that should be part of the
# ECO global report.
# Use absolute or relative paths.
#
# Default value:
#   None
#
# Example:
#   ECO_ECO_PROJECT_FILES_FOR_GLOBAL_REPORT += CoverageProject_CanCanFr1.xml
#   ECO_ECO_PROJECT_FILES_FOR_GLOBAL_REPORT += CoverageProject_CanCanFr2.xml
#------------------------------------------------------------------------------
ECO_ECO_PROJECT_FILES_FOR_GLOBAL_REPORT +=

#------------------------------------------------------------------------------
#------------------------ May be filled out (optional) ------------------------
# Specify the output path (absolute or relative) for the ECO global report.
#
# Default value:
#   .\GlobalReport
#
# Example:
#   ECO_GLOBAL_REPORT_PATH = .\Eco\GlobalReport
#------------------------------------------------------------------------------
ECO_GLOBAL_REPORT_PATH =

###############################################################################
# Section that contains advanced settings for ECO.
###############################################################################

#------------------------------------------------------------------------------
#------------------------ May be filled out (optional) ------------------------
# Specify if ECO should generate debug files during the "preparse phase".
# This is an advanced option and should only be set to 1 (enabled) if you need
# to debug the "preparse phase" of ECO.
#
# Choose one of the following values:
#   0 (disabled)
#   1 (enabled)
#
# Default value:
#   0 (disabled)
#
# Example:
#   ECO_GENERATE_PREPARSER_DEBUG_FILES = 1
#------------------------------------------------------------------------------
ECO_GENERATE_PREPARSER_DEBUG_FILES =

###############################################################################
# Section that contains the path to the ECO tool and the path to the
# "ECO Embedded Files".
###############################################################################

#------------------------------------------------------------------------------
#------------------------ May be filled out (optional) ------------------------
# Specify the path (absolute or relative) to the ECO tool.
#
# Default value:
#   D:\usr\usage\zCantate\zCantate_Eco\zCantate_Eco\Implementation\ECO\x64
#
# Example:
#   ECO_TOOL_PATH = D:\ECO\Tool\x64
#------------------------------------------------------------------------------
ECO_TOOL_PATH =

#------------------------------------------------------------------------------
#------------------------ May be filled out (optional) ------------------------
# Specify the path (absolute or relative) to the "ECO Embedded" files. These
# are:
#   ECO.c, ECO.h, ECO_Cfg.h, ECO_CoverageData.h
#
# Default value:
#   D:\usr\usage\zCantate\zCantate_Eco\zCantate_Eco\Implementation\ECO_Embedded
#
# Example:
#   ECO_EMBEDDED_FILES_PATH = D:\ECO\ECO_Embedded_Files
#------------------------------------------------------------------------------
ECO_EMBEDDED_FILES_PATH =

#------------------------------------------------------------------------------
#------------------------ May be filled out (optional) ------------------------
# Specify the path (absolute or relative) to the "ECO Embedded" output folders.
#
# Default values:
#   ECO_CPP_DIR:     eco-preprocessed
#   ECO_GEN_SRC_DIR: eco-gen-srcs
#   ECO_OBJ_DIR:     eco-obj
#   ECO_BIN_DIR:     eco-bin
#
# Example:
#   ECO_CPP_DIR = Eco\PreprocessedFiles
#------------------------------------------------------------------------------
ECO_CPP_DIR     = 
ECO_GEN_SRC_DIR = 
ECO_OBJ_DIR     = 
ECO_BIN_DIR     = 

# End of Makefile_ECO.config
