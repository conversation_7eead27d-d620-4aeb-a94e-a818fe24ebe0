/**********************************************************************************************************************
 *  COPYRIGHT
 *  -------------------------------------------------------------------------------------------------------------------
 *  \verbatim
 *
 *                 This software is copyright protected and proprietary to Vector Informatik GmbH.
 *                 Vector Informatik GmbH grants to you only those rights as set out in the license conditions.
 *                 All other rights remain with Vector Informatik GmbH.
 *  \endverbatim
 *  -------------------------------------------------------------------------------------------------------------------
 *  LICENSE
 *  -------------------------------------------------------------------------------------------------------------------
 *            Module: Os
 *           Program: MSR_Vector_SLP4
 *          Customer: AUTOSAR 4 Evaluation Bundle
 *       Expiry Date: Not restricted
 *  Ordered Derivat.: Aurix 2G/TC377
 *    License Scope : The usage is restricted to CBD2000456_D02
 *
 *  -------------------------------------------------------------------------------------------------------------------
 *  FILE DESCRIPTION
 *  -------------------------------------------------------------------------------------------------------------------
 *              File: Os_Hal_MemoryProtection_Lcfg.c
 *   Generation Time: 2025-08-05 10:37:18
 *           Project: Demo - Version 1.0
 *          Delivery: CBD2000456_D02
 *      Tool Version: DaVinci Configurator (beta) 5.21.40 SP2
 *
 *
 *********************************************************************************************************************/

/**********************************************************************************************************************
 ! BETA VERSION                                                                                                       !
 !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
 ! This version of DaVinci Configurator Pro and/or the related Basic Software Package is BETA software.               !
 ! BETA Software is basically operable, but not sufficiently tested, verified and/or qualified for use in series      !
 ! production and/or in vehicles operating on public or non-public roads.                                             !
 ! In particular, without limitation, BETA Software may cause unpredictable ECU behavior, may not provide all         !
 ! functions necessary for use in series production and/or may not comply with quality requirements which are         !
 ! necessary according to the state of the art. BETA Software must not be used in series production.                  !
 !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
**********************************************************************************************************************/

/* PRQA S 0777, 0779, 0828 EOF */ /* MD_MSR_Rule5.1, MD_MSR_Rule5.2, MD_MSR_Dir1.1 */

#define OS_HAL_MEMORYPROTECTION_LCFG_SOURCE

/**********************************************************************************************************************
 *  INCLUDES
 *********************************************************************************************************************/

/* AUTOSAR includes */
#include "Std_Types.h"

/* Os module declarations */
#include "Os_Hal_MemoryProtection_Cfg.h"
#include "Os_Hal_MemoryProtection_Lcfg.h"
#include "Os_Hal_MemoryProtection.h"

/* Os kernel module dependencies */

/* Os hal dependencies */


/**********************************************************************************************************************
 *  LOCAL CONSTANT MACROS
 *********************************************************************************************************************/

/**********************************************************************************************************************
 *  LOCAL FUNCTION MACROS
 *********************************************************************************************************************/

/**********************************************************************************************************************
 *  LOCAL DATA TYPES AND STRUCTURES
 *********************************************************************************************************************/

/**********************************************************************************************************************
 *  LOCAL DATA PROTOTYPES
 *********************************************************************************************************************/

/**********************************************************************************************************************
 *  GLOBAL DATA
 *********************************************************************************************************************/

/**********************************************************************************************************************
 *  GLOBAL CONSTANT DATA
 *********************************************************************************************************************/

#define OS_START_SEC_CORE0_CONST_UNSPECIFIED
#include "Os_MemMap_OsSections.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

/*! HAL memory protection configuration data: OsCore0 */
static CONST(Os_Hal_MpuRegionRangeConfigType, OS_CONST) OsCfg_Hal_Mp_OsCore0_DataRegions[6];
static CONST(Os_Hal_MpuRegionRangeConfigType, OS_CONST) OsCfg_Hal_Mp_OsCore0_DataRegions[6] =
{
  {
    /* MPU region: Stack region */
    /* .StartAddress  = */ (uint32)0x0uL,  /* PRQA S 0306 */ /* MD_Os_Hal_Rule11.4_0306 */
    /* .EndAddress    = */ (uint32)0x0uL  /* PRQA S 0306 */ /* MD_Os_Hal_Rule11.4_0306 */
  },
  {
    /* MPU region: READ_ALL_Core0 */
    /* .StartAddress  = */ (uint32)0x00000000uL,  /* PRQA S 0306, 0324 */ /* MD_Os_Hal_Rule11.4_0306, MD_Os_Hal_Rule11.2_0324 */
    /* .EndAddress    = */ (uint32)0xFFFFFFF8uL  /* PRQA S 0306, 0324 */ /* MD_Os_Hal_Rule11.4_0306, MD_Os_Hal_Rule11.2_0324 */
  },
  {
    /* MPU region: TRUSTED_WRITE_OWN_SPR_Core0 */
    /* .StartAddress  = */ (uint32)0x00000000uL,  /* PRQA S 0306, 0324 */ /* MD_Os_Hal_Rule11.4_0306, MD_Os_Hal_Rule11.2_0324 */
    /* .EndAddress    = */ (uint32)0x7FFFFFF8uL  /* PRQA S 0306, 0324 */ /* MD_Os_Hal_Rule11.4_0306, MD_Os_Hal_Rule11.2_0324 */
  },
  {
    /* MPU region: TRUSTED_WRITE_SEGMENT_8_To_B_Core0 */
    /* .StartAddress  = */ (uint32)0x80000000uL,  /* PRQA S 0306, 0324 */ /* MD_Os_Hal_Rule11.4_0306, MD_Os_Hal_Rule11.2_0324 */
    /* .EndAddress    = */ (uint32)0xBFFFFFF8uL  /* PRQA S 0306, 0324 */ /* MD_Os_Hal_Rule11.4_0306, MD_Os_Hal_Rule11.2_0324 */
  },
  {
    /* MPU region: TRUSTED_WRITE_SEGMENT_E_To_F_Core0 */
    /* .StartAddress  = */ (uint32)0xE0000000uL,  /* PRQA S 0306, 0324 */ /* MD_Os_Hal_Rule11.4_0306, MD_Os_Hal_Rule11.2_0324 */
    /* .EndAddress    = */ (uint32)0xFFFFFFF8uL  /* PRQA S 0306, 0324 */ /* MD_Os_Hal_Rule11.4_0306, MD_Os_Hal_Rule11.2_0324 */
  },
  {
    /* MPU region: OsMemoryRegion_ALL */
    /* .StartAddress  = */ (uint32)0x00000000uL,  /* PRQA S 0306, 0324 */ /* MD_Os_Hal_Rule11.4_0306, MD_Os_Hal_Rule11.2_0324 */
    /* .EndAddress    = */ (uint32)0xFFFFFFF8uL  /* PRQA S 0306, 0324 */ /* MD_Os_Hal_Rule11.4_0306, MD_Os_Hal_Rule11.2_0324 */
  },
};
static CONST(Os_Hal_MpuRegionRangeConfigType, OS_CONST) OsCfg_Hal_Mp_OsCore0_CodeRegions[1];
static CONST(Os_Hal_MpuRegionRangeConfigType, OS_CONST) OsCfg_Hal_Mp_OsCore0_CodeRegions[1] =
{
  {
    /* MPU region: EXC_ALL_Core0 */
    /* .StartAddress  = */ (uint32)0x00000000uL,  /* PRQA S 0306, 0324 */ /* MD_Os_Hal_Rule11.4_0306, MD_Os_Hal_Rule11.2_0324 */
    /* .EndAddress    = */ (uint32)0xFFFFFFF8uL  /* PRQA S 0306, 0324 */ /* MD_Os_Hal_Rule11.4_0306, MD_Os_Hal_Rule11.2_0324 */
  },
};
CONST(Os_Hal_MpCoreConfigType, OS_CONST) OsCfg_Hal_Mp_OsCore0 =
{
  /* .MpuDataRegionStartSlot           = */ 0,
  /* .MpuDataRegionCount               = */ 6,
  /* .MpuDataRegions                   = */ OsCfg_Hal_Mp_OsCore0_DataRegions,
  {
  /* Read access bit mask */
  /* MpuDataRegionReadEnablePS0        = */ OS_HAL_COREMPU_ENABLE_REGION0 | OS_HAL_COREMPU_ENABLE_REGION1 | OS_HAL_COREMPU_DISABLE_REGION2 | OS_HAL_COREMPU_DISABLE_REGION3 | OS_HAL_COREMPU_DISABLE_REGION4 | OS_HAL_COREMPU_ENABLE_REGION5,
  /* MpuDataRegionReadEnablePS1        = */ OS_HAL_COREMPU_ENABLE_REGION0 | OS_HAL_COREMPU_ENABLE_REGION1 | OS_HAL_COREMPU_DISABLE_REGION2 | OS_HAL_COREMPU_DISABLE_REGION3 | OS_HAL_COREMPU_DISABLE_REGION4 | OS_HAL_COREMPU_ENABLE_REGION5,
  /* MpuDataRegionReadEnablePS2        = */ OS_HAL_COREMPU_ENABLE_REGION0 | OS_HAL_COREMPU_ENABLE_REGION1 | OS_HAL_COREMPU_DISABLE_REGION2 | OS_HAL_COREMPU_DISABLE_REGION3 | OS_HAL_COREMPU_DISABLE_REGION4 | OS_HAL_COREMPU_ENABLE_REGION5,
  /* MpuDataRegionReadEnablePS3        = */ OS_HAL_COREMPU_ENABLE_REGION0 | OS_HAL_COREMPU_ENABLE_REGION1 | OS_HAL_COREMPU_DISABLE_REGION2 | OS_HAL_COREMPU_DISABLE_REGION3 | OS_HAL_COREMPU_DISABLE_REGION4 | OS_HAL_COREMPU_ENABLE_REGION5,
  /* MpuDataRegionReadEnablePS4        = */ OS_HAL_COREMPU_ENABLE_REGION0 | OS_HAL_COREMPU_ENABLE_REGION1 | OS_HAL_COREMPU_DISABLE_REGION2 | OS_HAL_COREMPU_DISABLE_REGION3 | OS_HAL_COREMPU_DISABLE_REGION4 | OS_HAL_COREMPU_ENABLE_REGION5,
  /* MpuDataRegionReadEnablePS5        = */ OS_HAL_COREMPU_ENABLE_REGION0 | OS_HAL_COREMPU_ENABLE_REGION1 | OS_HAL_COREMPU_DISABLE_REGION2 | OS_HAL_COREMPU_DISABLE_REGION3 | OS_HAL_COREMPU_DISABLE_REGION4 | OS_HAL_COREMPU_ENABLE_REGION5,
  },
  {
  /* Write access bit mask */
  /* MpuDataRegionWriteEnablePS0       = */ OS_HAL_COREMPU_ENABLE_REGION0 | OS_HAL_COREMPU_DISABLE_REGION1 | OS_HAL_COREMPU_ENABLE_REGION2 | OS_HAL_COREMPU_ENABLE_REGION3 | OS_HAL_COREMPU_ENABLE_REGION4 | OS_HAL_COREMPU_ENABLE_REGION5,
  /* MpuDataRegionWriteEnablePS1       = */ OS_HAL_COREMPU_ENABLE_REGION0 | OS_HAL_COREMPU_DISABLE_REGION1 | OS_HAL_COREMPU_DISABLE_REGION2 | OS_HAL_COREMPU_DISABLE_REGION3 | OS_HAL_COREMPU_DISABLE_REGION4 | OS_HAL_COREMPU_ENABLE_REGION5,
  /* MpuDataRegionWriteEnablePS2       = */ OS_HAL_COREMPU_ENABLE_REGION0 | OS_HAL_COREMPU_DISABLE_REGION1 | OS_HAL_COREMPU_DISABLE_REGION2 | OS_HAL_COREMPU_DISABLE_REGION3 | OS_HAL_COREMPU_DISABLE_REGION4 | OS_HAL_COREMPU_ENABLE_REGION5,
  /* MpuDataRegionWriteEnablePS3       = */ OS_HAL_COREMPU_ENABLE_REGION0 | OS_HAL_COREMPU_DISABLE_REGION1 | OS_HAL_COREMPU_DISABLE_REGION2 | OS_HAL_COREMPU_DISABLE_REGION3 | OS_HAL_COREMPU_DISABLE_REGION4 | OS_HAL_COREMPU_ENABLE_REGION5,
  /* MpuDataRegionWriteEnablePS4       = */ OS_HAL_COREMPU_ENABLE_REGION0 | OS_HAL_COREMPU_DISABLE_REGION1 | OS_HAL_COREMPU_DISABLE_REGION2 | OS_HAL_COREMPU_DISABLE_REGION3 | OS_HAL_COREMPU_DISABLE_REGION4 | OS_HAL_COREMPU_ENABLE_REGION5,
  /* MpuDataRegionWriteEnablePS5       = */ OS_HAL_COREMPU_ENABLE_REGION0 | OS_HAL_COREMPU_DISABLE_REGION1 | OS_HAL_COREMPU_DISABLE_REGION2 | OS_HAL_COREMPU_DISABLE_REGION3 | OS_HAL_COREMPU_DISABLE_REGION4 | OS_HAL_COREMPU_ENABLE_REGION5,
  },
  
  /* .MpuCodeRegionStartSlot           = */ 0,
  /* .MpuCodeRegionCount               = */ 1,
  /* .MpuCodeRegions                   = */ OsCfg_Hal_Mp_OsCore0_CodeRegions,
  {
  /* Execution right bit mask */
  /* MpuCodeRegionExecutionEnablePS0   = */ 0uL | OS_HAL_COREMPU_ENABLE_REGION0,
  /* MpuCodeRegionExecutionEnablePS1   = */ 0uL | OS_HAL_COREMPU_ENABLE_REGION0,
  /* MpuCodeRegionExecutionEnablePS2   = */ 0uL | OS_HAL_COREMPU_ENABLE_REGION0,
  /* MpuCodeRegionExecutionEnablePS3   = */ 0uL | OS_HAL_COREMPU_ENABLE_REGION0,
  /* MpuCodeRegionExecutionEnablePS4   = */ 0uL | OS_HAL_COREMPU_ENABLE_REGION0,
  /* MpuCodeRegionExecutionEnablePS5   = */ 0uL | OS_HAL_COREMPU_ENABLE_REGION0,
  }
};

/*! HAL memory protection configuration data: OsApplication_NonTrusted_Core0 */
CONST(Os_Hal_MpAppConfigType, OS_CONST) OsCfg_Hal_Mp_OsApplication_NonTrusted_Core0 =
{
  /* .ProtectionSet                   = */ 0,
  
  /* .MpuDataRegionStartSlot          = */ 6,
  /* .MpuDataRegionCount              = */ 0,
  /* .MpuDataRegions                  = */ NULL_PTR,
  /* .MpuDataRegionEnableMask         = */ 0uL,
  /* .MpuDataRegionReadEnable         = */ 0uL,
  /* .MpuDataRegionWriteEnable        = */ 0uL,
  
  /* .MpuCodeRegionStartSlot          = */ 1,
  /* .MpuCodeRegionCount              = */ 0,
  /* .MpuCodeRegions                  = */ NULL_PTR,
  /* .MpuCodeRegionEnableMask         = */ 0uL,
  /* .MpuCodeRegionExecutionEnable    = */ 0uL,
};

/*! HAL memory protection configuration data: OsApplication_Trusted_Core0 */
CONST(Os_Hal_MpAppConfigType, OS_CONST) OsCfg_Hal_Mp_OsApplication_Trusted_Core0 =
{
  /* .ProtectionSet                   = */ 0,
  
  /* .MpuDataRegionStartSlot          = */ 6,
  /* .MpuDataRegionCount              = */ 0,
  /* .MpuDataRegions                  = */ NULL_PTR,
  /* .MpuDataRegionEnableMask         = */ 0uL,
  /* .MpuDataRegionReadEnable         = */ 0uL,
  /* .MpuDataRegionWriteEnable        = */ 0uL,
  
  /* .MpuCodeRegionStartSlot          = */ 1,
  /* .MpuCodeRegionCount              = */ 0,
  /* .MpuCodeRegions                  = */ NULL_PTR,
  /* .MpuCodeRegionEnableMask         = */ 0uL,
  /* .MpuCodeRegionExecutionEnable    = */ 0uL,
};

/*! HAL memory protection configuration data: SystemApplication_OsCore0 */
CONST(Os_Hal_MpAppConfigType, OS_CONST) OsCfg_Hal_Mp_SystemApplication_OsCore0 =
{
  /* .ProtectionSet                   = */ 0,
  
  /* .MpuDataRegionStartSlot          = */ 6,
  /* .MpuDataRegionCount              = */ 0,
  /* .MpuDataRegions                  = */ NULL_PTR,
  /* .MpuDataRegionEnableMask         = */ 0uL,
  /* .MpuDataRegionReadEnable         = */ 0uL,
  /* .MpuDataRegionWriteEnable        = */ 0uL,
  
  /* .MpuCodeRegionStartSlot          = */ 1,
  /* .MpuCodeRegionCount              = */ 0,
  /* .MpuCodeRegions                  = */ NULL_PTR,
  /* .MpuCodeRegionEnableMask         = */ 0uL,
  /* .MpuCodeRegionExecutionEnable    = */ 0uL,
};

/*! HAL memory protection configuration data: CanIsr_7 */
CONST(Os_Hal_MpThreadConfigType, OS_CONST) OsCfg_Hal_Mp_CanIsr_7 =
{
  /* .ProtectionSet                   = */ 0,
  
  /* .MpuDataRegionStartSlot          = */ 6,
  /* .MpuDataRegionCount              = */ 0,
  /* .MpuDataRegions                  = */ NULL_PTR,
  /* .MpuDataRegionEnableMask         = */ 0uL,
  /* .MpuDataRegionReadEnable         = */ 0uL,
  /* .MpuDataRegionWriteEnable        = */ 0uL,
  
  /* .MpuCodeRegionStartSlot          = */ 1,
  /* .MpuCodeRegionCount              = */ 0,
  /* .MpuCodeRegions                  = */ NULL_PTR,
  /* .MpuCodeRegionEnableMask         = */ 0uL,
  /* .MpuCodeRegionExecutionEnable    = */ 0uL,
};

/*! HAL memory protection configuration data: CounterIsr_SystemTimer */
CONST(Os_Hal_MpThreadConfigType, OS_CONST) OsCfg_Hal_Mp_CounterIsr_SystemTimer =
{
  /* .ProtectionSet                   = */ 0,
  
  /* .MpuDataRegionStartSlot          = */ 6,
  /* .MpuDataRegionCount              = */ 0,
  /* .MpuDataRegions                  = */ NULL_PTR,
  /* .MpuDataRegionEnableMask         = */ 0uL,
  /* .MpuDataRegionReadEnable         = */ 0uL,
  /* .MpuDataRegionWriteEnable        = */ 0uL,
  
  /* .MpuCodeRegionStartSlot          = */ 1,
  /* .MpuCodeRegionCount              = */ 0,
  /* .MpuCodeRegions                  = */ NULL_PTR,
  /* .MpuCodeRegionEnableMask         = */ 0uL,
  /* .MpuCodeRegionExecutionEnable    = */ 0uL,
};

/*! HAL memory protection configuration data: CounterIsr_TpCounter_OsCore0 */
CONST(Os_Hal_MpThreadConfigType, OS_CONST) OsCfg_Hal_Mp_CounterIsr_TpCounter_OsCore0 =
{
  /* .ProtectionSet                   = */ 0,
  
  /* .MpuDataRegionStartSlot          = */ 6,
  /* .MpuDataRegionCount              = */ 0,
  /* .MpuDataRegions                  = */ NULL_PTR,
  /* .MpuDataRegionEnableMask         = */ 0uL,
  /* .MpuDataRegionReadEnable         = */ 0uL,
  /* .MpuDataRegionWriteEnable        = */ 0uL,
  
  /* .MpuCodeRegionStartSlot          = */ 1,
  /* .MpuCodeRegionCount              = */ 0,
  /* .MpuCodeRegions                  = */ NULL_PTR,
  /* .MpuCodeRegionEnableMask         = */ 0uL,
  /* .MpuCodeRegionExecutionEnable    = */ 0uL,
};

/*! HAL memory protection configuration data: Fr_IrqLine0 */
CONST(Os_Hal_MpThreadConfigType, OS_CONST) OsCfg_Hal_Mp_Fr_IrqLine0 =
{
  /* .ProtectionSet                   = */ 0,
  
  /* .MpuDataRegionStartSlot          = */ 6,
  /* .MpuDataRegionCount              = */ 0,
  /* .MpuDataRegions                  = */ NULL_PTR,
  /* .MpuDataRegionEnableMask         = */ 0uL,
  /* .MpuDataRegionReadEnable         = */ 0uL,
  /* .MpuDataRegionWriteEnable        = */ 0uL,
  
  /* .MpuCodeRegionStartSlot          = */ 1,
  /* .MpuCodeRegionCount              = */ 0,
  /* .MpuCodeRegions                  = */ NULL_PTR,
  /* .MpuCodeRegionEnableMask         = */ 0uL,
  /* .MpuCodeRegionExecutionEnable    = */ 0uL,
};

/*! HAL memory protection configuration data: Fr_IrqTimer0 */
CONST(Os_Hal_MpThreadConfigType, OS_CONST) OsCfg_Hal_Mp_Fr_IrqTimer0 =
{
  /* .ProtectionSet                   = */ 0,
  
  /* .MpuDataRegionStartSlot          = */ 6,
  /* .MpuDataRegionCount              = */ 0,
  /* .MpuDataRegions                  = */ NULL_PTR,
  /* .MpuDataRegionEnableMask         = */ 0uL,
  /* .MpuDataRegionReadEnable         = */ 0uL,
  /* .MpuDataRegionWriteEnable        = */ 0uL,
  
  /* .MpuCodeRegionStartSlot          = */ 1,
  /* .MpuCodeRegionCount              = */ 0,
  /* .MpuCodeRegions                  = */ NULL_PTR,
  /* .MpuCodeRegionEnableMask         = */ 0uL,
  /* .MpuCodeRegionExecutionEnable    = */ 0uL,
};

/*! HAL memory protection configuration data: Lin_Channel_2_EX_Extended_Error_Interrupt */
CONST(Os_Hal_MpThreadConfigType, OS_CONST) OsCfg_Hal_Mp_Lin_Channel_2_EX_Extended_Error_Interrupt =
{
  /* .ProtectionSet                   = */ 0,
  
  /* .MpuDataRegionStartSlot          = */ 6,
  /* .MpuDataRegionCount              = */ 0,
  /* .MpuDataRegions                  = */ NULL_PTR,
  /* .MpuDataRegionEnableMask         = */ 0uL,
  /* .MpuDataRegionReadEnable         = */ 0uL,
  /* .MpuDataRegionWriteEnable        = */ 0uL,
  
  /* .MpuCodeRegionStartSlot          = */ 1,
  /* .MpuCodeRegionCount              = */ 0,
  /* .MpuCodeRegions                  = */ NULL_PTR,
  /* .MpuCodeRegionEnableMask         = */ 0uL,
  /* .MpuCodeRegionExecutionEnable    = */ 0uL,
};

/*! HAL memory protection configuration data: Lin_Channel_2_RX_Receive_Interrupt */
CONST(Os_Hal_MpThreadConfigType, OS_CONST) OsCfg_Hal_Mp_Lin_Channel_2_RX_Receive_Interrupt =
{
  /* .ProtectionSet                   = */ 0,
  
  /* .MpuDataRegionStartSlot          = */ 6,
  /* .MpuDataRegionCount              = */ 0,
  /* .MpuDataRegions                  = */ NULL_PTR,
  /* .MpuDataRegionEnableMask         = */ 0uL,
  /* .MpuDataRegionReadEnable         = */ 0uL,
  /* .MpuDataRegionWriteEnable        = */ 0uL,
  
  /* .MpuCodeRegionStartSlot          = */ 1,
  /* .MpuCodeRegionCount              = */ 0,
  /* .MpuCodeRegions                  = */ NULL_PTR,
  /* .MpuCodeRegionEnableMask         = */ 0uL,
  /* .MpuCodeRegionExecutionEnable    = */ 0uL,
};

/*! HAL memory protection configuration data: Lin_Channel_2_TX_Transmit_Interrupt */
CONST(Os_Hal_MpThreadConfigType, OS_CONST) OsCfg_Hal_Mp_Lin_Channel_2_TX_Transmit_Interrupt =
{
  /* .ProtectionSet                   = */ 0,
  
  /* .MpuDataRegionStartSlot          = */ 6,
  /* .MpuDataRegionCount              = */ 0,
  /* .MpuDataRegions                  = */ NULL_PTR,
  /* .MpuDataRegionEnableMask         = */ 0uL,
  /* .MpuDataRegionReadEnable         = */ 0uL,
  /* .MpuDataRegionWriteEnable        = */ 0uL,
  
  /* .MpuCodeRegionStartSlot          = */ 1,
  /* .MpuCodeRegionCount              = */ 0,
  /* .MpuCodeRegions                  = */ NULL_PTR,
  /* .MpuCodeRegionEnableMask         = */ 0uL,
  /* .MpuCodeRegionExecutionEnable    = */ 0uL,
};

/*! HAL memory protection configuration data: Default_BSW_Async_Task */
CONST(Os_Hal_MpThreadConfigType, OS_CONST) OsCfg_Hal_Mp_Default_BSW_Async_Task =
{
  /* .ProtectionSet                   = */ 0,
  
  /* .MpuDataRegionStartSlot          = */ 6,
  /* .MpuDataRegionCount              = */ 0,
  /* .MpuDataRegions                  = */ NULL_PTR,
  /* .MpuDataRegionEnableMask         = */ 0uL,
  /* .MpuDataRegionReadEnable         = */ 0uL,
  /* .MpuDataRegionWriteEnable        = */ 0uL,
  
  /* .MpuCodeRegionStartSlot          = */ 1,
  /* .MpuCodeRegionCount              = */ 0,
  /* .MpuCodeRegions                  = */ NULL_PTR,
  /* .MpuCodeRegionEnableMask         = */ 0uL,
  /* .MpuCodeRegionExecutionEnable    = */ 0uL,
};

/*! HAL memory protection configuration data: Default_BSW_Sync_Task */
CONST(Os_Hal_MpThreadConfigType, OS_CONST) OsCfg_Hal_Mp_Default_BSW_Sync_Task =
{
  /* .ProtectionSet                   = */ 0,
  
  /* .MpuDataRegionStartSlot          = */ 6,
  /* .MpuDataRegionCount              = */ 0,
  /* .MpuDataRegions                  = */ NULL_PTR,
  /* .MpuDataRegionEnableMask         = */ 0uL,
  /* .MpuDataRegionReadEnable         = */ 0uL,
  /* .MpuDataRegionWriteEnable        = */ 0uL,
  
  /* .MpuCodeRegionStartSlot          = */ 1,
  /* .MpuCodeRegionCount              = */ 0,
  /* .MpuCodeRegions                  = */ NULL_PTR,
  /* .MpuCodeRegionEnableMask         = */ 0uL,
  /* .MpuCodeRegionExecutionEnable    = */ 0uL,
};

/*! HAL memory protection configuration data: Default_Init_Task */
CONST(Os_Hal_MpThreadConfigType, OS_CONST) OsCfg_Hal_Mp_Default_Init_Task =
{
  /* .ProtectionSet                   = */ 0,
  
  /* .MpuDataRegionStartSlot          = */ 6,
  /* .MpuDataRegionCount              = */ 0,
  /* .MpuDataRegions                  = */ NULL_PTR,
  /* .MpuDataRegionEnableMask         = */ 0uL,
  /* .MpuDataRegionReadEnable         = */ 0uL,
  /* .MpuDataRegionWriteEnable        = */ 0uL,
  
  /* .MpuCodeRegionStartSlot          = */ 1,
  /* .MpuCodeRegionCount              = */ 0,
  /* .MpuCodeRegions                  = */ NULL_PTR,
  /* .MpuCodeRegionEnableMask         = */ 0uL,
  /* .MpuCodeRegionExecutionEnable    = */ 0uL,
};

/*! HAL memory protection configuration data: Default_Init_Task_Trusted */
CONST(Os_Hal_MpThreadConfigType, OS_CONST) OsCfg_Hal_Mp_Default_Init_Task_Trusted =
{
  /* .ProtectionSet                   = */ 0,
  
  /* .MpuDataRegionStartSlot          = */ 6,
  /* .MpuDataRegionCount              = */ 0,
  /* .MpuDataRegions                  = */ NULL_PTR,
  /* .MpuDataRegionEnableMask         = */ 0uL,
  /* .MpuDataRegionReadEnable         = */ 0uL,
  /* .MpuDataRegionWriteEnable        = */ 0uL,
  
  /* .MpuCodeRegionStartSlot          = */ 1,
  /* .MpuCodeRegionCount              = */ 0,
  /* .MpuCodeRegions                  = */ NULL_PTR,
  /* .MpuCodeRegionEnableMask         = */ 0uL,
  /* .MpuCodeRegionExecutionEnable    = */ 0uL,
};

/*! HAL memory protection configuration data: Default_RTE_Mode_switch_Task */
CONST(Os_Hal_MpThreadConfigType, OS_CONST) OsCfg_Hal_Mp_Default_RTE_Mode_switch_Task =
{
  /* .ProtectionSet                   = */ 0,
  
  /* .MpuDataRegionStartSlot          = */ 6,
  /* .MpuDataRegionCount              = */ 0,
  /* .MpuDataRegions                  = */ NULL_PTR,
  /* .MpuDataRegionEnableMask         = */ 0uL,
  /* .MpuDataRegionReadEnable         = */ 0uL,
  /* .MpuDataRegionWriteEnable        = */ 0uL,
  
  /* .MpuCodeRegionStartSlot          = */ 1,
  /* .MpuCodeRegionCount              = */ 0,
  /* .MpuCodeRegions                  = */ NULL_PTR,
  /* .MpuCodeRegionEnableMask         = */ 0uL,
  /* .MpuCodeRegionExecutionEnable    = */ 0uL,
};

/*! HAL memory protection configuration data: IdleTask_OsCore0 */
CONST(Os_Hal_MpThreadConfigType, OS_CONST) OsCfg_Hal_Mp_IdleTask_OsCore0 =
{
  /* .ProtectionSet                   = */ 0,
  
  /* .MpuDataRegionStartSlot          = */ 6,
  /* .MpuDataRegionCount              = */ 0,
  /* .MpuDataRegions                  = */ NULL_PTR,
  /* .MpuDataRegionEnableMask         = */ 0uL,
  /* .MpuDataRegionReadEnable         = */ 0uL,
  /* .MpuDataRegionWriteEnable        = */ 0uL,
  
  /* .MpuCodeRegionStartSlot          = */ 1,
  /* .MpuCodeRegionCount              = */ 0,
  /* .MpuCodeRegions                  = */ NULL_PTR,
  /* .MpuCodeRegionEnableMask         = */ 0uL,
  /* .MpuCodeRegionExecutionEnable    = */ 0uL,
};

/*! HAL memory protection configuration data: StartApplication_Appl_Init_Task */
CONST(Os_Hal_MpThreadConfigType, OS_CONST) OsCfg_Hal_Mp_StartApplication_Appl_Init_Task =
{
  /* .ProtectionSet                   = */ 0,
  
  /* .MpuDataRegionStartSlot          = */ 6,
  /* .MpuDataRegionCount              = */ 0,
  /* .MpuDataRegions                  = */ NULL_PTR,
  /* .MpuDataRegionEnableMask         = */ 0uL,
  /* .MpuDataRegionReadEnable         = */ 0uL,
  /* .MpuDataRegionWriteEnable        = */ 0uL,
  
  /* .MpuCodeRegionStartSlot          = */ 1,
  /* .MpuCodeRegionCount              = */ 0,
  /* .MpuCodeRegions                  = */ NULL_PTR,
  /* .MpuCodeRegionEnableMask         = */ 0uL,
  /* .MpuCodeRegionExecutionEnable    = */ 0uL,
};

/*! HAL memory protection configuration data: StartApplication_Appl_Task */
CONST(Os_Hal_MpThreadConfigType, OS_CONST) OsCfg_Hal_Mp_StartApplication_Appl_Task =
{
  /* .ProtectionSet                   = */ 0,
  
  /* .MpuDataRegionStartSlot          = */ 6,
  /* .MpuDataRegionCount              = */ 0,
  /* .MpuDataRegions                  = */ NULL_PTR,
  /* .MpuDataRegionEnableMask         = */ 0uL,
  /* .MpuDataRegionReadEnable         = */ 0uL,
  /* .MpuDataRegionWriteEnable        = */ 0uL,
  
  /* .MpuCodeRegionStartSlot          = */ 1,
  /* .MpuCodeRegionCount              = */ 0,
  /* .MpuCodeRegions                  = */ NULL_PTR,
  /* .MpuCodeRegionEnableMask         = */ 0uL,
  /* .MpuCodeRegionExecutionEnable    = */ 0uL,
};

#define OS_STOP_SEC_CORE0_CONST_UNSPECIFIED
#include "Os_MemMap_OsSections.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */


#define OS_START_SEC_CONST_UNSPECIFIED
#include "Os_MemMap_OsSections.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

/*! HAL memory protection configuration data: SystemMpu */
CONST(Os_Hal_MpSystemConfigType, OS_CONST) OsCfg_Hal_Mp_SystemMpu =
{
  /* .SysMpuId  = */ 0
};

/*! HAL memory protection configuration data: EmptyThread */

CONST(Os_Hal_MpThreadConfigType, OS_CONST) OsCfg_Hal_Mp_EmptyThread =
{
  /* .ProtectionSet                   = */ 0,
  
  /* .MpuDataRegionStartSlot          = */ 0,
  /* .MpuDataRegionCount              = */ 0,
  /* .MpuDataRegions                  = */ NULL_PTR,
  /* .MpuDataRegionEnableMask         = */ 0uL,
  /* .MpuDataRegionReadEnable         = */ 0uL,
  /* .MpuDataRegionWriteEnable        = */ 0uL,
  
  /* .MpuCodeRegionStartSlot          = */ 0,
  /* .MpuCodeRegionCount              = */ 0,
  /* .MpuCodeRegions                  = */ NULL_PTR,
  /* .MpuCodeRegionEnableMask         = */ 0uL,
  /* .MpuCodeRegionExecutionEnable    = */ 0uL
};

#define OS_STOP_SEC_CONST_UNSPECIFIED
#include "Os_MemMap_OsSections.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */


/**********************************************************************************************************************
 *  LOCAL FUNCTION PROTOTYPES
 *********************************************************************************************************************/

/**********************************************************************************************************************
 *  LOCAL FUNCTIONS
 *********************************************************************************************************************/

/**********************************************************************************************************************
 *  GLOBAL FUNCTIONS
 *********************************************************************************************************************/


/**********************************************************************************************************************
 *  END OF FILE: Os_Hal_MemoryProtection_Lcfg.c
 *********************************************************************************************************************/
