#include "FrSM.h"

#include "FrAppl.h"

// void Appl_TricoreAurixInit(void)
// {
	// return;
// }

void ApplFr_ISR_CycleStart(void)
{
	return;
}

void ApplFr_ISR_Timer0(void)
{
	FrIf_JobListExec_0();
	return;
}

FUNC( void, FRSM_APPL_CODE ) ApplFr_OutOfSync( CONST (NetworkHandleType, AUTOMATIC) NetworkHandle )
{   
    int DummyVar = 0;
    DummyVar ++;
    /*
    while (1)
    {
        ;
    }
    */
	return;
}
