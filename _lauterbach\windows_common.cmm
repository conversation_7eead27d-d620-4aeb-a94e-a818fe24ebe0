;/**********************************************************************************************************************
;  AUTHOR IDENTITY
; ----------------------------------------------------------------------------------------------------------------------
;  Name                          Initials      Company
;  ----------------------------  ------------  -------------------------------------------------------------------------
;  <PERSON>               v<PERSON>        Vector Informatik GmbH
;  Joachim Wenzel                visjwo        Vector Informatik GmbH
;  Emanuel Schnierle             visese        Vector Informatik GmbH
;  Hahn Andreas                  vishan        Vector Informatik GmbH
;  Derick Beng Yuh               visydg        Vector Informatik GmbH
;-----------------------------------------------------------------------------------------------------------------------
;  REVISION HISTORY
; ----------------------------------------------------------------------------------------------------------------------
;  Version   Date        Author  Description
;  --------  ----------  ------  ---------------------------------------------------------------------------------------
;  01.00.00  2017-07-13  visscs  Initial creation
;**********************************************************************************************************************/

 B::
 
 TOOLBAR ON
 STATUSBAR ON
 FramePOS ,,,,Maximized
 
 WinCLEAR
 
 if (&UseHighResWindows==1.)
 (
   ; Format: WinPOS [<pos>] [<size>] [<scale>] [<windowname>] [<state>] [<header>]
   ; <state>: Normal | Iconic | Maximized
    
    WinPOS 0.0 0.0 148. 58. 14. 1. DataList
    WinTABS 10. 10. 25. 62.
    Data.List
    IF ("&DebugMode"=="ASM")
    (
      mode.ASM ;Show only HLL Code and no assembly by default
    )
    ELSE IF ("&DebugMode"=="MIX")
    (
      mode.MIX ;Show only HLL Code and no assembly by default
    )
    ELSE ("&DebugMode"=="HLL")
    (
      mode.HLL ;Show only HLL Code and no assembly by default
    ) 
    
    WinPOS 0.14 65.5 55. 36. 0. 0. Reg
    Register /SPOTLIGHT
    
    WinPOS 61.0 65.5 87. 34. 0. 0. SymBrowser
    WinTABS 51. 26.
    symbol.browse.function
    
    WinPOS 154. 0.17 64. 33. 0. 0. WatchLoc
    Var.Local %HEX %SPOTLIGHT
    
    WinPOS 224. 0.17 85. 39. 0. 0. CallStack
    Frame.view
    
    WinPOS 224. 46.5 85. 43. 0. 1. Breakpoints
    WinTABS 13. 16. 0. 0. 0. 0. 0.
    break.list
    
    WinPOS 154.0 38.5 64. 61. 0. 0. WatchVar
    Var.Watch %HEX %SPOTLIGHT
 )
 ELSE
 (
 
    ;Format: WinPOS [<pos>] [<size>] [<scale>] [<windowname>] [<state>] [<header>]
    ;<state>: Normal | Iconic | Maximized
   
   WinPOS 0.0 0.0 120. 35. 14. 1. DataList
   WinTABS 10. 10. 25. 62.
   Data.List
   IF ("&DebugMode"=="ASM")
   (
     mode.ASM ;Show only HLL Code and no assembly by default
   )
   ELSE IF ("&DebugMode"=="MIX")
   (
     mode.MIX ;Show only HLL Code and no assembly by default
   )
   ELSE ("&DebugMode"=="HLL")
   (
     mode.HLL ;Show only HLL Code and no assembly by default
   ) 
   
   WinPOS 0.0 42. 55. 25. 0. 0. Reg
   Register /SPOTLIGHT
   
   WinPOS 61. 42. 59. 23. 0. 0. SymBrowser
   WinTABS 51. 26.
   symbol.browse.function
   
   WinPOS 126. 0. 50. 20. 0. 0. WatchLoc
   Var.Local %HEX %SPOTLIGHT
   
   WinPOS 182.0 0. 85. 18. 0. 0. CallStack
   Frame.view
   
   WinPOS 182. 25. 85. 30. 0. 5. Breakpoints
   WinTABS 13. 16. 0. 0. 0. 0. 0.
   break.list
   
   WinPOS 126. 25. 50. 40. 0. 0. WatchVar
   Var.Watch %HEX %SPOTLIGHT
 )
 
 WinPAGE.select MainPage
 
 
 ENDDO
