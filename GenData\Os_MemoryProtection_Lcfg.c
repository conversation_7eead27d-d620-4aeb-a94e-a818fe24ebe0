/**********************************************************************************************************************
 *  COPYRIGHT
 *  -------------------------------------------------------------------------------------------------------------------
 *  \verbatim
 *
 *                 This software is copyright protected and proprietary to Vector Informatik GmbH.
 *                 Vector Informatik GmbH grants to you only those rights as set out in the license conditions.
 *                 All other rights remain with Vector Informatik GmbH.
 *  \endverbatim
 *  -------------------------------------------------------------------------------------------------------------------
 *  LICENSE
 *  -------------------------------------------------------------------------------------------------------------------
 *            Module: Os
 *           Program: MSR_Vector_SLP4
 *          Customer: AUTOSAR 4 Evaluation Bundle
 *       Expiry Date: Not restricted
 *  Ordered Derivat.: Aurix 2G/TC377
 *    License Scope : The usage is restricted to CBD2000456_D02
 *
 *  -------------------------------------------------------------------------------------------------------------------
 *  FILE DESCRIPTION
 *  -------------------------------------------------------------------------------------------------------------------
 *              File: Os_MemoryProtection_Lcfg.c
 *   Generation Time: 2025-08-05 10:37:18
 *           Project: Demo - Version 1.0
 *          Delivery: CBD2000456_D02
 *      Tool Version: DaVinci Configurator (beta) 5.21.40 SP2
 *
 *
 *********************************************************************************************************************/

/**********************************************************************************************************************
 ! BETA VERSION                                                                                                       !
 !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
 ! This version of DaVinci Configurator Pro and/or the related Basic Software Package is BETA software.               !
 ! BETA Software is basically operable, but not sufficiently tested, verified and/or qualified for use in series      !
 ! production and/or in vehicles operating on public or non-public roads.                                             !
 ! In particular, without limitation, BETA Software may cause unpredictable ECU behavior, may not provide all         !
 ! functions necessary for use in series production and/or may not comply with quality requirements which are         !
 ! necessary according to the state of the art. BETA Software must not be used in series production.                  !
 !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
**********************************************************************************************************************/

/* PRQA S 0777, 0779, 0828 EOF */ /* MD_MSR_Rule5.1, MD_MSR_Rule5.2, MD_MSR_Dir1.1 */

#define OS_MEMORYROTECTION_LCFG_SOURCE

/**********************************************************************************************************************
 *  INCLUDES
 *********************************************************************************************************************/

/* AUTOSAR includes */
#include "Std_Types.h"

/* Os module declarations */
#include "Os_MemoryProtection_Lcfg.h"
#include "Os_MemoryProtection.h"

/* Os kernel module dependencies */

/* Os hal dependencies */
#include "Os_Hal_MemoryProtection_Lcfg.h"


/**********************************************************************************************************************
 *  LOCAL CONSTANT MACROS
 *********************************************************************************************************************/

/**********************************************************************************************************************
 *  LOCAL FUNCTION MACROS
 *********************************************************************************************************************/

/**********************************************************************************************************************
 *  LOCAL DATA TYPES AND STRUCTURES
 *********************************************************************************************************************/

/**********************************************************************************************************************
 *  LOCAL DATA PROTOTYPES
 *********************************************************************************************************************/

/**********************************************************************************************************************
 *  GLOBAL DATA
 *********************************************************************************************************************/

/**********************************************************************************************************************
 *  GLOBAL CONSTANT DATA
 *********************************************************************************************************************/

#define OS_START_SEC_CORE0_CONST_UNSPECIFIED
#include "Os_MemMap_OsSections.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

/*! Memory protection configuration data: OsCore0 */
CONST(Os_MpCoreConfigType, OS_CONST) OsCfg_Mp_OsCore0 =
{
  /* .HwConfig = */ &OsCfg_Hal_Mp_OsCore0
};

/*! Memory protection configuration data: OsApplication_NonTrusted_Core0 */
CONST(Os_MpAccessRightsType, OS_CONST) OsCfg_Mp_OsApplication_NonTrusted_Core0 =
{
  /* .AppAccessRights    = */ &OsCfg_Hal_Mp_OsApplication_NonTrusted_Core0,
  /* .ThreadAccessRights = */ &OsCfg_Hal_Mp_EmptyThread
};

/*! Memory protection configuration data: OsApplication_Trusted_Core0 */
CONST(Os_MpAccessRightsType, OS_CONST) OsCfg_Mp_OsApplication_Trusted_Core0 =
{
  /* .AppAccessRights    = */ &OsCfg_Hal_Mp_OsApplication_Trusted_Core0,
  /* .ThreadAccessRights = */ &OsCfg_Hal_Mp_EmptyThread
};

/*! Memory protection configuration data: SystemApplication_OsCore0 */
CONST(Os_MpAccessRightsType, OS_CONST) OsCfg_Mp_SystemApplication_OsCore0 =
{
  /* .AppAccessRights    = */ &OsCfg_Hal_Mp_SystemApplication_OsCore0,
  /* .ThreadAccessRights = */ &OsCfg_Hal_Mp_EmptyThread
};

/*! Memory protection configuration data: CanIsr_7 */
CONST(Os_MpAccessRightsType, OS_CONST) OsCfg_Mp_CanIsr_7 =
{
  /* .AppAccessRights    = */ &OsCfg_Hal_Mp_SystemApplication_OsCore0,
  /* .ThreadAccessRights = */ &OsCfg_Hal_Mp_CanIsr_7
};

/*! Memory protection configuration data: CounterIsr_SystemTimer */
CONST(Os_MpAccessRightsType, OS_CONST) OsCfg_Mp_CounterIsr_SystemTimer =
{
  /* .AppAccessRights    = */ &OsCfg_Hal_Mp_SystemApplication_OsCore0,
  /* .ThreadAccessRights = */ &OsCfg_Hal_Mp_CounterIsr_SystemTimer
};

/*! Memory protection configuration data: CounterIsr_TpCounter_OsCore0 */
CONST(Os_MpAccessRightsType, OS_CONST) OsCfg_Mp_CounterIsr_TpCounter_OsCore0 =
{
  /* .AppAccessRights    = */ &OsCfg_Hal_Mp_SystemApplication_OsCore0,
  /* .ThreadAccessRights = */ &OsCfg_Hal_Mp_CounterIsr_TpCounter_OsCore0
};

/*! Memory protection configuration data: Fr_IrqLine0 */
CONST(Os_MpAccessRightsType, OS_CONST) OsCfg_Mp_Fr_IrqLine0 =
{
  /* .AppAccessRights    = */ &OsCfg_Hal_Mp_SystemApplication_OsCore0,
  /* .ThreadAccessRights = */ &OsCfg_Hal_Mp_Fr_IrqLine0
};

/*! Memory protection configuration data: Fr_IrqTimer0 */
CONST(Os_MpAccessRightsType, OS_CONST) OsCfg_Mp_Fr_IrqTimer0 =
{
  /* .AppAccessRights    = */ &OsCfg_Hal_Mp_SystemApplication_OsCore0,
  /* .ThreadAccessRights = */ &OsCfg_Hal_Mp_Fr_IrqTimer0
};

/*! Memory protection configuration data: Lin_Channel_2_EX_Extended_Error_Interrupt */
CONST(Os_MpAccessRightsType, OS_CONST) OsCfg_Mp_Lin_Channel_2_EX_Extended_Error_Interrupt =
{
  /* .AppAccessRights    = */ &OsCfg_Hal_Mp_SystemApplication_OsCore0,
  /* .ThreadAccessRights = */ &OsCfg_Hal_Mp_Lin_Channel_2_EX_Extended_Error_Interrupt
};

/*! Memory protection configuration data: Lin_Channel_2_RX_Receive_Interrupt */
CONST(Os_MpAccessRightsType, OS_CONST) OsCfg_Mp_Lin_Channel_2_RX_Receive_Interrupt =
{
  /* .AppAccessRights    = */ &OsCfg_Hal_Mp_SystemApplication_OsCore0,
  /* .ThreadAccessRights = */ &OsCfg_Hal_Mp_Lin_Channel_2_RX_Receive_Interrupt
};

/*! Memory protection configuration data: Lin_Channel_2_TX_Transmit_Interrupt */
CONST(Os_MpAccessRightsType, OS_CONST) OsCfg_Mp_Lin_Channel_2_TX_Transmit_Interrupt =
{
  /* .AppAccessRights    = */ &OsCfg_Hal_Mp_SystemApplication_OsCore0,
  /* .ThreadAccessRights = */ &OsCfg_Hal_Mp_Lin_Channel_2_TX_Transmit_Interrupt
};

/*! Memory protection configuration data: Default_BSW_Async_Task */
CONST(Os_MpAccessRightsType, OS_CONST) OsCfg_Mp_Default_BSW_Async_Task =
{
  /* .AppAccessRights    = */ &OsCfg_Hal_Mp_OsApplication_NonTrusted_Core0,
  /* .ThreadAccessRights = */ &OsCfg_Hal_Mp_Default_BSW_Async_Task
};

/*! Memory protection configuration data: Default_BSW_Sync_Task */
CONST(Os_MpAccessRightsType, OS_CONST) OsCfg_Mp_Default_BSW_Sync_Task =
{
  /* .AppAccessRights    = */ &OsCfg_Hal_Mp_OsApplication_Trusted_Core0,
  /* .ThreadAccessRights = */ &OsCfg_Hal_Mp_Default_BSW_Sync_Task
};

/*! Memory protection configuration data: Default_Init_Task */
CONST(Os_MpAccessRightsType, OS_CONST) OsCfg_Mp_Default_Init_Task =
{
  /* .AppAccessRights    = */ &OsCfg_Hal_Mp_OsApplication_NonTrusted_Core0,
  /* .ThreadAccessRights = */ &OsCfg_Hal_Mp_Default_Init_Task
};

/*! Memory protection configuration data: Default_Init_Task_Trusted */
CONST(Os_MpAccessRightsType, OS_CONST) OsCfg_Mp_Default_Init_Task_Trusted =
{
  /* .AppAccessRights    = */ &OsCfg_Hal_Mp_OsApplication_Trusted_Core0,
  /* .ThreadAccessRights = */ &OsCfg_Hal_Mp_Default_Init_Task_Trusted
};

/*! Memory protection configuration data: Default_RTE_Mode_switch_Task */
CONST(Os_MpAccessRightsType, OS_CONST) OsCfg_Mp_Default_RTE_Mode_switch_Task =
{
  /* .AppAccessRights    = */ &OsCfg_Hal_Mp_OsApplication_NonTrusted_Core0,
  /* .ThreadAccessRights = */ &OsCfg_Hal_Mp_Default_RTE_Mode_switch_Task
};

/*! Memory protection configuration data: IdleTask_OsCore0 */
CONST(Os_MpAccessRightsType, OS_CONST) OsCfg_Mp_IdleTask_OsCore0 =
{
  /* .AppAccessRights    = */ &OsCfg_Hal_Mp_SystemApplication_OsCore0,
  /* .ThreadAccessRights = */ &OsCfg_Hal_Mp_IdleTask_OsCore0
};

/*! Memory protection configuration data: StartApplication_Appl_Init_Task */
CONST(Os_MpAccessRightsType, OS_CONST) OsCfg_Mp_StartApplication_Appl_Init_Task =
{
  /* .AppAccessRights    = */ &OsCfg_Hal_Mp_OsApplication_NonTrusted_Core0,
  /* .ThreadAccessRights = */ &OsCfg_Hal_Mp_StartApplication_Appl_Init_Task
};

/*! Memory protection configuration data: StartApplication_Appl_Task */
CONST(Os_MpAccessRightsType, OS_CONST) OsCfg_Mp_StartApplication_Appl_Task =
{
  /* .AppAccessRights    = */ &OsCfg_Hal_Mp_OsApplication_NonTrusted_Core0,
  /* .ThreadAccessRights = */ &OsCfg_Hal_Mp_StartApplication_Appl_Task
};

#define OS_STOP_SEC_CORE0_CONST_UNSPECIFIED
#include "Os_MemMap_OsSections.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */


#define OS_START_SEC_CONST_UNSPECIFIED
#include "Os_MemMap_OsSections.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

/*! Memory protection configuration data: SystemMpu */
CONST(Os_MpSystemConfigType, OS_CONST) OsCfg_Mp_SystemMpu =
{
  /* .HwConfig = */ &OsCfg_Hal_Mp_SystemMpu
};

#define OS_STOP_SEC_CONST_UNSPECIFIED
#include "Os_MemMap_OsSections.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */


/**********************************************************************************************************************
 *  LOCAL FUNCTION PROTOTYPES
 *********************************************************************************************************************/

/**********************************************************************************************************************
 *  LOCAL FUNCTIONS
 *********************************************************************************************************************/

/**********************************************************************************************************************
 *  GLOBAL FUNCTIONS
 *********************************************************************************************************************/


/**********************************************************************************************************************
 *  END OF FILE: Os_MemoryProtection_Lcfg.c
 *********************************************************************************************************************/
