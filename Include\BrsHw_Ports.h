
/**********************************************************************************************************************
  COPYRIGHT
-----------------------------------------------------------------------------------------------------------------------
  \par      copyright
  \verbatim
  Copyright (c) 2020 by Vector Informatik GmbH.                                                  All rights reserved.

                This software is copyright protected and proprietary to Vector Informatik GmbH.
                Vector Informatik GmbH grants to you only those rights as set out in the license conditions.
                All other rights remain with Vector Informatik GmbH.
  \endverbatim
-----------------------------------------------------------------------------------------------------------------------
  FILE DESCRIPTION
-----------------------------------------------------------------------------------------------------------------------
  \file  File:  BrsHw_Ports.h
      Project:  Vector Basic Runtime System
       Module:  BrsHw for platform Infineon Aurix/AurixPlus

  \brief Description:  This header file contains the information for the evalboard specific port settings,
                       supported by this Brs implementation.

  \attention Please note:
    The demo and example programs only show special aspects of the software. With regard to the fact
    that these programs are meant for demonstration purposes only, Vector Informatik liability shall be
    expressly excluded in cases of ordinary negligence, to the extent admissible by law or statute.
**********************************************************************************************************************/

/**********************************************************************************************************************
  AUTHOR IDENTITY
 ----------------------------------------------------------------------------------------------------------------------
  Name                          Initials      Company
  ----------------------------  ------------  -------------------------------------------------------------------------
  Benjamin Walter               visbwa        Vector Informatik GmbH
  Torsten Schmidt               visto         Vector Informatik GmbH
  Sascha Mauser                 vismaa        Vector Informatik GmbH
-----------------------------------------------------------------------------------------------------------------------
  REVISION HISTORY
 ----------------------------------------------------------------------------------------------------------------------
  Version   Date        Author  Description
  --------  ----------  ------  ---------------------------------------------------------------------------------------
  01.00.00  2020-05-28  visto   Initial version based on Template for vBaseEnv 2.0
            2020-05-28  vismaa  Changed FRxB_ERRNA to FRxB_ERRNB, diff against vBaseEnv2.0 template
**********************************************************************************************************************/

#ifndef _BRSHW_PORTS_H_
#define _BRSHW_PORTS_H_

/*******************************************************************************
  Generic PORT definition types
********************************************************************************/
typedef enum
{
  BRSHW_PORT_PORTGROUP_0,
  BRSHW_PORT_PORTGROUP_1,
  BRSHW_PORT_PORTGROUP_2,
  BRSHW_PORT_PORTGROUP_3,
  BRSHW_PORT_PORTGROUP_4,
  BRSHW_PORT_PORTGROUP_5,
  BRSHW_PORT_PORTGROUP_6,
  BRSHW_PORT_PORTGROUP_7,
  BRSHW_PORT_PORTGROUP_8,
  BRSHW_PORT_PORTGROUP_9,
  BRSHW_PORT_PORTGROUP_10,
  BRSHW_PORT_PORTGROUP_11,
  BRSHW_PORT_PORTGROUP_12,
  BRSHW_PORT_PORTGROUP_13,
  BRSHW_PORT_PORTGROUP_14,
  BRSHW_PORT_PORTGROUP_15,
  BRSHW_PORT_PORTGROUP_16,
  BRSHW_PORT_PORTGROUP_17,
  BRSHW_PORT_PORTGROUP_18,
  BRSHW_PORT_PORTGROUP_19,
  BRSHW_PORT_PORTGROUP_20,
  BRSHW_PORT_PORTGROUP_21,
  BRSHW_PORT_PORTGROUP_22,
  BRSHW_PORT_PORTGROUP_23,
  BRSHW_PORT_PORTGROUP_24,
  BRSHW_PORT_PORTGROUP_25,
  BRSHW_PORT_PORTGROUP_26,
  BRSHW_PORT_PORTGROUP_27,
  BRSHW_PORT_PORTGROUP_28,
  BRSHW_PORT_PORTGROUP_29,
  BRSHW_PORT_PORTGROUP_30,
  BRSHW_PORT_PORTGROUP_31,
  BRSHW_PORT_PORTGROUP_32,
  BRSHW_PORT_PORTGROUP_33,
  BRSHW_PORT_PORTGROUP_34,
  BRSHW_PORT_PORTGROUP_35,
  BRSHW_PORT_PORTGROUP_36,
  BRSHW_PORT_PORTGROUP_37,
  BRSHW_PORT_PORTGROUP_38,
  BRSHW_PORT_PORTGROUP_39,
  BRSHW_PORT_PORTGROUP_40,
  BRSHW_PORT_PORTGROUP_41
}brsHw_Port_PortGroupType;

typedef enum
{
  BRSHW_PORT_PORTNUMBER_0,
  BRSHW_PORT_PORTNUMBER_1,
  BRSHW_PORT_PORTNUMBER_2,
  BRSHW_PORT_PORTNUMBER_3,
  BRSHW_PORT_PORTNUMBER_4,
  BRSHW_PORT_PORTNUMBER_5,
  BRSHW_PORT_PORTNUMBER_6,
  BRSHW_PORT_PORTNUMBER_7,
  BRSHW_PORT_PORTNUMBER_8,
  BRSHW_PORT_PORTNUMBER_9,
  BRSHW_PORT_PORTNUMBER_10,
  BRSHW_PORT_PORTNUMBER_11,
  BRSHW_PORT_PORTNUMBER_12,
  BRSHW_PORT_PORTNUMBER_13,
  BRSHW_PORT_PORTNUMBER_14,
  BRSHW_PORT_PORTNUMBER_15,
  BRSHW_PORT_PORTNUMBER_16
}brsHw_Port_PortNumberType;

typedef enum
{
  BRSHW_PORT_ALT_0,
  BRSHW_PORT_ALT_1,
  BRSHW_PORT_ALT_2,
  BRSHW_PORT_ALT_3,
  BRSHW_PORT_ALT_4,
  BRSHW_PORT_ALT_5,
  BRSHW_PORT_ALT_6,
  BRSHW_PORT_ALT_7,
  BRSHW_PORT_ALT_8,
  BRSHW_PORT_ALT_9,
  BRSHW_PORT_ALT_10,
  BRSHW_PORT_ALT_11,
  BRSHW_PORT_ALT_12,
  BRSHW_PORT_ALT_13,
  BRSHW_PORT_ALT_14,
  BRSHW_PORT_ALT_15,
  BRSHW_PORT_ALT_16,
  BRSHW_PORT_ALT_17,
  BRSHW_PORT_ALT_18,
  BRSHW_PORT_ALT_19,
  BRSHW_PORT_ALT_20
}brsHw_Port_AlternativeType;

typedef struct
{
  brsHw_Port_PortGroupType   portGroup;
  brsHw_Port_PortNumberType  portNumber;
  brsHw_Port_AlternativeType portAlternative;
}brsHw_Port_PortType;

#if defined (_BRSHW_C_)
  #define BRSHW_PORT_PIN(name, group, number, alternative) const brsHw_Port_PortType name = {group, number, alternative}
#else
  #define BRSHW_PORT_PIN(name, group, number, alternative) extern const brsHw_Port_PortType name
#endif

typedef enum
{
  PORT_STRONG_DRIVER_SHARP_EDGE,
  PORT_STRONG_DRIVER_MEDIUM_EDGE,
  PORT_MEDIUM_DRIVER,
  PORT_RGMII_DRIVER,
}brsHw_Port_Driver_Setting;

typedef struct
{
  brsHw_Port_Driver_Setting PortDriverSetting;
}brsHw_Port_ConfType;

#if defined (_BRSHW_C_)
  #define BRSHW_PORT_PIN(name, group, number, alternative) const brsHw_Port_PortType name = {group, number, alternative}
#else
  #define BRSHW_PORT_PIN(name, group, number, alternative) extern const brsHw_Port_PortType name
#endif

#define BRSHW_PORT_LOGIC_HIGH (uint8)1
#define BRSHW_PORT_LOGIC_LOW  (uint8)0

/*******************************************************************************
  PIN configuration for alive LED support
********************************************************************************/
#if defined (BRS_ENABLE_SUPPORT_LEDS)
#  if defined (BRS_EVA_BOARD_TRIBOARD_TC2x2)
BRSHW_PORT_PIN(BRSHW_PORT_LED, BRSHW_PORT_PORTGROUP_2, BRSHW_PORT_PORTNUMBER_0, BRSHW_PORT_ALT_0);

#  elif defined (BRS_EVA_BOARD_TRIBOARD_TC2x3)
BRSHW_PORT_PIN(BRSHW_PORT_LED, BRSHW_PORT_PORTGROUP_11, BRSHW_PORT_PORTNUMBER_8, BRSHW_PORT_ALT_0);

#  elif defined (BRS_EVA_BOARD_TRIBOARD_TC2x4) || \
        defined (BRS_EVA_BOARD_TRIBOARD_TC2x5) || \
        defined (BRS_EVA_BOARD_TRIBOARD_TC2x7) || \
        defined (BRS_EVA_BOARD_TRIBOARD_TC2x8) || \
        defined (BRS_EVA_BOARD_TRIBOARD_TC2x9)
BRSHW_PORT_PIN(BRSHW_PORT_LED, BRSHW_PORT_PORTGROUP_33, BRSHW_PORT_PORTNUMBER_6, BRSHW_PORT_ALT_0);

#  elif defined (BRS_EVA_BOARD_TRIBOARD_TC3x7) || \
        defined (BRS_EVA_BOARD_TRIBOARD_TC3x9)
BRSHW_PORT_PIN(BRSHW_PORT_LED, BRSHW_PORT_PORTGROUP_33, BRSHW_PORT_PORTNUMBER_4, BRSHW_PORT_ALT_0);

#  elif defined (BRS_EVA_BOARD_CUSTOMER_HARDWARE)
#error "For EVA_BOARD = CUSTOMER_HARDWARE check if a LED is available and adjust this structure to your needs"*/
BRSHW_PORT_PIN(BRSHW_PORT_LED, BRSHW_PORT_PORTGROUP_0, BRSHW_PORT_PORTNUMBER_0, BRSHW_PORT_ALT_0);

# else
  #error "Your chosen EvaBoard is not yet supported for LED support. Feel free to add your EvaBoard on top, or disable BRS LED support."
# endif /*BRS_EVA_BOARD_x*/
#endif /*BRS_ENABLE_SUPPORT_LEDS*/

/*******************************************************************************
  PIN configuration for toggle WD pin support
********************************************************************************/
#if defined (BRS_ENABLE_SUPPORT_TOGGLE_WD_PIN)
# if defined (BRS_ENABLE_FBL_SUPPORT)
  #define BRS_START_SEC_RAM_CONST
  #include "Brs_MemMap.h"
# endif

  #error "Please configure here the needed WD-toggle pin and uncomment this error, or disable BRS_ENABLE_SUPPORT_TOGGLE_WD_PIN"*/

BRSHW_PORT_PIN(BRSHW_PORT_TOGGLE_WD, BRSHW_PORT_PORTGROUP_x, BRSHW_PORT_PORTNUMBER_x, BRSHW_PORT_ALT_x);

# if defined (BRS_ENABLE_FBL_SUPPORT)
  #define BRS_STOP_SEC_RAM_CONST
  #include "Brs_MemMap.h"
# endif
#endif /*BRS_ENABLE_SUPPORT_WD_PIN*/

/*******************************************************************************
  PIN configuration for toggle CUSTOM pin support
********************************************************************************/
#if defined (BRS_ENABLE_SUPPORT_TOGGLE_CUSTOM_PIN)
# if defined (BRS_ENABLE_FBL_SUPPORT)
  #define BRS_START_SEC_RAM_CONST
  #include "Brs_MemMap.h"
# endif

  #error "Please configure here the needed custom toggle pin and uncomment this error, or disable BRS_ENABLE_SUPPORT_TOGGLE_CUSTOM_PIN"*/

BRSHW_PORT_PIN(BRSHW_PORT_TOGGLE_CUSTOM, BRSHW_PORT_PORTGROUP_x, BRSHW_PORT_PORTNUMBER_x, BRSHW_PORT_ALT_x);

# if defined (BRS_ENABLE_FBL_SUPPORT)
  #define BRS_STOP_SEC_RAM_CONST
  #include "Brs_MemMap.h"
# endif
#endif /*BRS_ENABLE_SUPPORT_CUSTOM_PIN*/

/*******************************************************************************
  ------------------------------------------------------------
  COMMUNICATION DRIVER SPECIFIC PORT SETTINGS
  ------------------------------------------------------------
********************************************************************************/
/*******************************************************************************
  GENERAL COMMUNICATION DRIVER SPECIFIC SETTINGS
********************************************************************************/
#if defined (_BRSHW_C_)
const brsHw_Port_ConfType BRSHW_PORT_CONF_CAN = {PORT_STRONG_DRIVER_MEDIUM_EDGE};

const brsHw_Port_ConfType BRSHW_PORT_CONF_ETHERNET = {PORT_RGMII_DRIVER};

const brsHw_Port_ConfType BRSHW_PORT_CONF_SHARPEDGE = {PORT_STRONG_DRIVER_SHARP_EDGE};
#endif /*_BRSHW_C_*/

/*******************************************************************************
  CAN driver
********************************************************************************/
#if defined (BRS_ENABLE_CAN_SUPPORT)
# if defined (BRS_ENABLE_CAN_CHANNEL_0)
#  if defined (BRS_EVA_BOARD_TRIBOARD_TC2x4) || \
      defined (BRS_EVA_BOARD_TRIBOARD_TC2x5) || \
      defined (BRS_EVA_BOARD_TRIBOARD_TC2x7) || \
      defined (BRS_EVA_BOARD_TRIBOARD_TC3x7) || \
      defined (BRS_EVA_BOARD_TRIBOARD_TC3x9)
BRSHW_PORT_PIN(BRSHW_PORT_CAN0_TX, BRSHW_PORT_PORTGROUP_20, BRSHW_PORT_PORTNUMBER_8, BRSHW_PORT_ALT_5);
BRSHW_PORT_PIN(BRSHW_PORT_CAN0_RX, BRSHW_PORT_PORTGROUP_20, BRSHW_PORT_PORTNUMBER_7, BRSHW_PORT_ALT_0);

#  elif defined (BRS_EVA_BOARD_TRIBOARD_TC2x2) || \
        defined (BRS_EVA_BOARD_TRIBOARD_TC2x3)
BRSHW_PORT_PIN(BRSHW_PORT_CAN0_TX, BRSHW_PORT_PORTGROUP_33, BRSHW_PORT_PORTNUMBER_8, BRSHW_PORT_ALT_5);
BRSHW_PORT_PIN(BRSHW_PORT_CAN0_RX, BRSHW_PORT_PORTGROUP_33, BRSHW_PORT_PORTNUMBER_7, BRSHW_PORT_ALT_0);

#  elif defined (BRS_EVA_BOARD_TRIBOARD_TC2x8) || \
        defined (BRS_EVA_BOARD_TRIBOARD_TC2x9)
BRSHW_PORT_PIN(BRSHW_PORT_CAN0_TX, BRSHW_PORT_PORTGROUP_34, BRSHW_PORT_PORTNUMBER_1, BRSHW_PORT_ALT_4);
BRSHW_PORT_PIN(BRSHW_PORT_CAN0_RX, BRSHW_PORT_PORTGROUP_34, BRSHW_PORT_PORTNUMBER_2, BRSHW_PORT_ALT_0);

#  elif defined (BRS_EVA_BOARD_CUSTOMER_HARDWARE)
#error "For EVA_BOARD = CUSTOMER_HARDWARE check which pins are used for CAN0 and adjust this structure to your needs"*/
BRSHW_PORT_PIN(BRSHW_PORT_CAN0_TX, BRSHW_PORT_PORTGROUP_0, BRSHW_PORT_PORTNUMBER_0, BRSHW_PORT_ALT_0);
BRSHW_PORT_PIN(BRSHW_PORT_CAN0_RX, BRSHW_PORT_PORTGROUP_0, BRSHW_PORT_PORTNUMBER_0, BRSHW_PORT_ALT_0);

#  else
  #error "Your EvalBoard is not yet supported CAN channel 0"
#  endif /*BRS_EVA_BOARD_x*/
# endif /*BRS_ENABLE_CAN_CHANNEL_0*/

# if defined (BRS_ENABLE_CAN_CHANNEL_1)
#  if defined (BRS_EVA_BOARD_TRIBOARD_TC2x2) || \
      defined (BRS_EVA_BOARD_TRIBOARD_TC2x3) || \
      defined (BRS_EVA_BOARD_TRIBOARD_TC2x4) || \
      defined (BRS_EVA_BOARD_TRIBOARD_TC2x5) || \
      defined (BRS_EVA_BOARD_TRIBOARD_TC2x7) || \
      defined (BRS_EVA_BOARD_TRIBOARD_TC2x8) || \
      defined (BRS_EVA_BOARD_TRIBOARD_TC2x9)
BRSHW_PORT_PIN(BRSHW_PORT_CAN1_TX, BRSHW_PORT_PORTGROUP_14, BRSHW_PORT_PORTNUMBER_0, BRSHW_PORT_ALT_5);
BRSHW_PORT_PIN(BRSHW_PORT_CAN1_RX, BRSHW_PORT_PORTGROUP_14, BRSHW_PORT_PORTNUMBER_1, BRSHW_PORT_ALT_0);

#  elif defined (BRS_EVA_BOARD_TRIBOARD_TC3x7) || \
        defined (BRS_EVA_BOARD_TRIBOARD_TC3x9)
BRSHW_PORT_PIN(BRSHW_PORT_CAN1_TX, BRSHW_PORT_PORTGROUP_23, BRSHW_PORT_PORTNUMBER_1, BRSHW_PORT_ALT_5);
  /*P23.1: Extension Board Pin X7.1*/
BRSHW_PORT_PIN(BRSHW_PORT_CAN1_RX, BRSHW_PORT_PORTGROUP_23, BRSHW_PORT_PORTNUMBER_0, BRSHW_PORT_ALT_0);
  /*P23.0: Extension Board Pin X4.15 (x704!)*/

#  elif defined (BRS_EVA_BOARD_CUSTOMER_HARDWARE)
#error "For EVA_BOARD = CUSTOMER_HARDWARE check which pins are used for CAN1 and adjust this structure to your needs"*/
BRSHW_PORT_PIN(BRSHW_PORT_CAN1_TX, BRSHW_PORT_PORTGROUP_0, BRSHW_PORT_PORTNUMBER_0, BRSHW_PORT_ALT_0);
BRSHW_PORT_PIN(BRSHW_PORT_CAN1_RX, BRSHW_PORT_PORTGROUP_0, BRSHW_PORT_PORTNUMBER_0, BRSHW_PORT_ALT_0);

#  else
  #error "PortPins for your EvalBoard are not yet supported with CAN channel 1"
#  endif /*BRS_EVA_BOARD_x*/
# endif /*BRS_ENABLE_CAN_CHANNEL_1*/

# if defined (BRS_ENABLE_CAN_CHANNEL_2)
#  if defined (BRS_EVA_BOARD_TRIBOARD_TC2x2) || \
      defined (BRS_EVA_BOARD_TRIBOARD_TC2x3) || \
      defined (BRS_EVA_BOARD_TRIBOARD_TC2x4) || \
      defined (BRS_EVA_BOARD_TRIBOARD_TC2x5) || \
      defined (BRS_EVA_BOARD_TRIBOARD_TC2x7) || \
      defined (BRS_EVA_BOARD_TRIBOARD_TC2x8)
BRSHW_PORT_PIN(BRSHW_PORT_CAN2_TX, BRSHW_PORT_PORTGROUP_15, BRSHW_PORT_PORTNUMBER_0, BRSHW_PORT_ALT_5);
BRSHW_PORT_PIN(BRSHW_PORT_CAN2_RX, BRSHW_PORT_PORTGROUP_15, BRSHW_PORT_PORTNUMBER_1, BRSHW_PORT_ALT_0);

#  elif defined (BRS_EVA_BOARD_TRIBOARD_TC3x7) || \
        defined (BRS_EVA_BOARD_TRIBOARD_TC3x9)
BRSHW_PORT_PIN(BRSHW_PORT_CAN2_TX, BRSHW_PORT_PORTGROUP_15, BRSHW_PORT_PORTNUMBER_2, BRSHW_PORT_ALT_5);
  /*P15.2: Extension Board Pin X4.3*/
BRSHW_PORT_PIN(BRSHW_PORT_CAN2_RX, BRSHW_PORT_PORTGROUP_15, BRSHW_PORT_PORTNUMBER_3, BRSHW_PORT_ALT_0);
  /*P15.3: Extension Board Pin X4.5*/

#  elif defined (BRS_EVA_BOARD_CUSTOMER_HARDWARE)
#error "For EVA_BOARD = CUSTOMER_HARDWARE check which pins are used for CAN2 and adjust this structure to your needs"*/
BRSHW_PORT_PIN(BRSHW_PORT_CAN2_TX, BRSHW_PORT_PORTGROUP_0, BRSHW_PORT_PORTNUMBER_0, BRSHW_PORT_ALT_0);
BRSHW_PORT_PIN(BRSHW_PORT_CAN2_RX, BRSHW_PORT_PORTGROUP_0, BRSHW_PORT_PORTNUMBER_0, BRSHW_PORT_ALT_0);

#  else
  #error "PortPins for your EvalBoard are not yet supported with CAN channel 2"
#  endif /*BRS_EVA_BOARD_x*/
# endif /*BRS_ENABLE_CAN_CHANNEL_2*/

# if defined (BRS_ENABLE_CAN_CHANNEL_3)
#  if defined (BRS_EVA_BOARD_TRIBOARD_TC2x4) || \
      defined (BRS_EVA_BOARD_TRIBOARD_TC2x5) || \
      defined (BRS_EVA_BOARD_TRIBOARD_TC2x7) || \
      defined (BRS_EVA_BOARD_TRIBOARD_TC2x8)
BRSHW_PORT_PIN(BRSHW_PORT_CAN3_TX, BRSHW_PORT_PORTGROUP_20, BRSHW_PORT_PORTNUMBER_10, BRSHW_PORT_ALT_5);
BRSHW_PORT_PIN(BRSHW_PORT_CAN3_RX, BRSHW_PORT_PORTGROUP_20, BRSHW_PORT_PORTNUMBER_9, BRSHW_PORT_ALT_0);

#  elif defined (BRS_EVA_BOARD_TRIBOARD_TC3x7) || \
        defined (BRS_EVA_BOARD_TRIBOARD_TC3x9)
BRSHW_PORT_PIN(BRSHW_PORT_CAN3_TX, BRSHW_PORT_PORTGROUP_10, BRSHW_PORT_PORTNUMBER_3, BRSHW_PORT_ALT_6);
  /*P10.3: Extension Board Pin X1.3*/
BRSHW_PORT_PIN(BRSHW_PORT_CAN3_RX, BRSHW_PORT_PORTGROUP_10, BRSHW_PORT_PORTNUMBER_2, BRSHW_PORT_ALT_0);
  /*P10.2: Extension Board Pin X9.1*/

#  elif defined (BRS_EVA_BOARD_CUSTOMER_HARDWARE)
#error "For EVA_BOARD = CUSTOMER_HARDWARE check which pins are used for CAN3 and adjust this structure to your needs"*/
BRSHW_PORT_PIN(BRSHW_PORT_CAN3_TX, BRSHW_PORT_PORTGROUP_0, BRSHW_PORT_PORTNUMBER_0, BRSHW_PORT_ALT_0);
BRSHW_PORT_PIN(BRSHW_PORT_CAN3_RX, BRSHW_PORT_PORTGROUP_0, BRSHW_PORT_PORTNUMBER_0, BRSHW_PORT_ALT_0);

#  else
  #error "PortPins for your EvalBoard are not yet supported with CAN channel 3"
#  endif /*BRS_EVA_BOARD_x*/
# endif /*BRS_ENABLE_CAN_CHANNEL_3*/

# if defined (BRS_ENABLE_CAN_CHANNEL_4)
#  if defined (BRS_EVA_BOARD_TRIBOARD_TC3x7) || \
      defined (BRS_EVA_BOARD_TRIBOARD_TC3x9)
BRSHW_PORT_PIN(BRSHW_PORT_CAN4_TX, BRSHW_PORT_PORTGROUP_20, BRSHW_PORT_PORTNUMBER_10, BRSHW_PORT_ALT_5);
  /*P20.10: Extension Board Pin X6.7*/
BRSHW_PORT_PIN(BRSHW_PORT_CAN4_RX, BRSHW_PORT_PORTGROUP_20, BRSHW_PORT_PORTNUMBER_9, BRSHW_PORT_ALT_0);
  /*P20.9: Extension Board Pin X8.7*/

#  else
  #error "PortPins for your EvalBoard are not yet supported with CAN channel 4"
#  endif /*BRS_EVA_BOARD_x*/
# endif /*BRS_ENABLE_CAN_CHANNEL_4*/

# if defined (BRS_ENABLE_CAN_CHANNEL_5)
#  if defined (BRS_EVA_BOARD_TRIBOARD_TC3x7) || \
      defined (BRS_EVA_BOARD_TRIBOARD_TC3x9)
BRSHW_PORT_PIN(BRSHW_PORT_CAN5_TX, BRSHW_PORT_PORTGROUP_23, BRSHW_PORT_PORTNUMBER_2, BRSHW_PORT_ALT_5);
  /*P23.2: Extension Board Pin X8.1 (x704!)*/
BRSHW_PORT_PIN(BRSHW_PORT_CAN5_RX, BRSHW_PORT_PORTGROUP_23, BRSHW_PORT_PORTNUMBER_3, BRSHW_PORT_ALT_0);
  /*P23.3: Extension Board Pin X7.5*/

#  else
  #error "PortPins for your EvalBoard are not yet supported with CAN channel 5"
#  endif /*BRS_EVA_BOARD_x*/
# endif /*BRS_ENABLE_CAN_CHANNEL_5*/

# if defined (BRS_ENABLE_CAN_CHANNEL_6)
#  if defined (BRS_EVA_BOARD_TRIBOARD_TC3x7) || \
      defined (BRS_EVA_BOARD_TRIBOARD_TC3x9)
BRSHW_PORT_PIN(BRSHW_PORT_CAN6_TX, BRSHW_PORT_PORTGROUP_10, BRSHW_PORT_PORTNUMBER_7, BRSHW_PORT_ALT_5);
  /*P10.7: Extension Board Pin X7.3*/
BRSHW_PORT_PIN(BRSHW_PORT_CAN6_RX, BRSHW_PORT_PORTGROUP_10, BRSHW_PORT_PORTNUMBER_8, BRSHW_PORT_ALT_0);
  /*P10.8: Extension Board Pin X1.3*/

#  else
  #error "PortPins for your EvalBoard are not yet supported with CAN channel 6"
#  endif /*BRS_EVA_BOARD_x*/
# endif /*BRS_ENABLE_CAN_CHANNEL_6*/

# if defined (BRS_ENABLE_CAN_CHANNEL_7)
  #error "Port config for these CAN channels not yet implemented!"
# endif
#endif /*BRS_ENABLE_CAN_SUPPORT*/

/*******************************************************************************
  LIN driver
********************************************************************************/
#if defined (BRS_ENABLE_LIN_SUPPORT)
# if defined (BRS_ENABLE_LIN_CHANNEL_0)
#  if defined (BRS_EVA_BOARD_TRIBOARD_TC2x2) || \
      defined (BRS_EVA_BOARD_TRIBOARD_TC2x3) || \
      defined (BRS_EVA_BOARD_TRIBOARD_TC2x4) || \
      defined (BRS_EVA_BOARD_TRIBOARD_TC2x5) || \
      defined (BRS_EVA_BOARD_TRIBOARD_TC2x7)
BRSHW_PORT_PIN(BRSHW_PORT_LIN0_TX, BRSHW_PORT_PORTGROUP_15, BRSHW_PORT_PORTNUMBER_2, BRSHW_PORT_ALT_2);
BRSHW_PORT_PIN(BRSHW_PORT_LIN0_RX, BRSHW_PORT_PORTGROUP_15, BRSHW_PORT_PORTNUMBER_3, BRSHW_PORT_ALT_0);

#  elif defined (BRS_EVA_BOARD_TRIBOARD_TC2x8)
BRSHW_PORT_PIN(BRSHW_PORT_LIN0_TX, BRSHW_PORT_PORTGROUP_1, BRSHW_PORT_PORTNUMBER_13, BRSHW_PORT_ALT_4);
BRSHW_PORT_PIN(BRSHW_PORT_LIN0_RX, BRSHW_PORT_PORTGROUP_1, BRSHW_PORT_PORTNUMBER_8, BRSHW_PORT_ALT_0);

#  elif defined (BRS_EVA_BOARD_TRIBOARD_TC3x7) || \
        defined (BRS_EVA_BOARD_TRIBOARD_TC3x9)
BRSHW_PORT_PIN(BRSHW_PORT_LIN0_TX, BRSHW_PORT_PORTGROUP_14, BRSHW_PORT_PORTNUMBER_0, BRSHW_PORT_ALT_2);
BRSHW_PORT_PIN(BRSHW_PORT_LIN0_RX, BRSHW_PORT_PORTGROUP_14, BRSHW_PORT_PORTNUMBER_1, BRSHW_PORT_ALT_0);

#  else
  #error "PortPins for your EvalBoard are not yet supported with LIN channel 0"
#  endif /*BRS_EVA_BOARD_x*/
# endif /*BRS_ENABLE_LIN_CHANNEL_0*/

# if defined (BRS_ENABLE_LIN_CHANNEL_1)
#  if defined (BRS_EVA_BOARD_TRIBOARD_TC2x4) || \
      defined (BRS_EVA_BOARD_TRIBOARD_TC2x5) || \
      defined (BRS_EVA_BOARD_TRIBOARD_TC2x7) || \
      defined (BRS_EVA_BOARD_TRIBOARD_TC2x8) || \
      defined (BRS_EVA_BOARD_TRIBOARD_TC2x9)
BRSHW_PORT_PIN(BRSHW_PORT_LIN1_TX, BRSHW_PORT_PORTGROUP_15, BRSHW_PORT_PORTNUMBER_4, BRSHW_PORT_ALT_2);
BRSHW_PORT_PIN(BRSHW_PORT_LIN1_RX, BRSHW_PORT_PORTGROUP_15, BRSHW_PORT_PORTNUMBER_5, BRSHW_PORT_ALT_0);

#  elif defined (BRS_EVA_BOARD_TRIBOARD_TC2x2)
BRSHW_PORT_PIN(BRSHW_PORT_LIN1_TX, BRSHW_PORT_PORTGROUP_15, BRSHW_PORT_PORTNUMBER_5, BRSHW_PORT_ALT_2);
BRSHW_PORT_PIN(BRSHW_PORT_LIN1_RX, BRSHW_PORT_PORTGROUP_20, BRSHW_PORT_PORTNUMBER_9, BRSHW_PORT_ALT_0);

#  elif defined (BRS_EVA_BOARD_TRIBOARD_TC2x3)
BRSHW_PORT_PIN(BRSHW_PORT_LIN1_TX, BRSHW_PORT_PORTGROUP_20, BRSHW_PORT_PORTNUMBER_10, BRSHW_PORT_ALT_2);
BRSHW_PORT_PIN(BRSHW_PORT_LIN1_RX, BRSHW_PORT_PORTGROUP_20, BRSHW_PORT_PORTNUMBER_9, BRSHW_PORT_ALT_0);

#  elif defined (BRS_EVA_BOARD_TRIBOARD_TC3x7) || \
        defined (BRS_EVA_BOARD_TRIBOARD_TC3x9)
BRSHW_PORT_PIN(BRSHW_PORT_LIN1_TX, BRSHW_PORT_PORTGROUP_15, BRSHW_PORT_PORTNUMBER_0, BRSHW_PORT_ALT_2);
  /*P15.0: Extension Board Pin X1.15*/
BRSHW_PORT_PIN(BRSHW_PORT_LIN1_RX, BRSHW_PORT_PORTGROUP_15, BRSHW_PORT_PORTNUMBER_1, BRSHW_PORT_ALT_0);
  /*P15.1: Extension Board Pin X2.1*/

#  else
  #error "PortPins for your EvalBoard are not yet supported with LIN channel 1"
#  endif /*BRS_EVA_BOARD_x*/
# endif /*BRS_ENABLE_LIN_CHANNEL_1*/

# if defined (BRS_ENABLE_LIN_CHANNEL_2)
#  if defined (BRS_EVA_BOARD_TRIBOARD_TC3x7) || \
      defined (BRS_EVA_BOARD_TRIBOARD_TC3x9)
BRSHW_PORT_PIN(BRSHW_PORT_LIN2_TX, BRSHW_PORT_PORTGROUP_10, BRSHW_PORT_PORTNUMBER_5, BRSHW_PORT_ALT_2);
  /*P10.5: Extension Board Pin X9.5*/
BRSHW_PORT_PIN(BRSHW_PORT_LIN2_RX, BRSHW_PORT_PORTGROUP_2, BRSHW_PORT_PORTNUMBER_1, BRSHW_PORT_ALT_0);
  /*P2.1: Extension Board Pin X1.7 (x704!)*/

#  else
  #error "PortPins for your EvalBoard are not yet supported with LIN channel 2"
#  endif /*BRS_EVA_BOARD_x*/
# endif /*BRS_ENABLE_LIN_CHANNEL_2*/

# if defined (BRS_ENABLE_LIN_CHANNEL_3)
#  if defined (BRS_EVA_BOARD_TRIBOARD_TC3x7) || \
        defined (BRS_EVA_BOARD_TRIBOARD_TC3x9)
BRSHW_PORT_PIN(BRSHW_PORT_LIN3_TX, BRSHW_PORT_PORTGROUP_0, BRSHW_PORT_PORTNUMBER_0, BRSHW_PORT_ALT_3);
  /*P0.0: Extension Board Pin X4.15*/
BRSHW_PORT_PIN(BRSHW_PORT_LIN3_RX, BRSHW_PORT_PORTGROUP_0, BRSHW_PORT_PORTNUMBER_1, BRSHW_PORT_ALT_0);
  /*P0.1: Extension Board Pin X4.13*/

#  else
  #error "Port config for these LIN channels not yet implemented!"
#  endif /*BRS_EVA_BOARD_x*/
# endif /*BRS_ENABLE_LIN_CHANNEL_3*/

# if defined (BRS_ENABLE_LIN_CHANNEL_4) || \
     defined (BRS_ENABLE_LIN_CHANNEL_5) || \
     defined (BRS_ENABLE_LIN_CHANNEL_6)
  #error "Port config for these LIN channels not yet implemented!"
# endif
#endif /*BRS_ENABLE_LIN_SUPPORT*/

/*******************************************************************************
  FLEXRAY driver
********************************************************************************/
#if defined (BRS_ENABLE_FLEXRAY_SUPPORT)
# if defined (BRS_EVA_BOARD_TRIBOARD_TC2x3)
  /* ERAY-A */
BRSHW_PORT_PIN(BRSHW_PORT_FR0A_TX, BRSHW_PORT_PORTGROUP_2, BRSHW_PORT_PORTNUMBER_0, BRSHW_PORT_ALT_6);
BRSHW_PORT_PIN(BRSHW_PORT_FR0A_RX, BRSHW_PORT_PORTGROUP_2, BRSHW_PORT_PORTNUMBER_1, BRSHW_PORT_ALT_0);
BRSHW_PORT_PIN(BRSHW_PORT_FR0A_TXEN, BRSHW_PORT_PORTGROUP_2, BRSHW_PORT_PORTNUMBER_4, BRSHW_PORT_ALT_6);
BRSHW_PORT_PIN(BRSHW_PORT_FR0A_ERRNA, BRSHW_PORT_PORTGROUP_15, BRSHW_PORT_PORTNUMBER_5, BRSHW_PORT_ALT_7);
#define _BRSHW_PORT_FR0A_TRCV_STB
BRSHW_PORT_PIN(BRSHW_PORT_FR0A_TRCV_STB, BRSHW_PORT_PORTGROUP_11, BRSHW_PORT_PORTNUMBER_3, BRSHW_PORT_ALT_0);/* pull up */
#define _BRSHW_PORT_FR0A_TRCV_EN
BRSHW_PORT_PIN(BRSHW_PORT_FR0A_TRCV_EN, BRSHW_PORT_PORTGROUP_11, BRSHW_PORT_PORTNUMBER_2, BRSHW_PORT_ALT_0);/* pull up */

  /* ERAY-B */
BRSHW_PORT_PIN(BRSHW_PORT_FR0B_TX, BRSHW_PORT_PORTGROUP_2, BRSHW_PORT_PORTNUMBER_2, BRSHW_PORT_ALT_6);
BRSHW_PORT_PIN(BRSHW_PORT_FR0B_RX, BRSHW_PORT_PORTGROUP_2, BRSHW_PORT_PORTNUMBER_3, BRSHW_PORT_ALT_0);
BRSHW_PORT_PIN(BRSHW_PORT_FR0B_TXEN, BRSHW_PORT_PORTGROUP_2, BRSHW_PORT_PORTNUMBER_5, BRSHW_PORT_ALT_6);
BRSHW_PORT_PIN(BRSHW_PORT_FR0B_ERRNB, BRSHW_PORT_PORTGROUP_11, BRSHW_PORT_PORTNUMBER_6, BRSHW_PORT_ALT_7);
#define _BRSHW_PORT_FR0B_TRCV_STB
BRSHW_PORT_PIN(BRSHW_PORT_FR0B_TRCV_STB, BRSHW_PORT_PORTGROUP_10, BRSHW_PORT_PORTNUMBER_6, BRSHW_PORT_ALT_0);
#define _BRSHW_PORT_FR0B_TRCV_EN
BRSHW_PORT_PIN(BRSHW_PORT_FR0B_TRCV_EN, BRSHW_PORT_PORTGROUP_10, BRSHW_PORT_PORTNUMBER_5, BRSHW_PORT_ALT_0);

# elif defined (BRS_EVA_BOARD_TRIBOARD_TC2x4)
  /* ERAY-A */
BRSHW_PORT_PIN(BRSHW_PORT_FR0A_TX, BRSHW_PORT_PORTGROUP_2, BRSHW_PORT_PORTNUMBER_0, BRSHW_PORT_ALT_6);
BRSHW_PORT_PIN(BRSHW_PORT_FR0A_RX, BRSHW_PORT_PORTGROUP_2, BRSHW_PORT_PORTNUMBER_1, BRSHW_PORT_ALT_0);
BRSHW_PORT_PIN(BRSHW_PORT_FR0A_TXEN, BRSHW_PORT_PORTGROUP_2, BRSHW_PORT_PORTNUMBER_4, BRSHW_PORT_ALT_6);
BRSHW_PORT_PIN(BRSHW_PORT_FR0A_ERRNA, BRSHW_PORT_PORTGROUP_0, BRSHW_PORT_PORTNUMBER_3, BRSHW_PORT_ALT_7);
#define _BRSHW_PORT_FR0A_TRCV_STB
BRSHW_PORT_PIN(BRSHW_PORT_FR0A_TRCV_STB, BRSHW_PORT_PORTGROUP_15, BRSHW_PORT_PORTNUMBER_7, BRSHW_PORT_ALT_0);/*pull-up*/
#define _BRSHW_PORT_FR0A_TRCV_EN
BRSHW_PORT_PIN(BRSHW_PORT_FR0A_TRCV_EN, BRSHW_PORT_PORTGROUP_15, BRSHW_PORT_PORTNUMBER_6, BRSHW_PORT_ALT_0);/*pull-up*/

  /* ERAY-B */
BRSHW_PORT_PIN(BRSHW_PORT_FR0B_TX, BRSHW_PORT_PORTGROUP_2, BRSHW_PORT_PORTNUMBER_2, BRSHW_PORT_ALT_6);
BRSHW_PORT_PIN(BRSHW_PORT_FR0B_RX, BRSHW_PORT_PORTGROUP_2, BRSHW_PORT_PORTNUMBER_3, BRSHW_PORT_ALT_0);
BRSHW_PORT_PIN(BRSHW_PORT_FR0B_TXEN, BRSHW_PORT_PORTGROUP_2, BRSHW_PORT_PORTNUMBER_5, BRSHW_PORT_ALT_6);
BRSHW_PORT_PIN(BRSHW_PORT_FR0B_ERRNB, BRSHW_PORT_PORTGROUP_15, BRSHW_PORT_PORTNUMBER_8, BRSHW_PORT_ALT_7);
#define _BRSHW_PORT_FR0B_TRCV_STB
BRSHW_PORT_PIN(BRSHW_PORT_FR0B_TRCV_STB, BRSHW_PORT_PORTGROUP_10, BRSHW_PORT_PORTNUMBER_6, BRSHW_PORT_ALT_0);/*pull-up*/
#define _BRSHW_PORT_FR0B_TRCV_EN
BRSHW_PORT_PIN(BRSHW_PORT_FR0B_TRCV_EN, BRSHW_PORT_PORTGROUP_10, BRSHW_PORT_PORTNUMBER_5, BRSHW_PORT_ALT_0);/*pull-up*/

# elif defined (BRS_EVA_BOARD_TRIBOARD_TC2x5) || \
       defined (BRS_EVA_BOARD_TRIBOARD_TC2x7)
  /* ERAY-A */
BRSHW_PORT_PIN(BRSHW_PORT_FR0A_TX, BRSHW_PORT_PORTGROUP_2, BRSHW_PORT_PORTNUMBER_0, BRSHW_PORT_ALT_6);
BRSHW_PORT_PIN(BRSHW_PORT_FR0A_RX, BRSHW_PORT_PORTGROUP_2, BRSHW_PORT_PORTNUMBER_1, BRSHW_PORT_ALT_0);
BRSHW_PORT_PIN(BRSHW_PORT_FR0A_TXEN, BRSHW_PORT_PORTGROUP_2, BRSHW_PORT_PORTNUMBER_4, BRSHW_PORT_ALT_6);
BRSHW_PORT_PIN(BRSHW_PORT_FR0A_ERRNA, BRSHW_PORT_PORTGROUP_10, BRSHW_PORT_PORTNUMBER_8, BRSHW_PORT_ALT_7);
#define _BRSHW_PORT_FR0A_TRCV_STB
BRSHW_PORT_PIN(BRSHW_PORT_FR0A_TRCV_STB, BRSHW_PORT_PORTGROUP_15, BRSHW_PORT_PORTNUMBER_7, BRSHW_PORT_ALT_0);/*pull-up*/
#define _BRSHW_PORT_FR0A_TRCV_EN
BRSHW_PORT_PIN(BRSHW_PORT_FR0A_TRCV_EN, BRSHW_PORT_PORTGROUP_15, BRSHW_PORT_PORTNUMBER_6, BRSHW_PORT_ALT_0);/*pull-up*/

  /* ERAY-B */
BRSHW_PORT_PIN(BRSHW_PORT_FR0B_TX, BRSHW_PORT_PORTGROUP_2, BRSHW_PORT_PORTNUMBER_2, BRSHW_PORT_ALT_6);
BRSHW_PORT_PIN(BRSHW_PORT_FR0B_RX, BRSHW_PORT_PORTGROUP_2, BRSHW_PORT_PORTNUMBER_3, BRSHW_PORT_ALT_0);
BRSHW_PORT_PIN(BRSHW_PORT_FR0B_TXEN, BRSHW_PORT_PORTGROUP_2, BRSHW_PORT_PORTNUMBER_5, BRSHW_PORT_ALT_6);
BRSHW_PORT_PIN(BRSHW_PORT_FR0B_ERRNB, BRSHW_PORT_PORTGROUP_15, BRSHW_PORT_PORTNUMBER_8, BRSHW_PORT_ALT_7);
#define _BRSHW_PORT_FR0B_TRCV_STB
BRSHW_PORT_PIN(BRSHW_PORT_FR0B_TRCV_STB, BRSHW_PORT_PORTGROUP_10, BRSHW_PORT_PORTNUMBER_6, BRSHW_PORT_ALT_0);/*pull-up*/
#define _BRSHW_PORT_FR0B_TRCV_EN
BRSHW_PORT_PIN(BRSHW_PORT_FR0B_TRCV_EN, BRSHW_PORT_PORTGROUP_10, BRSHW_PORT_PORTNUMBER_5, BRSHW_PORT_ALT_0);/*pull-up*/

# elif defined (BRS_EVA_BOARD_TRIBOARD_TC2x8) || \
       defined (BRS_EVA_BOARD_TRIBOARD_TC2x9)
  /* ERAY-A */
BRSHW_PORT_PIN(BRSHW_PORT_FR0A_TX, BRSHW_PORT_PORTGROUP_14, BRSHW_PORT_PORTNUMBER_10, BRSHW_PORT_ALT_6);
BRSHW_PORT_PIN(BRSHW_PORT_FR0A_RX, BRSHW_PORT_PORTGROUP_14, BRSHW_PORT_PORTNUMBER_8, BRSHW_PORT_ALT_0);
BRSHW_PORT_PIN(BRSHW_PORT_FR0A_TXEN, BRSHW_PORT_PORTGROUP_14, BRSHW_PORT_PORTNUMBER_9, BRSHW_PORT_ALT_6);
BRSHW_PORT_PIN(BRSHW_PORT_FR0A_ERRNA, BRSHW_PORT_PORTGROUP_10, BRSHW_PORT_PORTNUMBER_8, BRSHW_PORT_ALT_7);
#define _BRSHW_PORT_FR0A_TRCV_STB
BRSHW_PORT_PIN(BRSHW_PORT_FR0A_TRCV_STB, BRSHW_PORT_PORTGROUP_15, BRSHW_PORT_PORTNUMBER_7, BRSHW_PORT_ALT_0);/*pull-up*/
#define _BRSHW_PORT_FR0A_TRCV_EN
BRSHW_PORT_PIN(BRSHW_PORT_FR0A_TRCV_EN, BRSHW_PORT_PORTGROUP_15, BRSHW_PORT_PORTNUMBER_6, BRSHW_PORT_ALT_0);/*pull-up*/

  /* ERAY-B */
BRSHW_PORT_PIN(BRSHW_PORT_FR0B_TX, BRSHW_PORT_PORTGROUP_14, BRSHW_PORT_PORTNUMBER_5, BRSHW_PORT_ALT_6);
BRSHW_PORT_PIN(BRSHW_PORT_FR0B_RX, BRSHW_PORT_PORTGROUP_14, BRSHW_PORT_PORTNUMBER_7, BRSHW_PORT_ALT_0);
BRSHW_PORT_PIN(BRSHW_PORT_FR0B_TXEN, BRSHW_PORT_PORTGROUP_14, BRSHW_PORT_PORTNUMBER_6, BRSHW_PORT_ALT_6);
BRSHW_PORT_PIN(BRSHW_PORT_FR0B_ERRNB, BRSHW_PORT_PORTGROUP_15, BRSHW_PORT_PORTNUMBER_8, BRSHW_PORT_ALT_7);
#define _BRSHW_PORT_FR0B_TRCV_STB
BRSHW_PORT_PIN(BRSHW_PORT_FR0B_TRCV_STB, BRSHW_PORT_PORTGROUP_10, BRSHW_PORT_PORTNUMBER_6, BRSHW_PORT_ALT_0);/*pull-up*/
#define _BRSHW_PORT_FR0B_TRCV_EN
BRSHW_PORT_PIN(BRSHW_PORT_FR0B_TRCV_EN, BRSHW_PORT_PORTGROUP_10, BRSHW_PORT_PORTNUMBER_5, BRSHW_PORT_ALT_0);/*pull-up*/

# elif defined (BRS_EVA_BOARD_TRIBOARD_TC3x7) || \
       defined (BRS_EVA_BOARD_TRIBOARD_TC3x9)
  /* ERAY 0 - channel A */
  /* STBN, BGE -> hardwired to high (V_UC) */
BRSHW_PORT_PIN(BRSHW_PORT_FR0A_TX, BRSHW_PORT_PORTGROUP_14, BRSHW_PORT_PORTNUMBER_10, BRSHW_PORT_ALT_6);
BRSHW_PORT_PIN(BRSHW_PORT_FR0A_RX, BRSHW_PORT_PORTGROUP_14, BRSHW_PORT_PORTNUMBER_8, BRSHW_PORT_ALT_0);
BRSHW_PORT_PIN(BRSHW_PORT_FR0A_TXEN, BRSHW_PORT_PORTGROUP_14, BRSHW_PORT_PORTNUMBER_9, BRSHW_PORT_ALT_6);
BRSHW_PORT_PIN(BRSHW_PORT_FR0A_ERRNA, BRSHW_PORT_PORTGROUP_32, BRSHW_PORT_PORTNUMBER_2, BRSHW_PORT_ALT_0);
#define _BRSHW_PORT_FR0A_TRCV_EN
BRSHW_PORT_PIN(BRSHW_PORT_FR0A_TRCV_EN, BRSHW_PORT_PORTGROUP_32, BRSHW_PORT_PORTNUMBER_3, BRSHW_PORT_ALT_0);

  /* ERAY 0 - channel B */
  /* STBN, BGE -> hardwired to high (V_UC) */
BRSHW_PORT_PIN(BRSHW_PORT_FR0B_TX, BRSHW_PORT_PORTGROUP_14, BRSHW_PORT_PORTNUMBER_5, BRSHW_PORT_ALT_6);
BRSHW_PORT_PIN(BRSHW_PORT_FR0B_RX, BRSHW_PORT_PORTGROUP_14, BRSHW_PORT_PORTNUMBER_7, BRSHW_PORT_ALT_0);
BRSHW_PORT_PIN(BRSHW_PORT_FR0B_TXEN, BRSHW_PORT_PORTGROUP_14, BRSHW_PORT_PORTNUMBER_6, BRSHW_PORT_ALT_6);
BRSHW_PORT_PIN(BRSHW_PORT_FR0B_ERRNB, BRSHW_PORT_PORTGROUP_20, BRSHW_PORT_PORTNUMBER_9, BRSHW_PORT_ALT_0);
#define _BRSHW_PORT_FR0B_TRCV_EN
BRSHW_PORT_PIN(BRSHW_PORT_FR0B_TRCV_EN, BRSHW_PORT_PORTGROUP_20, BRSHW_PORT_PORTNUMBER_10, BRSHW_PORT_ALT_0);

  /* ERAY 1 - channel A */
  /* No transceiver equipped onboard! Connection to external transceiver necessary! */
BRSHW_PORT_PIN(BRSHW_PORT_FR1A_TX, BRSHW_PORT_PORTGROUP_1, BRSHW_PORT_PORTNUMBER_12, BRSHW_PORT_ALT_6);
BRSHW_PORT_PIN(BRSHW_PORT_FR1A_RX, BRSHW_PORT_PORTGROUP_14, BRSHW_PORT_PORTNUMBER_8, BRSHW_PORT_ALT_0);
BRSHW_PORT_PIN(BRSHW_PORT_FR1A_TXEN, BRSHW_PORT_PORTGROUP_1, BRSHW_PORT_PORTNUMBER_14, BRSHW_PORT_ALT_6);
BRSHW_PORT_PIN(BRSHW_PORT_FR1A_ERRNA, BRSHW_PORT_PORTGROUP_15, BRSHW_PORT_PORTNUMBER_13, BRSHW_PORT_ALT_0);

  /* ERAY 1 - channel B */
  /* No transceiver equipped onboard! Connection to external transceiver necessary! */
BRSHW_PORT_PIN(BRSHW_PORT_FR1B_TX, BRSHW_PORT_PORTGROUP_1, BRSHW_PORT_PORTNUMBER_13, BRSHW_PORT_ALT_6);
BRSHW_PORT_PIN(BRSHW_PORT_FR1B_RX, BRSHW_PORT_PORTGROUP_14, BRSHW_PORT_PORTNUMBER_7, BRSHW_PORT_ALT_0);
BRSHW_PORT_PIN(BRSHW_PORT_FR1B_TXEN, BRSHW_PORT_PORTGROUP_2, BRSHW_PORT_PORTNUMBER_15, BRSHW_PORT_ALT_6);
BRSHW_PORT_PIN(BRSHW_PORT_FR1B_ERRNB, BRSHW_PORT_PORTGROUP_15, BRSHW_PORT_PORTNUMBER_14, BRSHW_PORT_ALT_0);

# else
  #error "PortPins for your EvalBoard are not yet supported with FR"
# endif /*BRS_EVA_BOARD_x*/
#endif /*BRS_ENABLE_FLEXRAY_SUPPORT*/

/*******************************************************************************
  ETHERNET driver
********************************************************************************/
#if defined (BRS_ENABLE_ETHERNET_SUPPORT)
# if defined (BRS_EVA_BOARD_TRIBOARD_TC2x4) || \
     defined (BRS_EVA_BOARD_TRIBOARD_TC2x5) || \
     defined (BRS_EVA_BOARD_TRIBOARD_TC2x7) || \
     defined (BRS_EVA_BOARD_TRIBOARD_TC2x8) || \
     defined (BRS_EVA_BOARD_TRIBOARD_TC2x9)
  /* RMII interface!!! */
BRSHW_PORT_PIN(BRSHW_PORT_ETH0_TXCLK, BRSHW_PORT_PORTGROUP_11, BRSHW_PORT_PORTNUMBER_5, BRSHW_PORT_ALT_2);
BRSHW_PORT_PIN(BRSHW_PORT_ETH0_REFCLK, BRSHW_PORT_PORTGROUP_11, BRSHW_PORT_PORTNUMBER_12, BRSHW_PORT_ALT_0);
BRSHW_PORT_PIN(BRSHW_PORT_ETH0_TXD0, BRSHW_PORT_PORTGROUP_11, BRSHW_PORT_PORTNUMBER_3, BRSHW_PORT_ALT_6);
BRSHW_PORT_PIN(BRSHW_PORT_ETH0_TXD1, BRSHW_PORT_PORTGROUP_11, BRSHW_PORT_PORTNUMBER_2, BRSHW_PORT_ALT_6);
BRSHW_PORT_PIN(BRSHW_PORT_ETH0_TXEN, BRSHW_PORT_PORTGROUP_11, BRSHW_PORT_PORTNUMBER_6, BRSHW_PORT_ALT_6);
BRSHW_PORT_PIN(BRSHW_PORT_ETH0_RXD0, BRSHW_PORT_PORTGROUP_11, BRSHW_PORT_PORTNUMBER_10, BRSHW_PORT_ALT_0);
BRSHW_PORT_PIN(BRSHW_PORT_ETH0_RXD1, BRSHW_PORT_PORTGROUP_11, BRSHW_PORT_PORTNUMBER_9, BRSHW_PORT_ALT_0);
BRSHW_PORT_PIN(BRSHW_PORT_ETH0_CRSDV, BRSHW_PORT_PORTGROUP_11, BRSHW_PORT_PORTNUMBER_11, BRSHW_PORT_ALT_0);

#  if defined (BRS_EVA_BOARD_TRIBOARD_TC2x5) || \
      defined (BRS_EVA_BOARD_TRIBOARD_TC2x7)
BRSHW_PORT_PIN(BRSHW_PORT_ETH0_MDIO, BRSHW_PORT_PORTGROUP_21, BRSHW_PORT_PORTNUMBER_1, BRSHW_PORT_ALT_6);
#define _BRSHW_PORT_ETH0_MDC
BRSHW_PORT_PIN(BRSHW_PORT_ETH0_MDC, BRSHW_PORT_PORTGROUP_21, BRSHW_PORT_PORTNUMBER_0, BRSHW_PORT_ALT_6);
#  endif /*BRS_EVA_BOARD_x*/

#  if defined (BRS_EVA_BOARD_TRIBOARD_TC2x4)
BRSHW_PORT_PIN(BRSHW_PORT_ETH0_MDIO, BRSHW_PORT_PORTGROUP_0, BRSHW_PORT_PORTNUMBER_0, BRSHW_PORT_ALT_0);
#define _BRSHW_PORT_ETH0_MDC
BRSHW_PORT_PIN(BRSHW_PORT_ETH0_MDC, BRSHW_PORT_PORTGROUP_2, BRSHW_PORT_PORTNUMBER_8, BRSHW_PORT_ALT_6);
#  endif /*BRS_EVA_BOARD_TRIBOARD_TC2x4*/

#  if defined (BRS_EVA_BOARD_TRIBOARD_TC2x8) || \
      defined (BRS_EVA_BOARD_TRIBOARD_TC2x9)
BRSHW_PORT_PIN(BRSHW_PORT_ETH0_MDIO, BRSHW_PORT_PORTGROUP_12, BRSHW_PORT_PORTNUMBER_1, BRSHW_PORT_ALT_6);
#define _BRSHW_PORT_ETH0_MDC
BRSHW_PORT_PIN(BRSHW_PORT_ETH0_MDC, BRSHW_PORT_PORTGROUP_12, BRSHW_PORT_PORTNUMBER_0, BRSHW_PORT_ALT_6);
#  endif /*BRS_EVA_BOARD_TRIBOARD_TC2x8||BRS_EVA_BOARD_TRIBOARD_TC2x9*/

# elif defined (BRS_EVA_BOARD_TRIBOARD_TC3x7) || \
       defined (BRS_EVA_BOARD_TRIBOARD_TC3x9)
BRSHW_PORT_PIN(BRSHW_PORT_ETH0_TXCLK, BRSHW_PORT_PORTGROUP_11, BRSHW_PORT_PORTNUMBER_4, BRSHW_PORT_ALT_6);
BRSHW_PORT_PIN(BRSHW_PORT_ETH0_RXCLK, BRSHW_PORT_PORTGROUP_11, BRSHW_PORT_PORTNUMBER_12, BRSHW_PORT_ALT_0);
#define _BRSHW_PORT_ETH0_RXCLK
BRSHW_PORT_PIN(BRSHW_PORT_ETH0_REFCLK, BRSHW_PORT_PORTGROUP_11, BRSHW_PORT_PORTNUMBER_5, BRSHW_PORT_ALT_0);
BRSHW_PORT_PIN(BRSHW_PORT_ETH0_TXD0, BRSHW_PORT_PORTGROUP_11, BRSHW_PORT_PORTNUMBER_3, BRSHW_PORT_ALT_6);
BRSHW_PORT_PIN(BRSHW_PORT_ETH0_TXD1, BRSHW_PORT_PORTGROUP_11, BRSHW_PORT_PORTNUMBER_2, BRSHW_PORT_ALT_6);
#define _BRSHW_PORT_ETH0_TXD2
BRSHW_PORT_PIN(BRSHW_PORT_ETH0_TXD2, BRSHW_PORT_PORTGROUP_11, BRSHW_PORT_PORTNUMBER_1, BRSHW_PORT_ALT_6);
#define _BRSHW_PORT_ETH0_TXD3
BRSHW_PORT_PIN(BRSHW_PORT_ETH0_TXD3, BRSHW_PORT_PORTGROUP_11, BRSHW_PORT_PORTNUMBER_0, BRSHW_PORT_ALT_6);
BRSHW_PORT_PIN(BRSHW_PORT_ETH0_TXEN, BRSHW_PORT_PORTGROUP_11, BRSHW_PORT_PORTNUMBER_6, BRSHW_PORT_ALT_6);
BRSHW_PORT_PIN(BRSHW_PORT_ETH0_RXD0, BRSHW_PORT_PORTGROUP_11, BRSHW_PORT_PORTNUMBER_10, BRSHW_PORT_ALT_0);
BRSHW_PORT_PIN(BRSHW_PORT_ETH0_RXD1, BRSHW_PORT_PORTGROUP_11, BRSHW_PORT_PORTNUMBER_9, BRSHW_PORT_ALT_0);
#define _BRSHW_PORT_ETH0_RXD2
BRSHW_PORT_PIN(BRSHW_PORT_ETH0_RXD2, BRSHW_PORT_PORTGROUP_11, BRSHW_PORT_PORTNUMBER_8, BRSHW_PORT_ALT_0);
#define _BRSHW_PORT_ETH0_RXD3
BRSHW_PORT_PIN(BRSHW_PORT_ETH0_RXD3, BRSHW_PORT_PORTGROUP_11, BRSHW_PORT_PORTNUMBER_7, BRSHW_PORT_ALT_0);
BRSHW_PORT_PIN(BRSHW_PORT_ETH0_CRSDV, BRSHW_PORT_PORTGROUP_11, BRSHW_PORT_PORTNUMBER_11, BRSHW_PORT_ALT_0);
BRSHW_PORT_PIN(BRSHW_PORT_ETH0_MDIO, BRSHW_PORT_PORTGROUP_12, BRSHW_PORT_PORTNUMBER_1, BRSHW_PORT_ALT_0);
#define _BRSHW_PORT_ETH0_MDC
BRSHW_PORT_PIN(BRSHW_PORT_ETH0_MDC, BRSHW_PORT_PORTGROUP_12, BRSHW_PORT_PORTNUMBER_0, BRSHW_PORT_ALT_6);

# else
  #error "PortPins for your EvalBoard are not yet supported with ETH"
# endif /*BRS_EVA_BOARD_x*/
#endif /*BRS_ENABLE_ETHERNET_SUPPORT*/

#endif /*_BRSHW_PORTS_H_*/
