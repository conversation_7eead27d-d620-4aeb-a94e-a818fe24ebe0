; Trace32 Configuration file for AMP multicore debugging
;
; Syntax:
;  t32m*.exe [<-option> <filename> <arguments>] [<-option> <filename> <arguments>]
;    Available options:
;    -c Define T32 configuration file. Default               : config.t32
;    -s Define T32 startup script.     Default (if available): t32.cmm
; Parameters:
;   t32mtc -c config_multicore.t32 <intercom_port> <title> <interface-to-debug-module> [interface options]
; Examples:
;   t32mtc -c config_multicore.t32 10000 TriCore_CORE0 USB CORE=1 -s _start.cmm
;   t32mtc -c config_multicore.t32 10000 TriCore_CORE0 NET NODE=pod-mob2 PACKLEN=1024 CORE=1 -s _start.cmm
;
; $Id: config_multicore.t32 4585 2012-03-07 08:46:22Z mobermeir $

IC=NETASSIST
PORT=${1}

; Environment variables
OS=
ID=T32${1}
TMP=D:\TEMP
SYS=~~\
HELP=~~\pdf

PBI=
${3}
${4}
${5}
${6}

; Printer settings
PRINTER=WINDOWS


; Screen fonts
SCREEN=
FONT=SMALL
HEADER=TRACE32 ${2}
