-c99
--integer-enumeration
-D__CPU__=userdef162
-D__CPU_USERDEF162__
--core=tc1.6.2
--fp-model=+float
-DBRS_PLATFORM_AURIX
-DBRS_COMP_TASKING
-I../../../external/Components/BswM/Implementation
-I../../../external/Components/Can/Implementation
-I../../../external/Components/CanIf/Implementation
-I../../../external/Components/CanNm/Implementation
-I../../../external/Components/CanSM/Implementation
-I../../../external/Components/CanTp/Implementation
-I../../../external/Components/Com/Implementation
-I../../../external/Components/ComM/Implementation
-I../../../external/Components/Crc
-I../../../external/Components/Crc/Implementation
-I../../../external/Components/Dcm/Implementation
-I../../../external/Components/Dem/Implementation
-I../../../external/Components/Det/Implementation
-I../../../external/Components/E2E/Implementation
-I../../../external/Components/E2EXf/Implementation
-I../../../external/Components/EcuM/Implementation
-I../../../external/Components/Fr/Implementation
-I../../../external/Components/FrIf/Implementation
-I../../../external/Components/FrNm/Implementation
-I../../../external/Components/FrSM/Implementation
-I../../../external/Components/GenData
-I../../../external/Components/Lin/Implementation
-I../../../external/Components/LinIf/Implementation
-I../../../external/Components/LinNm/Implementation
-I../../../external/Components/LinSM/Implementation
-I../../../external/Components/Mcal_Tc3xx
-I../../../external/Components/Mcal_Tc3xx/Implementation
-I../../../external/Components/MemIf
-I../../../external/Components/MemIf/Implementation
-I../../../external/Components/Nm/Implementation
-I../../../external/Components/NvM
-I../../../external/Components/NvM/Implementation
-I../../../external/Components/Os/Implementation
-I../../../external/Components/PduR/Implementation
-I../../../external/Components/VStdLib/Implementation
-I../../../external/Components/vLinkGen/Implementation
-I../../../external/ThirdParty/Mcal_Tc3xx/Supply/MC-ISAR_AS422_TC3xx_BASIC_1.40.0/DemoWorkspace/McalDemo/TC37A_ED/0_Src/BaseSw/Infra/Autosar_Srv
-I../../../external/ThirdParty/Mcal_Tc3xx/Supply/MC-ISAR_AS422_TC3xx_BASIC_1.40.0/McIsar/Src/Infra_Prod/Sfr/TC37xA_ED/_Reg
-I../../../external/ThirdParty/Mcal_Tc3xx/Supply/MC-ISAR_AS422_TC3xx_BASIC_1.40.0/McIsar/Src/Mcal/Tricore/Fee/ssc/inc
-I../../../external/ThirdParty/Mcal_Tc3xx/Supply/MC-ISAR_AS422_TC3xx_BASIC_1.40.0/McIsar/Src/Mcal/Tricore/Fls_17_Dmu/ssc/inc
-I../../../external/ThirdParty/Mcal_Tc3xx/Supply/MC-ISAR_AS422_TC3xx_BASIC_1.40.0/McIsar/Src/Mcal/Tricore/McalLib/ssc/inc
-I../../../external/ThirdParty/Mcal_Tc3xx/Supply/MC-ISAR_AS422_TC3xx_BASIC_1.40.0/McIsar/Src/Mcal/Tricore/Mcu/ssc/inc
-I../../../external/ThirdParty/Mcal_Tc3xx/Supply/MC-ISAR_AS422_TC3xx_BASIC_1.40.0/McIsar/Src/Mcal/Tricore/Port/ssc/inc
-I../../../external/Components/_Common/Implementation
-IGenData
-IInclude
-ID:/uti/Tasking/TriCore/6.2r2p2/ctc/include
-IGenData/Components
-IGenData/inc
--debug-info=default
--align=4
--default-a0-size=0
--default-a1-size=0
--default-near-size=0
--optimize=aceFgIklMnopRsUvwy,+predict
--tradeoff=2
--language=-gcc,+volatile,-strings,-comments
-o
obj\external\Components\Os\Implementation\Os_Trap.src
../../../external/Components/Os/Implementation/Os_Trap.c
