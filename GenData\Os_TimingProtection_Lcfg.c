/**********************************************************************************************************************
 *  COPYRIGHT
 *  -------------------------------------------------------------------------------------------------------------------
 *  \verbatim
 *
 *                 This software is copyright protected and proprietary to Vector Informatik GmbH.
 *                 Vector Informatik GmbH grants to you only those rights as set out in the license conditions.
 *                 All other rights remain with Vector Informatik GmbH.
 *  \endverbatim
 *  -------------------------------------------------------------------------------------------------------------------
 *  LICENSE
 *  -------------------------------------------------------------------------------------------------------------------
 *            Module: Os
 *           Program: MSR_Vector_SLP4
 *          Customer: AUTOSAR 4 Evaluation Bundle
 *       Expiry Date: Not restricted
 *  Ordered Derivat.: Aurix 2G/TC377
 *    License Scope : The usage is restricted to CBD2000456_D02
 *
 *  -------------------------------------------------------------------------------------------------------------------
 *  FILE DESCRIPTION
 *  -------------------------------------------------------------------------------------------------------------------
 *              File: Os_TimingProtection_Lcfg.c
 *   Generation Time: 2025-08-05 10:37:19
 *           Project: Demo - Version 1.0
 *          Delivery: CBD2000456_D02
 *      Tool Version: DaVinci Configurator (beta) 5.21.40 SP2
 *
 *
 *********************************************************************************************************************/

/**********************************************************************************************************************
 ! BETA VERSION                                                                                                       !
 !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
 ! This version of DaVinci Configurator Pro and/or the related Basic Software Package is BETA software.               !
 ! BETA Software is basically operable, but not sufficiently tested, verified and/or qualified for use in series      !
 ! production and/or in vehicles operating on public or non-public roads.                                             !
 ! In particular, without limitation, BETA Software may cause unpredictable ECU behavior, may not provide all         !
 ! functions necessary for use in series production and/or may not comply with quality requirements which are         !
 ! necessary according to the state of the art. BETA Software must not be used in series production.                  !
 !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
**********************************************************************************************************************/

/* PRQA S 0777, 0779, 0828 EOF */ /* MD_MSR_Rule5.1, MD_MSR_Rule5.2, MD_MSR_Dir1.1 */

#define OS_TIMINGPROTECTION_LCFG_SOURCE

/**********************************************************************************************************************
 *  INCLUDES
 *********************************************************************************************************************/

/* AUTOSAR includes */
#include "Std_Types.h"

/* Os module declarations */
#include "Os_TimingProtection_Lcfg.h"
#include "Os_TimingProtection.h" 

/* Os kernel module dependencies */
#include "Os_Cfg.h"
#include "Os_Common.h"
#include "Os_Counter_Lcfg.h"
#include "Os_Lcfg.h"

/* Os hal dependencies */
#include "Os_Hal_Cfg.h"


/**********************************************************************************************************************
 *  LOCAL CONSTANT MACROS
 *********************************************************************************************************************/

/**********************************************************************************************************************
 *  LOCAL FUNCTION MACROS
 *********************************************************************************************************************/

/**********************************************************************************************************************
 *  LOCAL DATA TYPES AND STRUCTURES
 *********************************************************************************************************************/

/**********************************************************************************************************************
 *  LOCAL DATA PROTOTYPES
 *********************************************************************************************************************/

#define OS_START_SEC_CORE0_VAR_NOINIT_UNSPECIFIED
#include "Os_MemMap_OsSections.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

/*! Dynamic timing protection data: OsCore0 */
OS_LOCAL VAR(Os_TpType, OS_VAR_NOINIT) OsCfg_Tp_OsCore0_Dyn;

/*! Dynamic timing protection data: Os_CoreInitHook_OsCore0 */
OS_LOCAL VAR(Os_TpOwnerThreadType, OS_VAR_NOINIT) OsCfg_Tp_Os_CoreInitHook_OsCore0_Dyn;

/*! Dynamic timing protection data: CanIsr_7 */
OS_LOCAL VAR(Os_TpOwnerThreadType, OS_VAR_NOINIT) OsCfg_Tp_CanIsr_7_Dyn;

/*! Dynamic timing protection data: CounterIsr_SystemTimer */
OS_LOCAL VAR(Os_TpOwnerThreadType, OS_VAR_NOINIT) OsCfg_Tp_CounterIsr_SystemTimer_Dyn;

/*! Dynamic timing protection data: CounterIsr_TpCounter_OsCore0 */

/*! Dynamic timing protection data: Fr_IrqLine0 */
OS_LOCAL VAR(Os_TpOwnerThreadType, OS_VAR_NOINIT) OsCfg_Tp_Fr_IrqLine0_Dyn;

/*! Dynamic timing protection data: Fr_IrqTimer0 */
OS_LOCAL VAR(Os_TpOwnerThreadType, OS_VAR_NOINIT) OsCfg_Tp_Fr_IrqTimer0_Dyn;

/*! Dynamic timing protection data: Lin_Channel_2_EX_Extended_Error_Interrupt */
OS_LOCAL VAR(Os_TpOwnerThreadType, OS_VAR_NOINIT) OsCfg_Tp_Lin_Channel_2_EX_Extended_Error_Interrupt_Dyn;

/*! Dynamic timing protection data: Lin_Channel_2_RX_Receive_Interrupt */
OS_LOCAL VAR(Os_TpOwnerThreadType, OS_VAR_NOINIT) OsCfg_Tp_Lin_Channel_2_RX_Receive_Interrupt_Dyn;

/*! Dynamic timing protection data: Lin_Channel_2_TX_Transmit_Interrupt */
OS_LOCAL VAR(Os_TpOwnerThreadType, OS_VAR_NOINIT) OsCfg_Tp_Lin_Channel_2_TX_Transmit_Interrupt_Dyn;

/*! Dynamic timing protection data: Default_BSW_Async_Task */
OS_LOCAL VAR(Os_TpOwnerThreadType, OS_VAR_NOINIT) OsCfg_Tp_Default_BSW_Async_Task_Dyn;

/*! Dynamic timing protection data: Default_BSW_Sync_Task */
OS_LOCAL VAR(Os_TpOwnerThreadType, OS_VAR_NOINIT) OsCfg_Tp_Default_BSW_Sync_Task_Dyn;

/*! Dynamic timing protection data: Default_Init_Task */
OS_LOCAL VAR(Os_TpOwnerThreadType, OS_VAR_NOINIT) OsCfg_Tp_Default_Init_Task_Dyn;

/*! Dynamic timing protection data: Default_Init_Task_Trusted */
OS_LOCAL VAR(Os_TpOwnerThreadType, OS_VAR_NOINIT) OsCfg_Tp_Default_Init_Task_Trusted_Dyn;

/*! Dynamic timing protection data: Default_RTE_Mode_switch_Task */
OS_LOCAL VAR(Os_TpBudgetType, OS_VAR_NOINIT) OsCfg_Tp_Default_RTE_Mode_switch_Task_ExecutionBudget_Dyn;
OS_LOCAL VAR(Os_TpBudgetType, OS_VAR_NOINIT) OsCfg_Tp_Default_RTE_Mode_switch_Task_AllIntLockBudget_Dyn;
OS_LOCAL VAR(Os_TpBudgetType, OS_VAR_NOINIT) OsCfg_Tp_Default_RTE_Mode_switch_Task_OsIntLockBudget_Dyn;
OS_LOCAL VAR(Os_TpOwnerThreadType, OS_VAR_NOINIT) OsCfg_Tp_Default_RTE_Mode_switch_Task_Dyn;

/*! Dynamic timing protection data: IdleTask_OsCore0 */
OS_LOCAL VAR(Os_TpOwnerThreadType, OS_VAR_NOINIT) OsCfg_Tp_IdleTask_OsCore0_Dyn;

/*! Dynamic timing protection data: StartApplication_Appl_Init_Task */
OS_LOCAL VAR(Os_TpOwnerThreadType, OS_VAR_NOINIT) OsCfg_Tp_StartApplication_Appl_Init_Task_Dyn;

/*! Dynamic timing protection data: StartApplication_Appl_Task */
OS_LOCAL VAR(Os_TpOwnerThreadType, OS_VAR_NOINIT) OsCfg_Tp_StartApplication_Appl_Task_Dyn;

#define OS_STOP_SEC_CORE0_VAR_NOINIT_UNSPECIFIED
#include "Os_MemMap_OsSections.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */


/**********************************************************************************************************************
 *  GLOBAL DATA
 *********************************************************************************************************************/

/**********************************************************************************************************************
 *  GLOBAL CONSTANT DATA
 *********************************************************************************************************************/

#define OS_START_SEC_CORE0_CONST_UNSPECIFIED
#include "Os_MemMap_OsSections.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

/*! Empty resource lock budgets: OsCore0 */
OS_LOCAL CONST(Os_TpBudgetConfigRefType, OS_CONST)
  OsCfg_Tp_OsCore0_EmptyResourceLockBudgets[OS_RESOURCEID_COUNT + 1] =
{
  /* [0] = */ NULL_PTR,
  /* [1] = */ NULL_PTR
};

/*! Timing protection configuration data: OsCore0 */
CONST(Os_TpConfigType, OS_CONST) OsCfg_Tp_OsCore0 =
{
  /* .Timer = */ &OsCfg_Counter_TpCounter_OsCore0,
  /* .Dyn   = */ &OsCfg_Tp_OsCore0_Dyn
};

/*! Timing protection configuration data: Os_CoreInitHook_OsCore0 */
CONST(Os_TpOwnerThreadConfigType, OS_CONST) OsCfg_Tp_Os_CoreInitHook_OsCore0 =
{
  /* .Base                   = */
  {
    /* .ThreadKind = */ OS_TPTHREADKIND_OWNER
  },
  /* .TimeFrame              = */ ((Os_InterArrivalTimeType) 0), /* 0.0 sec */
  /* .ExecutionBudget        = */
  {
    /* .Dyn        = */ NULL_PTR,
    /* .BudgetKind = */ OS_TPMONITORINGTYPE_NONE,
    /* .Budget     = */ 0uL /* 0.0 sec */
  },
  /* .AllInterruptLockBudget = */
  {
    /* .Dyn        = */ NULL_PTR,
    /* .BudgetKind = */ OS_TPMONITORINGTYPE_NONE,
    /* .Budget     = */ 0uL /* 0.0 sec */
  },
  /* .OsInterruptLockBudget  = */
  {
    /* .Dyn        = */ NULL_PTR,
    /* .BudgetKind = */ OS_TPMONITORINGTYPE_NONE,
    /* .Budget     = */ 0uL /* 0.0 sec */
  },
  /* .ResourceLockBudgets    = */ OsCfg_Tp_OsCore0_EmptyResourceLockBudgets,
  /* .Dyn                    = */ &OsCfg_Tp_Os_CoreInitHook_OsCore0_Dyn
};

/*! Timing protection configuration data: CanIsr_7 */
CONST(Os_TpOwnerThreadConfigType, OS_CONST) OsCfg_Tp_CanIsr_7 =
{
  /* .Base                   = */
  {
    /* .ThreadKind = */ OS_TPTHREADKIND_OWNER
  },
  /* .TimeFrame              = */ ((Os_InterArrivalTimeType) 0), /* 0.0 sec */
  /* .ExecutionBudget        = */
  {
    /* .Dyn        = */ NULL_PTR,
    /* .BudgetKind = */ OS_TPMONITORINGTYPE_NONE,
    /* .Budget     = */ 0uL /* 0.0 sec */
  },
  /* .AllInterruptLockBudget = */
  {
    /* .Dyn        = */ NULL_PTR,
    /* .BudgetKind = */ OS_TPMONITORINGTYPE_NONE,
    /* .Budget     = */ 0uL /* 0.0 sec */
  },
  /* .OsInterruptLockBudget  = */
  {
    /* .Dyn        = */ NULL_PTR,
    /* .BudgetKind = */ OS_TPMONITORINGTYPE_NONE,
    /* .Budget     = */ 0uL /* 0.0 sec */
  },
  /* .ResourceLockBudgets    = */ OsCfg_Tp_OsCore0_EmptyResourceLockBudgets,
  /* .Dyn                    = */ &OsCfg_Tp_CanIsr_7_Dyn
};

/*! Timing protection configuration data: CounterIsr_SystemTimer */
CONST(Os_TpOwnerThreadConfigType, OS_CONST) OsCfg_Tp_CounterIsr_SystemTimer =
{
  /* .Base                   = */
  {
    /* .ThreadKind = */ OS_TPTHREADKIND_OWNER
  },
  /* .TimeFrame              = */ ((Os_InterArrivalTimeType) 0), /* 0.0 sec */
  /* .ExecutionBudget        = */
  {
    /* .Dyn        = */ NULL_PTR,
    /* .BudgetKind = */ OS_TPMONITORINGTYPE_NONE,
    /* .Budget     = */ 0uL /* 0.0 sec */
  },
  /* .AllInterruptLockBudget = */
  {
    /* .Dyn        = */ NULL_PTR,
    /* .BudgetKind = */ OS_TPMONITORINGTYPE_NONE,
    /* .Budget     = */ 0uL /* 0.0 sec */
  },
  /* .OsInterruptLockBudget  = */
  {
    /* .Dyn        = */ NULL_PTR,
    /* .BudgetKind = */ OS_TPMONITORINGTYPE_NONE,
    /* .Budget     = */ 0uL /* 0.0 sec */
  },
  /* .ResourceLockBudgets    = */ OsCfg_Tp_OsCore0_EmptyResourceLockBudgets,
  /* .Dyn                    = */ &OsCfg_Tp_CounterIsr_SystemTimer_Dyn
};

/*! Timing protection configuration data: CounterIsr_TpCounter_OsCore0 */
CONST(Os_TpThreadConfigType, OS_CONST) OsCfg_Tp_CounterIsr_TpCounter_OsCore0 =
{
    /* .ThreadKind = */ OS_TPTHREADKIND_TPISR
  };

/*! Timing protection configuration data: Fr_IrqLine0 */
CONST(Os_TpOwnerThreadConfigType, OS_CONST) OsCfg_Tp_Fr_IrqLine0 =
{
  /* .Base                   = */
  {
    /* .ThreadKind = */ OS_TPTHREADKIND_OWNER
  },
  /* .TimeFrame              = */ ((Os_InterArrivalTimeType) 0), /* 0.0 sec */
  /* .ExecutionBudget        = */
  {
    /* .Dyn        = */ NULL_PTR,
    /* .BudgetKind = */ OS_TPMONITORINGTYPE_NONE,
    /* .Budget     = */ 0uL /* 0.0 sec */
  },
  /* .AllInterruptLockBudget = */
  {
    /* .Dyn        = */ NULL_PTR,
    /* .BudgetKind = */ OS_TPMONITORINGTYPE_NONE,
    /* .Budget     = */ 0uL /* 0.0 sec */
  },
  /* .OsInterruptLockBudget  = */
  {
    /* .Dyn        = */ NULL_PTR,
    /* .BudgetKind = */ OS_TPMONITORINGTYPE_NONE,
    /* .Budget     = */ 0uL /* 0.0 sec */
  },
  /* .ResourceLockBudgets    = */ OsCfg_Tp_OsCore0_EmptyResourceLockBudgets,
  /* .Dyn                    = */ &OsCfg_Tp_Fr_IrqLine0_Dyn
};

/*! Timing protection configuration data: Fr_IrqTimer0 */
CONST(Os_TpOwnerThreadConfigType, OS_CONST) OsCfg_Tp_Fr_IrqTimer0 =
{
  /* .Base                   = */
  {
    /* .ThreadKind = */ OS_TPTHREADKIND_OWNER
  },
  /* .TimeFrame              = */ ((Os_InterArrivalTimeType) 0), /* 0.0 sec */
  /* .ExecutionBudget        = */
  {
    /* .Dyn        = */ NULL_PTR,
    /* .BudgetKind = */ OS_TPMONITORINGTYPE_NONE,
    /* .Budget     = */ 0uL /* 0.0 sec */
  },
  /* .AllInterruptLockBudget = */
  {
    /* .Dyn        = */ NULL_PTR,
    /* .BudgetKind = */ OS_TPMONITORINGTYPE_NONE,
    /* .Budget     = */ 0uL /* 0.0 sec */
  },
  /* .OsInterruptLockBudget  = */
  {
    /* .Dyn        = */ NULL_PTR,
    /* .BudgetKind = */ OS_TPMONITORINGTYPE_NONE,
    /* .Budget     = */ 0uL /* 0.0 sec */
  },
  /* .ResourceLockBudgets    = */ OsCfg_Tp_OsCore0_EmptyResourceLockBudgets,
  /* .Dyn                    = */ &OsCfg_Tp_Fr_IrqTimer0_Dyn
};

/*! Timing protection configuration data: Lin_Channel_2_EX_Extended_Error_Interrupt */
CONST(Os_TpOwnerThreadConfigType, OS_CONST) OsCfg_Tp_Lin_Channel_2_EX_Extended_Error_Interrupt =
{
  /* .Base                   = */
  {
    /* .ThreadKind = */ OS_TPTHREADKIND_OWNER
  },
  /* .TimeFrame              = */ ((Os_InterArrivalTimeType) 0), /* 0.0 sec */
  /* .ExecutionBudget        = */
  {
    /* .Dyn        = */ NULL_PTR,
    /* .BudgetKind = */ OS_TPMONITORINGTYPE_NONE,
    /* .Budget     = */ 0uL /* 0.0 sec */
  },
  /* .AllInterruptLockBudget = */
  {
    /* .Dyn        = */ NULL_PTR,
    /* .BudgetKind = */ OS_TPMONITORINGTYPE_NONE,
    /* .Budget     = */ 0uL /* 0.0 sec */
  },
  /* .OsInterruptLockBudget  = */
  {
    /* .Dyn        = */ NULL_PTR,
    /* .BudgetKind = */ OS_TPMONITORINGTYPE_NONE,
    /* .Budget     = */ 0uL /* 0.0 sec */
  },
  /* .ResourceLockBudgets    = */ OsCfg_Tp_OsCore0_EmptyResourceLockBudgets,
  /* .Dyn                    = */ &OsCfg_Tp_Lin_Channel_2_EX_Extended_Error_Interrupt_Dyn
};

/*! Timing protection configuration data: Lin_Channel_2_RX_Receive_Interrupt */
CONST(Os_TpOwnerThreadConfigType, OS_CONST) OsCfg_Tp_Lin_Channel_2_RX_Receive_Interrupt =
{
  /* .Base                   = */
  {
    /* .ThreadKind = */ OS_TPTHREADKIND_OWNER
  },
  /* .TimeFrame              = */ ((Os_InterArrivalTimeType) 0), /* 0.0 sec */
  /* .ExecutionBudget        = */
  {
    /* .Dyn        = */ NULL_PTR,
    /* .BudgetKind = */ OS_TPMONITORINGTYPE_NONE,
    /* .Budget     = */ 0uL /* 0.0 sec */
  },
  /* .AllInterruptLockBudget = */
  {
    /* .Dyn        = */ NULL_PTR,
    /* .BudgetKind = */ OS_TPMONITORINGTYPE_NONE,
    /* .Budget     = */ 0uL /* 0.0 sec */
  },
  /* .OsInterruptLockBudget  = */
  {
    /* .Dyn        = */ NULL_PTR,
    /* .BudgetKind = */ OS_TPMONITORINGTYPE_NONE,
    /* .Budget     = */ 0uL /* 0.0 sec */
  },
  /* .ResourceLockBudgets    = */ OsCfg_Tp_OsCore0_EmptyResourceLockBudgets,
  /* .Dyn                    = */ &OsCfg_Tp_Lin_Channel_2_RX_Receive_Interrupt_Dyn
};

/*! Timing protection configuration data: Lin_Channel_2_TX_Transmit_Interrupt */
CONST(Os_TpOwnerThreadConfigType, OS_CONST) OsCfg_Tp_Lin_Channel_2_TX_Transmit_Interrupt =
{
  /* .Base                   = */
  {
    /* .ThreadKind = */ OS_TPTHREADKIND_OWNER
  },
  /* .TimeFrame              = */ ((Os_InterArrivalTimeType) 0), /* 0.0 sec */
  /* .ExecutionBudget        = */
  {
    /* .Dyn        = */ NULL_PTR,
    /* .BudgetKind = */ OS_TPMONITORINGTYPE_NONE,
    /* .Budget     = */ 0uL /* 0.0 sec */
  },
  /* .AllInterruptLockBudget = */
  {
    /* .Dyn        = */ NULL_PTR,
    /* .BudgetKind = */ OS_TPMONITORINGTYPE_NONE,
    /* .Budget     = */ 0uL /* 0.0 sec */
  },
  /* .OsInterruptLockBudget  = */
  {
    /* .Dyn        = */ NULL_PTR,
    /* .BudgetKind = */ OS_TPMONITORINGTYPE_NONE,
    /* .Budget     = */ 0uL /* 0.0 sec */
  },
  /* .ResourceLockBudgets    = */ OsCfg_Tp_OsCore0_EmptyResourceLockBudgets,
  /* .Dyn                    = */ &OsCfg_Tp_Lin_Channel_2_TX_Transmit_Interrupt_Dyn
};

/*! Timing protection configuration data: Default_BSW_Async_Task */
CONST(Os_TpOwnerThreadConfigType, OS_CONST) OsCfg_Tp_Default_BSW_Async_Task =
{
  /* .Base                   = */
  {
    /* .ThreadKind = */ OS_TPTHREADKIND_OWNER
  },
  /* .TimeFrame              = */ ((Os_InterArrivalTimeType) 0), /* 0.0 sec */
  /* .ExecutionBudget        = */
  {
    /* .Dyn        = */ NULL_PTR,
    /* .BudgetKind = */ OS_TPMONITORINGTYPE_NONE,
    /* .Budget     = */ 0uL /* 0.0 sec */
  },
  /* .AllInterruptLockBudget = */
  {
    /* .Dyn        = */ NULL_PTR,
    /* .BudgetKind = */ OS_TPMONITORINGTYPE_NONE,
    /* .Budget     = */ 0uL /* 0.0 sec */
  },
  /* .OsInterruptLockBudget  = */
  {
    /* .Dyn        = */ NULL_PTR,
    /* .BudgetKind = */ OS_TPMONITORINGTYPE_NONE,
    /* .Budget     = */ 0uL /* 0.0 sec */
  },
  /* .ResourceLockBudgets    = */ OsCfg_Tp_OsCore0_EmptyResourceLockBudgets,
  /* .Dyn                    = */ &OsCfg_Tp_Default_BSW_Async_Task_Dyn
};

/*! Timing protection configuration data: Default_BSW_Sync_Task */
CONST(Os_TpOwnerThreadConfigType, OS_CONST) OsCfg_Tp_Default_BSW_Sync_Task =
{
  /* .Base                   = */
  {
    /* .ThreadKind = */ OS_TPTHREADKIND_OWNER
  },
  /* .TimeFrame              = */ ((Os_InterArrivalTimeType) 0), /* 0.0 sec */
  /* .ExecutionBudget        = */
  {
    /* .Dyn        = */ NULL_PTR,
    /* .BudgetKind = */ OS_TPMONITORINGTYPE_NONE,
    /* .Budget     = */ 0uL /* 0.0 sec */
  },
  /* .AllInterruptLockBudget = */
  {
    /* .Dyn        = */ NULL_PTR,
    /* .BudgetKind = */ OS_TPMONITORINGTYPE_NONE,
    /* .Budget     = */ 0uL /* 0.0 sec */
  },
  /* .OsInterruptLockBudget  = */
  {
    /* .Dyn        = */ NULL_PTR,
    /* .BudgetKind = */ OS_TPMONITORINGTYPE_NONE,
    /* .Budget     = */ 0uL /* 0.0 sec */
  },
  /* .ResourceLockBudgets    = */ OsCfg_Tp_OsCore0_EmptyResourceLockBudgets,
  /* .Dyn                    = */ &OsCfg_Tp_Default_BSW_Sync_Task_Dyn
};

/*! Timing protection configuration data: Default_Init_Task */
CONST(Os_TpOwnerThreadConfigType, OS_CONST) OsCfg_Tp_Default_Init_Task =
{
  /* .Base                   = */
  {
    /* .ThreadKind = */ OS_TPTHREADKIND_OWNER
  },
  /* .TimeFrame              = */ ((Os_InterArrivalTimeType) 0), /* 0.0 sec */
  /* .ExecutionBudget        = */
  {
    /* .Dyn        = */ NULL_PTR,
    /* .BudgetKind = */ OS_TPMONITORINGTYPE_NONE,
    /* .Budget     = */ 0uL /* 0.0 sec */
  },
  /* .AllInterruptLockBudget = */
  {
    /* .Dyn        = */ NULL_PTR,
    /* .BudgetKind = */ OS_TPMONITORINGTYPE_NONE,
    /* .Budget     = */ 0uL /* 0.0 sec */
  },
  /* .OsInterruptLockBudget  = */
  {
    /* .Dyn        = */ NULL_PTR,
    /* .BudgetKind = */ OS_TPMONITORINGTYPE_NONE,
    /* .Budget     = */ 0uL /* 0.0 sec */
  },
  /* .ResourceLockBudgets    = */ OsCfg_Tp_OsCore0_EmptyResourceLockBudgets,
  /* .Dyn                    = */ &OsCfg_Tp_Default_Init_Task_Dyn
};

/*! Timing protection configuration data: Default_Init_Task_Trusted */
CONST(Os_TpOwnerThreadConfigType, OS_CONST) OsCfg_Tp_Default_Init_Task_Trusted =
{
  /* .Base                   = */
  {
    /* .ThreadKind = */ OS_TPTHREADKIND_OWNER
  },
  /* .TimeFrame              = */ ((Os_InterArrivalTimeType) 0), /* 0.0 sec */
  /* .ExecutionBudget        = */
  {
    /* .Dyn        = */ NULL_PTR,
    /* .BudgetKind = */ OS_TPMONITORINGTYPE_NONE,
    /* .Budget     = */ 0uL /* 0.0 sec */
  },
  /* .AllInterruptLockBudget = */
  {
    /* .Dyn        = */ NULL_PTR,
    /* .BudgetKind = */ OS_TPMONITORINGTYPE_NONE,
    /* .Budget     = */ 0uL /* 0.0 sec */
  },
  /* .OsInterruptLockBudget  = */
  {
    /* .Dyn        = */ NULL_PTR,
    /* .BudgetKind = */ OS_TPMONITORINGTYPE_NONE,
    /* .Budget     = */ 0uL /* 0.0 sec */
  },
  /* .ResourceLockBudgets    = */ OsCfg_Tp_OsCore0_EmptyResourceLockBudgets,
  /* .Dyn                    = */ &OsCfg_Tp_Default_Init_Task_Trusted_Dyn
};

/*! Timing protection configuration data: Default_RTE_Mode_switch_Task */
CONST(Os_TpOwnerThreadConfigType, OS_CONST) OsCfg_Tp_Default_RTE_Mode_switch_Task =
{
  /* .Base                   = */
  {
    /* .ThreadKind = */ OS_TPTHREADKIND_OWNER
  },
  /* .TimeFrame              = */ ((Os_InterArrivalTimeType) 1000), /* 1.0 sec */
  /* .ExecutionBudget        = */
  {
    /* .Dyn        = */ (P2VAR(Os_TpBudgetType, AUTOMATIC, OS_VAR_NOINIT)) &OsCfg_Tp_Default_RTE_Mode_switch_Task_ExecutionBudget_Dyn,
    /* .BudgetKind = */ OS_TPMONITORINGTYPE_EXECUTION,
    /* .Budget     = */ 1000uL /* 1.0 sec */
  },
  /* .AllInterruptLockBudget = */
  {
    /* .Dyn        = */ (P2VAR(Os_TpBudgetType, AUTOMATIC, OS_VAR_NOINIT)) &OsCfg_Tp_Default_RTE_Mode_switch_Task_AllIntLockBudget_Dyn,
    /* .BudgetKind = */ OS_TPMONITORINGTYPE_LOCK,
    /* .Budget     = */ 1000uL /* 1.0 sec */
  },
  /* .OsInterruptLockBudget  = */
  {
    /* .Dyn        = */ (P2VAR(Os_TpBudgetType, AUTOMATIC, OS_VAR_NOINIT)) &OsCfg_Tp_Default_RTE_Mode_switch_Task_OsIntLockBudget_Dyn,
    /* .BudgetKind = */ OS_TPMONITORINGTYPE_LOCK,
    /* .Budget     = */ 1000uL /* 1.0 sec */
  },
  /* .ResourceLockBudgets    = */ OsCfg_Tp_OsCore0_EmptyResourceLockBudgets,
  /* .Dyn                    = */ &OsCfg_Tp_Default_RTE_Mode_switch_Task_Dyn
};

/*! Timing protection configuration data: IdleTask_OsCore0 */
CONST(Os_TpOwnerThreadConfigType, OS_CONST) OsCfg_Tp_IdleTask_OsCore0 =
{
  /* .Base                   = */
  {
    /* .ThreadKind = */ OS_TPTHREADKIND_OWNER
  },
  /* .TimeFrame              = */ ((Os_InterArrivalTimeType) 0), /* 0.0 sec */
  /* .ExecutionBudget        = */
  {
    /* .Dyn        = */ NULL_PTR,
    /* .BudgetKind = */ OS_TPMONITORINGTYPE_NONE,
    /* .Budget     = */ 0uL /* 0.0 sec */
  },
  /* .AllInterruptLockBudget = */
  {
    /* .Dyn        = */ NULL_PTR,
    /* .BudgetKind = */ OS_TPMONITORINGTYPE_NONE,
    /* .Budget     = */ 0uL /* 0.0 sec */
  },
  /* .OsInterruptLockBudget  = */
  {
    /* .Dyn        = */ NULL_PTR,
    /* .BudgetKind = */ OS_TPMONITORINGTYPE_NONE,
    /* .Budget     = */ 0uL /* 0.0 sec */
  },
  /* .ResourceLockBudgets    = */ OsCfg_Tp_OsCore0_EmptyResourceLockBudgets,
  /* .Dyn                    = */ &OsCfg_Tp_IdleTask_OsCore0_Dyn
};

/*! Timing protection configuration data: StartApplication_Appl_Init_Task */
CONST(Os_TpOwnerThreadConfigType, OS_CONST) OsCfg_Tp_StartApplication_Appl_Init_Task =
{
  /* .Base                   = */
  {
    /* .ThreadKind = */ OS_TPTHREADKIND_OWNER
  },
  /* .TimeFrame              = */ ((Os_InterArrivalTimeType) 0), /* 0.0 sec */
  /* .ExecutionBudget        = */
  {
    /* .Dyn        = */ NULL_PTR,
    /* .BudgetKind = */ OS_TPMONITORINGTYPE_NONE,
    /* .Budget     = */ 0uL /* 0.0 sec */
  },
  /* .AllInterruptLockBudget = */
  {
    /* .Dyn        = */ NULL_PTR,
    /* .BudgetKind = */ OS_TPMONITORINGTYPE_NONE,
    /* .Budget     = */ 0uL /* 0.0 sec */
  },
  /* .OsInterruptLockBudget  = */
  {
    /* .Dyn        = */ NULL_PTR,
    /* .BudgetKind = */ OS_TPMONITORINGTYPE_NONE,
    /* .Budget     = */ 0uL /* 0.0 sec */
  },
  /* .ResourceLockBudgets    = */ OsCfg_Tp_OsCore0_EmptyResourceLockBudgets,
  /* .Dyn                    = */ &OsCfg_Tp_StartApplication_Appl_Init_Task_Dyn
};

/*! Timing protection configuration data: StartApplication_Appl_Task */
CONST(Os_TpOwnerThreadConfigType, OS_CONST) OsCfg_Tp_StartApplication_Appl_Task =
{
  /* .Base                   = */
  {
    /* .ThreadKind = */ OS_TPTHREADKIND_OWNER
  },
  /* .TimeFrame              = */ ((Os_InterArrivalTimeType) 0), /* 0.0 sec */
  /* .ExecutionBudget        = */
  {
    /* .Dyn        = */ NULL_PTR,
    /* .BudgetKind = */ OS_TPMONITORINGTYPE_NONE,
    /* .Budget     = */ 0uL /* 0.0 sec */
  },
  /* .AllInterruptLockBudget = */
  {
    /* .Dyn        = */ NULL_PTR,
    /* .BudgetKind = */ OS_TPMONITORINGTYPE_NONE,
    /* .Budget     = */ 0uL /* 0.0 sec */
  },
  /* .OsInterruptLockBudget  = */
  {
    /* .Dyn        = */ NULL_PTR,
    /* .BudgetKind = */ OS_TPMONITORINGTYPE_NONE,
    /* .Budget     = */ 0uL /* 0.0 sec */
  },
  /* .ResourceLockBudgets    = */ OsCfg_Tp_OsCore0_EmptyResourceLockBudgets,
  /* .Dyn                    = */ &OsCfg_Tp_StartApplication_Appl_Task_Dyn
};

#define OS_STOP_SEC_CORE0_CONST_UNSPECIFIED
#include "Os_MemMap_OsSections.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */


#define OS_START_SEC_CONST_UNSPECIFIED
#include "Os_MemMap_OsSections.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

/*! Timing protection configuration data: EmptyThread */
CONST(Os_TpThreadConfigType, OS_CONST) OsCfg_Tp_EmptyThread =
{
    /* .ThreadKind = */ OS_TPTHREADKIND_HEIR
  };

#define OS_STOP_SEC_CONST_UNSPECIFIED
#include "Os_MemMap_OsSections.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */


/**********************************************************************************************************************
 *  LOCAL FUNCTION PROTOTYPES
 *********************************************************************************************************************/

/**********************************************************************************************************************
 *  LOCAL FUNCTIONS
 *********************************************************************************************************************/

/**********************************************************************************************************************
 *  GLOBAL FUNCTIONS
 *********************************************************************************************************************/


/**********************************************************************************************************************
 *  END OF FILE: Os_TimingProtection_Lcfg.c
 *********************************************************************************************************************/
