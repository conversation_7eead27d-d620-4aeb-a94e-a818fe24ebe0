/**********************************************************************************************************************
 *  COPYRIGHT
 *  -------------------------------------------------------------------------------------------------------------------
 *  \verbatim
 *
 *                 This software is copyright protected and proprietary to Vector Informatik GmbH.
 *                 Vector Informatik GmbH grants to you only those rights as set out in the license conditions.
 *                 All other rights remain with Vector Informatik GmbH.
 *  \endverbatim
 *  -------------------------------------------------------------------------------------------------------------------
 *  LICENSE
 *  -------------------------------------------------------------------------------------------------------------------
 *            Module: PduR
 *           Program: MSR_Vector_SLP4
 *          Customer: AUTOSAR 4 Evaluation Bundle
 *       Expiry Date: Not restricted
 *  Ordered Derivat.: Aurix 2G/TC377
 *    License Scope : The usage is restricted to CBD2000456_D02
 *
 *  -------------------------------------------------------------------------------------------------------------------
 *  FILE DESCRIPTION
 *  -------------------------------------------------------------------------------------------------------------------
 *              File: PduR_Lcfg.c
 *   Generation Time: 2025-08-05 10:37:17
 *           Project: Demo - Version 1.0
 *          Delivery: CBD2000456_D02
 *      Tool Version: DaVinci Configurator (beta) 5.21.40 SP2
 *
 *
 *********************************************************************************************************************/

/**********************************************************************************************************************
 ! BETA VERSION                                                                                                       !
 !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
 ! This version of DaVinci Configurator Pro and/or the related Basic Software Package is BETA software.               !
 ! BETA Software is basically operable, but not sufficiently tested, verified and/or qualified for use in series      !
 ! production and/or in vehicles operating on public or non-public roads.                                             !
 ! In particular, without limitation, BETA Software may cause unpredictable ECU behavior, may not provide all         !
 ! functions necessary for use in series production and/or may not comply with quality requirements which are         !
 ! necessary according to the state of the art. BETA Software must not be used in series production.                  !
 !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
**********************************************************************************************************************/

#define PDUR_LCFG_SOURCE

/**********************************************************************************************************************
 * MISRA / PCLINT JUSTIFICATION
 *********************************************************************************************************************/

/**********************************************************************************************************************
 * INCLUDES
 *********************************************************************************************************************/
#include "PduR.h"
#include "PduR_Fm.h"
#include "PduR_IFQ.h"
#include "PduR_Sm.h"
#include "PduR_RmIf.h"
#include "PduR_RmTp.h"
#include "PduR_RmTp_TxInst.h"
#include "SchM_PduR.h"
#if(PDUR_DEV_ERROR_REPORT == STD_ON)
# include "Det.h"
#endif
# include "PduR_Lcfg.h"



/* Include headers with callbacks */
#include "PduR_CanIf.h"
#include "PduR_CanNm.h"
#include "PduR_CanTp.h"
#include "PduR_Com.h"
#include "PduR_Dcm.h"
#include "PduR_FrIf.h"
#include "PduR_FrNm.h"
#include "PduR_LinIf.h"
#include "PduR_LinTp.h"
#include "PduR_CddLinTpStub.h"


/**********************************************************************************************************************
 * LOCAL CONSTANT MACROS
 *********************************************************************************************************************/

/**********************************************************************************************************************
 * LOCAL FUNCTION MACROS
 *********************************************************************************************************************/

/**********************************************************************************************************************
 * LOCAL DATA TYPES AND STRUCTURES
 *********************************************************************************************************************/

/**********************************************************************************************************************
 * LOCAL DATA PROTOTYPES
 *********************************************************************************************************************/

/**********************************************************************************************************************
 * LOCAL FUNCTION PROTOTYPES
 *********************************************************************************************************************/
/* Exclusive Area Wrapper functions */
static FUNC(void, PDUR_CODE) PduR_SchM_Enter_PduR_PDUR_EXCLUSIVE_AREA_0(void);
static FUNC(void, PDUR_CODE) PduR_SchM_Exit_PduR_PDUR_EXCLUSIVE_AREA_0(void);
  
/**********************************************************************************************************************
 * LOCAL DATA
 *********************************************************************************************************************/

/**********************************************************************************************************************
 * GLOBAL DATA
 *********************************************************************************************************************/
/* \trace SPEC-663, SPEC-661 */

/**********************************************************************************************************************
  CONFIGURATION CLASS: PRE_COMPILE
  SECTION: GLOBAL DATA
**********************************************************************************************************************/
/**********************************************************************************************************************
  PduR_ExclusiveAreaRom
**********************************************************************************************************************/
/** 
  \var    PduR_ExclusiveAreaRom
  \brief  PduR Exclusive Area Locks
  \details
  Element    Description
  Lock       Lock function
  Unlock     Unlock function
*/ 
#define PDUR_START_SEC_CONST_UNSPECIFIED
/*lint -save -esym(961, 19.1) */
#include "PduR_MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */
CONST(PduR_ExclusiveAreaRomType, PDUR_CONST) PduR_ExclusiveAreaRom[1] = {  /* PRQA S 1514, 1533 */  /* MD_CSL_ObjectOnlyAccessedOnce */
    /* Index    Lock                                        Unlock                                           Referable Keys */
  { /*     0 */ PduR_SchM_Enter_PduR_PDUR_EXCLUSIVE_AREA_0, PduR_SchM_Exit_PduR_PDUR_EXCLUSIVE_AREA_0 }   /* [/ActiveEcuC/PduR/PduRRoutingTables/PduRLock_PduRExclusiveArea] */
};
#define PDUR_STOP_SEC_CONST_UNSPECIFIED
/*lint -save -esym(961, 19.1) */
#include "PduR_MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */

/**********************************************************************************************************************
  PduR_GeneralPropertiesRom
**********************************************************************************************************************/
/** 
  \var    PduR_GeneralPropertiesRom
  \brief  General Properties Switches of the PduR.
  \details
  Element         Description
  hasIfRouting
*/ 
#define PDUR_START_SEC_CONST_UNSPECIFIED
/*lint -save -esym(961, 19.1) */
#include "PduR_MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */
CONST(PduR_GeneralPropertiesRomType, PDUR_CONST) PduR_GeneralPropertiesRom[1] = {  /* PRQA S 1514, 1533 */  /* MD_CSL_ObjectOnlyAccessedOnce */
    /* Index    hasIfRouting        Referable Keys */
  { /*     0 */         TRUE }   /* [/ActiveEcuC/PduR] */
};
#define PDUR_STOP_SEC_CONST_UNSPECIFIED
/*lint -save -esym(961, 19.1) */
#include "PduR_MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */

/**********************************************************************************************************************
  PduR_MmRom
**********************************************************************************************************************/
/** 
  \var    PduR_MmRom
  \brief  Module manager: Contains all function pointers of the bordering modules.
  \details
  Element                         Description
  DestApplicationManagerRomIdx    the index of the 1:1 relation pointing to PduR_DestApplicationManagerRom
  MaskedBits                      contains bitcoded the boolean data of PduR_IfCancelTransmitSupportedOfMmRom, PduR_LoIfOfMmRom, PduR_LoTpOfMmRom, PduR_RmGDestRomUsedOfMmRom, PduR_TpCancelTransmitSupportedOfMmRom, PduR_UpIfOfMmRom, PduR_UpTpOfMmRom
  RmGDestRomEndIdx                the end index of the 0:n relation pointing to PduR_RmGDestRom
  RmGDestRomStartIdx              the start index of the 0:n relation pointing to PduR_RmGDestRom
  LoIfCancelTransmitFctPtr        Lower layer cancel transmit function pointers.
  LoTpCancelTransmitFctPtr        Lower layer cancel transmit function pointers.
  UpTpCopyRxDataFctPtr            Transport protocol CopyRxData function pointers
  UpTpCopyTxDataFctPtr            Transport protocol CopyTxData function pointers
  UpIfRxIndicationFctPtr          Upper layer communication interface Rx indication function pointers.
  UpIfTxConfirmationFctPtr        Upper layer communication interface Tx confimation function pointers
  UpTpStartOfReceptionFctPtr      Transport protocol StartOfReception function pointers
  UpTpTpRxIndicationFctPtr        Transport protocol TpRxIndication function pointers
  UpTpTpTxConfirmationFctPtr      Transport protocol TpTxConfimation function pointers
  LoIfTransmitFctPtr              Lower layer If transmit function pointers
  LoTpTransmitFctPtr              Lower layer Tp transmit function pointers
  UpIfTriggerTransmitFctPtr       Upper layer trigger transmit function pointers
*/ 
#define PDUR_START_SEC_CONST_UNSPECIFIED
/*lint -save -esym(961, 19.1) */
#include "PduR_MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */
CONST(PduR_MmRomType, PDUR_CONST) PduR_MmRom[10] = {  /* PRQA S 1514, 1533 */  /* MD_CSL_ObjectOnlyAccessedOnce */
    /* Index    DestApplicationManagerRomIdx  MaskedBits  RmGDestRomEndIdx  RmGDestRomStartIdx  LoIfCancelTransmitFctPtr  LoTpCancelTransmitFctPtr  UpTpCopyRxDataFctPtr     UpTpCopyTxDataFctPtr     UpIfRxIndicationFctPtr  UpIfTxConfirmationFctPtr  UpTpStartOfReceptionFctPtr     UpTpTpRxIndicationFctPtr     UpTpTpTxConfirmationFctPtr     LoIfTransmitFctPtr  LoTpTransmitFctPtr  UpIfTriggerTransmitFctPtr        Comment                         Referable Keys */
  { /*     0 */                           0u,      0x68u,               6u,                 0u, CanIf_CancelTransmit    , NULL_PTR                , NULL_PTR               , NULL_PTR               , NULL_PTR              , NULL_PTR                , NULL_PTR                     , NULL_PTR                   , NULL_PTR                     , CanIf_Transmit    , NULL_PTR          , NULL_PTR                  },  /* [BswModule: CanIf]  */  /* [/ActiveEcuC/PduR/CanIf, /ActiveEcuC/PduR] */
  { /*     1 */                           0u,      0x28u,               7u,                 6u, NULL_PTR                , NULL_PTR                , NULL_PTR               , NULL_PTR               , NULL_PTR              , NULL_PTR                , NULL_PTR                     , NULL_PTR                   , NULL_PTR                     , CanNm_Transmit    , NULL_PTR          , NULL_PTR                  },  /* [BswModule: CanNm]  */  /* [/ActiveEcuC/PduR/CanNm, /ActiveEcuC/PduR] */
  { /*     2 */                           0u,      0x1Cu,               8u,                 7u, NULL_PTR                , CanTp_CancelTransmit    , NULL_PTR               , NULL_PTR               , NULL_PTR              , NULL_PTR                , NULL_PTR                     , NULL_PTR                   , NULL_PTR                     , NULL_PTR          , CanTp_Transmit    , NULL_PTR                  },  /* [BswModule: CanTp]  */  /* [/ActiveEcuC/PduR/CanTp, /ActiveEcuC/PduR] */
  { /*     3 */                           0u,      0x09u,              10u,                 8u, NULL_PTR                , NULL_PTR                , CddLinTpStub_CopyRxData, CddLinTpStub_CopyTxData, NULL_PTR              , NULL_PTR                , CddLinTpStub_StartOfReception, CddLinTpStub_TpRxIndication, CddLinTpStub_TpTxConfirmation, NULL_PTR          , NULL_PTR          , NULL_PTR                  },  /* [BswModule: CddLin] */  /* [/ActiveEcuC/PduR/CddLin, /ActiveEcuC/PduR] */
  { /*     4 */                           0u,      0x0Au,              22u,                10u, NULL_PTR                , NULL_PTR                , NULL_PTR               , NULL_PTR               , Com_RxIndication      , NULL_PTR                , NULL_PTR                     , NULL_PTR                   , NULL_PTR                     , NULL_PTR          , NULL_PTR          , Com_TriggerTransmit       },  /* [BswModule: Com]    */  /* [/ActiveEcuC/PduR/Com, /ActiveEcuC/PduR] */
  { /*     5 */                           0u,      0x0Bu,              24u,                22u, NULL_PTR                , NULL_PTR                , Dcm_CopyRxData         , Dcm_CopyTxData         , NULL_PTR              , Dcm_TxConfirmation      , Dcm_StartOfReception         , Dcm_TpRxIndication         , Dcm_TpTxConfirmation         , NULL_PTR          , NULL_PTR          , NULL_PTR                  },  /* [BswModule: Dcm]    */  /* [/ActiveEcuC/PduR/Dcm, /ActiveEcuC/PduR] */
  { /*     6 */                           0u,      0x28u,              30u,                24u, NULL_PTR                , NULL_PTR                , NULL_PTR               , NULL_PTR               , NULL_PTR              , NULL_PTR                , NULL_PTR                     , NULL_PTR                   , NULL_PTR                     , FrIf_Transmit     , NULL_PTR          , NULL_PTR                  },  /* [BswModule: FrIf]   */  /* [/ActiveEcuC/PduR/FrIf, /ActiveEcuC/PduR] */
  { /*     7 */                           0u,      0x28u,              31u,                30u, NULL_PTR                , NULL_PTR                , NULL_PTR               , NULL_PTR               , NULL_PTR              , NULL_PTR                , NULL_PTR                     , NULL_PTR                   , NULL_PTR                     , FrNm_Transmit     , NULL_PTR          , NULL_PTR                  },  /* [BswModule: FrNm]   */  /* [/ActiveEcuC/PduR/FrNm, /ActiveEcuC/PduR] */
  { /*     8 */                           0u,      0x28u,              33u,                31u, NULL_PTR                , NULL_PTR                , NULL_PTR               , NULL_PTR               , NULL_PTR              , NULL_PTR                , NULL_PTR                     , NULL_PTR                   , NULL_PTR                     , LinIf_Transmit    , NULL_PTR          , NULL_PTR                  },  /* [BswModule: LinIf]  */  /* [/ActiveEcuC/PduR/LinIf, /ActiveEcuC/PduR] */
  { /*     9 */                           0u,      0x18u,              36u,                33u, NULL_PTR                , NULL_PTR                , NULL_PTR               , NULL_PTR               , NULL_PTR              , NULL_PTR                , NULL_PTR                     , NULL_PTR                   , NULL_PTR                     , NULL_PTR          , LinTp_Transmit    , NULL_PTR                  }   /* [BswModule: LinTp]  */  /* [/ActiveEcuC/PduR/LinTp, /ActiveEcuC/PduR] */
};
#define PDUR_STOP_SEC_CONST_UNSPECIFIED
/*lint -save -esym(961, 19.1) */
#include "PduR_MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */

/**********************************************************************************************************************
  PduR_PartitionIdentifiers
**********************************************************************************************************************/
/** 
  \var    PduR_PartitionIdentifiers
  \brief  the partition context in Config
  \details
  Element         Description
  PartitionSNV
*/ 
#define PDUR_START_SEC_CONST_UNSPECIFIED
/*lint -save -esym(961, 19.1) */
#include "PduR_MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */
CONST(PduR_PartitionIdentifiersType, PDUR_CONST) PduR_PartitionIdentifiers[1] = {  /* PRQA S 1514, 1533 */  /* MD_CSL_ObjectOnlyAccessedOnce */
    /* Index    PartitionSNV       */
  { /*     0 */ CommonSharedMemory }
};
#define PDUR_STOP_SEC_CONST_UNSPECIFIED
/*lint -save -esym(961, 19.1) */
#include "PduR_MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */

/**********************************************************************************************************************
  PduR_RmDestRom
**********************************************************************************************************************/
/** 
  \var    PduR_RmDestRom
  \brief  PduR RoutiongPathManager destPdu Table
  \details
  Element                      Description
  PartitionIndexOfCsl      
  PduLengthHandlingStrategy    The strategy how larger than configured If Pdus are handled.
  RmGDestRomIdx                the index of the 1:1 relation pointing to PduR_RmGDestRom
  RmSrcRomIdx                  the index of the 1:1 relation pointing to PduR_RmSrcRom
  RoutingType                  Type of the Routing (API Forwarding, Gateway).
*/ 
#define PDUR_START_SEC_CONST_UNSPECIFIED
/*lint -save -esym(961, 19.1) */
#include "PduR_MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */
CONST(PduR_RmDestRomType, PDUR_CONST) PduR_RmDestRom[36] = {  /* PRQA S 1514, 1533 */  /* MD_CSL_ObjectOnlyAccessedOnce */
    /* Index    PartitionIndexOfCsl                            PduLengthHandlingStrategy                         RmGDestRomIdx  RmSrcRomIdx  RoutingType                                                 Comment                                                                              Referable Keys */
  { /*     0 */                  0u  /* CommonSharedMemory */, PDUR_IGNORE_PDULENGTHHANDLINGSTRATEGYOFRMDESTROM,           16u,          6u, PDUR_IF_UNBUFFERED_RX_API_FWD_ROUTINGTYPEOFRMDESTROM },  /* [PduRDestPdu: msg_RxCycle_E2eProf1C_500_10_oCAN_23e9789c_Rx_01c8980f_Rx] */  /* [/ActiveEcuC/PduR/PduRRoutingTables/PduRRoutingTable/msg_RxCycle_E2eProf1C_500_10_oCAN_23e9789c_Rx/PduRSrcPdu_01c8980f, /ActiveEcuC/PduR/PduRRoutingTables/PduRRoutingTable/msg_RxCycle_E2eProf1C_500_10_oCAN_23e9789c_Rx/msg_RxCycle_E2eProf1C_500_10_oCAN_23e9789c_Rx_01c8980f_Rx] */
  { /*     1 */                  0u  /* CommonSharedMemory */, PDUR_IGNORE_PDULENGTHHANDLINGSTRATEGYOFRMDESTROM,           31u,         16u, PDUR_IF_UNBUFFERED_TX_API_FWD_ROUTINGTYPEOFRMDESTROM },  /* [PduRDestPdu: Frame_LinTr_MyECU2_oLIN00_a05ec237_Tx]                     */  /* [/ActiveEcuC/PduR/PduRRoutingTables/PduRRoutingTable/Frame_LinTr_MyECU2_oLIN00_3a9f0ce6_Tx/PduRSrcPdu_12782a39, /ActiveEcuC/PduR/PduRRoutingTables/PduRRoutingTable/Frame_LinTr_MyECU2_oLIN00_3a9f0ce6_Tx/Frame_LinTr_MyECU2_oLIN00_a05ec237_Tx] */
  { /*     2 */                  0u  /* CommonSharedMemory */, PDUR_IGNORE_PDULENGTHHANDLINGSTRATEGYOFRMDESTROM,           29u,         35u, PDUR_IF_UNBUFFERED_TX_API_FWD_ROUTINGTYPEOFRMDESTROM },  /* [PduRDestPdu: pdu_TxStat_64_2f41aa7f_Tx]                                 */  /* [/ActiveEcuC/PduR/PduRRoutingTables/PduRRoutingTable/pdu_TxStat_64_519c4828_Tx/PduRSrcPdu_15da8b7f, /ActiveEcuC/PduR/PduRRoutingTables/PduRRoutingTable/pdu_TxStat_64_519c4828_Tx/pdu_TxStat_64_2f41aa7f_Tx] */
  { /*     3 */                  0u  /* CommonSharedMemory */, PDUR_IGNORE_PDULENGTHHANDLINGSTRATEGYOFRMDESTROM,           20u,         10u, PDUR_IF_UNBUFFERED_RX_API_FWD_ROUTINGTYPEOFRMDESTROM },  /* [PduRDestPdu: pdu_RxStat_10_51092b83_Rx_2bf83137_Rx]                     */  /* [/ActiveEcuC/PduR/PduRRoutingTables/PduRRoutingTable/pdu_RxStat_10_51092b83_Rx/PduRSrcPdu_2bf83137, /ActiveEcuC/PduR/PduRRoutingTables/PduRRoutingTable/pdu_RxStat_10_51092b83_Rx/pdu_RxStat_10_51092b83_Rx_2bf83137_Rx] */
  { /*     4 */                  0u  /* CommonSharedMemory */, PDUR_IGNORE_PDULENGTHHANDLINGSTRATEGYOFRMDESTROM,            2u,         25u, PDUR_IF_UNBUFFERED_TX_API_FWD_ROUTINGTYPEOFRMDESTROM },  /* [PduRDestPdu: msg_TxCycle10_0_oCAN_2d7b6a87_Tx]                          */  /* [/ActiveEcuC/PduR/PduRRoutingTables/PduRRoutingTable/msg_TxCycle10_0_oCAN_85bf3e37_Tx/PduRSrcPdu_2e41d513, /ActiveEcuC/PduR/PduRRoutingTables/PduRRoutingTable/msg_TxCycle10_0_oCAN_85bf3e37_Tx/msg_TxCycle10_0_oCAN_2d7b6a87_Tx] */
  { /*     5 */                  0u  /* CommonSharedMemory */, PDUR_IGNORE_PDULENGTHHANDLINGSTRATEGYOFRMDESTROM,            4u,         28u, PDUR_IF_UNBUFFERED_TX_API_FWD_ROUTINGTYPEOFRMDESTROM },  /* [PduRDestPdu: msg_TxEvent_10_oCAN_b9443fef_Tx]                           */  /* [/ActiveEcuC/PduR/PduRRoutingTables/PduRRoutingTable/msg_TxEvent_10_oCAN_b2cd4fc2_Tx/PduRSrcPdu_36788591, /ActiveEcuC/PduR/PduRRoutingTables/PduRRoutingTable/msg_TxEvent_10_oCAN_b2cd4fc2_Tx/msg_TxEvent_10_oCAN_b9443fef_Tx] */
  { /*     6 */                  0u  /* CommonSharedMemory */, PDUR_IGNORE_PDULENGTHHANDLINGSTRATEGYOFRMDESTROM,           17u,          7u, PDUR_IF_UNBUFFERED_RX_API_FWD_ROUTINGTYPEOFRMDESTROM },  /* [PduRDestPdu: msg_RxEvent_20_oCAN_13517c6b_Rx_37e6280b_Rx]               */  /* [/ActiveEcuC/PduR/PduRRoutingTables/PduRRoutingTable/msg_RxEvent_20_oCAN_13517c6b_Rx/PduRSrcPdu_37e6280b, /ActiveEcuC/PduR/PduRRoutingTables/PduRRoutingTable/msg_RxEvent_20_oCAN_13517c6b_Rx/msg_RxEvent_20_oCAN_13517c6b_Rx_37e6280b_Rx] */
  { /*     7 */                  0u  /* CommonSharedMemory */, PDUR_IGNORE_PDULENGTHHANDLINGSTRATEGYOFRMDESTROM,           26u,         32u, PDUR_IF_UNBUFFERED_TX_API_FWD_ROUTINGTYPEOFRMDESTROM },  /* [PduRDestPdu: pdu_TxDyn_16_44389cbf_Tx]                                  */  /* [/ActiveEcuC/PduR/PduRRoutingTables/PduRRoutingTable/pdu_TxDyn_16_3c646bcf_Tx/PduRSrcPdu_3920f728, /ActiveEcuC/PduR/PduRRoutingTables/PduRRoutingTable/pdu_TxDyn_16_3c646bcf_Tx/pdu_TxDyn_16_44389cbf_Tx] */
  { /*     8 */                  0u  /* CommonSharedMemory */, PDUR_IGNORE_PDULENGTHHANDLINGSTRATEGYOFRMDESTROM,           19u,          9u, PDUR_IF_UNBUFFERED_RX_API_FWD_ROUTINGTYPEOFRMDESTROM },  /* [PduRDestPdu: pdu_RxDyn_64_600910d1_Rx_45b853db_Rx]                      */  /* [/ActiveEcuC/PduR/PduRRoutingTables/PduRRoutingTable/pdu_RxDyn_64_600910d1_Rx/PduRSrcPdu_45b853db, /ActiveEcuC/PduR/PduRRoutingTables/PduRRoutingTable/pdu_RxDyn_64_600910d1_Rx/pdu_RxDyn_64_600910d1_Rx_45b853db_Rx] */
  { /*     9 */                  0u  /* CommonSharedMemory */, PDUR_IGNORE_PDULENGTHHANDLINGSTRATEGYOFRMDESTROM,           12u,          2u, PDUR_IF_UNBUFFERED_RX_API_FWD_ROUTINGTYPEOFRMDESTROM },  /* [PduRDestPdu: PDU_Dummy_RearECU_9177c4f3_Rx_5699e50b_Rx]                 */  /* [/ActiveEcuC/PduR/PduRRoutingTables/PduRRoutingTable/PDU_Dummy_RearECU_9177c4f3_Rx/PduRSrcPdu_5699e50b, /ActiveEcuC/PduR/PduRRoutingTables/PduRRoutingTable/PDU_Dummy_RearECU_9177c4f3_Rx/PDU_Dummy_RearECU_9177c4f3_Rx_5699e50b_Rx] */
  { /*    10 */                  0u  /* CommonSharedMemory */, PDUR_IGNORE_PDULENGTHHANDLINGSTRATEGYOFRMDESTROM,            6u,         31u, PDUR_IF_UNBUFFERED_TX_API_FWD_ROUTINGTYPEOFRMDESTROM },  /* [PduRDestPdu: msg_nm_MyECU_oCAN_c97b60cc_Tx_616fda32_Tx]                 */  /* [/ActiveEcuC/PduR/PduRRoutingTables/PduRRoutingTable/msg_nm_MyECU_oCAN_c97b60cc_Tx/PduRSrcPdu_616fda32, /ActiveEcuC/PduR/PduRRoutingTables/PduRRoutingTable/msg_nm_MyECU_oCAN_c97b60cc_Tx/msg_nm_MyECU_oCAN_c97b60cc_Tx_616fda32_Tx] */
  { /*    11 */                  0u  /* CommonSharedMemory */, PDUR_IGNORE_PDULENGTHHANDLINGSTRATEGYOFRMDESTROM,            1u,         26u, PDUR_IF_UNBUFFERED_TX_API_FWD_ROUTINGTYPEOFRMDESTROM },  /* [PduRDestPdu: msg_TxCycle1000_10_oCAN_6dd6f284_Tx]                       */  /* [/ActiveEcuC/PduR/PduRRoutingTables/PduRRoutingTable/msg_TxCycle1000_10_oCAN_d74aed68_Tx/PduRSrcPdu_704563f1, /ActiveEcuC/PduR/PduRRoutingTables/PduRRoutingTable/msg_TxCycle1000_10_oCAN_d74aed68_Tx/msg_TxCycle1000_10_oCAN_6dd6f284_Tx] */
  { /*    12 */                  0u  /* CommonSharedMemory */, PDUR_UNUSED_PDULENGTHHANDLINGSTRATEGYOFRMDESTROM,            9u,         13u, PDUR_TP_UNBUFFERED_RX_API_FWD_ROUTINGTYPEOFRMDESTROM },  /* [PduRDestPdu: SlaveResp_Slave3_oLIN00_6f91e3ac_Rx_74fe1f87_Rx]           */  /* [/ActiveEcuC/PduR/PduRRoutingTables/PduRRoutingTable/SlaveResp_Slave3_oLIN00_6f91e3ac_Rx/PduRSrcPdu_74fe1f87, /ActiveEcuC/PduR/PduRRoutingTables/PduRRoutingTable/SlaveResp_Slave3_oLIN00_6f91e3ac_Rx/SlaveResp_Slave3_oLIN00_6f91e3ac_Rx_74fe1f87_Rx] */
  { /*    13 */                  0u  /* CommonSharedMemory */, PDUR_IGNORE_PDULENGTHHANDLINGSTRATEGYOFRMDESTROM,           32u,         17u, PDUR_IF_UNBUFFERED_TX_API_FWD_ROUTINGTYPEOFRMDESTROM },  /* [PduRDestPdu: Frame_LinTr_MyECU_oLIN00_392a06a5_Tx]                      */  /* [/ActiveEcuC/PduR/PduRRoutingTables/PduRRoutingTable/Frame_LinTr_MyECU_oLIN00_ce6095e4_Tx/PduRSrcPdu_783591af, /ActiveEcuC/PduR/PduRRoutingTables/PduRRoutingTable/Frame_LinTr_MyECU_oLIN00_ce6095e4_Tx/Frame_LinTr_MyECU_oLIN00_392a06a5_Tx] */
  { /*    14 */                  0u  /* CommonSharedMemory */, PDUR_IGNORE_PDULENGTHHANDLINGSTRATEGYOFRMDESTROM,           18u,          8u, PDUR_IF_UNBUFFERED_RX_API_FWD_ROUTINGTYPEOFRMDESTROM },  /* [PduRDestPdu: msg_StartAppl_Rx_MyECU_oCAN_fe93a56e_Rx_7a86d966_Rx]       */  /* [/ActiveEcuC/PduR/PduRRoutingTables/PduRRoutingTable/msg_StartAppl_Rx_MyECU_oCAN_fe93a56e_Rx/PduRSrcPdu_7a86d966, /ActiveEcuC/PduR/PduRRoutingTables/PduRRoutingTable/msg_StartAppl_Rx_MyECU_oCAN_fe93a56e_Rx/msg_StartAppl_Rx_MyECU_oCAN_fe93a56e_Rx_7a86d966_Rx] */
  { /*    15 */                  0u  /* CommonSharedMemory */, PDUR_IGNORE_PDULENGTHHANDLINGSTRATEGYOFRMDESTROM,           10u,          0u, PDUR_IF_UNBUFFERED_RX_API_FWD_ROUTINGTYPEOFRMDESTROM },  /* [PduRDestPdu: Frame_LinTr_RearECU_oLIN00_64640cc1_Rx_87852900_Rx]        */  /* [/ActiveEcuC/PduR/PduRRoutingTables/PduRRoutingTable/Frame_LinTr_RearECU_oLIN00_64640cc1_Rx/PduRSrcPdu_87852900, /ActiveEcuC/PduR/PduRRoutingTables/PduRRoutingTable/Frame_LinTr_RearECU_oLIN00_64640cc1_Rx/Frame_LinTr_RearECU_oLIN00_64640cc1_Rx_87852900_Rx] */
  { /*    16 */                  0u  /* CommonSharedMemory */, PDUR_UNUSED_PDULENGTHHANDLINGSTRATEGYOFRMDESTROM,           33u,         18u, PDUR_TP_UNBUFFERED_TX_API_FWD_ROUTINGTYPEOFRMDESTROM },  /* [PduRDestPdu: MasterReq_RearECU_oLIN00_a4a697e2_Tx]                      */  /* [/ActiveEcuC/PduR/PduRRoutingTables/PduRRoutingTable/MasterReq_RearECU_oLIN00_8ed7799b_Tx/PduRSrcPdu_88fade16, /ActiveEcuC/PduR/PduRRoutingTables/PduRRoutingTable/MasterReq_RearECU_oLIN00_8ed7799b_Tx/MasterReq_RearECU_oLIN00_a4a697e2_Tx] */
  { /*    17 */                  0u  /* CommonSharedMemory */, PDUR_IGNORE_PDULENGTHHANDLINGSTRATEGYOFRMDESTROM,           25u,         22u, PDUR_IF_UNBUFFERED_TX_API_FWD_ROUTINGTYPEOFRMDESTROM },  /* [PduRDestPdu: PDU_Transmit_MyECU_dcbaa590_Tx]                            */  /* [/ActiveEcuC/PduR/PduRRoutingTables/PduRRoutingTable/PDU_Transmit_MyECU_05398c7a_Tx/PduRSrcPdu_8c1e0786, /ActiveEcuC/PduR/PduRRoutingTables/PduRRoutingTable/PDU_Transmit_MyECU_05398c7a_Tx/PDU_Transmit_MyECU_dcbaa590_Tx] */
  { /*    18 */                  0u  /* CommonSharedMemory */, PDUR_IGNORE_PDULENGTHHANDLINGSTRATEGYOFRMDESTROM,           11u,          1u, PDUR_IF_UNBUFFERED_RX_API_FWD_ROUTINGTYPEOFRMDESTROM },  /* [PduRDestPdu: Frame_LinTr_Slave3_oLIN00_eb2bd0ab_Rx_8cf2ea35_Rx]         */  /* [/ActiveEcuC/PduR/PduRRoutingTables/PduRRoutingTable/Frame_LinTr_Slave3_oLIN00_eb2bd0ab_Rx/PduRSrcPdu_8cf2ea35, /ActiveEcuC/PduR/PduRRoutingTables/PduRRoutingTable/Frame_LinTr_Slave3_oLIN00_eb2bd0ab_Rx/Frame_LinTr_Slave3_oLIN00_eb2bd0ab_Rx_8cf2ea35_Rx] */
  { /*    19 */                  0u  /* CommonSharedMemory */, PDUR_UNUSED_PDULENGTHHANDLINGSTRATEGYOFRMDESTROM,           35u,         20u, PDUR_TP_UNBUFFERED_TX_API_FWD_ROUTINGTYPEOFRMDESTROM },  /* [PduRDestPdu: MasterReq_oLIN00_3234fe1b_Tx]                              */  /* [/ActiveEcuC/PduR/PduRRoutingTables/PduRRoutingTable/MasterReq_oLIN00_4a2bb011_Tx/PduRSrcPdu_927d3065, /ActiveEcuC/PduR/PduRRoutingTables/PduRRoutingTable/MasterReq_oLIN00_4a2bb011_Tx/MasterReq_oLIN00_3234fe1b_Tx] */
  { /*    20 */                  0u  /* CommonSharedMemory */, PDUR_IGNORE_PDULENGTHHANDLINGSTRATEGYOFRMDESTROM,           21u,         11u, PDUR_IF_UNBUFFERED_RX_API_FWD_ROUTINGTYPEOFRMDESTROM },  /* [PduRDestPdu: pdu_RxStat_30_25bd2d72_Rx_9474dc2d_Rx]                     */  /* [/ActiveEcuC/PduR/PduRRoutingTables/PduRRoutingTable/pdu_RxStat_30_25bd2d72_Rx/PduRSrcPdu_9474dc2d, /ActiveEcuC/PduR/PduRRoutingTables/PduRRoutingTable/pdu_RxStat_30_25bd2d72_Rx/pdu_RxStat_30_25bd2d72_Rx_9474dc2d_Rx] */
  { /*    21 */                  0u  /* CommonSharedMemory */, PDUR_IGNORE_PDULENGTHHANDLINGSTRATEGYOFRMDESTROM,           14u,          4u, PDUR_IF_UNBUFFERED_RX_API_FWD_ROUTINGTYPEOFRMDESTROM },  /* [PduRDestPdu: msg_RxCycle100_0_oCAN_c71398f9_Rx_9e00b2d3_Rx]             */  /* [/ActiveEcuC/PduR/PduRRoutingTables/PduRRoutingTable/msg_RxCycle100_0_oCAN_c71398f9_Rx/PduRSrcPdu_9e00b2d3, /ActiveEcuC/PduR/PduRRoutingTables/PduRRoutingTable/msg_RxCycle100_0_oCAN_c71398f9_Rx/msg_RxCycle100_0_oCAN_c71398f9_Rx_9e00b2d3_Rx] */
  { /*    22 */                  0u  /* CommonSharedMemory */, PDUR_UNUSED_PDULENGTHHANDLINGSTRATEGYOFRMDESTROM,           23u,         15u, PDUR_TP_UNBUFFERED_RX_API_FWD_ROUTINGTYPEOFRMDESTROM },  /* [PduRDestPdu: msg_diag_Request_MyECU_oCAN_ca029ee7_Rx_9fd2cbe7_Rx]       */  /* [/ActiveEcuC/PduR/PduRRoutingTables/PduRRoutingTable/msg_diag_Request_MyECU_oCAN_ca029ee7_Rx/PduRSrcPdu_9fd2cbe7, /ActiveEcuC/PduR/PduRRoutingTables/PduRRoutingTable/msg_diag_Request_MyECU_oCAN_ca029ee7_Rx/msg_diag_Request_MyECU_oCAN_ca029ee7_Rx_9fd2cbe7_Rx] */
  { /*    23 */                  0u  /* CommonSharedMemory */, PDUR_IGNORE_PDULENGTHHANDLINGSTRATEGYOFRMDESTROM,           28u,         34u, PDUR_IF_UNBUFFERED_TX_API_FWD_ROUTINGTYPEOFRMDESTROM },  /* [PduRDestPdu: pdu_TxStat_40_505d7b88_Tx]                                 */  /* [/ActiveEcuC/PduR/PduRRoutingTables/PduRRoutingTable/pdu_TxStat_40_64c7eeb3_Tx/PduRSrcPdu_a7760f35, /ActiveEcuC/PduR/PduRRoutingTables/PduRRoutingTable/pdu_TxStat_40_64c7eeb3_Tx/pdu_TxStat_40_505d7b88_Tx] */
  { /*    24 */                  0u  /* CommonSharedMemory */, PDUR_IGNORE_PDULENGTHHANDLINGSTRATEGYOFRMDESTROM,           27u,         33u, PDUR_IF_UNBUFFERED_TX_API_FWD_ROUTINGTYPEOFRMDESTROM },  /* [PduRDestPdu: pdu_TxDyn_64_0bd27c77_Tx]                                  */  /* [/ActiveEcuC/PduR/PduRRoutingTables/PduRRoutingTable/pdu_TxDyn_64_9d2b9c24_Tx/PduRSrcPdu_ab04820d, /ActiveEcuC/PduR/PduRRoutingTables/PduRRoutingTable/pdu_TxDyn_64_9d2b9c24_Tx/pdu_TxDyn_64_0bd27c77_Tx] */
  { /*    25 */                  0u  /* CommonSharedMemory */, PDUR_IGNORE_PDULENGTHHANDLINGSTRATEGYOFRMDESTROM,           15u,          5u, PDUR_IF_UNBUFFERED_RX_API_FWD_ROUTINGTYPEOFRMDESTROM },  /* [PduRDestPdu: msg_RxCycle500_20_oCAN_266969e8_Rx_b1d1dd8a_Rx]            */  /* [/ActiveEcuC/PduR/PduRRoutingTables/PduRRoutingTable/msg_RxCycle500_20_oCAN_266969e8_Rx/PduRSrcPdu_b1d1dd8a, /ActiveEcuC/PduR/PduRRoutingTables/PduRRoutingTable/msg_RxCycle500_20_oCAN_266969e8_Rx/msg_RxCycle500_20_oCAN_266969e8_Rx_b1d1dd8a_Rx] */
  { /*    26 */                  0u  /* CommonSharedMemory */, PDUR_IGNORE_PDULENGTHHANDLINGSTRATEGYOFRMDESTROM,           13u,          3u, PDUR_IF_UNBUFFERED_RX_API_FWD_ROUTINGTYPEOFRMDESTROM },  /* [PduRDestPdu: PDU_Fr_StartAppl_BothECU_RX_7a67d8c4_Rx_b2f650ce_Rx]       */  /* [/ActiveEcuC/PduR/PduRRoutingTables/PduRRoutingTable/PDU_Fr_StartAppl_BothECU_RX_7a67d8c4_Rx/PduRSrcPdu_b2f650ce, /ActiveEcuC/PduR/PduRRoutingTables/PduRRoutingTable/PDU_Fr_StartAppl_BothECU_RX_7a67d8c4_Rx/PDU_Fr_StartAppl_BothECU_RX_7a67d8c4_Rx_b2f650ce_Rx] */
  { /*    27 */                  0u  /* CommonSharedMemory */, PDUR_UNUSED_PDULENGTHHANDLINGSTRATEGYOFRMDESTROM,           34u,         19u, PDUR_TP_UNBUFFERED_TX_API_FWD_ROUTINGTYPEOFRMDESTROM },  /* [PduRDestPdu: MasterReq_Slave3_oLIN00_93dc5ed4_Tx]                       */  /* [/ActiveEcuC/PduR/PduRRoutingTables/PduRRoutingTable/MasterReq_Slave3_oLIN00_a4cffd2e_Tx/PduRSrcPdu_b376d1ba, /ActiveEcuC/PduR/PduRRoutingTables/PduRRoutingTable/MasterReq_Slave3_oLIN00_a4cffd2e_Tx/MasterReq_Slave3_oLIN00_93dc5ed4_Tx] */
  { /*    28 */                  0u  /* CommonSharedMemory */, PDUR_IGNORE_PDULENGTHHANDLINGSTRATEGYOFRMDESTROM,            3u,         27u, PDUR_IF_UNBUFFERED_TX_API_FWD_ROUTINGTYPEOFRMDESTROM },  /* [PduRDestPdu: msg_TxCycle_E2eProf1C_500_30_oCAN_995018f6_Tx]             */  /* [/ActiveEcuC/PduR/PduRRoutingTables/PduRRoutingTable/msg_TxCycle_E2eProf1C_500_30_oCAN_eeefc687_Tx/PduRSrcPdu_c3d2922e, /ActiveEcuC/PduR/PduRRoutingTables/PduRRoutingTable/msg_TxCycle_E2eProf1C_500_30_oCAN_eeefc687_Tx/msg_TxCycle_E2eProf1C_500_30_oCAN_995018f6_Tx] */
  { /*    29 */                  0u  /* CommonSharedMemory */, PDUR_IGNORE_PDULENGTHHANDLINGSTRATEGYOFRMDESTROM,           30u,         23u, PDUR_IF_UNBUFFERED_TX_API_FWD_ROUTINGTYPEOFRMDESTROM },  /* [PduRDestPdu: PDU_nm_MyECU_Fr_ae963333_Tx_c844becc_Tx]                   */  /* [/ActiveEcuC/PduR/PduRRoutingTables/PduRRoutingTable/PDU_nm_MyECU_Fr_ae963333_Tx/PduRSrcPdu_c844becc, /ActiveEcuC/PduR/PduRRoutingTables/PduRRoutingTable/PDU_nm_MyECU_Fr_ae963333_Tx/PDU_nm_MyECU_Fr_ae963333_Tx_c844becc_Tx] */
  { /*    30 */                  0u  /* CommonSharedMemory */, PDUR_UNUSED_PDULENGTHHANDLINGSTRATEGYOFRMDESTROM,           22u,         14u, PDUR_TP_UNBUFFERED_RX_API_FWD_ROUTINGTYPEOFRMDESTROM },  /* [PduRDestPdu: msg_diag_RequestGlobal_oCAN_e06c7e2d_Rx_c958f031_Rx]       */  /* [/ActiveEcuC/PduR/PduRRoutingTables/PduRRoutingTable/msg_diag_RequestGlobal_oCAN_e06c7e2d_Rx/PduRSrcPdu_c958f031, /ActiveEcuC/PduR/PduRRoutingTables/PduRRoutingTable/msg_diag_RequestGlobal_oCAN_e06c7e2d_Rx/msg_diag_RequestGlobal_oCAN_e06c7e2d_Rx_c958f031_Rx] */
  { /*    31 */                  0u  /* CommonSharedMemory */, PDUR_IGNORE_PDULENGTHHANDLINGSTRATEGYOFRMDESTROM,            0u,         24u, PDUR_IF_UNBUFFERED_TX_API_FWD_ROUTINGTYPEOFRMDESTROM },  /* [PduRDestPdu: msg_StartAppl_Tx_MyECU_oCAN_c14d78df_Tx]                   */  /* [/ActiveEcuC/PduR/PduRRoutingTables/PduRRoutingTable/msg_StartAppl_Tx_MyECU_oCAN_ad461e3f_Tx/PduRSrcPdu_cd5f4416, /ActiveEcuC/PduR/PduRRoutingTables/PduRRoutingTable/msg_StartAppl_Tx_MyECU_oCAN_ad461e3f_Tx/msg_StartAppl_Tx_MyECU_oCAN_c14d78df_Tx] */
  { /*    32 */                  0u  /* CommonSharedMemory */, PDUR_IGNORE_PDULENGTHHANDLINGSTRATEGYOFRMDESTROM,           24u,         21u, PDUR_IF_UNBUFFERED_TX_API_FWD_ROUTINGTYPEOFRMDESTROM },  /* [PduRDestPdu: PDU_Fr_StartAppl_MyECU_TX_161591f9_Tx]                     */  /* [/ActiveEcuC/PduR/PduRRoutingTables/PduRRoutingTable/PDU_Fr_StartAppl_MyECU_TX_44b0b044_Tx/PduRSrcPdu_d1717db2, /ActiveEcuC/PduR/PduRRoutingTables/PduRRoutingTable/PDU_Fr_StartAppl_MyECU_TX_44b0b044_Tx/PDU_Fr_StartAppl_MyECU_TX_161591f9_Tx] */
  { /*    33 */                  0u  /* CommonSharedMemory */, PDUR_UNUSED_PDULENGTHHANDLINGSTRATEGYOFRMDESTROM,            8u,         12u, PDUR_TP_UNBUFFERED_RX_API_FWD_ROUTINGTYPEOFRMDESTROM },  /* [PduRDestPdu: SlaveResp_RearECU_oLIN00_fa842351_Rx_e930d1d3_Rx]          */  /* [/ActiveEcuC/PduR/PduRRoutingTables/PduRRoutingTable/SlaveResp_RearECU_oLIN00_fa842351_Rx/PduRSrcPdu_e930d1d3, /ActiveEcuC/PduR/PduRRoutingTables/PduRRoutingTable/SlaveResp_RearECU_oLIN00_fa842351_Rx/SlaveResp_RearECU_oLIN00_fa842351_Rx_e930d1d3_Rx] */
  { /*    34 */                  0u  /* CommonSharedMemory */, PDUR_IGNORE_PDULENGTHHANDLINGSTRATEGYOFRMDESTROM,            5u,         30u, PDUR_IF_UNBUFFERED_TX_API_FWD_ROUTINGTYPEOFRMDESTROM },  /* [PduRDestPdu: msg_diag_Uudt_Response_MyECU_oCAN_e0fa8c3a_Tx]             */  /* [/ActiveEcuC/PduR/PduRRoutingTables/PduRRoutingTable/msg_diag_Uudt_Response_MyECU_oCAN_f130d337_Tx/PduRSrcPdu_ec58e685, /ActiveEcuC/PduR/PduRRoutingTables/PduRRoutingTable/msg_diag_Uudt_Response_MyECU_oCAN_f130d337_Tx/msg_diag_Uudt_Response_MyECU_oCAN_e0fa8c3a_Tx] */
  { /*    35 */                  0u  /* CommonSharedMemory */, PDUR_UNUSED_PDULENGTHHANDLINGSTRATEGYOFRMDESTROM,            7u,         29u, PDUR_TP_UNBUFFERED_TX_API_FWD_ROUTINGTYPEOFRMDESTROM }   /* [PduRDestPdu: msg_diag_Response_MyECU_oCAN_84acb98b_Tx]                  */  /* [/ActiveEcuC/PduR/PduRRoutingTables/PduRRoutingTable/msg_diag_Response_MyECU_oCAN_06426912_Tx/PduRSrcPdu_f3446321, /ActiveEcuC/PduR/PduRRoutingTables/PduRRoutingTable/msg_diag_Response_MyECU_oCAN_06426912_Tx/msg_diag_Response_MyECU_oCAN_84acb98b_Tx] */
};
#define PDUR_STOP_SEC_CONST_UNSPECIFIED
/*lint -save -esym(961, 19.1) */
#include "PduR_MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */

/**********************************************************************************************************************
  PduR_RmGDestRom
**********************************************************************************************************************/
/** 
  \var    PduR_RmGDestRom
  \brief  PduR RoutingPathManager global destPdu Table
  \details
  Element                Description
  DestHnd                handle to be used as parameter for the StartOfReception, CopyRxData, Transmit or RxIndication function call.
  Direction              Direction of this Pdu: Rx or Tx
  MaxPduLength           Configured PduLength + metadata length.
  MmRomIdx               the index of the 1:1 relation pointing to PduR_MmRom
  PartitionIndexOfCsl
  RmDestRomIdx           the index of the 1:1 relation pointing to PduR_RmDestRom
*/ 
#define PDUR_START_SEC_CONST_UNSPECIFIED
/*lint -save -esym(961, 19.1) */
#include "PduR_MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */
CONST(PduR_RmGDestRomType, PDUR_CONST) PduR_RmGDestRom[36] = {  /* PRQA S 1514, 1533 */  /* MD_CSL_ObjectOnlyAccessedOnce */
    /* Index    DestHnd                                                                       Direction                      MaxPduLength  MmRomIdx  PartitionIndexOfCsl                            RmDestRomIdx        Comment                                                                         Referable Keys */
  { /*     0 */              CanIfConf_CanIfTxPduCfg_msg_StartAppl_Tx_MyECU_oCAN_b6f80cd2_Tx, PDUR_TX_DIRECTIONOFRMGDESTROM,          64u,       0u,                  0u  /* CommonSharedMemory */,          31u },  /* [Global PduRDestPdu: msg_StartAppl_Tx_MyECU_oCAN_c14d78df_Tx]       */  /* [/ActiveEcuC/EcuC/EcucPduCollection/msg_StartAppl_Tx_MyECU_oCAN_c14d78df_Tx, /ActiveEcuC/PduR/CanIf] */
  { /*     1 */                  CanIfConf_CanIfTxPduCfg_msg_TxCycle1000_10_oCAN_fd90faa0_Tx, PDUR_TX_DIRECTIONOFRMGDESTROM,           2u,       0u,                  0u  /* CommonSharedMemory */,          11u },  /* [Global PduRDestPdu: msg_TxCycle1000_10_oCAN_6dd6f284_Tx]           */  /* [/ActiveEcuC/EcuC/EcucPduCollection/msg_TxCycle1000_10_oCAN_6dd6f284_Tx, /ActiveEcuC/PduR/CanIf] */
  { /*     2 */                     CanIfConf_CanIfTxPduCfg_msg_TxCycle10_0_oCAN_9c370e88_Tx, PDUR_TX_DIRECTIONOFRMGDESTROM,           7u,       0u,                  0u  /* CommonSharedMemory */,           4u },  /* [Global PduRDestPdu: msg_TxCycle10_0_oCAN_2d7b6a87_Tx]              */  /* [/ActiveEcuC/EcuC/EcucPduCollection/msg_TxCycle10_0_oCAN_2d7b6a87_Tx, /ActiveEcuC/PduR/CanIf] */
  { /*     3 */        CanIfConf_CanIfTxPduCfg_msg_TxCycle_E2eProf1C_500_30_oCAN_f209e413_Tx, PDUR_TX_DIRECTIONOFRMGDESTROM,           8u,       0u,                  0u  /* CommonSharedMemory */,          28u },  /* [Global PduRDestPdu: msg_TxCycle_E2eProf1C_500_30_oCAN_995018f6_Tx] */  /* [/ActiveEcuC/EcuC/EcucPduCollection/msg_TxCycle_E2eProf1C_500_30_oCAN_995018f6_Tx, /ActiveEcuC/PduR/CanIf] */
  { /*     4 */                      CanIfConf_CanIfTxPduCfg_msg_TxEvent_10_oCAN_b963731b_Tx, PDUR_TX_DIRECTIONOFRMGDESTROM,           7u,       0u,                  0u  /* CommonSharedMemory */,           5u },  /* [Global PduRDestPdu: msg_TxEvent_10_oCAN_b9443fef_Tx]               */  /* [/ActiveEcuC/EcuC/EcucPduCollection/msg_TxEvent_10_oCAN_b9443fef_Tx, /ActiveEcuC/PduR/CanIf] */
  { /*     5 */        CanIfConf_CanIfTxPduCfg_msg_diag_Uudt_Response_MyECU_oCAN_6917a2df_Tx, PDUR_TX_DIRECTIONOFRMGDESTROM,           8u,       0u,                  0u  /* CommonSharedMemory */,          34u },  /* [Global PduRDestPdu: msg_diag_Uudt_Response_MyECU_oCAN_e0fa8c3a_Tx] */  /* [/ActiveEcuC/EcuC/EcucPduCollection/msg_diag_Uudt_Response_MyECU_oCAN_e0fa8c3a_Tx, /ActiveEcuC/PduR/CanIf] */
  { /*     6 */                      CanNmConf_CanNmUserDataTxPdu_msg_nm_MyECU_oCAN_3680fbe4, PDUR_TX_DIRECTIONOFRMGDESTROM,           6u,       1u,                  0u  /* CommonSharedMemory */,          10u },  /* [Global PduRDestPdu: msg_nm_MyECU_oCAN_c97b60cc_Tx]                 */  /* [/ActiveEcuC/EcuC/EcucPduCollection/msg_nm_MyECU_oCAN_c97b60cc_Tx, /ActiveEcuC/PduR/CanNm] */
  { /*     7 */                                   CanTpConf_CanTpTxNSdu_CanTpTxNSdu_dab96b77, PDUR_TX_DIRECTIONOFRMGDESTROM,           8u,       2u,                  0u  /* CommonSharedMemory */,          35u },  /* [Global PduRDestPdu: msg_diag_Response_MyECU_oCAN_84acb98b_Tx]      */  /* [/ActiveEcuC/EcuC/EcucPduCollection/msg_diag_Response_MyECU_oCAN_84acb98b_Tx, /ActiveEcuC/PduR/CanTp] */
  { /*     8 */ CddLinTpStubConf_CddPduRUpperLayerRxPdu_SlaveResp_RearECU_oLIN00_c13bc529_Rx, PDUR_RX_DIRECTIONOFRMGDESTROM,           8u,       3u,                  0u  /* CommonSharedMemory */,          33u },  /* [Global PduRDestPdu: SlaveResp_RearECU_oLIN00_c13bc529_Rx]          */  /* [/ActiveEcuC/EcuC/EcucPduCollection/SlaveResp_RearECU_oLIN00_c13bc529_Rx, /ActiveEcuC/PduR/CddLin] */
  { /*     9 */  CddLinTpStubConf_CddPduRUpperLayerRxPdu_SlaveResp_Slave3_oLIN00_97811d8d_Rx, PDUR_RX_DIRECTIONOFRMGDESTROM,           8u,       3u,                  0u  /* CommonSharedMemory */,          12u },  /* [Global PduRDestPdu: SlaveResp_Slave3_oLIN00_97811d8d_Rx]           */  /* [/ActiveEcuC/EcuC/EcucPduCollection/SlaveResp_Slave3_oLIN00_97811d8d_Rx, /ActiveEcuC/PduR/CddLin] */
  { /*    10 */                       ComConf_ComIPdu_Frame_LinTr_RearECU_oLIN00_22758e6f_Rx, PDUR_RX_DIRECTIONOFRMGDESTROM,           1u,       4u,                  0u  /* CommonSharedMemory */,          15u },  /* [Global PduRDestPdu: Frame_LinTr_RearECU_oLIN00_22758e6f_Rx]        */  /* [/ActiveEcuC/EcuC/EcucPduCollection/Frame_LinTr_RearECU_oLIN00_22758e6f_Rx, /ActiveEcuC/PduR/Com] */
  { /*    11 */                        ComConf_ComIPdu_Frame_LinTr_Slave3_oLIN00_bac14905_Rx, PDUR_RX_DIRECTIONOFRMGDESTROM,           3u,       4u,                  0u  /* CommonSharedMemory */,          18u },  /* [Global PduRDestPdu: Frame_LinTr_Slave3_oLIN00_bac14905_Rx]         */  /* [/ActiveEcuC/EcuC/EcucPduCollection/Frame_LinTr_Slave3_oLIN00_bac14905_Rx, /ActiveEcuC/PduR/Com] */
  { /*    12 */                                ComConf_ComIPdu_PDU_Dummy_RearECU_1e8d611e_Rx, PDUR_RX_DIRECTIONOFRMGDESTROM,          16u,       4u,                  0u  /* CommonSharedMemory */,           9u },  /* [Global PduRDestPdu: PDU_Dummy_RearECU_1e8d611e_Rx]                 */  /* [/ActiveEcuC/EcuC/EcucPduCollection/PDU_Dummy_RearECU_1e8d611e_Rx, /ActiveEcuC/PduR/Com] */
  { /*    13 */                      ComConf_ComIPdu_PDU_Fr_StartAppl_BothECU_RX_42a9ded9_Rx, PDUR_RX_DIRECTIONOFRMGDESTROM,          16u,       4u,                  0u  /* CommonSharedMemory */,          26u },  /* [Global PduRDestPdu: PDU_Fr_StartAppl_BothECU_RX_42a9ded9_Rx]       */  /* [/ActiveEcuC/EcuC/EcucPduCollection/PDU_Fr_StartAppl_BothECU_RX_42a9ded9_Rx, /ActiveEcuC/PduR/Com] */
  { /*    14 */                            ComConf_ComIPdu_msg_RxCycle100_0_oCAN_1e247d16_Rx, PDUR_RX_DIRECTIONOFRMGDESTROM,           1u,       4u,                  0u  /* CommonSharedMemory */,          21u },  /* [Global PduRDestPdu: msg_RxCycle100_0_oCAN_1e247d16_Rx]             */  /* [/ActiveEcuC/EcuC/EcucPduCollection/msg_RxCycle100_0_oCAN_1e247d16_Rx, /ActiveEcuC/PduR/Com] */
  { /*    15 */                           ComConf_ComIPdu_msg_RxCycle500_20_oCAN_a691adb3_Rx, PDUR_RX_DIRECTIONOFRMGDESTROM,           6u,       4u,                  0u  /* CommonSharedMemory */,          25u },  /* [Global PduRDestPdu: msg_RxCycle500_20_oCAN_a691adb3_Rx]            */  /* [/ActiveEcuC/EcuC/EcucPduCollection/msg_RxCycle500_20_oCAN_a691adb3_Rx, /ActiveEcuC/PduR/Com] */
  { /*    16 */                ComConf_ComIPdu_msg_RxCycle_E2eProf1C_500_10_oCAN_e8110737_Rx, PDUR_RX_DIRECTIONOFRMGDESTROM,           8u,       4u,                  0u  /* CommonSharedMemory */,           0u },  /* [Global PduRDestPdu: msg_RxCycle_E2eProf1C_500_10_oCAN_e8110737_Rx] */  /* [/ActiveEcuC/EcuC/EcucPduCollection/msg_RxCycle_E2eProf1C_500_10_oCAN_e8110737_Rx, /ActiveEcuC/PduR/Com] */
  { /*    17 */                              ComConf_ComIPdu_msg_RxEvent_20_oCAN_a1df81ad_Rx, PDUR_RX_DIRECTIONOFRMGDESTROM,           4u,       4u,                  0u  /* CommonSharedMemory */,           6u },  /* [Global PduRDestPdu: msg_RxEvent_20_oCAN_a1df81ad_Rx]               */  /* [/ActiveEcuC/EcuC/EcucPduCollection/msg_RxEvent_20_oCAN_a1df81ad_Rx, /ActiveEcuC/PduR/Com] */
  { /*    18 */                      ComConf_ComIPdu_msg_StartAppl_Rx_MyECU_oCAN_0da3c1e2_Rx, PDUR_RX_DIRECTIONOFRMGDESTROM,          64u,       4u,                  0u  /* CommonSharedMemory */,          14u },  /* [Global PduRDestPdu: msg_StartAppl_Rx_MyECU_oCAN_0da3c1e2_Rx]       */  /* [/ActiveEcuC/EcuC/EcucPduCollection/msg_StartAppl_Rx_MyECU_oCAN_0da3c1e2_Rx, /ActiveEcuC/PduR/Com] */
  { /*    19 */                                     ComConf_ComIPdu_pdu_RxDyn_64_5b4495a3_Rx, PDUR_RX_DIRECTIONOFRMGDESTROM,           8u,       4u,                  0u  /* CommonSharedMemory */,           8u },  /* [Global PduRDestPdu: pdu_RxDyn_64_5b4495a3_Rx]                      */  /* [/ActiveEcuC/EcuC/EcucPduCollection/pdu_RxDyn_64_5b4495a3_Rx, /ActiveEcuC/PduR/Com] */
  { /*    20 */                                    ComConf_ComIPdu_pdu_RxStat_10_6aaa637c_Rx, PDUR_RX_DIRECTIONOFRMGDESTROM,           4u,       4u,                  0u  /* CommonSharedMemory */,           3u },  /* [Global PduRDestPdu: pdu_RxStat_10_6aaa637c_Rx]                     */  /* [/ActiveEcuC/EcuC/EcucPduCollection/pdu_RxStat_10_6aaa637c_Rx, /ActiveEcuC/PduR/Com] */
  { /*    21 */                                    ComConf_ComIPdu_pdu_RxStat_30_589c01fe_Rx, PDUR_RX_DIRECTIONOFRMGDESTROM,           7u,       4u,                  0u  /* CommonSharedMemory */,          20u },  /* [Global PduRDestPdu: pdu_RxStat_30_589c01fe_Rx]                     */  /* [/ActiveEcuC/EcuC/EcucPduCollection/pdu_RxStat_30_589c01fe_Rx, /ActiveEcuC/PduR/Com] */
  { /*    22 */    DcmConf_DcmDslProtocolRx_msg_diag_RequestGlobal_oCAN_2eba11fb_Rx_1026d879, PDUR_RX_DIRECTIONOFRMGDESTROM,           7u,       5u,                  0u  /* CommonSharedMemory */,          30u },  /* [Global PduRDestPdu: msg_diag_RequestGlobal_oCAN_2eba11fb_Rx]       */  /* [/ActiveEcuC/EcuC/EcucPduCollection/msg_diag_RequestGlobal_oCAN_2eba11fb_Rx, /ActiveEcuC/PduR/Dcm] */
  { /*    23 */    DcmConf_DcmDslProtocolRx_msg_diag_Request_MyECU_oCAN_cd2e72ff_Rx_db217017, PDUR_RX_DIRECTIONOFRMGDESTROM,           8u,       5u,                  0u  /* CommonSharedMemory */,          22u },  /* [Global PduRDestPdu: msg_diag_Request_MyECU_oCAN_cd2e72ff_Rx]       */  /* [/ActiveEcuC/EcuC/EcucPduCollection/msg_diag_Request_MyECU_oCAN_cd2e72ff_Rx, /ActiveEcuC/PduR/Dcm] */
  { /*    24 */                     FrIfConf_FrIfTxPdu_PDU_Fr_StartAppl_MyECU_TX_161591f9_Tx, PDUR_TX_DIRECTIONOFRMGDESTROM,          16u,       6u,                  0u  /* CommonSharedMemory */,          32u },  /* [Global PduRDestPdu: PDU_Fr_StartAppl_MyECU_TX_161591f9_Tx]         */  /* [/ActiveEcuC/EcuC/EcucPduCollection/PDU_Fr_StartAppl_MyECU_TX_161591f9_Tx, /ActiveEcuC/PduR/FrIf] */
  { /*    25 */                            FrIfConf_FrIfTxPdu_PDU_Transmit_MyECU_dcbaa590_Tx, PDUR_TX_DIRECTIONOFRMGDESTROM,           8u,       6u,                  0u  /* CommonSharedMemory */,          17u },  /* [Global PduRDestPdu: PDU_Transmit_MyECU_dcbaa590_Tx]                */  /* [/ActiveEcuC/EcuC/EcucPduCollection/PDU_Transmit_MyECU_dcbaa590_Tx, /ActiveEcuC/PduR/FrIf] */
  { /*    26 */                                  FrIfConf_FrIfTxPdu_pdu_TxDyn_16_44389cbf_Tx, PDUR_TX_DIRECTIONOFRMGDESTROM,           2u,       6u,                  0u  /* CommonSharedMemory */,           7u },  /* [Global PduRDestPdu: pdu_TxDyn_16_44389cbf_Tx]                      */  /* [/ActiveEcuC/EcuC/EcucPduCollection/pdu_TxDyn_16_44389cbf_Tx, /ActiveEcuC/PduR/FrIf] */
  { /*    27 */                                  FrIfConf_FrIfTxPdu_pdu_TxDyn_64_0bd27c77_Tx, PDUR_TX_DIRECTIONOFRMGDESTROM,           8u,       6u,                  0u  /* CommonSharedMemory */,          24u },  /* [Global PduRDestPdu: pdu_TxDyn_64_0bd27c77_Tx]                      */  /* [/ActiveEcuC/EcuC/EcucPduCollection/pdu_TxDyn_64_0bd27c77_Tx, /ActiveEcuC/PduR/FrIf] */
  { /*    28 */                                 FrIfConf_FrIfTxPdu_pdu_TxStat_40_505d7b88_Tx, PDUR_TX_DIRECTIONOFRMGDESTROM,           5u,       6u,                  0u  /* CommonSharedMemory */,          23u },  /* [Global PduRDestPdu: pdu_TxStat_40_505d7b88_Tx]                     */  /* [/ActiveEcuC/EcuC/EcucPduCollection/pdu_TxStat_40_505d7b88_Tx, /ActiveEcuC/PduR/FrIf] */
  { /*    29 */                                 FrIfConf_FrIfTxPdu_pdu_TxStat_64_2f41aa7f_Tx, PDUR_TX_DIRECTIONOFRMGDESTROM,           8u,       6u,                  0u  /* CommonSharedMemory */,           2u },  /* [Global PduRDestPdu: pdu_TxStat_64_2f41aa7f_Tx]                     */  /* [/ActiveEcuC/EcuC/EcucPduCollection/pdu_TxStat_64_2f41aa7f_Tx, /ActiveEcuC/PduR/FrIf] */
  { /*    30 */                       FrNmConf_FrNmUserDataTxPdu_PDU_nm_MyECU_Fr_ae963333_Tx, PDUR_TX_DIRECTIONOFRMGDESTROM,           6u,       7u,                  0u  /* CommonSharedMemory */,          29u },  /* [Global PduRDestPdu: PDU_nm_MyECU_Fr_ae963333_Tx]                   */  /* [/ActiveEcuC/EcuC/EcucPduCollection/PDU_nm_MyECU_Fr_ae963333_Tx, /ActiveEcuC/PduR/FrNm] */
  { /*    31 */                                     LinIfConf_LinIfTxPdu_LinIfTxPdu_754fc8ba, PDUR_TX_DIRECTIONOFRMGDESTROM,           3u,       8u,                  0u  /* CommonSharedMemory */,           1u },  /* [Global PduRDestPdu: Frame_LinTr_MyECU2_oLIN00_a05ec237_Tx]         */  /* [/ActiveEcuC/EcuC/EcucPduCollection/Frame_LinTr_MyECU2_oLIN00_a05ec237_Tx, /ActiveEcuC/PduR/LinIf] */
  { /*    32 */                                     LinIfConf_LinIfTxPdu_LinIfTxPdu_52dda9c7, PDUR_TX_DIRECTIONOFRMGDESTROM,           1u,       8u,                  0u  /* CommonSharedMemory */,          13u },  /* [Global PduRDestPdu: Frame_LinTr_MyECU_oLIN00_392a06a5_Tx]          */  /* [/ActiveEcuC/EcuC/EcucPduCollection/Frame_LinTr_MyECU_oLIN00_392a06a5_Tx, /ActiveEcuC/PduR/LinIf] */
  { /*    33 */                   LinTpConf_LinTpTxNSdu_MasterReq_RearECU_oLIN00_8ed7799b_Tx, PDUR_TX_DIRECTIONOFRMGDESTROM,           8u,       9u,                  0u  /* CommonSharedMemory */,          16u },  /* [Global PduRDestPdu: MasterReq_RearECU_oLIN00_a4a697e2_Tx]          */  /* [/ActiveEcuC/EcuC/EcucPduCollection/MasterReq_RearECU_oLIN00_a4a697e2_Tx, /ActiveEcuC/PduR/LinTp] */
  { /*    34 */                    LinTpConf_LinTpTxNSdu_MasterReq_Slave3_oLIN00_a4cffd2e_Tx, PDUR_TX_DIRECTIONOFRMGDESTROM,           8u,       9u,                  0u  /* CommonSharedMemory */,          27u },  /* [Global PduRDestPdu: MasterReq_Slave3_oLIN00_93dc5ed4_Tx]           */  /* [/ActiveEcuC/EcuC/EcucPduCollection/MasterReq_Slave3_oLIN00_93dc5ed4_Tx, /ActiveEcuC/PduR/LinTp] */
  { /*    35 */                           LinTpConf_LinTpTxNSdu_MasterReq_oLIN00_4a2bb011_Tx, PDUR_TX_DIRECTIONOFRMGDESTROM,           8u,       9u,                  0u  /* CommonSharedMemory */,          19u }   /* [Global PduRDestPdu: MasterReq_oLIN00_3234fe1b_Tx]                  */  /* [/ActiveEcuC/EcuC/EcucPduCollection/MasterReq_oLIN00_3234fe1b_Tx, /ActiveEcuC/PduR/LinTp] */
};
#define PDUR_STOP_SEC_CONST_UNSPECIFIED
/*lint -save -esym(961, 19.1) */
#include "PduR_MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */

/**********************************************************************************************************************
  PduR_RmSrcRom
**********************************************************************************************************************/
/** 
  \var    PduR_RmSrcRom
  \brief  PduR RoutiongManager SrcPdu Table
  \details
  Element                Description
  MaskedBits             contains bitcoded the boolean data of PduR_TriggerTransmitSupportedOfRmSrcRom, PduR_TxConfirmationSupportedOfRmSrcRom
  MmRomIdx               the index of the 1:1 relation pointing to PduR_MmRom
  PartitionIndexOfCsl
  RmDestRomStartIdx      the start index of the 1:n relation pointing to PduR_RmDestRom
  SrcHnd                 handle to be used as parameter for the TxConfirmation or TriggerTransmit function call.
*/ 
#define PDUR_START_SEC_CONST_UNSPECIFIED
/*lint -save -esym(961, 19.1) */
#include "PduR_MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */
CONST(PduR_RmSrcRomType, PDUR_CONST) PduR_RmSrcRom[36] = {  /* PRQA S 1514, 1533 */  /* MD_CSL_ObjectOnlyAccessedOnce */
    /* Index    MaskedBits  MmRomIdx  PartitionIndexOfCsl                            RmDestRomStartIdx  SrcHnd                                                                              Comment                                       Referable Keys */
  { /*     0 */      0x00u,       8u,                  0u  /* CommonSharedMemory */,               15u,                                                     PDUR_NO_SRCHNDOFRMSRCROM },  /* [PduRSrcPdu: PduRSrcPdu_87852900] */  /* [/ActiveEcuC/PduR/PduRRoutingTables/PduRRoutingTable/Frame_LinTr_RearECU_oLIN00_64640cc1_Rx/PduRSrcPdu_87852900] */
  { /*     1 */      0x00u,       8u,                  0u  /* CommonSharedMemory */,               18u,                                                     PDUR_NO_SRCHNDOFRMSRCROM },  /* [PduRSrcPdu: PduRSrcPdu_8cf2ea35] */  /* [/ActiveEcuC/PduR/PduRRoutingTables/PduRRoutingTable/Frame_LinTr_Slave3_oLIN00_eb2bd0ab_Rx/PduRSrcPdu_8cf2ea35] */
  { /*     2 */      0x00u,       6u,                  0u  /* CommonSharedMemory */,                9u,                                                     PDUR_NO_SRCHNDOFRMSRCROM },  /* [PduRSrcPdu: PduRSrcPdu_5699e50b] */  /* [/ActiveEcuC/PduR/PduRRoutingTables/PduRRoutingTable/PDU_Dummy_RearECU_9177c4f3_Rx/PduRSrcPdu_5699e50b] */
  { /*     3 */      0x00u,       6u,                  0u  /* CommonSharedMemory */,               26u,                                                     PDUR_NO_SRCHNDOFRMSRCROM },  /* [PduRSrcPdu: PduRSrcPdu_b2f650ce] */  /* [/ActiveEcuC/PduR/PduRRoutingTables/PduRRoutingTable/PDU_Fr_StartAppl_BothECU_RX_7a67d8c4_Rx/PduRSrcPdu_b2f650ce] */
  { /*     4 */      0x00u,       0u,                  0u  /* CommonSharedMemory */,               21u,                                                     PDUR_NO_SRCHNDOFRMSRCROM },  /* [PduRSrcPdu: PduRSrcPdu_9e00b2d3] */  /* [/ActiveEcuC/PduR/PduRRoutingTables/PduRRoutingTable/msg_RxCycle100_0_oCAN_c71398f9_Rx/PduRSrcPdu_9e00b2d3] */
  { /*     5 */      0x00u,       0u,                  0u  /* CommonSharedMemory */,               25u,                                                     PDUR_NO_SRCHNDOFRMSRCROM },  /* [PduRSrcPdu: PduRSrcPdu_b1d1dd8a] */  /* [/ActiveEcuC/PduR/PduRRoutingTables/PduRRoutingTable/msg_RxCycle500_20_oCAN_266969e8_Rx/PduRSrcPdu_b1d1dd8a] */
  { /*     6 */      0x00u,       0u,                  0u  /* CommonSharedMemory */,                0u,                                                     PDUR_NO_SRCHNDOFRMSRCROM },  /* [PduRSrcPdu: PduRSrcPdu_01c8980f] */  /* [/ActiveEcuC/PduR/PduRRoutingTables/PduRRoutingTable/msg_RxCycle_E2eProf1C_500_10_oCAN_23e9789c_Rx/PduRSrcPdu_01c8980f] */
  { /*     7 */      0x00u,       0u,                  0u  /* CommonSharedMemory */,                6u,                                                     PDUR_NO_SRCHNDOFRMSRCROM },  /* [PduRSrcPdu: PduRSrcPdu_37e6280b] */  /* [/ActiveEcuC/PduR/PduRRoutingTables/PduRRoutingTable/msg_RxEvent_20_oCAN_13517c6b_Rx/PduRSrcPdu_37e6280b] */
  { /*     8 */      0x00u,       0u,                  0u  /* CommonSharedMemory */,               14u,                                                     PDUR_NO_SRCHNDOFRMSRCROM },  /* [PduRSrcPdu: PduRSrcPdu_7a86d966] */  /* [/ActiveEcuC/PduR/PduRRoutingTables/PduRRoutingTable/msg_StartAppl_Rx_MyECU_oCAN_fe93a56e_Rx/PduRSrcPdu_7a86d966] */
  { /*     9 */      0x00u,       6u,                  0u  /* CommonSharedMemory */,                8u,                                                     PDUR_NO_SRCHNDOFRMSRCROM },  /* [PduRSrcPdu: PduRSrcPdu_45b853db] */  /* [/ActiveEcuC/PduR/PduRRoutingTables/PduRRoutingTable/pdu_RxDyn_64_600910d1_Rx/PduRSrcPdu_45b853db] */
  { /*    10 */      0x00u,       6u,                  0u  /* CommonSharedMemory */,                3u,                                                     PDUR_NO_SRCHNDOFRMSRCROM },  /* [PduRSrcPdu: PduRSrcPdu_2bf83137] */  /* [/ActiveEcuC/PduR/PduRRoutingTables/PduRRoutingTable/pdu_RxStat_10_51092b83_Rx/PduRSrcPdu_2bf83137] */
  { /*    11 */      0x00u,       6u,                  0u  /* CommonSharedMemory */,               20u,                                                     PDUR_NO_SRCHNDOFRMSRCROM },  /* [PduRSrcPdu: PduRSrcPdu_9474dc2d] */  /* [/ActiveEcuC/PduR/PduRRoutingTables/PduRRoutingTable/pdu_RxStat_30_25bd2d72_Rx/PduRSrcPdu_9474dc2d] */
  { /*    12 */      0x00u,       9u,                  0u  /* CommonSharedMemory */,               33u,                                                     PDUR_NO_SRCHNDOFRMSRCROM },  /* [PduRSrcPdu: PduRSrcPdu_e930d1d3] */  /* [/ActiveEcuC/PduR/PduRRoutingTables/PduRRoutingTable/SlaveResp_RearECU_oLIN00_fa842351_Rx/PduRSrcPdu_e930d1d3] */
  { /*    13 */      0x00u,       9u,                  0u  /* CommonSharedMemory */,               12u,                                                     PDUR_NO_SRCHNDOFRMSRCROM },  /* [PduRSrcPdu: PduRSrcPdu_74fe1f87] */  /* [/ActiveEcuC/PduR/PduRRoutingTables/PduRRoutingTable/SlaveResp_Slave3_oLIN00_6f91e3ac_Rx/PduRSrcPdu_74fe1f87] */
  { /*    14 */      0x00u,       2u,                  0u  /* CommonSharedMemory */,               30u,                                                     PDUR_NO_SRCHNDOFRMSRCROM },  /* [PduRSrcPdu: PduRSrcPdu_c958f031] */  /* [/ActiveEcuC/PduR/PduRRoutingTables/PduRRoutingTable/msg_diag_RequestGlobal_oCAN_e06c7e2d_Rx/PduRSrcPdu_c958f031] */
  { /*    15 */      0x00u,       2u,                  0u  /* CommonSharedMemory */,               22u,                                                     PDUR_NO_SRCHNDOFRMSRCROM },  /* [PduRSrcPdu: PduRSrcPdu_9fd2cbe7] */  /* [/ActiveEcuC/PduR/PduRRoutingTables/PduRRoutingTable/msg_diag_Request_MyECU_oCAN_ca029ee7_Rx/PduRSrcPdu_9fd2cbe7] */
  { /*    16 */      0x02u,       4u,                  0u  /* CommonSharedMemory */,                1u,                        ComConf_ComIPdu_Frame_LinTr_MyECU2_oLIN00_3a9f0ce6_Tx },  /* [PduRSrcPdu: PduRSrcPdu_12782a39] */  /* [/ActiveEcuC/PduR/PduRRoutingTables/PduRRoutingTable/Frame_LinTr_MyECU2_oLIN00_3a9f0ce6_Tx/PduRSrcPdu_12782a39] */
  { /*    17 */      0x02u,       4u,                  0u  /* CommonSharedMemory */,               13u,                         ComConf_ComIPdu_Frame_LinTr_MyECU_oLIN00_ce6095e4_Tx },  /* [PduRSrcPdu: PduRSrcPdu_783591af] */  /* [/ActiveEcuC/PduR/PduRRoutingTables/PduRRoutingTable/Frame_LinTr_MyECU_oLIN00_ce6095e4_Tx/PduRSrcPdu_783591af] */
  { /*    18 */      0x00u,       3u,                  0u  /* CommonSharedMemory */,               16u, CddLinTpStubConf_CddPduRUpperLayerTxPdu_MasterReq_RearECU_oLIN00_8ed7799b_Tx },  /* [PduRSrcPdu: PduRSrcPdu_88fade16] */  /* [/ActiveEcuC/PduR/PduRRoutingTables/PduRRoutingTable/MasterReq_RearECU_oLIN00_8ed7799b_Tx/PduRSrcPdu_88fade16] */
  { /*    19 */      0x00u,       3u,                  0u  /* CommonSharedMemory */,               27u,  CddLinTpStubConf_CddPduRUpperLayerTxPdu_MasterReq_Slave3_oLIN00_a4cffd2e_Tx },  /* [PduRSrcPdu: PduRSrcPdu_b376d1ba] */  /* [/ActiveEcuC/PduR/PduRRoutingTables/PduRRoutingTable/MasterReq_Slave3_oLIN00_a4cffd2e_Tx/PduRSrcPdu_b376d1ba] */
  { /*    20 */      0x00u,       3u,                  0u  /* CommonSharedMemory */,               19u,         CddLinTpStubConf_CddPduRUpperLayerTxPdu_MasterReq_oLIN00_4a2bb011_Tx },  /* [PduRSrcPdu: PduRSrcPdu_927d3065] */  /* [/ActiveEcuC/PduR/PduRRoutingTables/PduRRoutingTable/MasterReq_oLIN00_4a2bb011_Tx/PduRSrcPdu_927d3065] */
  { /*    21 */      0x02u,       4u,                  0u  /* CommonSharedMemory */,               32u,                        ComConf_ComIPdu_PDU_Fr_StartAppl_MyECU_TX_44b0b044_Tx },  /* [PduRSrcPdu: PduRSrcPdu_d1717db2] */  /* [/ActiveEcuC/PduR/PduRRoutingTables/PduRRoutingTable/PDU_Fr_StartAppl_MyECU_TX_44b0b044_Tx/PduRSrcPdu_d1717db2] */
  { /*    22 */      0x02u,       4u,                  0u  /* CommonSharedMemory */,               17u,                               ComConf_ComIPdu_PDU_Transmit_MyECU_05398c7a_Tx },  /* [PduRSrcPdu: PduRSrcPdu_8c1e0786] */  /* [/ActiveEcuC/PduR/PduRRoutingTables/PduRRoutingTable/PDU_Transmit_MyECU_05398c7a_Tx/PduRSrcPdu_8c1e0786] */
  { /*    23 */      0x02u,       4u,                  0u  /* CommonSharedMemory */,               29u,                                  ComConf_ComIPdu_PDU_nm_MyECU_Fr_ae963333_Tx },  /* [PduRSrcPdu: PduRSrcPdu_c844becc] */  /* [/ActiveEcuC/PduR/PduRRoutingTables/PduRRoutingTable/PDU_nm_MyECU_Fr_ae963333_Tx/PduRSrcPdu_c844becc] */
  { /*    24 */      0x00u,       4u,                  0u  /* CommonSharedMemory */,               31u,                                                     PDUR_NO_SRCHNDOFRMSRCROM },  /* [PduRSrcPdu: PduRSrcPdu_cd5f4416] */  /* [/ActiveEcuC/PduR/PduRRoutingTables/PduRRoutingTable/msg_StartAppl_Tx_MyECU_oCAN_ad461e3f_Tx/PduRSrcPdu_cd5f4416] */
  { /*    25 */      0x00u,       4u,                  0u  /* CommonSharedMemory */,                4u,                                                     PDUR_NO_SRCHNDOFRMSRCROM },  /* [PduRSrcPdu: PduRSrcPdu_2e41d513] */  /* [/ActiveEcuC/PduR/PduRRoutingTables/PduRRoutingTable/msg_TxCycle10_0_oCAN_85bf3e37_Tx/PduRSrcPdu_2e41d513] */
  { /*    26 */      0x00u,       4u,                  0u  /* CommonSharedMemory */,               11u,                                                     PDUR_NO_SRCHNDOFRMSRCROM },  /* [PduRSrcPdu: PduRSrcPdu_704563f1] */  /* [/ActiveEcuC/PduR/PduRRoutingTables/PduRRoutingTable/msg_TxCycle1000_10_oCAN_d74aed68_Tx/PduRSrcPdu_704563f1] */
  { /*    27 */      0x00u,       4u,                  0u  /* CommonSharedMemory */,               28u,                                                     PDUR_NO_SRCHNDOFRMSRCROM },  /* [PduRSrcPdu: PduRSrcPdu_c3d2922e] */  /* [/ActiveEcuC/PduR/PduRRoutingTables/PduRRoutingTable/msg_TxCycle_E2eProf1C_500_30_oCAN_eeefc687_Tx/PduRSrcPdu_c3d2922e] */
  { /*    28 */      0x00u,       4u,                  0u  /* CommonSharedMemory */,                5u,                                                     PDUR_NO_SRCHNDOFRMSRCROM },  /* [PduRSrcPdu: PduRSrcPdu_36788591] */  /* [/ActiveEcuC/PduR/PduRRoutingTables/PduRRoutingTable/msg_TxEvent_10_oCAN_b2cd4fc2_Tx/PduRSrcPdu_36788591] */
  { /*    29 */      0x00u,       5u,                  0u  /* CommonSharedMemory */,               35u,   DcmConf_DcmDslProtocolTx_msg_diag_Response_MyECU_oCAN_06426912_Tx_5d336398 },  /* [PduRSrcPdu: PduRSrcPdu_f3446321] */  /* [/ActiveEcuC/PduR/PduRRoutingTables/PduRRoutingTable/msg_diag_Response_MyECU_oCAN_06426912_Tx/PduRSrcPdu_f3446321] */
  { /*    30 */      0x01u,       5u,                  0u  /* CommonSharedMemory */,               34u,                    DcmConf_DcmDslPeriodicConnection_DcmDslPeriodicConnection },  /* [PduRSrcPdu: PduRSrcPdu_ec58e685] */  /* [/ActiveEcuC/PduR/PduRRoutingTables/PduRRoutingTable/msg_diag_Uudt_Response_MyECU_oCAN_f130d337_Tx/PduRSrcPdu_ec58e685] */
  { /*    31 */      0x02u,       4u,                  0u  /* CommonSharedMemory */,               10u,                                ComConf_ComIPdu_msg_nm_MyECU_oCAN_c97b60cc_Tx },  /* [PduRSrcPdu: PduRSrcPdu_616fda32] */  /* [/ActiveEcuC/PduR/PduRRoutingTables/PduRRoutingTable/msg_nm_MyECU_oCAN_c97b60cc_Tx/PduRSrcPdu_616fda32] */
  { /*    32 */      0x02u,       4u,                  0u  /* CommonSharedMemory */,                7u,                                     ComConf_ComIPdu_pdu_TxDyn_16_3c646bcf_Tx },  /* [PduRSrcPdu: PduRSrcPdu_3920f728] */  /* [/ActiveEcuC/PduR/PduRRoutingTables/PduRRoutingTable/pdu_TxDyn_16_3c646bcf_Tx/PduRSrcPdu_3920f728] */
  { /*    33 */      0x02u,       4u,                  0u  /* CommonSharedMemory */,               24u,                                     ComConf_ComIPdu_pdu_TxDyn_64_9d2b9c24_Tx },  /* [PduRSrcPdu: PduRSrcPdu_ab04820d] */  /* [/ActiveEcuC/PduR/PduRRoutingTables/PduRRoutingTable/pdu_TxDyn_64_9d2b9c24_Tx/PduRSrcPdu_ab04820d] */
  { /*    34 */      0x02u,       4u,                  0u  /* CommonSharedMemory */,               23u,                                    ComConf_ComIPdu_pdu_TxStat_40_64c7eeb3_Tx },  /* [PduRSrcPdu: PduRSrcPdu_a7760f35] */  /* [/ActiveEcuC/PduR/PduRRoutingTables/PduRRoutingTable/pdu_TxStat_40_64c7eeb3_Tx/PduRSrcPdu_a7760f35] */
  { /*    35 */      0x02u,       4u,                  0u  /* CommonSharedMemory */,                2u,                                    ComConf_ComIPdu_pdu_TxStat_64_519c4828_Tx }   /* [PduRSrcPdu: PduRSrcPdu_15da8b7f] */  /* [/ActiveEcuC/PduR/PduRRoutingTables/PduRRoutingTable/pdu_TxStat_64_519c4828_Tx/PduRSrcPdu_15da8b7f] */
};
#define PDUR_STOP_SEC_CONST_UNSPECIFIED
/*lint -save -esym(961, 19.1) */
#include "PduR_MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */

/**********************************************************************************************************************
  PduR_RmTransmitFctPtr
**********************************************************************************************************************/
/** 
  \var    PduR_RmTransmitFctPtr
  \brief  Internal routing manager Transmit functions.
*/ 
#define PDUR_START_SEC_CONST_UNSPECIFIED
/*lint -save -esym(961, 19.1) */
#include "PduR_MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */
CONST(PduR_RmTransmitFctPtrType, PDUR_CONST) PduR_RmTransmitFctPtr[2] = {  /* PRQA S 1514, 1533 */  /* MD_CSL_ObjectOnlyAccessedOnce */
  /* Index     RmTransmitFctPtr                  Referable Keys */
  /*     0 */ PduR_RmIf_RoutePdu           ,  /* [PduR_RmIf_RoutePdu] */
  /*     1 */ PduR_RmTp_Transmit_MultiDest    /* [PduR_RmTp_Transmit_MultiDest] */
};
#define PDUR_STOP_SEC_CONST_UNSPECIFIED
/*lint -save -esym(961, 19.1) */
#include "PduR_MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */

/**********************************************************************************************************************
  PduR_RxIf2Dest
**********************************************************************************************************************/
/** 
  \var    PduR_RxIf2Dest
  \brief  This table contains all routing information to perform the Rx handling of an interface routing. Used in the &lt;LLIf&gt;_RxIndication
  \details
  Element                Description
  PartitionIndexOfCsl
*/ 
#define PDUR_START_SEC_CONST_UNSPECIFIED
/*lint -save -esym(961, 19.1) */
#include "PduR_MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */
CONST(PduR_RxIf2DestType, PDUR_CONST) PduR_RxIf2Dest[12] = {  /* PRQA S 1514, 1533 */  /* MD_CSL_ObjectOnlyAccessedOnce */
    /* Index    PartitionIndexOfCsl                                  Referable Keys */
  { /*     0 */                  0u  /* CommonSharedMemory */ },  /* [/ActiveEcuC/PduR/PduRRoutingTables/PduRRoutingTable/Frame_LinTr_RearECU_oLIN00_64640cc1_Rx/PduRSrcPdu_87852900] */
  { /*     1 */                  0u  /* CommonSharedMemory */ },  /* [/ActiveEcuC/PduR/PduRRoutingTables/PduRRoutingTable/Frame_LinTr_Slave3_oLIN00_eb2bd0ab_Rx/PduRSrcPdu_8cf2ea35] */
  { /*     2 */                  0u  /* CommonSharedMemory */ },  /* [/ActiveEcuC/PduR/PduRRoutingTables/PduRRoutingTable/PDU_Dummy_RearECU_9177c4f3_Rx/PduRSrcPdu_5699e50b] */
  { /*     3 */                  0u  /* CommonSharedMemory */ },  /* [/ActiveEcuC/PduR/PduRRoutingTables/PduRRoutingTable/PDU_Fr_StartAppl_BothECU_RX_7a67d8c4_Rx/PduRSrcPdu_b2f650ce] */
  { /*     4 */                  0u  /* CommonSharedMemory */ },  /* [/ActiveEcuC/PduR/PduRRoutingTables/PduRRoutingTable/msg_RxCycle100_0_oCAN_c71398f9_Rx/PduRSrcPdu_9e00b2d3] */
  { /*     5 */                  0u  /* CommonSharedMemory */ },  /* [/ActiveEcuC/PduR/PduRRoutingTables/PduRRoutingTable/msg_RxCycle500_20_oCAN_266969e8_Rx/PduRSrcPdu_b1d1dd8a] */
  { /*     6 */                  0u  /* CommonSharedMemory */ },  /* [/ActiveEcuC/PduR/PduRRoutingTables/PduRRoutingTable/msg_RxCycle_E2eProf1C_500_10_oCAN_23e9789c_Rx/PduRSrcPdu_01c8980f] */
  { /*     7 */                  0u  /* CommonSharedMemory */ },  /* [/ActiveEcuC/PduR/PduRRoutingTables/PduRRoutingTable/msg_RxEvent_20_oCAN_13517c6b_Rx/PduRSrcPdu_37e6280b] */
  { /*     8 */                  0u  /* CommonSharedMemory */ },  /* [/ActiveEcuC/PduR/PduRRoutingTables/PduRRoutingTable/msg_StartAppl_Rx_MyECU_oCAN_fe93a56e_Rx/PduRSrcPdu_7a86d966] */
  { /*     9 */                  0u  /* CommonSharedMemory */ },  /* [/ActiveEcuC/PduR/PduRRoutingTables/PduRRoutingTable/pdu_RxDyn_64_600910d1_Rx/PduRSrcPdu_45b853db] */
  { /*    10 */                  0u  /* CommonSharedMemory */ },  /* [/ActiveEcuC/PduR/PduRRoutingTables/PduRRoutingTable/pdu_RxStat_10_51092b83_Rx/PduRSrcPdu_2bf83137] */
  { /*    11 */                  0u  /* CommonSharedMemory */ }   /* [/ActiveEcuC/PduR/PduRRoutingTables/PduRRoutingTable/pdu_RxStat_30_25bd2d72_Rx/PduRSrcPdu_9474dc2d] */
};
#define PDUR_STOP_SEC_CONST_UNSPECIFIED
/*lint -save -esym(961, 19.1) */
#include "PduR_MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */

/**********************************************************************************************************************
  PduR_RxTp2Dest
**********************************************************************************************************************/
/** 
  \var    PduR_RxTp2Dest
  \brief  This table contains all routing information to perform the Rx handling of a Tp Routing. Used in the PduR_&lt;LLTp&gt;_StartOfReception, PduR_&lt;LLTp&gt;_CopyRxData and PduR_&lt;LLTp&gt;_RxIndication.
  \details
  Element                Description
  PartitionIndexOfCsl
*/ 
#define PDUR_START_SEC_CONST_UNSPECIFIED
/*lint -save -esym(961, 19.1) */
#include "PduR_MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */
CONST(PduR_RxTp2DestType, PDUR_CONST) PduR_RxTp2Dest[4] = {  /* PRQA S 1514, 1533 */  /* MD_CSL_ObjectOnlyAccessedOnce */
    /* Index    PartitionIndexOfCsl                                  Referable Keys */
  { /*     0 */                  0u  /* CommonSharedMemory */ },  /* [/ActiveEcuC/PduR/PduRRoutingTables/PduRRoutingTable/SlaveResp_RearECU_oLIN00_fa842351_Rx/PduRSrcPdu_e930d1d3] */
  { /*     1 */                  0u  /* CommonSharedMemory */ },  /* [/ActiveEcuC/PduR/PduRRoutingTables/PduRRoutingTable/SlaveResp_Slave3_oLIN00_6f91e3ac_Rx/PduRSrcPdu_74fe1f87] */
  { /*     2 */                  0u  /* CommonSharedMemory */ },  /* [/ActiveEcuC/PduR/PduRRoutingTables/PduRRoutingTable/msg_diag_RequestGlobal_oCAN_e06c7e2d_Rx/PduRSrcPdu_c958f031] */
  { /*     3 */                  0u  /* CommonSharedMemory */ }   /* [/ActiveEcuC/PduR/PduRRoutingTables/PduRRoutingTable/msg_diag_Request_MyECU_oCAN_ca029ee7_Rx/PduRSrcPdu_9fd2cbe7] */
};
#define PDUR_STOP_SEC_CONST_UNSPECIFIED
/*lint -save -esym(961, 19.1) */
#include "PduR_MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */

/**********************************************************************************************************************
  PduR_Tx2Lo
**********************************************************************************************************************/
/** 
  \var    PduR_Tx2Lo
  \brief  Contains all information to route a Pdu from a upper layer to a lower layer module, or to cancel a transmission
  \details
  Element                Description
  MaskedBits             contains bitcoded the boolean data of PduR_CancelTransmitUsedOfTx2Lo, PduR_RmSrcRomUsedOfTx2Lo
  PartitionIndexOfCsl
  RmTransmitFctPtrIdx    the index of the 1:1 relation pointing to PduR_RmTransmitFctPtr
*/ 
#define PDUR_START_SEC_CONST_UNSPECIFIED
/*lint -save -esym(961, 19.1) */
#include "PduR_MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */
CONST(PduR_Tx2LoType, PDUR_CONST) PduR_Tx2Lo[20] = {  /* PRQA S 1514, 1533 */  /* MD_CSL_ObjectOnlyAccessedOnce */
    /* Index    MaskedBits  PartitionIndexOfCsl                            RmTransmitFctPtrIdx        Referable Keys */
  { /*     0 */      0x01u,                  0u  /* CommonSharedMemory */,                  0u },  /* [/ActiveEcuC/PduR/PduRRoutingTables/PduRRoutingTable/Frame_LinTr_MyECU2_oLIN00_3a9f0ce6_Tx/PduRSrcPdu_12782a39] */
  { /*     1 */      0x01u,                  0u  /* CommonSharedMemory */,                  0u },  /* [/ActiveEcuC/PduR/PduRRoutingTables/PduRRoutingTable/Frame_LinTr_MyECU_oLIN00_ce6095e4_Tx/PduRSrcPdu_783591af] */
  { /*     2 */      0x01u,                  0u  /* CommonSharedMemory */,                  1u },  /* [/ActiveEcuC/PduR/PduRRoutingTables/PduRRoutingTable/MasterReq_RearECU_oLIN00_8ed7799b_Tx/PduRSrcPdu_88fade16] */
  { /*     3 */      0x01u,                  0u  /* CommonSharedMemory */,                  1u },  /* [/ActiveEcuC/PduR/PduRRoutingTables/PduRRoutingTable/MasterReq_Slave3_oLIN00_a4cffd2e_Tx/PduRSrcPdu_b376d1ba] */
  { /*     4 */      0x01u,                  0u  /* CommonSharedMemory */,                  1u },  /* [/ActiveEcuC/PduR/PduRRoutingTables/PduRRoutingTable/MasterReq_oLIN00_4a2bb011_Tx/PduRSrcPdu_927d3065] */
  { /*     5 */      0x01u,                  0u  /* CommonSharedMemory */,                  0u },  /* [/ActiveEcuC/PduR/PduRRoutingTables/PduRRoutingTable/PDU_Fr_StartAppl_MyECU_TX_44b0b044_Tx/PduRSrcPdu_d1717db2] */
  { /*     6 */      0x01u,                  0u  /* CommonSharedMemory */,                  0u },  /* [/ActiveEcuC/PduR/PduRRoutingTables/PduRRoutingTable/PDU_Transmit_MyECU_05398c7a_Tx/PduRSrcPdu_8c1e0786] */
  { /*     7 */      0x01u,                  0u  /* CommonSharedMemory */,                  0u },  /* [/ActiveEcuC/PduR/PduRRoutingTables/PduRRoutingTable/PDU_nm_MyECU_Fr_ae963333_Tx/PduRSrcPdu_c844becc] */
  { /*     8 */      0x01u,                  0u  /* CommonSharedMemory */,                  0u },  /* [/ActiveEcuC/PduR/PduRRoutingTables/PduRRoutingTable/msg_StartAppl_Tx_MyECU_oCAN_ad461e3f_Tx/PduRSrcPdu_cd5f4416] */
  { /*     9 */      0x01u,                  0u  /* CommonSharedMemory */,                  0u },  /* [/ActiveEcuC/PduR/PduRRoutingTables/PduRRoutingTable/msg_TxCycle10_0_oCAN_85bf3e37_Tx/PduRSrcPdu_2e41d513] */
  { /*    10 */      0x01u,                  0u  /* CommonSharedMemory */,                  0u },  /* [/ActiveEcuC/PduR/PduRRoutingTables/PduRRoutingTable/msg_TxCycle1000_10_oCAN_d74aed68_Tx/PduRSrcPdu_704563f1] */
  { /*    11 */      0x01u,                  0u  /* CommonSharedMemory */,                  0u },  /* [/ActiveEcuC/PduR/PduRRoutingTables/PduRRoutingTable/msg_TxCycle_E2eProf1C_500_30_oCAN_eeefc687_Tx/PduRSrcPdu_c3d2922e] */
  { /*    12 */      0x01u,                  0u  /* CommonSharedMemory */,                  0u },  /* [/ActiveEcuC/PduR/PduRRoutingTables/PduRRoutingTable/msg_TxEvent_10_oCAN_b2cd4fc2_Tx/PduRSrcPdu_36788591] */
  { /*    13 */      0x03u,                  0u  /* CommonSharedMemory */,                  1u },  /* [/ActiveEcuC/PduR/PduRRoutingTables/PduRRoutingTable/msg_diag_Response_MyECU_oCAN_06426912_Tx/PduRSrcPdu_f3446321] */
  { /*    14 */      0x03u,                  0u  /* CommonSharedMemory */,                  0u },  /* [/ActiveEcuC/PduR/PduRRoutingTables/PduRRoutingTable/msg_diag_Uudt_Response_MyECU_oCAN_f130d337_Tx/PduRSrcPdu_ec58e685] */
  { /*    15 */      0x01u,                  0u  /* CommonSharedMemory */,                  0u },  /* [/ActiveEcuC/PduR/PduRRoutingTables/PduRRoutingTable/msg_nm_MyECU_oCAN_c97b60cc_Tx/PduRSrcPdu_616fda32] */
  { /*    16 */      0x01u,                  0u  /* CommonSharedMemory */,                  0u },  /* [/ActiveEcuC/PduR/PduRRoutingTables/PduRRoutingTable/pdu_TxDyn_16_3c646bcf_Tx/PduRSrcPdu_3920f728] */
  { /*    17 */      0x01u,                  0u  /* CommonSharedMemory */,                  0u },  /* [/ActiveEcuC/PduR/PduRRoutingTables/PduRRoutingTable/pdu_TxDyn_64_9d2b9c24_Tx/PduRSrcPdu_ab04820d] */
  { /*    18 */      0x01u,                  0u  /* CommonSharedMemory */,                  0u },  /* [/ActiveEcuC/PduR/PduRRoutingTables/PduRRoutingTable/pdu_TxStat_40_64c7eeb3_Tx/PduRSrcPdu_a7760f35] */
  { /*    19 */      0x01u,                  0u  /* CommonSharedMemory */,                  0u }   /* [/ActiveEcuC/PduR/PduRRoutingTables/PduRRoutingTable/pdu_TxStat_64_519c4828_Tx/PduRSrcPdu_15da8b7f] */
};
#define PDUR_STOP_SEC_CONST_UNSPECIFIED
/*lint -save -esym(961, 19.1) */
#include "PduR_MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */

/**********************************************************************************************************************
  PduR_TxIf2Up
**********************************************************************************************************************/
/** 
  \var    PduR_TxIf2Up
  \brief  This table contains all routing information to perform the Tx handling of an interface routing. Used in the &lt;LLIf&gt;_TriggerTransmit and &lt;LLIf&gt;_TxConfirmation
  \details
  Element                Description
  PartitionIndexOfCsl
  RmGDestRomIdx          the index of the 1:1 relation pointing to PduR_RmGDestRom
*/ 
#define PDUR_START_SEC_CONST_UNSPECIFIED
/*lint -save -esym(961, 19.1) */
#include "PduR_MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */
CONST(PduR_TxIf2UpType, PDUR_CONST) PduR_TxIf2Up[16] = {  /* PRQA S 1514, 1533 */  /* MD_CSL_ObjectOnlyAccessedOnce */
    /* Index    PartitionIndexOfCsl                            RmGDestRomIdx        Referable Keys */
  { /*     0 */                  0u  /* CommonSharedMemory */,           31u },  /* [/ActiveEcuC/EcuC/EcucPduCollection/Frame_LinTr_MyECU2_oLIN00_a05ec237_Tx] */
  { /*     1 */                  0u  /* CommonSharedMemory */,           32u },  /* [/ActiveEcuC/EcuC/EcucPduCollection/Frame_LinTr_MyECU_oLIN00_392a06a5_Tx] */
  { /*     2 */                  0u  /* CommonSharedMemory */,           24u },  /* [/ActiveEcuC/EcuC/EcucPduCollection/PDU_Fr_StartAppl_MyECU_TX_161591f9_Tx] */
  { /*     3 */                  0u  /* CommonSharedMemory */,           25u },  /* [/ActiveEcuC/EcuC/EcucPduCollection/PDU_Transmit_MyECU_dcbaa590_Tx] */
  { /*     4 */                  0u  /* CommonSharedMemory */,           30u },  /* [/ActiveEcuC/EcuC/EcucPduCollection/PDU_nm_MyECU_Fr_ae963333_Tx] */
  { /*     5 */                  0u  /* CommonSharedMemory */,            0u },  /* [/ActiveEcuC/EcuC/EcucPduCollection/msg_StartAppl_Tx_MyECU_oCAN_c14d78df_Tx] */
  { /*     6 */                  0u  /* CommonSharedMemory */,            2u },  /* [/ActiveEcuC/EcuC/EcucPduCollection/msg_TxCycle10_0_oCAN_2d7b6a87_Tx] */
  { /*     7 */                  0u  /* CommonSharedMemory */,            1u },  /* [/ActiveEcuC/EcuC/EcucPduCollection/msg_TxCycle1000_10_oCAN_6dd6f284_Tx] */
  { /*     8 */                  0u  /* CommonSharedMemory */,            3u },  /* [/ActiveEcuC/EcuC/EcucPduCollection/msg_TxCycle_E2eProf1C_500_30_oCAN_995018f6_Tx] */
  { /*     9 */                  0u  /* CommonSharedMemory */,            4u },  /* [/ActiveEcuC/EcuC/EcucPduCollection/msg_TxEvent_10_oCAN_b9443fef_Tx] */
  { /*    10 */                  0u  /* CommonSharedMemory */,            5u },  /* [/ActiveEcuC/EcuC/EcucPduCollection/msg_diag_Uudt_Response_MyECU_oCAN_e0fa8c3a_Tx] */
  { /*    11 */                  0u  /* CommonSharedMemory */,            6u },  /* [/ActiveEcuC/EcuC/EcucPduCollection/msg_nm_MyECU_oCAN_c97b60cc_Tx] */
  { /*    12 */                  0u  /* CommonSharedMemory */,           26u },  /* [/ActiveEcuC/EcuC/EcucPduCollection/pdu_TxDyn_16_44389cbf_Tx] */
  { /*    13 */                  0u  /* CommonSharedMemory */,           27u },  /* [/ActiveEcuC/EcuC/EcucPduCollection/pdu_TxDyn_64_0bd27c77_Tx] */
  { /*    14 */                  0u  /* CommonSharedMemory */,           28u },  /* [/ActiveEcuC/EcuC/EcucPduCollection/pdu_TxStat_40_505d7b88_Tx] */
  { /*    15 */                  0u  /* CommonSharedMemory */,           29u }   /* [/ActiveEcuC/EcuC/EcucPduCollection/pdu_TxStat_64_2f41aa7f_Tx] */
};
#define PDUR_STOP_SEC_CONST_UNSPECIFIED
/*lint -save -esym(961, 19.1) */
#include "PduR_MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */

/**********************************************************************************************************************
  PduR_TxTp2Src
**********************************************************************************************************************/
/** 
  \var    PduR_TxTp2Src
  \brief  This table contains all routing information to perform the Tx handling of a transport protocol routing, Used in the &lt;LoTp&gt;_CopyTxData and &lt;LoTp&gt;_TxConfirmation
  \details
  Element                Description
  PartitionIndexOfCsl
  RmGDestRomIdx          the index of the 1:1 relation pointing to PduR_RmGDestRom
*/ 
#define PDUR_START_SEC_CONST_UNSPECIFIED
/*lint -save -esym(961, 19.1) */
#include "PduR_MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */
CONST(PduR_TxTp2SrcType, PDUR_CONST) PduR_TxTp2Src[4] = {  /* PRQA S 1514, 1533 */  /* MD_CSL_ObjectOnlyAccessedOnce */
    /* Index    PartitionIndexOfCsl                            RmGDestRomIdx        Referable Keys */
  { /*     0 */                  0u  /* CommonSharedMemory */,           33u },  /* [/ActiveEcuC/EcuC/EcucPduCollection/MasterReq_RearECU_oLIN00_a4a697e2_Tx] */
  { /*     1 */                  0u  /* CommonSharedMemory */,           34u },  /* [/ActiveEcuC/EcuC/EcucPduCollection/MasterReq_Slave3_oLIN00_93dc5ed4_Tx] */
  { /*     2 */                  0u  /* CommonSharedMemory */,           35u },  /* [/ActiveEcuC/EcuC/EcucPduCollection/MasterReq_oLIN00_3234fe1b_Tx] */
  { /*     3 */                  0u  /* CommonSharedMemory */,            7u }   /* [/ActiveEcuC/EcuC/EcucPduCollection/msg_diag_Response_MyECU_oCAN_84acb98b_Tx] */
};
#define PDUR_STOP_SEC_CONST_UNSPECIFIED
/*lint -save -esym(961, 19.1) */
#include "PduR_MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */

/**********************************************************************************************************************
  PduR_Initialized
**********************************************************************************************************************/
/** 
  \var    PduR_Initialized
  \brief  Initialization state of PduR. TRUE, if PduR_Init() has been called, else FALSE
*/ 
#define PDUR_START_SEC_VAR_ZERO_INIT_8BIT
/*lint -save -esym(961, 19.1) */
#include "PduR_MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */
VAR(PduR_InitializedType, PDUR_VAR_ZERO_INIT) PduR_Initialized = FALSE;  /* PRQA S 1514, 1533 */  /* MD_CSL_ObjectOnlyAccessedOnce */
#define PDUR_STOP_SEC_VAR_ZERO_INIT_8BIT
/*lint -save -esym(961, 19.1) */
#include "PduR_MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */

/**********************************************************************************************************************
  PduR_PCPartitionConfig
**********************************************************************************************************************/
/** 
  \var    PduR_PCPartitionConfig
  \details
  Element        Description
  Initialized    the pointer to PduR_Initialized
*/ 
#define PDUR_START_SEC_CONST_UNSPECIFIED
/*lint -save -esym(961, 19.1) */
#include "PduR_MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */
CONST(PduR_PCPartitionConfigType, PDUR_CONST) PduR_PCPartitionConfig[1] = {  /* PRQA S 1514, 1533 */  /* MD_CSL_ObjectOnlyAccessedOnce */
  { /* Index: 0 Keys: [] */
      (&(PduR_Initialized))  /**< the pointer to PduR_Initialized */
  }
};
#define PDUR_STOP_SEC_CONST_UNSPECIFIED
/*lint -save -esym(961, 19.1) */
#include "PduR_MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */


/**********************************************************************************************************************
  CONFIGURATION CLASS: LINK
  SECTION: GLOBAL DATA
**********************************************************************************************************************/


#define PDUR_START_SEC_VAR_ZERO_INIT_UNSPECIFIED
#include "PduR_MemMap.h"    /* PRQA S 5087 */        /* MD_MSR_MemMap */

#if(PDUR_USE_INIT_POINTER == STD_ON)
P2CONST(PduR_PBConfigType, PDUR_VAR_ZERO_INIT, PDUR_PBCFG) PduR_ConfigDataPtr = (const PduR_PBConfigType *) NULL_PTR;
#endif

#define PDUR_STOP_SEC_VAR_ZERO_INIT_UNSPECIFIED
#include "PduR_MemMap.h"    /* PRQA S 5087 */        /* MD_MSR_MemMap */

#define PDUR_START_SEC_VAR_NOINIT_UNSPECIFIED
#include "PduR_MemMap.h"    /* PRQA S 5087 */        /* MD_MSR_MemMap */

VAR(boolean, PDUR_VAR_NOINIT) PduR_PreInitialized;

#define PDUR_STOP_SEC_VAR_NOINIT_UNSPECIFIED
#include "PduR_MemMap.h"    /* PRQA S 5087 */        /* MD_MSR_MemMap */
  
/**********************************************************************************************************************
 * LOCAL FUNCTIONS
 *********************************************************************************************************************/

/**********************************************************************************************************************
  GLOBAL FUNCTIONS
**********************************************************************************************************************/
#define PDUR_START_SEC_CODE
/* PRQA S 5087 1 */ /* MD_MSR_MemMap */
#include "PduR_MemMap.h"

/* Exclusive Area Wrapper functions */

/**********************************************************************************************************************
 * PduR_SchM_Enter_PduR_PDUR_EXCLUSIVE_AREA_0
 *********************************************************************************************************************/
/*!
 * \internal
 * -  enter the EA  
 * \endinternal
 *********************************************************************************************************************/
static FUNC(void, PDUR_CODE) PduR_SchM_Enter_PduR_PDUR_EXCLUSIVE_AREA_0(void)
{
  SchM_Enter_PduR_PDUR_EXCLUSIVE_AREA_0();
}


/**********************************************************************************************************************
 * PduR_SchM_Exit_PduR_PDUR_EXCLUSIVE_AREA_0
 *********************************************************************************************************************/
/*!
 * \internal
 * -  exit the EA  
 * \endinternal
 *********************************************************************************************************************/
static FUNC(void, PDUR_CODE) PduR_SchM_Exit_PduR_PDUR_EXCLUSIVE_AREA_0(void)
{
  SchM_Exit_PduR_PDUR_EXCLUSIVE_AREA_0();
}


/* Upper Layer Interface APIs */

/**********************************************************************************************************************
 * PduR_ComTransmit
 *********************************************************************************************************************/
/*!
 * \internal
 * - call upper layer Transmit function. 
 * \endinternal
 *********************************************************************************************************************/
FUNC(Std_ReturnType, PDUR_CODE) PduR_ComTransmit(PduIdType id, P2CONST(PduInfoType, AUTOMATIC, PDUR_APPL_DATA) info) /* COV_PDUR_WRAPPER_FUNC */
{
  Std_ReturnType retVal = E_NOT_OK;        /* PRQA S 2981 */ /* MD_MSR_RetVal */
  
#if (PDUR_TX2LO == STD_ON) /* COV_PDUR_RX_OR_TX_ONLY_CONFIG */
  retVal = PduR_UpTransmit(id, info); /* SBSW_PDUR_EXTERNAL_API_CALL_FORWARDING_ONLY */
#else
  PduR_Det_ReportError(PDUR_FCT_TX, PDUR_E_PDU_ID_INVALID);
#endif

  PDUR_DUMMY_STATEMENT(id);     /* PRQA S 1338, 2983, 3112 */ /* MD_MSR_DummyStmt */ /* lint -e{438} */
  PDUR_DUMMY_STATEMENT(info);   /* PRQA S 1338, 2983, 3112 */ /* MD_MSR_DummyStmt */ /* lint -e{438} */

  return retVal;
}


/**********************************************************************************************************************
 * PduR_DcmTransmit
 *********************************************************************************************************************/
/*!
 * \internal
 * - call upper layer Transmit function. 
 * \endinternal
 *********************************************************************************************************************/
FUNC(Std_ReturnType, PDUR_CODE) PduR_DcmTransmit(PduIdType id, P2CONST(PduInfoType, AUTOMATIC, PDUR_APPL_DATA) info) /* COV_PDUR_WRAPPER_FUNC */
{
  Std_ReturnType retVal = E_NOT_OK;        /* PRQA S 2981 */ /* MD_MSR_RetVal */
  
#if (PDUR_TX2LO == STD_ON) /* COV_PDUR_RX_OR_TX_ONLY_CONFIG */
  retVal = PduR_UpTransmit(id, info); /* SBSW_PDUR_EXTERNAL_API_CALL_FORWARDING_ONLY */
#else
  PduR_Det_ReportError(PDUR_FCT_TX, PDUR_E_PDU_ID_INVALID);
#endif

  PDUR_DUMMY_STATEMENT(id);     /* PRQA S 1338, 2983, 3112 */ /* MD_MSR_DummyStmt */ /* lint -e{438} */
  PDUR_DUMMY_STATEMENT(info);   /* PRQA S 1338, 2983, 3112 */ /* MD_MSR_DummyStmt */ /* lint -e{438} */

  return retVal;
}


/**********************************************************************************************************************
 * PduR_CddLinTpStubTransmit
 *********************************************************************************************************************/
/*!
 * \internal
 * - call upper layer Transmit function. 
 * \endinternal
 *********************************************************************************************************************/
FUNC(Std_ReturnType, PDUR_CODE) PduR_CddLinTpStubTransmit(PduIdType id, P2CONST(PduInfoType, AUTOMATIC, PDUR_APPL_DATA) info) /* COV_PDUR_WRAPPER_FUNC */
{
  Std_ReturnType retVal = E_NOT_OK;        /* PRQA S 2981 */ /* MD_MSR_RetVal */
  
#if (PDUR_TX2LO == STD_ON) /* COV_PDUR_RX_OR_TX_ONLY_CONFIG */
  retVal = PduR_UpTransmit(id, info); /* SBSW_PDUR_EXTERNAL_API_CALL_FORWARDING_ONLY */
#else
  PduR_Det_ReportError(PDUR_FCT_TX, PDUR_E_PDU_ID_INVALID);
#endif

  PDUR_DUMMY_STATEMENT(id);     /* PRQA S 1338, 2983, 3112 */ /* MD_MSR_DummyStmt */ /* lint -e{438} */
  PDUR_DUMMY_STATEMENT(info);   /* PRQA S 1338, 2983, 3112 */ /* MD_MSR_DummyStmt */ /* lint -e{438} */

  return retVal;
}


/* Communication Interface APIs */

/**********************************************************************************************************************
 * PduR_CanIfRxIndication
 *********************************************************************************************************************/
/*!
 * \internal
 * -  call internal general IfRxIndication function.  
 * \endinternal
 *********************************************************************************************************************/
FUNC(void, PDUR_CODE) PduR_CanIfRxIndication(PduIdType RxPduId, P2CONST(PduInfoType, AUTOMATIC, PDUR_APPL_DATA) info) /* COV_PDUR_WRAPPER_FUNC */
{
#if (PDUR_RXIF2DEST == STD_ON) /* COV_PDUR_RX_OR_TX_ONLY_CONFIG */
  PduR_LoIfRxIndication(RxPduId, info);  /* SBSW_PDUR_EXTERNAL_API_CALL_FORWARDING_ONLY */
#else
  PduR_Det_ReportError(PDUR_FCT_IFRXIND, PDUR_E_PDU_ID_INVALID);
#endif
  PDUR_DUMMY_STATEMENT(RxPduId);        /* PRQA S 1338, 2983, 3112 */ /* MD_MSR_DummyStmt */ /* lint -e{438} */
  PDUR_DUMMY_STATEMENT(info);   		/* PRQA S 1338, 2983, 3112 */ /* MD_MSR_DummyStmt */ /* lint -e{438} */
}


/**********************************************************************************************************************
 * PduR_CanIfTxConfirmation
 *********************************************************************************************************************/
/*!
 * \internal
 * - call internal general communication interface TxConfirmation function.
 * \endinternal
 *********************************************************************************************************************/
FUNC(void, PDUR_CODE) PduR_CanIfTxConfirmation(PduIdType TxPduId) /* COV_PDUR_WRAPPER_FUNC */
{
#if (PDUR_TXCONFIRMATIONUSEDOFTXIF2UP == STD_ON)
  PduR_LoIfTxConfirmation(TxPduId);
#endif
  PDUR_DUMMY_STATEMENT(TxPduId);        /* PRQA S 1338, 2983, 3112 */ /* MD_MSR_DummyStmt */ /* lint -e{438} */
}



/**********************************************************************************************************************
 * PduR_CanNmRxIndication
 *********************************************************************************************************************/
/*!
 * \internal
 * -  call internal general IfRxIndication function.  
 * \endinternal
 *********************************************************************************************************************/
FUNC(void, PDUR_CODE) PduR_CanNmRxIndication(PduIdType RxPduId, P2CONST(PduInfoType, AUTOMATIC, PDUR_APPL_DATA) info) /* COV_PDUR_WRAPPER_FUNC */
{
#if (PDUR_RXIF2DEST == STD_ON) /* COV_PDUR_RX_OR_TX_ONLY_CONFIG */
  PduR_LoIfRxIndication(RxPduId, info);  /* SBSW_PDUR_EXTERNAL_API_CALL_FORWARDING_ONLY */
#else
  PduR_Det_ReportError(PDUR_FCT_IFRXIND, PDUR_E_PDU_ID_INVALID);
#endif
  PDUR_DUMMY_STATEMENT(RxPduId);        /* PRQA S 1338, 2983, 3112 */ /* MD_MSR_DummyStmt */ /* lint -e{438} */
  PDUR_DUMMY_STATEMENT(info);   		/* PRQA S 1338, 2983, 3112 */ /* MD_MSR_DummyStmt */ /* lint -e{438} */
}


/**********************************************************************************************************************
 * PduR_FrIfRxIndication
 *********************************************************************************************************************/
/*!
 * \internal
 * -  call internal general IfRxIndication function.  
 * \endinternal
 *********************************************************************************************************************/
FUNC(void, PDUR_CODE) PduR_FrIfRxIndication(PduIdType RxPduId, P2CONST(PduInfoType, AUTOMATIC, PDUR_APPL_DATA) info) /* COV_PDUR_WRAPPER_FUNC */
{
#if (PDUR_RXIF2DEST == STD_ON) /* COV_PDUR_RX_OR_TX_ONLY_CONFIG */
  PduR_LoIfRxIndication(RxPduId, info);  /* SBSW_PDUR_EXTERNAL_API_CALL_FORWARDING_ONLY */
#else
  PduR_Det_ReportError(PDUR_FCT_IFRXIND, PDUR_E_PDU_ID_INVALID);
#endif
  PDUR_DUMMY_STATEMENT(RxPduId);        /* PRQA S 1338, 2983, 3112 */ /* MD_MSR_DummyStmt */ /* lint -e{438} */
  PDUR_DUMMY_STATEMENT(info);   		/* PRQA S 1338, 2983, 3112 */ /* MD_MSR_DummyStmt */ /* lint -e{438} */
}


/**********************************************************************************************************************
 * PduR_FrNmRxIndication
 *********************************************************************************************************************/
/*!
 * \internal
 * -  call internal general IfRxIndication function.  
 * \endinternal
 *********************************************************************************************************************/
FUNC(void, PDUR_CODE) PduR_FrNmRxIndication(PduIdType RxPduId, P2CONST(PduInfoType, AUTOMATIC, PDUR_APPL_DATA) info) /* COV_PDUR_WRAPPER_FUNC */
{
#if (PDUR_RXIF2DEST == STD_ON) /* COV_PDUR_RX_OR_TX_ONLY_CONFIG */
  PduR_LoIfRxIndication(RxPduId, info);  /* SBSW_PDUR_EXTERNAL_API_CALL_FORWARDING_ONLY */
#else
  PduR_Det_ReportError(PDUR_FCT_IFRXIND, PDUR_E_PDU_ID_INVALID);
#endif
  PDUR_DUMMY_STATEMENT(RxPduId);        /* PRQA S 1338, 2983, 3112 */ /* MD_MSR_DummyStmt */ /* lint -e{438} */
  PDUR_DUMMY_STATEMENT(info);   		/* PRQA S 1338, 2983, 3112 */ /* MD_MSR_DummyStmt */ /* lint -e{438} */
}


/**********************************************************************************************************************
 * PduR_LinIfRxIndication
 *********************************************************************************************************************/
/*!
 * \internal
 * -  call internal general IfRxIndication function.  
 * \endinternal
 *********************************************************************************************************************/
FUNC(void, PDUR_CODE) PduR_LinIfRxIndication(PduIdType RxPduId, P2CONST(PduInfoType, AUTOMATIC, PDUR_APPL_DATA) info) /* COV_PDUR_WRAPPER_FUNC */
{
#if (PDUR_RXIF2DEST == STD_ON) /* COV_PDUR_RX_OR_TX_ONLY_CONFIG */
  PduR_LoIfRxIndication(RxPduId, info);  /* SBSW_PDUR_EXTERNAL_API_CALL_FORWARDING_ONLY */
#else
  PduR_Det_ReportError(PDUR_FCT_IFRXIND, PDUR_E_PDU_ID_INVALID);
#endif
  PDUR_DUMMY_STATEMENT(RxPduId);        /* PRQA S 1338, 2983, 3112 */ /* MD_MSR_DummyStmt */ /* lint -e{438} */
  PDUR_DUMMY_STATEMENT(info);   		/* PRQA S 1338, 2983, 3112 */ /* MD_MSR_DummyStmt */ /* lint -e{438} */
}


/**********************************************************************************************************************
 * PduR_LinIfTxConfirmation
 *********************************************************************************************************************/
/*!
 * \internal
 * - call internal general communication interface TxConfirmation function.
 * \endinternal
 *********************************************************************************************************************/
FUNC(void, PDUR_CODE) PduR_LinIfTxConfirmation(PduIdType TxPduId) /* COV_PDUR_WRAPPER_FUNC */
{
#if (PDUR_TXCONFIRMATIONUSEDOFTXIF2UP == STD_ON)
  PduR_LoIfTxConfirmation(TxPduId);
#endif
  PDUR_DUMMY_STATEMENT(TxPduId);        /* PRQA S 1338, 2983, 3112 */ /* MD_MSR_DummyStmt */ /* lint -e{438} */
}




/**********************************************************************************************************************
 * PduR_CanNmTriggerTransmit
 *********************************************************************************************************************/
/*!
 * \internal
 * - call internal general TriggerTransmit function.  
 * \endinternal
 *********************************************************************************************************************/
FUNC(Std_ReturnType, PDUR_CODE) PduR_CanNmTriggerTransmit(PduIdType TxPduId, P2VAR(PduInfoType, AUTOMATIC, PDUR_APPL_DATA) info)
{
  return PduR_LoIfTriggerTransmit(TxPduId, info); /* SBSW_PDUR_EXTERNAL_API_CALL_FORWARDING_ONLY */
}


/**********************************************************************************************************************
 * PduR_FrIfTriggerTransmit
 *********************************************************************************************************************/
/*!
 * \internal
 * - call internal general TriggerTransmit function.  
 * \endinternal
 *********************************************************************************************************************/
FUNC(Std_ReturnType, PDUR_CODE) PduR_FrIfTriggerTransmit(PduIdType TxPduId, P2VAR(PduInfoType, AUTOMATIC, PDUR_APPL_DATA) info)
{
  return PduR_LoIfTriggerTransmit(TxPduId, info); /* SBSW_PDUR_EXTERNAL_API_CALL_FORWARDING_ONLY */
}


/**********************************************************************************************************************
 * PduR_FrNmTriggerTransmit
 *********************************************************************************************************************/
/*!
 * \internal
 * - call internal general TriggerTransmit function.  
 * \endinternal
 *********************************************************************************************************************/
FUNC(Std_ReturnType, PDUR_CODE) PduR_FrNmTriggerTransmit(PduIdType TxPduId, P2VAR(PduInfoType, AUTOMATIC, PDUR_APPL_DATA) info)
{
  return PduR_LoIfTriggerTransmit(TxPduId, info); /* SBSW_PDUR_EXTERNAL_API_CALL_FORWARDING_ONLY */
}


/**********************************************************************************************************************
 * PduR_LinIfTriggerTransmit
 *********************************************************************************************************************/
/*!
 * \internal
 * - call internal general TriggerTransmit function.  
 * \endinternal
 *********************************************************************************************************************/
FUNC(Std_ReturnType, PDUR_CODE) PduR_LinIfTriggerTransmit(PduIdType TxPduId, P2VAR(PduInfoType, AUTOMATIC, PDUR_APPL_DATA) info)
{
  return PduR_LoIfTriggerTransmit(TxPduId, info); /* SBSW_PDUR_EXTERNAL_API_CALL_FORWARDING_ONLY */
}


/* Transport Protocol APIs */

/**********************************************************************************************************************
 * PduR_CanTpStartOfReception
 *********************************************************************************************************************/
 /*!
 * \internal
 * - call transport protocoll StartOfReception function.  
 * \endinternal
 *********************************************************************************************************************/
FUNC(BufReq_ReturnType, PDUR_CODE) PduR_CanTpStartOfReception(PduIdType id, P2VAR(PduInfoType, AUTOMATIC, PDUR_APPL_DATA) info, PduLengthType TpSduLength, P2VAR(PduLengthType, AUTOMATIC, PDUR_APPL_DATA) bufferSizePtr) /* COV_PDUR_WRAPPER_FUNC */
{
  BufReq_ReturnType retVal = BUFREQ_E_NOT_OK;        /* PRQA S 2981 */ /* MD_MSR_RetVal */

#if (PDUR_RXTP2DEST == STD_ON) /* COV_PDUR_RX_OR_TX_ONLY_CONFIG */
  retVal = PduR_LoTpStartOfReception(id, info, TpSduLength, bufferSizePtr); /* SBSW_PDUR_EXTERNAL_API_CALL_FORWARDING_ONLY */
#else
  PduR_Det_ReportError(PDUR_FCT_SOR, PDUR_E_PDU_ID_INVALID);
#endif

  PDUR_DUMMY_STATEMENT(id);     		/* PRQA S 1338, 2983, 3112 */ /* MD_MSR_DummyStmt */ /* lint -e{438} */
  PDUR_DUMMY_STATEMENT(info);   		/* PRQA S 1338, 2983, 3112 */ /* MD_MSR_DummyStmt */ /* lint -e{438} */
  PDUR_DUMMY_STATEMENT(TpSduLength);    /* PRQA S 1338, 2983, 3112 */ /* MD_MSR_DummyStmt */ /* lint -e{438} */
  PDUR_DUMMY_STATEMENT(bufferSizePtr);  /* PRQA S 1338, 2983, 3112 */ /* MD_MSR_DummyStmt */ /* lint -e{438} */

  return retVal;
}


/**********************************************************************************************************************
 * PduR_CanTpCopyRxData
 *********************************************************************************************************************/
/*!
 * \internal
 * - call internal general CopyRxData.
 * \endinternal
 *********************************************************************************************************************/
FUNC(BufReq_ReturnType, PDUR_CODE) PduR_CanTpCopyRxData(PduIdType id, P2VAR(PduInfoType, AUTOMATIC, PDUR_APPL_DATA) info, P2VAR(PduLengthType, AUTOMATIC, PDUR_APPL_DATA) bufferSizePtr) /* COV_PDUR_WRAPPER_FUNC */
{
  BufReq_ReturnType retVal = BUFREQ_E_NOT_OK;        /* PRQA S 2981 */ /* MD_MSR_RetVal */
  
#if (PDUR_RXTP2DEST == STD_ON) /* COV_PDUR_RX_OR_TX_ONLY_CONFIG */
  retVal = PduR_LoTpCopyRxData(id, info, bufferSizePtr); /* SBSW_PDUR_EXTERNAL_API_CALL_FORWARDING_ONLY */
#else
  PduR_Det_ReportError(PDUR_FCT_CPYRX, PDUR_E_PDU_ID_INVALID);
#endif

  PDUR_DUMMY_STATEMENT(id);     		/* PRQA S 1338, 2983, 3112 */ /* MD_MSR_DummyStmt */ /* lint -e{438} */
  PDUR_DUMMY_STATEMENT(info);  			/* PRQA S 1338, 2983, 3112 */ /* MD_MSR_DummyStmt */ /* lint -e{438} */
  PDUR_DUMMY_STATEMENT(bufferSizePtr);  /* PRQA S 1338, 2983, 3112 */ /* MD_MSR_DummyStmt */ /* lint -e{438} */
  
  return retVal;
}


/**********************************************************************************************************************
 * PduR_CanTpCopyTxData
 *********************************************************************************************************************/
/*!
 * \internal
 * - call internal general CopyTxData.
 * \endinternal
 *********************************************************************************************************************/
FUNC(BufReq_ReturnType, PDUR_CODE) PduR_CanTpCopyTxData(PduIdType id, P2VAR(PduInfoType, AUTOMATIC, PDUR_APPL_DATA) info, P2VAR(RetryInfoType, AUTOMATIC, PDUR_APPL_DATA) retry, P2VAR(PduLengthType, AUTOMATIC, PDUR_APPL_DATA) availableDataPtr) /* COV_PDUR_WRAPPER_FUNC */
{
  BufReq_ReturnType retVal = BUFREQ_E_NOT_OK;        /* PRQA S 2981 */ /* MD_MSR_RetVal */
  
#if(PDUR_TXTP2SRC == STD_ON) /* COV_PDUR_RX_OR_TX_ONLY_CONFIG */
  retVal = PduR_LoTpCopyTxData(id, info, retry, availableDataPtr); /* SBSW_PDUR_EXTERNAL_API_CALL_FORWARDING_ONLY */
#else
  PduR_Det_ReportError(PDUR_FCT_CPYTX, PDUR_E_PDU_ID_INVALID);
#endif

  PDUR_DUMMY_STATEMENT(id);     				/* PRQA S 1338, 2983, 3112 */ /* MD_MSR_DummyStmt */ /* lint -e{438} */
  PDUR_DUMMY_STATEMENT(info);  					/* PRQA S 1338, 2983, 3112 */ /* MD_MSR_DummyStmt */ /* lint -e{438} */
  PDUR_DUMMY_STATEMENT(retry);  				/* PRQA S 1338, 2983, 3112 */ /* MD_MSR_DummyStmt */ /* lint -e{438} */
  PDUR_DUMMY_STATEMENT(availableDataPtr);       /* PRQA S 1338, 2983, 3112 */ /* MD_MSR_DummyStmt */ /* lint -e{438} */
  
  return retVal;
}


/**********************************************************************************************************************
 * PduR_CanTpRxIndication
 *********************************************************************************************************************/
/*!
 * \internal
 * - call Tp RxIndication function.
 * \endinternal
 *********************************************************************************************************************/
FUNC(void, PDUR_CODE) PduR_CanTpRxIndication(PduIdType id, Std_ReturnType result) /* COV_PDUR_WRAPPER_FUNC */
{
#if (PDUR_RXTP2DEST == STD_ON) /* COV_PDUR_RX_OR_TX_ONLY_CONFIG */
  PduR_LoTpRxIndication(id, result);
#else
  PduR_Det_ReportError(PDUR_FCT_TPRXIND, PDUR_E_PDU_ID_INVALID);
#endif

  PDUR_DUMMY_STATEMENT(id);     /* PRQA S 1338, 2983, 3112 */ /* MD_MSR_DummyStmt */ /* lint -e{438} */
  PDUR_DUMMY_STATEMENT(result); /* PRQA S 1338, 2983, 3112 */ /* MD_MSR_DummyStmt */ /* lint -e{438} */
}


/**********************************************************************************************************************
 * PduR_CanTpTxConfirmation
 *********************************************************************************************************************/
/*!
 * \internal
 * - call Tp TxConfirmation function 
 * \endinternal
 *********************************************************************************************************************/
FUNC(void, PDUR_CODE) PduR_CanTpTxConfirmation(PduIdType id, Std_ReturnType result) /* COV_PDUR_WRAPPER_FUNC */
{
#if (PDUR_TXTP2SRC == STD_ON) /* COV_PDUR_RX_OR_TX_ONLY_CONFIG */
  PduR_LoTpTxConfirmation(id, result);
#else
  PduR_Det_ReportError(PDUR_FCT_TPTXCFM, PDUR_E_PDU_ID_INVALID);
#endif

  PDUR_DUMMY_STATEMENT(id);     /* PRQA S 1338, 2983, 3112 */ /* MD_MSR_DummyStmt */ /* lint -e{438} */
  PDUR_DUMMY_STATEMENT(result); /* PRQA S 1338, 2983, 3112 */ /* MD_MSR_DummyStmt */ /* lint -e{438} */
}


/**********************************************************************************************************************
 * PduR_LinTpStartOfReception
 *********************************************************************************************************************/
 /*!
 * \internal
 * - call transport protocoll StartOfReception function.  
 * \endinternal
 *********************************************************************************************************************/
FUNC(BufReq_ReturnType, PDUR_CODE) PduR_LinTpStartOfReception(PduIdType id, P2VAR(PduInfoType, AUTOMATIC, PDUR_APPL_DATA) info, PduLengthType TpSduLength, P2VAR(PduLengthType, AUTOMATIC, PDUR_APPL_DATA) bufferSizePtr) /* COV_PDUR_WRAPPER_FUNC */
{
  BufReq_ReturnType retVal = BUFREQ_E_NOT_OK;        /* PRQA S 2981 */ /* MD_MSR_RetVal */

#if (PDUR_RXTP2DEST == STD_ON) /* COV_PDUR_RX_OR_TX_ONLY_CONFIG */
  retVal = PduR_LoTpStartOfReception(id, info, TpSduLength, bufferSizePtr); /* SBSW_PDUR_EXTERNAL_API_CALL_FORWARDING_ONLY */
#else
  PduR_Det_ReportError(PDUR_FCT_SOR, PDUR_E_PDU_ID_INVALID);
#endif

  PDUR_DUMMY_STATEMENT(id);     		/* PRQA S 1338, 2983, 3112 */ /* MD_MSR_DummyStmt */ /* lint -e{438} */
  PDUR_DUMMY_STATEMENT(info);   		/* PRQA S 1338, 2983, 3112 */ /* MD_MSR_DummyStmt */ /* lint -e{438} */
  PDUR_DUMMY_STATEMENT(TpSduLength);    /* PRQA S 1338, 2983, 3112 */ /* MD_MSR_DummyStmt */ /* lint -e{438} */
  PDUR_DUMMY_STATEMENT(bufferSizePtr);  /* PRQA S 1338, 2983, 3112 */ /* MD_MSR_DummyStmt */ /* lint -e{438} */

  return retVal;
}


/**********************************************************************************************************************
 * PduR_LinTpCopyRxData
 *********************************************************************************************************************/
/*!
 * \internal
 * - call internal general CopyRxData.
 * \endinternal
 *********************************************************************************************************************/
FUNC(BufReq_ReturnType, PDUR_CODE) PduR_LinTpCopyRxData(PduIdType id, P2VAR(PduInfoType, AUTOMATIC, PDUR_APPL_DATA) info, P2VAR(PduLengthType, AUTOMATIC, PDUR_APPL_DATA) bufferSizePtr) /* COV_PDUR_WRAPPER_FUNC */
{
  BufReq_ReturnType retVal = BUFREQ_E_NOT_OK;        /* PRQA S 2981 */ /* MD_MSR_RetVal */
  
#if (PDUR_RXTP2DEST == STD_ON) /* COV_PDUR_RX_OR_TX_ONLY_CONFIG */
  retVal = PduR_LoTpCopyRxData(id, info, bufferSizePtr); /* SBSW_PDUR_EXTERNAL_API_CALL_FORWARDING_ONLY */
#else
  PduR_Det_ReportError(PDUR_FCT_CPYRX, PDUR_E_PDU_ID_INVALID);
#endif

  PDUR_DUMMY_STATEMENT(id);     		/* PRQA S 1338, 2983, 3112 */ /* MD_MSR_DummyStmt */ /* lint -e{438} */
  PDUR_DUMMY_STATEMENT(info);  			/* PRQA S 1338, 2983, 3112 */ /* MD_MSR_DummyStmt */ /* lint -e{438} */
  PDUR_DUMMY_STATEMENT(bufferSizePtr);  /* PRQA S 1338, 2983, 3112 */ /* MD_MSR_DummyStmt */ /* lint -e{438} */
  
  return retVal;
}


/**********************************************************************************************************************
 * PduR_LinTpCopyTxData
 *********************************************************************************************************************/
/*!
 * \internal
 * - call internal general CopyTxData.
 * \endinternal
 *********************************************************************************************************************/
FUNC(BufReq_ReturnType, PDUR_CODE) PduR_LinTpCopyTxData(PduIdType id, P2VAR(PduInfoType, AUTOMATIC, PDUR_APPL_DATA) info, P2VAR(RetryInfoType, AUTOMATIC, PDUR_APPL_DATA) retry, P2VAR(PduLengthType, AUTOMATIC, PDUR_APPL_DATA) availableDataPtr) /* COV_PDUR_WRAPPER_FUNC */
{
  BufReq_ReturnType retVal = BUFREQ_E_NOT_OK;        /* PRQA S 2981 */ /* MD_MSR_RetVal */
  
#if(PDUR_TXTP2SRC == STD_ON) /* COV_PDUR_RX_OR_TX_ONLY_CONFIG */
  retVal = PduR_LoTpCopyTxData(id, info, retry, availableDataPtr); /* SBSW_PDUR_EXTERNAL_API_CALL_FORWARDING_ONLY */
#else
  PduR_Det_ReportError(PDUR_FCT_CPYTX, PDUR_E_PDU_ID_INVALID);
#endif

  PDUR_DUMMY_STATEMENT(id);     				/* PRQA S 1338, 2983, 3112 */ /* MD_MSR_DummyStmt */ /* lint -e{438} */
  PDUR_DUMMY_STATEMENT(info);  					/* PRQA S 1338, 2983, 3112 */ /* MD_MSR_DummyStmt */ /* lint -e{438} */
  PDUR_DUMMY_STATEMENT(retry);  				/* PRQA S 1338, 2983, 3112 */ /* MD_MSR_DummyStmt */ /* lint -e{438} */
  PDUR_DUMMY_STATEMENT(availableDataPtr);       /* PRQA S 1338, 2983, 3112 */ /* MD_MSR_DummyStmt */ /* lint -e{438} */
  
  return retVal;
}


/**********************************************************************************************************************
 * PduR_LinTpRxIndication
 *********************************************************************************************************************/
/*!
 * \internal
 * - call Tp RxIndication function.
 * \endinternal
 *********************************************************************************************************************/
FUNC(void, PDUR_CODE) PduR_LinTpRxIndication(PduIdType id, Std_ReturnType result) /* COV_PDUR_WRAPPER_FUNC */
{
#if (PDUR_RXTP2DEST == STD_ON) /* COV_PDUR_RX_OR_TX_ONLY_CONFIG */
  PduR_LoTpRxIndication(id, result);
#else
  PduR_Det_ReportError(PDUR_FCT_TPRXIND, PDUR_E_PDU_ID_INVALID);
#endif

  PDUR_DUMMY_STATEMENT(id);     /* PRQA S 1338, 2983, 3112 */ /* MD_MSR_DummyStmt */ /* lint -e{438} */
  PDUR_DUMMY_STATEMENT(result); /* PRQA S 1338, 2983, 3112 */ /* MD_MSR_DummyStmt */ /* lint -e{438} */
}


/**********************************************************************************************************************
 * PduR_LinTpTxConfirmation
 *********************************************************************************************************************/
/*!
 * \internal
 * - call Tp TxConfirmation function 
 * \endinternal
 *********************************************************************************************************************/
FUNC(void, PDUR_CODE) PduR_LinTpTxConfirmation(PduIdType id, Std_ReturnType result) /* COV_PDUR_WRAPPER_FUNC */
{
#if (PDUR_TXTP2SRC == STD_ON) /* COV_PDUR_RX_OR_TX_ONLY_CONFIG */
  PduR_LoTpTxConfirmation(id, result);
#else
  PduR_Det_ReportError(PDUR_FCT_TPTXCFM, PDUR_E_PDU_ID_INVALID);
#endif

  PDUR_DUMMY_STATEMENT(id);     /* PRQA S 1338, 2983, 3112 */ /* MD_MSR_DummyStmt */ /* lint -e{438} */
  PDUR_DUMMY_STATEMENT(result); /* PRQA S 1338, 2983, 3112 */ /* MD_MSR_DummyStmt */ /* lint -e{438} */
}


/* CancelReceive global without UseTag API */

/* ChangeParameter global without UseTag  API */

/* Communication Interface / Transport Protocol APIs */

/* Communication Interface / Transport Protocol APIs */

/**********************************************************************************************************************
 * PduR_DcmCancelTransmit
 *********************************************************************************************************************/
/*!
 * \internal
 * - call internal general CancelTransmit function.
 * \endinternal
 *********************************************************************************************************************/
FUNC(Std_ReturnType, PDUR_CODE) PduR_DcmCancelTransmit(PduIdType id)
{
  return PduR_CancelTransmit(id); /* SBSW_PDUR_EXTERNAL_API_CALL_FORWARDING_ONLY */
}


/**********************************************************************************************************************
  CONFIGURATION CLASS: LINK
  SECTION: GLOBAL FUNCTIONS
**********************************************************************************************************************/


#define PDUR_STOP_SEC_CODE
/* PRQA S 5087 1 */ /* MD_MSR_MemMap */
#include "PduR_MemMap.h"

/* SBSW_JUSTIFICATION_BEGIN

  \ID SBSW_PDUR_EXTERNAL_API_CALL_FORWARDING_ID_AND_PTR
    \DESCRIPTION    The API is called with an Id and a pointer.
                    The API call is forwarded using a function pointer which is read using ComStackLib macros.
                    
    \COUNTERMEASURE \N The function pointer is read using the passed Id (and ComStackLib macros). 
                       The Id is a SNV provided by the lower layer. 
                       It is checked at runtime if it is in the valid range.
                       The pointer is provided by the lower layer and is assumed to be valid. It is passed as is to the corresponding function. 

  \ID SBSW_PDUR_EXTERNAL_API_CALL_FORWARDING_ID_ONLY
    \DESCRIPTION    The API is called with an Id.
                    The API call is forwarded using a function pointer which is read using ComStackLib macros.
                    
    \COUNTERMEASURE \R The function pointer is read using the passed Id (and ComStackLib macros). 
                       The Id is a SNV provided by the lower layer. 
                       It is checked at runtime if it is in the valid range.
                       
  \ID SBSW_PDUR_EXTERNAL_API_CALL_FORWARDING_ONLY
    \DESCRIPTION    The API call is forwarded to an internal function.
    \COUNTERMEASURE \N The parameter are checked in the called function.

 SBSW_JUSTIFICATION_END */
 
 /* COV_JUSTIFICATION_BEGIN
 
  \ID COV_PDUR_WRAPPER_FUNC
    \ACCEPT X
    \REASON Each neighbouring module call the same internal function. The call is covered by an other module. 
    
  \ID COV_PDUR_RX_OR_TX_ONLY_CONFIG
    \ACCEPT TX
    \REASON The API is mandatory if this Bsw module exists. In case of Rx or Tx only configs the API call is not forwarded to the internal function. 
 
 COV_JUSTIFICATION_END */
 

/**********************************************************************************************************************
 * END OF FILE: PduR_Lcfg.c
 *********************************************************************************************************************/

