;/**********************************************************************************************************************
;  AUTHOR IDENTITY
; ----------------------------------------------------------------------------------------------------------------------
;  Name                          Initials      Company
;  ----------------------------  ------------  -------------------------------------------------------------------------
;  <PERSON>               v<PERSON>        Vector Informatik GmbH
;  Joachim Wenzel                visjwo        Vector Informatik GmbH
;  Emanuel Schnierle             visese        Vector Informatik GmbH
;  Hahn Andreas                  vishan        Vector Informatik GmbH
;  Derick Beng Yuh               visydg        Vector Informatik GmbH
;-----------------------------------------------------------------------------------------------------------------------
;  REVISION HISTORY
; ----------------------------------------------------------------------------------------------------------------------
;  Version   Date        Author  Description
;  --------  ----------  ------  ---------------------------------------------------------------------------------------
;  01.00.00  2017-08-01  visscs  Initial creation
;**********************************************************************************************************************/

;========================================================================
;parameters
ENTRY %LINE &para_arch
GLOBAL &Architecture &ResetDebugTime

&Architecture=STRING.SCANAndExtract(STRING.UPpeR("&para_arch"), "ARCHITECTURE=", "0xFFFF")
&Architecture=STRING.LoWeR("&Architecture")
;ResetTime for Lauterbach debuggers without a valid license!
&ResetDebugTime=1
&ResetDebugTime=STRING.SCANAndExtract(STRING.UPpeR("&para_arch"), "RESETDEBUGTIME=", "0x1")

;========================================================================
;Variable declaration

GLOBAL &DerSpecFile
GLOBAL &FlashFile
GLOBAL &NumberOfCores
GLOBAL &Core

GLOBAL &T32Script
GLOBAL &WorkDir
GLOBAL &SuppressLoadingBP

GLOBAL &UseHighResWindows

&SuppressLoadingBP=FALSE()

;BMW only
GLOBAL &FlashFileAppl
GLOBAL &FlashFileBM
GLOBAL &FlashFileBTLD


;########################### User Section ###############################

&FlashFile="TestSuit.elf"

;Set DerSpecFile to a specific *.cmm script otherwise leave it empty to chose a cmm script in the Debugger.
&DerSpecFile="unknown"

;Set here the amount of Cores of your derivative!
;Be careful if you have a debugger with a demo license.
;In this case you should leave it on 1. or you have only 5 Minutes debug time!
&NumberOfCores=1.

;--------------------------- Optional ---------------------------------

;THE FOLLOWING PARAMETER IS OPTIONAL AND NOT NECESSARY FOR POWERPC!
;EDIT THIS ONLY IF YOU ARE SURE THAT IT IS THE CORRECT CPU!
&Core="unknown"

;Select the debugging mode
&DebugMode="HLL" ;Show only HLL Code and no assembly
;&DebugMode="ASM" ;Show only assembly
;&DebugMode="MIX" ;Show both, HLL Code and assembly

;Set the value to 1 only if you use Monitor resolution above 1920x1080
&UseHighResWindows=0.

;--------------------------- BMW only! ---------------------------------
;Put the name of the files in the "", but without the file suffix!
&FlashFileAppl="unknown"
&FlashFileBM="unknown"
&FlashFileBTLD="unknown"

;########################################################################
	
;========================================================================
;Script selection

;Save working directory of _lauterbach folder for later usage
&WorkDir=OS.PWD()

if "&DerSpecFile"=="unknown"
(
  if "&Architecture"=="powerpc" 
  (
    DIALOG.YESNO "Do you want to use the generic family script mpc5xxx.cmm?"
    LOCAL &answer
    ENTRY &answer

    if &answer
    (
      &T32Script="~~\demo\&Architecture\flash\mpc5xxx.cmm"
    )
    else
    (
      CHDIR ~~\demo\&Architecture\flash
      DIALOG.FILE *.cmm
      ENTRY &T32Script
    )
  )    
  else
  (
    CHDIR ~~\demo\&Architecture\flash
    DIALOG.FILE *.cmm
    ENTRY &T32Script
  ) 
)
else 
(
  if (os.file(&DerSpecFile))
  (
    ; use patched script from _lauterbach folder
    &T32Script="&DerSpecFile"
  )
  else
  (
    ; use original script from t32 installation
    &T32Script="~~\demo\&Architecture\flash\&DerSpecFile"
  )
)
 

;Restore working directory _lauterbach for further usage
CHDIR "&WorkDir"

;========================================================================
; CPU setup and Flash declaration

  if (os.file(&T32Script))
  (
    ;________________________________________________________________________
    ;It is necessary for the issue of some Infineon TriBoard's, that the DAP2 connecter will be not detected correctly.
    ;Enable error handler for debug port connection problems
    ERROR.RESET
    ON ERROR GOSUB
    (
      ;possible erros:  
      ;#emu_errdebug
      if ERROR.ID()=="#emu_errdebug"
      (
        DIALOG.OK "Your debugger can not connect to your device!" \
	              "This could be the result of..." \
	  			  "1) Check supply voltage of the Board." \
	  			  "2) If you have a TriBoard => Read the Manual in the _lauterbach folder of your SIP!"
      )  
      ;##cbf_nomorearg
      else if ERROR.ID()=="##cbf_nomorearg"
      (
        DIALOG.OK "Set the correct number of cores in _start.cmm!"
      )
      ;Shows the error id if no previous error ids matches
      else if ERROR.ID()!=""
      (
        DIALOG.OK "Error occurred : " ERROR.ID()
      )
      
      RETURN
    )
    ;________________________________________________________________________
     
    if ("&Core"!="unknown")
	(
	   ;This line is necessary to remove "" for the cmm parameter
	   &Core="&Core"
       do "&T32Script" PREPAREONLY CPU=&Core
	)
    else
	(
       do "&T32Script" PREPAREONLY
	)
	
	;Assining Cores (Workaround for missing core assigns in the default scripts)
	if (&NumberOfCores>1.)
	(
	  SYSTEM.DOWN
	  
	  if (&NumberOfCores==2.)
	  (
	    CORE.assign 1. 2.
	  )
	  else if (&NumberOfCores==3.)
	  (
	    CORE.assign 1. 2. 3.
	  )
	  else if (&NumberOfCores==4.)
	  (
	    CORE.assign 1. 2. 3. 4.
	  )
	  else if (&NumberOfCores==5.)
	  (
	    CORE.assign 1. 2. 3. 4. 5.
	  )
	  else if (&NumberOfCores==6.)
	  (
	    CORE.assign 1. 2. 3. 4. 5. 6.
	  )
	  SYSTEM.UP
	)
	 
    ;________________________________________________________________________
    ; Restore previous error handler
    ON ERROR inherit
    ;________________________________________________________________________
  )
  else
  (
    ;information about missing cmm script in T32 installation folder
    DIALOG.OK "Your choosen &T32Script is missing!"
  )
  
  ;load symbols to avoid missing breakpoint errors
  if ("&FlashFile"!="unknown")
  (
    DATA.LOAD.auto ..\&FlashFile /NOCODE
  )
  else
  (
    DIALOG.OK "Your binary file for download is missing!" \
	          "Set your script in the user Section of the _start.cmm OR" \
			  "flash your binary via the 'Download the binary (choose it manually)' button."
    
	;suppress loading the breakpoints, because of the symbols could not load and avoid the message box for each BP.
	&SuppressLoadingBP=TRUE()
  )

;========================================================================
;Load Toolbar

  if (os.file(.\toolbar_common.cmm))
  (
   do ".\toolbar_common.cmm"
  )

  if (os.file(.\toolbar_bmw.cmm))
  (
   do ".\toolbar_bmw.cmm"
  )
  
  if (os.file(.\toolbar_mc.cmm))
  (
   do ".\toolbar_mc.cmm"
  )

;========================================================================
;Delivery defined menu entry

  if (os.file(.\menu_common.cmm))
  (
   do ".\menu_common.cmm"
  )
  
;========================================================================
;Load Windows

  if (os.file(.\expert_settings_list.txt))
  (
   WinPAGE.Create Expert_Commands
   WinPAGE.select Expert_Commands
   WinCLEAR 
   WinPOS 0.1 0.1 100. 64. 0. 0. expert
   edit expert_settings_list.txt   
  )
 
  if (os.file(.\windows_mc.cmm))
  (
   WinPAGE.Create MultiCorePage
   do ".\windows_mc.cmm"
  )
  
  if (os.file(.\windows_userpage.cmm))
  (
   WinPAGE.Create UserPage
   do ".\windows_userpage.cmm"
  )

  if (os.file(.\windows_common.cmm))
  (
   WinPAGE.Create MainPage
   do ".\windows_common.cmm"
  )
 
 
;========================================================================
;Load Variables
;If you add some other variable files, also add them in the toolbar_common.cmm!

  if (os.file(.\var_ethernet.cmm))
  (
   do ".\var_ethernet.cmm"
  )
  
  if (os.file(.\var_det.cmm))
  (
   do ".\var_det.cmm"
  )
  
  if (os.file(.\var_os.cmm))
  (
   do ".\var_os.cmm"
  )

  if (os.file(.\var_user.cmm))
  (
   do ".\var_user.cmm"
  )

;========================================================================
;Load Breakpoints

;suppress loading the breakpoints, because of the symbols could not load and avoid the message box for each BP.
if &SuppressLoadingBP==FALSE()
(
    if (os.file(.\breakpoints_common.cmm))
    (
     do ".\breakpoints_common.cmm"
    )        
)

;========================================================================
;Orti Support

if ("&FlashFile"!="unknown")
(
  if (os.file(.\orti_common.cmm))
  (
   do ".\orti_common.cmm"
  )
)
 
;========================================================================
;Load Data View windows

  if (os.file(.\dataview_common.cmm))
  (   
   do ".\dataview_common.cmm"
  )

;========================================================================
WINPAGE.Select MainPage
  
register.reset

;========================================================================
;Demo time Support

  if (&ResetDebugTime==0x1)
  (
    GLOBAL &time
    &time=DATE.TIME()
  )
 
    AREA.Create StartTime
    AREA.Select StartTime
    
   if (&UseHighResWindows==1.)
   (
     WINPOS 224. 96.5 85. 5. 0. 0. IOStartTime   ;big monitor resolution
   )
   ELSE
   (
     WINPOS 182. 62. 85. 5. 0. 0. IOStartTime   ; small monitor resolution
   )
    
    AREA.View StartTime
    
    IF (&NumberOfCores==1.)
    (
      print "Trace32 start time: &time"
      print "If you have no license, your Trace32 demo expires in 30 minutes! (SingleCore)"
    )
    ELSE
    (
      print "Trace32 start time: &time"
      print "If you have no license, your Trace32 demo expires in 5 minutes! (MultiCore)"
    )
    ;AREA.Reset

ENDDO
