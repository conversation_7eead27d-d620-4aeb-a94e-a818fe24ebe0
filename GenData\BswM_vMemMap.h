/**********************************************************************************************************************
 *  COPYRIGHT
 *  -------------------------------------------------------------------------------------------------------------------
 *  \verbatim
 *
 *                 This software is copyright protected and proprietary to Vector Informatik GmbH.
 *                 Vector Informatik GmbH grants to you only those rights as set out in the license conditions.
 *                 All other rights remain with Vector Informatik GmbH.
 *  \endverbatim
 *  -------------------------------------------------------------------------------------------------------------------
 *  LICENSE
 *  -------------------------------------------------------------------------------------------------------------------
 *            Module: BswM
 *           Program: MSR_Vector_SLP4
 *          Customer: AUTOSAR 4 Evaluation Bundle
 *       Expiry Date: Not restricted
 *  Ordered Derivat.: Aurix 2G/TC377
 *    License Scope : The usage is restricted to CBD2000456_D02
 *
 *  -------------------------------------------------------------------------------------------------------------------
 *  FILE DESCRIPTION
 *  -------------------------------------------------------------------------------------------------------------------
 *              File: BswM_vMemMap.h
 *   Generation Time: 2025-08-05 10:37:19
 *           Project: Demo - Version 1.0
 *          Delivery: CBD2000456_D02
 *      Tool Version: DaVinci Configurator (beta) 5.21.40 SP2
 *
 *       Description: This File is a template for the MemMap.h 
 *                    This file has to be extended with the memory section defines required for your module.
 *
 *********************************************************************************************************************/

/**********************************************************************************************************************
 ! BETA VERSION                                                                                                       !
 !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
 ! This version of DaVinci Configurator Pro and/or the related Basic Software Package is BETA software.               !
 ! BETA Software is basically operable, but not sufficiently tested, verified and/or qualified for use in series      !
 ! production and/or in vehicles operating on public or non-public roads.                                             !
 ! In particular, without limitation, BETA Software may cause unpredictable ECU behavior, may not provide all         !
 ! functions necessary for use in series production and/or may not comply with quality requirements which are         !
 ! necessary according to the state of the art. BETA Software must not be used in series production.                  !
 !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
**********************************************************************************************************************/




/**********************************************************************************************************************
 *   BSWM START
 **********************************************************************************************************************/

# define MEMMAP_ERROR

/*****************  CODE sections   **********************************************************************************/  

/**********************************************************************************************************************
 * DO NOT CHANGE THIS COMMENT!           <USERBLOCK BSWM_START_SEC_CODE>
 *********************************************************************************************************************/
#ifdef  BSWM_START_SEC_CODE
  #undef BSWM_START_SEC_CODE   /* PRQA S 0841 */  /* MD_MSR_Undef */
  #define START_SEC_CODE
#endif
/**********************************************************************************************************************
 * DO NOT CHANGE THIS COMMENT!           </USERBLOCK>
 *********************************************************************************************************************/
/**********************************************************************************************************************
 * DO NOT CHANGE THIS COMMENT!           <USERBLOCK BSWM_STOP_SEC_CODE>
 *********************************************************************************************************************/
#ifdef  BSWM_STOP_SEC_CODE
  #undef BSWM_STOP_SEC_CODE   /* PRQA S 0841 */  /* MD_MSR_Undef */
  #define STOP_SEC_CODE
#endif
/**********************************************************************************************************************
 * DO NOT CHANGE THIS COMMENT!           </USERBLOCK>
 *********************************************************************************************************************/

/*****************  CONST sections   *********************************************************************************/  

/**********************************************************************************************************************
 * DO NOT CHANGE THIS COMMENT!           <USERBLOCK BSWM_START_SEC_CONST_16BIT>
 *********************************************************************************************************************/
#ifdef  BSWM_START_SEC_CONST_16BIT
  #undef BSWM_START_SEC_CONST_16BIT   /* PRQA S 0841 */  /* MD_MSR_Undef */
  #define START_SEC_CONST_16BIT
#endif
/**********************************************************************************************************************
 * DO NOT CHANGE THIS COMMENT!           </USERBLOCK>
 *********************************************************************************************************************/
/**********************************************************************************************************************
 * DO NOT CHANGE THIS COMMENT!           <USERBLOCK BSWM_START_SEC_CONST_8BIT>
 *********************************************************************************************************************/
#ifdef  BSWM_START_SEC_CONST_8BIT
  #undef BSWM_START_SEC_CONST_8BIT   /* PRQA S 0841 */  /* MD_MSR_Undef */
  #define START_SEC_CONST_8BIT
#endif
/**********************************************************************************************************************
 * DO NOT CHANGE THIS COMMENT!           </USERBLOCK>
 *********************************************************************************************************************/
/**********************************************************************************************************************
 * DO NOT CHANGE THIS COMMENT!           <USERBLOCK BSWM_START_SEC_CONST_UNSPECIFIED>
 *********************************************************************************************************************/
#ifdef  BSWM_START_SEC_CONST_UNSPECIFIED
  #undef BSWM_START_SEC_CONST_UNSPECIFIED   /* PRQA S 0841 */  /* MD_MSR_Undef */
  #define START_SEC_CONST_UNSPECIFIED
#endif
/**********************************************************************************************************************
 * DO NOT CHANGE THIS COMMENT!           </USERBLOCK>
 *********************************************************************************************************************/
/**********************************************************************************************************************
 * DO NOT CHANGE THIS COMMENT!           <USERBLOCK BSWM_STOP_SEC_CONST_16BIT>
 *********************************************************************************************************************/
#ifdef  BSWM_STOP_SEC_CONST_16BIT
  #undef BSWM_STOP_SEC_CONST_16BIT   /* PRQA S 0841 */  /* MD_MSR_Undef */
  #define STOP_SEC_CONST
#endif
/**********************************************************************************************************************
 * DO NOT CHANGE THIS COMMENT!           </USERBLOCK>
 *********************************************************************************************************************/
/**********************************************************************************************************************
 * DO NOT CHANGE THIS COMMENT!           <USERBLOCK BSWM_STOP_SEC_CONST_8BIT>
 *********************************************************************************************************************/
#ifdef  BSWM_STOP_SEC_CONST_8BIT
  #undef BSWM_STOP_SEC_CONST_8BIT   /* PRQA S 0841 */  /* MD_MSR_Undef */
  #define STOP_SEC_CONST
#endif
/**********************************************************************************************************************
 * DO NOT CHANGE THIS COMMENT!           </USERBLOCK>
 *********************************************************************************************************************/
/**********************************************************************************************************************
 * DO NOT CHANGE THIS COMMENT!           <USERBLOCK BSWM_STOP_SEC_CONST_UNSPECIFIED>
 *********************************************************************************************************************/
#ifdef  BSWM_STOP_SEC_CONST_UNSPECIFIED
  #undef BSWM_STOP_SEC_CONST_UNSPECIFIED   /* PRQA S 0841 */  /* MD_MSR_Undef */
  #define STOP_SEC_CONST
#endif
/**********************************************************************************************************************
 * DO NOT CHANGE THIS COMMENT!           </USERBLOCK>
 *********************************************************************************************************************/

/*****************  VAR sections   ***********************************************************************************/  

/**********************************************************************************************************************
 * DO NOT CHANGE THIS COMMENT!           <USERBLOCK BSWM_START_SEC_VAR_NOINIT_16BIT>
 *********************************************************************************************************************/
#ifdef  BSWM_START_SEC_VAR_NOINIT_16BIT
  #undef BSWM_START_SEC_VAR_NOINIT_16BIT   /* PRQA S 0841 */  /* MD_MSR_Undef */
  #define START_SEC_VAR_NOINIT_16BIT
#endif
/**********************************************************************************************************************
 * DO NOT CHANGE THIS COMMENT!           </USERBLOCK>
 *********************************************************************************************************************/
/**********************************************************************************************************************
 * DO NOT CHANGE THIS COMMENT!           <USERBLOCK BSWM_START_SEC_VAR_NOINIT_32BIT>
 *********************************************************************************************************************/
#ifdef  BSWM_START_SEC_VAR_NOINIT_32BIT
  #undef BSWM_START_SEC_VAR_NOINIT_32BIT   /* PRQA S 0841 */  /* MD_MSR_Undef */
  #define START_SEC_VAR_NOINIT_32BIT
#endif
/**********************************************************************************************************************
 * DO NOT CHANGE THIS COMMENT!           </USERBLOCK>
 *********************************************************************************************************************/
/**********************************************************************************************************************
 * DO NOT CHANGE THIS COMMENT!           <USERBLOCK BSWM_START_SEC_VAR_NOINIT_8BIT>
 *********************************************************************************************************************/
#ifdef  BSWM_START_SEC_VAR_NOINIT_8BIT
  #undef BSWM_START_SEC_VAR_NOINIT_8BIT   /* PRQA S 0841 */  /* MD_MSR_Undef */
  #define START_SEC_VAR_NOINIT_8BIT
#endif
/**********************************************************************************************************************
 * DO NOT CHANGE THIS COMMENT!           </USERBLOCK>
 *********************************************************************************************************************/
/**********************************************************************************************************************
 * DO NOT CHANGE THIS COMMENT!           <USERBLOCK BSWM_START_SEC_VAR_NOINIT_UNSPECIFIED>
 *********************************************************************************************************************/
#ifdef  BSWM_START_SEC_VAR_NOINIT_UNSPECIFIED
  #undef BSWM_START_SEC_VAR_NOINIT_UNSPECIFIED   /* PRQA S 0841 */  /* MD_MSR_Undef */
  #define START_SEC_VAR_NOINIT_UNSPECIFIED
#endif
/**********************************************************************************************************************
 * DO NOT CHANGE THIS COMMENT!           </USERBLOCK>
 *********************************************************************************************************************/
/**********************************************************************************************************************
 * DO NOT CHANGE THIS COMMENT!           <USERBLOCK BSWM_STOP_SEC_VAR_NOINIT_16BIT>
 *********************************************************************************************************************/
#ifdef  BSWM_STOP_SEC_VAR_NOINIT_16BIT
  #undef BSWM_STOP_SEC_VAR_NOINIT_16BIT   /* PRQA S 0841 */  /* MD_MSR_Undef */
  #define STOP_SEC_VAR
#endif
/**********************************************************************************************************************
 * DO NOT CHANGE THIS COMMENT!           </USERBLOCK>
 *********************************************************************************************************************/
/**********************************************************************************************************************
 * DO NOT CHANGE THIS COMMENT!           <USERBLOCK BSWM_STOP_SEC_VAR_NOINIT_32BIT>
 *********************************************************************************************************************/
#ifdef  BSWM_STOP_SEC_VAR_NOINIT_32BIT
  #undef BSWM_STOP_SEC_VAR_NOINIT_32BIT   /* PRQA S 0841 */  /* MD_MSR_Undef */
  #define STOP_SEC_VAR
#endif
/**********************************************************************************************************************
 * DO NOT CHANGE THIS COMMENT!           </USERBLOCK>
 *********************************************************************************************************************/
/**********************************************************************************************************************
 * DO NOT CHANGE THIS COMMENT!           <USERBLOCK BSWM_STOP_SEC_VAR_NOINIT_8BIT>
 *********************************************************************************************************************/
#ifdef  BSWM_STOP_SEC_VAR_NOINIT_8BIT
  #undef BSWM_STOP_SEC_VAR_NOINIT_8BIT   /* PRQA S 0841 */  /* MD_MSR_Undef */
  #define STOP_SEC_VAR
#endif
/**********************************************************************************************************************
 * DO NOT CHANGE THIS COMMENT!           </USERBLOCK>
 *********************************************************************************************************************/
/**********************************************************************************************************************
 * DO NOT CHANGE THIS COMMENT!           <USERBLOCK BSWM_STOP_SEC_VAR_NOINIT_UNSPECIFIED>
 *********************************************************************************************************************/
#ifdef  BSWM_STOP_SEC_VAR_NOINIT_UNSPECIFIED
  #undef BSWM_STOP_SEC_VAR_NOINIT_UNSPECIFIED   /* PRQA S 0841 */  /* MD_MSR_Undef */
  #define STOP_SEC_VAR
#endif
/**********************************************************************************************************************
 * DO NOT CHANGE THIS COMMENT!           </USERBLOCK>
 *********************************************************************************************************************/

#include "MemMap_Common.h"
/**********************************************************************************************************************
 *   BSWM END
 **********************************************************************************************************************/



