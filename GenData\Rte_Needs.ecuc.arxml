<?xml version="1.0" encoding="UTF-8"?>
<AUTOSAR xmlns="http://autosar.org/schema/r4.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://autosar.org/schema/r4.0 autosar_4-0-3.xsd">
    <AR-PACKAGES>
        <AR-PACKAGE>
            <SHORT-NAME>DaVinci_Project</SHORT-NAME>
            <ELEMENTS>
                <ECUC-MODULE-CONFIGURATION-VALUES>
                    <SHORT-NAME>Com</SHORT-NAME>
                    <DEFINITION-REF DEST="ECUC-MODULE-DEF">/MICROSAR/Com</DEFINITION-REF>
                    <CONTAINERS>
                        <ECUC-CONTAINER-VALUE>
                            <SHORT-NAME>ComConfig</SHORT-NAME>
                            <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Com/ComConfig</DEFINITION-REF>
                            <SUB-CONTAINERS>
                                <ECUC-CONTAINER-VALUE>
                                    <SHORT-NAME>SG_RgRx_Prof1C_RxCycle_omsg_RxCycle_E2eProf1C_500_10_oCAN_430c5af9_Rx</SHORT-NAME>
                                    <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Com/ComConfig/ComSignalGroup</DEFINITION-REF>
                                    <PARAMETER-VALUES>
                                        <ECUC-TEXTUAL-PARAM-VALUE>
                                            <DEFINITION-REF DEST="ECUC-FUNCTION-NAME-DEF">/MICROSAR/Com/ComConfig/ComSignalGroup/ComNotification</DEFINITION-REF>
                                            <VALUE>Rte_COMCbk_SG_RgRx_Prof1C_RxCycle_omsg_RxCycle_E2eProf1C_500_10_oCAN_430c5af9_Rx</VALUE>
                                        </ECUC-TEXTUAL-PARAM-VALUE>
                                    </PARAMETER-VALUES>
                                    <SUB-CONTAINERS>
                                        <ECUC-CONTAINER-VALUE>
                                            <SHORT-NAME>SigRx_Prof1C_CRC_omsg_RxCycle_E2eProf1C_500_10_oCAN_d6a2f34d_Rx</SHORT-NAME>
                                            <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Com/ComConfig/ComSignalGroup/ComGroupSignal</DEFINITION-REF>
                                            <PARAMETER-VALUES>
                                                <ECUC-TEXTUAL-PARAM-VALUE>
                                                    <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/MICROSAR/Com/ComConfig/ComSignalGroup/ComGroupSignal/ComSignalType</DEFINITION-REF>
                                                    <VALUE>UINT8</VALUE>
                                                </ECUC-TEXTUAL-PARAM-VALUE>
                                            </PARAMETER-VALUES>
                                        </ECUC-CONTAINER-VALUE>
                                        <ECUC-CONTAINER-VALUE>
                                            <SHORT-NAME>SigRx_Prof1C_DataId_HiByte_LoNib_omsg_RxCycle_E2eProf1C_500_10_oCAN_bdeb2c98_Rx</SHORT-NAME>
                                            <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Com/ComConfig/ComSignalGroup/ComGroupSignal</DEFINITION-REF>
                                            <PARAMETER-VALUES>
                                                <ECUC-TEXTUAL-PARAM-VALUE>
                                                    <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/MICROSAR/Com/ComConfig/ComSignalGroup/ComGroupSignal/ComSignalType</DEFINITION-REF>
                                                    <VALUE>UINT8</VALUE>
                                                </ECUC-TEXTUAL-PARAM-VALUE>
                                            </PARAMETER-VALUES>
                                        </ECUC-CONTAINER-VALUE>
                                        <ECUC-CONTAINER-VALUE>
                                            <SHORT-NAME>SigRx_Prof1C_SQ_omsg_RxCycle_E2eProf1C_500_10_oCAN_263a4149_Rx</SHORT-NAME>
                                            <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Com/ComConfig/ComSignalGroup/ComGroupSignal</DEFINITION-REF>
                                            <PARAMETER-VALUES>
                                                <ECUC-TEXTUAL-PARAM-VALUE>
                                                    <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/MICROSAR/Com/ComConfig/ComSignalGroup/ComGroupSignal/ComSignalType</DEFINITION-REF>
                                                    <VALUE>UINT8</VALUE>
                                                </ECUC-TEXTUAL-PARAM-VALUE>
                                            </PARAMETER-VALUES>
                                        </ECUC-CONTAINER-VALUE>
                                        <ECUC-CONTAINER-VALUE>
                                            <SHORT-NAME>SigRx_Prof1C_Sig32Bit_omsg_RxCycle_E2eProf1C_500_10_oCAN_f0dd3f4d_Rx</SHORT-NAME>
                                            <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Com/ComConfig/ComSignalGroup/ComGroupSignal</DEFINITION-REF>
                                            <PARAMETER-VALUES>
                                                <ECUC-TEXTUAL-PARAM-VALUE>
                                                    <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/MICROSAR/Com/ComConfig/ComSignalGroup/ComGroupSignal/ComSignalType</DEFINITION-REF>
                                                    <VALUE>UINT32</VALUE>
                                                </ECUC-TEXTUAL-PARAM-VALUE>
                                            </PARAMETER-VALUES>
                                        </ECUC-CONTAINER-VALUE>
                                        <ECUC-CONTAINER-VALUE>
                                            <SHORT-NAME>SigRx_Prof1C_Sig3Bit_omsg_RxCycle_E2eProf1C_500_10_oCAN_95ddcbc9_Rx</SHORT-NAME>
                                            <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Com/ComConfig/ComSignalGroup/ComGroupSignal</DEFINITION-REF>
                                            <PARAMETER-VALUES>
                                                <ECUC-TEXTUAL-PARAM-VALUE>
                                                    <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/MICROSAR/Com/ComConfig/ComSignalGroup/ComGroupSignal/ComSignalType</DEFINITION-REF>
                                                    <VALUE>UINT8</VALUE>
                                                </ECUC-TEXTUAL-PARAM-VALUE>
                                            </PARAMETER-VALUES>
                                        </ECUC-CONTAINER-VALUE>
                                        <ECUC-CONTAINER-VALUE>
                                            <SHORT-NAME>SigRx_Prof1C_Sig4Bit_omsg_RxCycle_E2eProf1C_500_10_oCAN_a713ffd7_Rx</SHORT-NAME>
                                            <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Com/ComConfig/ComSignalGroup/ComGroupSignal</DEFINITION-REF>
                                            <PARAMETER-VALUES>
                                                <ECUC-TEXTUAL-PARAM-VALUE>
                                                    <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/MICROSAR/Com/ComConfig/ComSignalGroup/ComGroupSignal/ComSignalType</DEFINITION-REF>
                                                    <VALUE>UINT8</VALUE>
                                                </ECUC-TEXTUAL-PARAM-VALUE>
                                            </PARAMETER-VALUES>
                                        </ECUC-CONTAINER-VALUE>
                                    </SUB-CONTAINERS>
                                </ECUC-CONTAINER-VALUE>
                                <ECUC-CONTAINER-VALUE>
                                    <SHORT-NAME>Sig_StartAppl_LinData_Rx_oFrame_LinTr_RearECU_oLIN00_182ca40c_Rx</SHORT-NAME>
                                    <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Com/ComConfig/ComSignal</DEFINITION-REF>
                                    <PARAMETER-VALUES>
                                        <ECUC-TEXTUAL-PARAM-VALUE>
                                            <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/MICROSAR/Com/ComConfig/ComSignal/ComSignalType</DEFINITION-REF>
                                            <VALUE>UINT8</VALUE>
                                        </ECUC-TEXTUAL-PARAM-VALUE>
                                    </PARAMETER-VALUES>
                                </ECUC-CONTAINER-VALUE>
                                <ECUC-CONTAINER-VALUE>
                                    <SHORT-NAME>Sig_StartAppl_LinData_Tx_oFrame_LinTr_MyECU_oLIN00_d383dc6b_Tx</SHORT-NAME>
                                    <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Com/ComConfig/ComSignal</DEFINITION-REF>
                                    <PARAMETER-VALUES>
                                        <ECUC-TEXTUAL-PARAM-VALUE>
                                            <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/MICROSAR/Com/ComConfig/ComSignal/ComSignalType</DEFINITION-REF>
                                            <VALUE>UINT8</VALUE>
                                        </ECUC-TEXTUAL-PARAM-VALUE>
                                    </PARAMETER-VALUES>
                                </ECUC-CONTAINER-VALUE>
                                <ECUC-CONTAINER-VALUE>
                                    <SHORT-NAME>Signal_Rx20bit_Cyclic_omsg_RxCycle500_20_oCAN_983b15aa_Rx</SHORT-NAME>
                                    <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Com/ComConfig/ComSignal</DEFINITION-REF>
                                    <PARAMETER-VALUES>
                                        <ECUC-TEXTUAL-PARAM-VALUE>
                                            <DEFINITION-REF DEST="ECUC-FUNCTION-NAME-DEF">/MICROSAR/Com/ComConfig/ComSignal/ComNotification</DEFINITION-REF>
                                            <VALUE>Rte_COMCbk_Signal_Rx20bit_Cyclic_omsg_RxCycle500_20_oCAN_983b15aa_Rx</VALUE>
                                        </ECUC-TEXTUAL-PARAM-VALUE>
                                        <ECUC-TEXTUAL-PARAM-VALUE>
                                            <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/MICROSAR/Com/ComConfig/ComSignal/ComSignalType</DEFINITION-REF>
                                            <VALUE>UINT32</VALUE>
                                        </ECUC-TEXTUAL-PARAM-VALUE>
                                    </PARAMETER-VALUES>
                                </ECUC-CONTAINER-VALUE>
                                <ECUC-CONTAINER-VALUE>
                                    <SHORT-NAME>Signal_SomeTxSignal_oPDU_Transmit_MyECU_763437bb_Tx</SHORT-NAME>
                                    <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Com/ComConfig/ComSignal</DEFINITION-REF>
                                    <PARAMETER-VALUES>
                                        <ECUC-TEXTUAL-PARAM-VALUE>
                                            <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/MICROSAR/Com/ComConfig/ComSignal/ComSignalType</DEFINITION-REF>
                                            <VALUE>UINT32</VALUE>
                                        </ECUC-TEXTUAL-PARAM-VALUE>
                                    </PARAMETER-VALUES>
                                </ECUC-CONTAINER-VALUE>
                                <ECUC-CONTAINER-VALUE>
                                    <SHORT-NAME>Signal_Tx10bit_Cyclic_omsg_TxCycle1000_10_oCAN_9539efc7_Tx</SHORT-NAME>
                                    <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Com/ComConfig/ComSignal</DEFINITION-REF>
                                    <PARAMETER-VALUES>
                                        <ECUC-TEXTUAL-PARAM-VALUE>
                                            <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/MICROSAR/Com/ComConfig/ComSignal/ComSignalType</DEFINITION-REF>
                                            <VALUE>UINT16</VALUE>
                                        </ECUC-TEXTUAL-PARAM-VALUE>
                                    </PARAMETER-VALUES>
                                </ECUC-CONTAINER-VALUE>
                                <ECUC-CONTAINER-VALUE>
                                    <SHORT-NAME>Signal_Tx24bit_Cyclic_omsg_TxCycle10_0_oCAN_393dd5fe_Tx</SHORT-NAME>
                                    <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Com/ComConfig/ComSignal</DEFINITION-REF>
                                    <PARAMETER-VALUES>
                                        <ECUC-TEXTUAL-PARAM-VALUE>
                                            <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/MICROSAR/Com/ComConfig/ComSignal/ComSignalType</DEFINITION-REF>
                                            <VALUE>UINT32</VALUE>
                                        </ECUC-TEXTUAL-PARAM-VALUE>
                                    </PARAMETER-VALUES>
                                </ECUC-CONTAINER-VALUE>
                                <ECUC-CONTAINER-VALUE>
                                    <SHORT-NAME>StartAppl_Fr_BothECU_RX_Ctrl_oPDU_Fr_StartAppl_BothECU_RX_cb3aeb40_Rx</SHORT-NAME>
                                    <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Com/ComConfig/ComSignal</DEFINITION-REF>
                                    <PARAMETER-VALUES>
                                        <ECUC-TEXTUAL-PARAM-VALUE>
                                            <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/MICROSAR/Com/ComConfig/ComSignal/ComSignalType</DEFINITION-REF>
                                            <VALUE>UINT16</VALUE>
                                        </ECUC-TEXTUAL-PARAM-VALUE>
                                    </PARAMETER-VALUES>
                                </ECUC-CONTAINER-VALUE>
                            </SUB-CONTAINERS>
                        </ECUC-CONTAINER-VALUE>
                    </CONTAINERS>
                </ECUC-MODULE-CONFIGURATION-VALUES>
                <ECUC-MODULE-CONFIGURATION-VALUES>
                    <SHORT-NAME>Rte</SHORT-NAME>
                    <DEFINITION-REF DEST="ECUC-MODULE-DEF">/MICROSAR/Rte</DEFINITION-REF>
                    <CONTAINERS>
                        <ECUC-CONTAINER-VALUE>
                            <SHORT-NAME>BswM</SHORT-NAME>
                            <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Rte/RteBswModuleInstance</DEFINITION-REF>
                            <SUB-CONTAINERS>
                                <ECUC-CONTAINER-VALUE>
                                    <SHORT-NAME>BswM_MainFunctionTimingEvent0</SHORT-NAME>
                                    <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Rte/RteBswModuleInstance/RteBswEventToTaskMapping</DEFINITION-REF>
                                    <REFERENCE-VALUES>
                                        <ECUC-REFERENCE-VALUE>
                                            <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Rte/RteBswModuleInstance/RteBswEventToTaskMapping/RteBswUsedOsEventRef</DEFINITION-REF>
                                            <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/DaVinci_Project/Os/Rte_Ev_Cyclic2_Default_BSW_Async_Task_0_10ms</VALUE-REF>
                                        </ECUC-REFERENCE-VALUE>
                                        <ECUC-REFERENCE-VALUE>
                                            <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Rte/RteBswModuleInstance/RteBswEventToTaskMapping/RteBswUsedOsAlarmRef</DEFINITION-REF>
                                            <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/DaVinci_Project/Os/Rte_Al_TE2_Default_BSW_Async_Task_0_10ms</VALUE-REF>
                                        </ECUC-REFERENCE-VALUE>
                                    </REFERENCE-VALUES>
                                </ECUC-CONTAINER-VALUE>
                            </SUB-CONTAINERS>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                            <SHORT-NAME>Can</SHORT-NAME>
                            <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Rte/RteBswModuleInstance</DEFINITION-REF>
                            <SUB-CONTAINERS>
                                <ECUC-CONTAINER-VALUE>
                                    <SHORT-NAME>Can_MainFunction_BusOffTimingEvent0</SHORT-NAME>
                                    <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Rte/RteBswModuleInstance/RteBswEventToTaskMapping</DEFINITION-REF>
                                    <REFERENCE-VALUES>
                                        <ECUC-REFERENCE-VALUE>
                                            <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Rte/RteBswModuleInstance/RteBswEventToTaskMapping/RteBswUsedOsEventRef</DEFINITION-REF>
                                            <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/DaVinci_Project/Os/Rte_Ev_Cyclic2_Default_BSW_Async_Task_0_10ms</VALUE-REF>
                                        </ECUC-REFERENCE-VALUE>
                                        <ECUC-REFERENCE-VALUE>
                                            <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Rte/RteBswModuleInstance/RteBswEventToTaskMapping/RteBswUsedOsAlarmRef</DEFINITION-REF>
                                            <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/DaVinci_Project/Os/Rte_Al_TE2_Default_BSW_Async_Task_0_10ms</VALUE-REF>
                                        </ECUC-REFERENCE-VALUE>
                                    </REFERENCE-VALUES>
                                </ECUC-CONTAINER-VALUE>
                                <ECUC-CONTAINER-VALUE>
                                    <SHORT-NAME>Can_MainFunction_ModeTimingEvent0</SHORT-NAME>
                                    <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Rte/RteBswModuleInstance/RteBswEventToTaskMapping</DEFINITION-REF>
                                    <REFERENCE-VALUES>
                                        <ECUC-REFERENCE-VALUE>
                                            <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Rte/RteBswModuleInstance/RteBswEventToTaskMapping/RteBswUsedOsEventRef</DEFINITION-REF>
                                            <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/DaVinci_Project/Os/Rte_Ev_Cyclic2_Default_BSW_Async_Task_0_10ms</VALUE-REF>
                                        </ECUC-REFERENCE-VALUE>
                                        <ECUC-REFERENCE-VALUE>
                                            <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Rte/RteBswModuleInstance/RteBswEventToTaskMapping/RteBswUsedOsAlarmRef</DEFINITION-REF>
                                            <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/DaVinci_Project/Os/Rte_Al_TE2_Default_BSW_Async_Task_0_10ms</VALUE-REF>
                                        </ECUC-REFERENCE-VALUE>
                                    </REFERENCE-VALUES>
                                </ECUC-CONTAINER-VALUE>
                                <ECUC-CONTAINER-VALUE>
                                    <SHORT-NAME>Can_MainFunction_WakeupTimingEvent0</SHORT-NAME>
                                    <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Rte/RteBswModuleInstance/RteBswEventToTaskMapping</DEFINITION-REF>
                                    <REFERENCE-VALUES>
                                        <ECUC-REFERENCE-VALUE>
                                            <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Rte/RteBswModuleInstance/RteBswEventToTaskMapping/RteBswUsedOsEventRef</DEFINITION-REF>
                                            <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/DaVinci_Project/Os/Rte_Ev_Cyclic2_Default_BSW_Async_Task_0_10ms</VALUE-REF>
                                        </ECUC-REFERENCE-VALUE>
                                        <ECUC-REFERENCE-VALUE>
                                            <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Rte/RteBswModuleInstance/RteBswEventToTaskMapping/RteBswUsedOsAlarmRef</DEFINITION-REF>
                                            <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/DaVinci_Project/Os/Rte_Al_TE2_Default_BSW_Async_Task_0_10ms</VALUE-REF>
                                        </ECUC-REFERENCE-VALUE>
                                    </REFERENCE-VALUES>
                                </ECUC-CONTAINER-VALUE>
                            </SUB-CONTAINERS>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                            <SHORT-NAME>CanIf</SHORT-NAME>
                            <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Rte/RteBswModuleInstance</DEFINITION-REF>
                            <SUB-CONTAINERS>
                            </SUB-CONTAINERS>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                            <SHORT-NAME>CanNm</SHORT-NAME>
                            <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Rte/RteBswModuleInstance</DEFINITION-REF>
                            <SUB-CONTAINERS>
                                <ECUC-CONTAINER-VALUE>
                                    <SHORT-NAME>CanNm_MainFunctionTimingEvent0</SHORT-NAME>
                                    <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Rte/RteBswModuleInstance/RteBswEventToTaskMapping</DEFINITION-REF>
                                    <REFERENCE-VALUES>
                                        <ECUC-REFERENCE-VALUE>
                                            <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Rte/RteBswModuleInstance/RteBswEventToTaskMapping/RteBswUsedOsEventRef</DEFINITION-REF>
                                            <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/DaVinci_Project/Os/Rte_Ev_Cyclic2_Default_BSW_Async_Task_0_10ms</VALUE-REF>
                                        </ECUC-REFERENCE-VALUE>
                                        <ECUC-REFERENCE-VALUE>
                                            <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Rte/RteBswModuleInstance/RteBswEventToTaskMapping/RteBswUsedOsAlarmRef</DEFINITION-REF>
                                            <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/DaVinci_Project/Os/Rte_Al_TE2_Default_BSW_Async_Task_0_10ms</VALUE-REF>
                                        </ECUC-REFERENCE-VALUE>
                                    </REFERENCE-VALUES>
                                </ECUC-CONTAINER-VALUE>
                            </SUB-CONTAINERS>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                            <SHORT-NAME>CanSM</SHORT-NAME>
                            <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Rte/RteBswModuleInstance</DEFINITION-REF>
                            <SUB-CONTAINERS>
                                <ECUC-CONTAINER-VALUE>
                                    <SHORT-NAME>CanSM_MainFunctionTimingEvent0</SHORT-NAME>
                                    <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Rte/RteBswModuleInstance/RteBswEventToTaskMapping</DEFINITION-REF>
                                    <REFERENCE-VALUES>
                                        <ECUC-REFERENCE-VALUE>
                                            <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Rte/RteBswModuleInstance/RteBswEventToTaskMapping/RteBswUsedOsEventRef</DEFINITION-REF>
                                            <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/DaVinci_Project/Os/Rte_Ev_Cyclic2_Default_BSW_Async_Task_0_10ms</VALUE-REF>
                                        </ECUC-REFERENCE-VALUE>
                                        <ECUC-REFERENCE-VALUE>
                                            <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Rte/RteBswModuleInstance/RteBswEventToTaskMapping/RteBswUsedOsAlarmRef</DEFINITION-REF>
                                            <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/DaVinci_Project/Os/Rte_Al_TE2_Default_BSW_Async_Task_0_10ms</VALUE-REF>
                                        </ECUC-REFERENCE-VALUE>
                                    </REFERENCE-VALUES>
                                </ECUC-CONTAINER-VALUE>
                            </SUB-CONTAINERS>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                            <SHORT-NAME>CanTp</SHORT-NAME>
                            <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Rte/RteBswModuleInstance</DEFINITION-REF>
                            <SUB-CONTAINERS>
                                <ECUC-CONTAINER-VALUE>
                                    <SHORT-NAME>CanTp_MainFunctionTimingEvent0</SHORT-NAME>
                                    <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Rte/RteBswModuleInstance/RteBswEventToTaskMapping</DEFINITION-REF>
                                    <REFERENCE-VALUES>
                                        <ECUC-REFERENCE-VALUE>
                                            <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Rte/RteBswModuleInstance/RteBswEventToTaskMapping/RteBswUsedOsEventRef</DEFINITION-REF>
                                            <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/DaVinci_Project/Os/Rte_Ev_Cyclic2_Default_BSW_Async_Task_0_5ms</VALUE-REF>
                                        </ECUC-REFERENCE-VALUE>
                                        <ECUC-REFERENCE-VALUE>
                                            <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Rte/RteBswModuleInstance/RteBswEventToTaskMapping/RteBswUsedOsAlarmRef</DEFINITION-REF>
                                            <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/DaVinci_Project/Os/Rte_Al_TE2_Default_BSW_Async_Task_0_5ms</VALUE-REF>
                                        </ECUC-REFERENCE-VALUE>
                                    </REFERENCE-VALUES>
                                </ECUC-CONTAINER-VALUE>
                            </SUB-CONTAINERS>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                            <SHORT-NAME>Com</SHORT-NAME>
                            <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Rte/RteBswModuleInstance</DEFINITION-REF>
                            <SUB-CONTAINERS>
                                <ECUC-CONTAINER-VALUE>
                                    <SHORT-NAME>Com_MainFunctionRxTimingEvent0</SHORT-NAME>
                                    <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Rte/RteBswModuleInstance/RteBswEventToTaskMapping</DEFINITION-REF>
                                    <REFERENCE-VALUES>
                                        <ECUC-REFERENCE-VALUE>
                                            <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Rte/RteBswModuleInstance/RteBswEventToTaskMapping/RteBswUsedOsEventRef</DEFINITION-REF>
                                            <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/DaVinci_Project/Os/Rte_Ev_Cyclic2_Default_BSW_Async_Task_0_10ms</VALUE-REF>
                                        </ECUC-REFERENCE-VALUE>
                                        <ECUC-REFERENCE-VALUE>
                                            <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Rte/RteBswModuleInstance/RteBswEventToTaskMapping/RteBswUsedOsAlarmRef</DEFINITION-REF>
                                            <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/DaVinci_Project/Os/Rte_Al_TE2_Default_BSW_Async_Task_0_10ms</VALUE-REF>
                                        </ECUC-REFERENCE-VALUE>
                                    </REFERENCE-VALUES>
                                </ECUC-CONTAINER-VALUE>
                                <ECUC-CONTAINER-VALUE>
                                    <SHORT-NAME>Com_MainFunctionTxTimingEvent0</SHORT-NAME>
                                    <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Rte/RteBswModuleInstance/RteBswEventToTaskMapping</DEFINITION-REF>
                                    <REFERENCE-VALUES>
                                        <ECUC-REFERENCE-VALUE>
                                            <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Rte/RteBswModuleInstance/RteBswEventToTaskMapping/RteBswUsedOsEventRef</DEFINITION-REF>
                                            <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/DaVinci_Project/Os/Rte_Ev_Cyclic2_Default_BSW_Async_Task_0_10ms</VALUE-REF>
                                        </ECUC-REFERENCE-VALUE>
                                        <ECUC-REFERENCE-VALUE>
                                            <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Rte/RteBswModuleInstance/RteBswEventToTaskMapping/RteBswUsedOsAlarmRef</DEFINITION-REF>
                                            <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/DaVinci_Project/Os/Rte_Al_TE2_Default_BSW_Async_Task_0_10ms</VALUE-REF>
                                        </ECUC-REFERENCE-VALUE>
                                    </REFERENCE-VALUES>
                                </ECUC-CONTAINER-VALUE>
                            </SUB-CONTAINERS>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                            <SHORT-NAME>ComM</SHORT-NAME>
                            <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Rte/RteBswModuleInstance</DEFINITION-REF>
                            <SUB-CONTAINERS>
                                <ECUC-CONTAINER-VALUE>
                                    <SHORT-NAME>ComM_MainFunction_0TimingEvent0</SHORT-NAME>
                                    <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Rte/RteBswModuleInstance/RteBswEventToTaskMapping</DEFINITION-REF>
                                    <REFERENCE-VALUES>
                                        <ECUC-REFERENCE-VALUE>
                                            <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Rte/RteBswModuleInstance/RteBswEventToTaskMapping/RteBswUsedOsEventRef</DEFINITION-REF>
                                            <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/DaVinci_Project/Os/Rte_Ev_Cyclic2_Default_BSW_Async_Task_0_20ms</VALUE-REF>
                                        </ECUC-REFERENCE-VALUE>
                                        <ECUC-REFERENCE-VALUE>
                                            <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Rte/RteBswModuleInstance/RteBswEventToTaskMapping/RteBswUsedOsAlarmRef</DEFINITION-REF>
                                            <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/DaVinci_Project/Os/Rte_Al_TE2_Default_BSW_Async_Task_0_20ms</VALUE-REF>
                                        </ECUC-REFERENCE-VALUE>
                                    </REFERENCE-VALUES>
                                </ECUC-CONTAINER-VALUE>
                                <ECUC-CONTAINER-VALUE>
                                    <SHORT-NAME>ComM_MainFunction_1TimingEvent0</SHORT-NAME>
                                    <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Rte/RteBswModuleInstance/RteBswEventToTaskMapping</DEFINITION-REF>
                                    <REFERENCE-VALUES>
                                        <ECUC-REFERENCE-VALUE>
                                            <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Rte/RteBswModuleInstance/RteBswEventToTaskMapping/RteBswUsedOsEventRef</DEFINITION-REF>
                                            <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/DaVinci_Project/Os/Rte_Ev_Cyclic2_Default_BSW_Async_Task_0_20ms</VALUE-REF>
                                        </ECUC-REFERENCE-VALUE>
                                        <ECUC-REFERENCE-VALUE>
                                            <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Rte/RteBswModuleInstance/RteBswEventToTaskMapping/RteBswUsedOsAlarmRef</DEFINITION-REF>
                                            <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/DaVinci_Project/Os/Rte_Al_TE2_Default_BSW_Async_Task_0_20ms</VALUE-REF>
                                        </ECUC-REFERENCE-VALUE>
                                    </REFERENCE-VALUES>
                                </ECUC-CONTAINER-VALUE>
                                <ECUC-CONTAINER-VALUE>
                                    <SHORT-NAME>ComM_MainFunction_2TimingEvent0</SHORT-NAME>
                                    <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Rte/RteBswModuleInstance/RteBswEventToTaskMapping</DEFINITION-REF>
                                    <REFERENCE-VALUES>
                                        <ECUC-REFERENCE-VALUE>
                                            <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Rte/RteBswModuleInstance/RteBswEventToTaskMapping/RteBswUsedOsEventRef</DEFINITION-REF>
                                            <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/DaVinci_Project/Os/Rte_Ev_Cyclic2_Default_BSW_Async_Task_0_20ms</VALUE-REF>
                                        </ECUC-REFERENCE-VALUE>
                                        <ECUC-REFERENCE-VALUE>
                                            <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Rte/RteBswModuleInstance/RteBswEventToTaskMapping/RteBswUsedOsAlarmRef</DEFINITION-REF>
                                            <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/DaVinci_Project/Os/Rte_Al_TE2_Default_BSW_Async_Task_0_20ms</VALUE-REF>
                                        </ECUC-REFERENCE-VALUE>
                                    </REFERENCE-VALUES>
                                </ECUC-CONTAINER-VALUE>
                            </SUB-CONTAINERS>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                            <SHORT-NAME>ComXf</SHORT-NAME>
                            <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Rte/RteBswModuleInstance</DEFINITION-REF>
                            <SUB-CONTAINERS>
                            </SUB-CONTAINERS>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                            <SHORT-NAME>Dcm</SHORT-NAME>
                            <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Rte/RteBswModuleInstance</DEFINITION-REF>
                            <SUB-CONTAINERS>
                                <ECUC-CONTAINER-VALUE>
                                    <SHORT-NAME>Dcm_MainFunctionTimingEvent0</SHORT-NAME>
                                    <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Rte/RteBswModuleInstance/RteBswEventToTaskMapping</DEFINITION-REF>
                                    <REFERENCE-VALUES>
                                        <ECUC-REFERENCE-VALUE>
                                            <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Rte/RteBswModuleInstance/RteBswEventToTaskMapping/RteBswUsedOsEventRef</DEFINITION-REF>
                                            <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/DaVinci_Project/Os/Rte_Ev_Cyclic2_Default_BSW_Async_Task_0_10ms</VALUE-REF>
                                        </ECUC-REFERENCE-VALUE>
                                        <ECUC-REFERENCE-VALUE>
                                            <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Rte/RteBswModuleInstance/RteBswEventToTaskMapping/RteBswUsedOsAlarmRef</DEFINITION-REF>
                                            <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/DaVinci_Project/Os/Rte_Al_TE2_Default_BSW_Async_Task_0_10ms</VALUE-REF>
                                        </ECUC-REFERENCE-VALUE>
                                    </REFERENCE-VALUES>
                                </ECUC-CONTAINER-VALUE>
                            </SUB-CONTAINERS>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                            <SHORT-NAME>Dem</SHORT-NAME>
                            <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Rte/RteBswModuleInstance</DEFINITION-REF>
                            <SUB-CONTAINERS>
                                <ECUC-CONTAINER-VALUE>
                                    <SHORT-NAME>Dem_MasterMainFunctionTimingEvent0</SHORT-NAME>
                                    <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Rte/RteBswModuleInstance/RteBswEventToTaskMapping</DEFINITION-REF>
                                    <REFERENCE-VALUES>
                                        <ECUC-REFERENCE-VALUE>
                                            <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Rte/RteBswModuleInstance/RteBswEventToTaskMapping/RteBswUsedOsEventRef</DEFINITION-REF>
                                            <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/DaVinci_Project/Os/Rte_Ev_Cyclic2_Default_BSW_Async_Task_0_10ms</VALUE-REF>
                                        </ECUC-REFERENCE-VALUE>
                                        <ECUC-REFERENCE-VALUE>
                                            <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Rte/RteBswModuleInstance/RteBswEventToTaskMapping/RteBswUsedOsAlarmRef</DEFINITION-REF>
                                            <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/DaVinci_Project/Os/Rte_Al_TE2_Default_BSW_Async_Task_0_10ms</VALUE-REF>
                                        </ECUC-REFERENCE-VALUE>
                                    </REFERENCE-VALUES>
                                </ECUC-CONTAINER-VALUE>
                                <ECUC-CONTAINER-VALUE>
                                    <SHORT-NAME>Dem_SatelliteMainFunctionTimingEvent0</SHORT-NAME>
                                    <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Rte/RteBswModuleInstance/RteBswEventToTaskMapping</DEFINITION-REF>
                                    <REFERENCE-VALUES>
                                        <ECUC-REFERENCE-VALUE>
                                            <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Rte/RteBswModuleInstance/RteBswEventToTaskMapping/RteBswUsedOsEventRef</DEFINITION-REF>
                                            <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/DaVinci_Project/Os/Rte_Ev_Cyclic2_Default_BSW_Async_Task_0_10ms</VALUE-REF>
                                        </ECUC-REFERENCE-VALUE>
                                        <ECUC-REFERENCE-VALUE>
                                            <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Rte/RteBswModuleInstance/RteBswEventToTaskMapping/RteBswUsedOsAlarmRef</DEFINITION-REF>
                                            <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/DaVinci_Project/Os/Rte_Al_TE2_Default_BSW_Async_Task_0_10ms</VALUE-REF>
                                        </ECUC-REFERENCE-VALUE>
                                    </REFERENCE-VALUES>
                                </ECUC-CONTAINER-VALUE>
                            </SUB-CONTAINERS>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                            <SHORT-NAME>Det</SHORT-NAME>
                            <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Rte/RteBswModuleInstance</DEFINITION-REF>
                            <SUB-CONTAINERS>
                            </SUB-CONTAINERS>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                            <SHORT-NAME>E2EXf</SHORT-NAME>
                            <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Rte/RteBswModuleInstance</DEFINITION-REF>
                            <SUB-CONTAINERS>
                            </SUB-CONTAINERS>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                            <SHORT-NAME>EcuM</SHORT-NAME>
                            <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Rte/RteBswModuleInstance</DEFINITION-REF>
                            <SUB-CONTAINERS>
                                <ECUC-CONTAINER-VALUE>
                                    <SHORT-NAME>EcuM_MainFunctionTimingEvent0</SHORT-NAME>
                                    <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Rte/RteBswModuleInstance/RteBswEventToTaskMapping</DEFINITION-REF>
                                    <REFERENCE-VALUES>
                                        <ECUC-REFERENCE-VALUE>
                                            <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Rte/RteBswModuleInstance/RteBswEventToTaskMapping/RteBswUsedOsEventRef</DEFINITION-REF>
                                            <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/DaVinci_Project/Os/Rte_Ev_Cyclic2_Default_BSW_Async_Task_0_10ms</VALUE-REF>
                                        </ECUC-REFERENCE-VALUE>
                                        <ECUC-REFERENCE-VALUE>
                                            <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Rte/RteBswModuleInstance/RteBswEventToTaskMapping/RteBswUsedOsAlarmRef</DEFINITION-REF>
                                            <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/DaVinci_Project/Os/Rte_Al_TE2_Default_BSW_Async_Task_0_10ms</VALUE-REF>
                                        </ECUC-REFERENCE-VALUE>
                                    </REFERENCE-VALUES>
                                </ECUC-CONTAINER-VALUE>
                            </SUB-CONTAINERS>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                            <SHORT-NAME>Fee</SHORT-NAME>
                            <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Rte/RteBswModuleInstance</DEFINITION-REF>
                            <SUB-CONTAINERS>
                                <ECUC-CONTAINER-VALUE>
                                    <SHORT-NAME>Fee_MainFunctionTimingEvent0</SHORT-NAME>
                                    <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Rte/RteBswModuleInstance/RteBswEventToTaskMapping</DEFINITION-REF>
                                    <REFERENCE-VALUES>
                                        <ECUC-REFERENCE-VALUE>
                                            <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Rte/RteBswModuleInstance/RteBswEventToTaskMapping/RteBswUsedOsEventRef</DEFINITION-REF>
                                            <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/DaVinci_Project/Os/Rte_Ev_Cyclic2_Default_BSW_Async_Task_0_10ms</VALUE-REF>
                                        </ECUC-REFERENCE-VALUE>
                                        <ECUC-REFERENCE-VALUE>
                                            <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Rte/RteBswModuleInstance/RteBswEventToTaskMapping/RteBswUsedOsAlarmRef</DEFINITION-REF>
                                            <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/DaVinci_Project/Os/Rte_Al_TE2_Default_BSW_Async_Task_0_10ms</VALUE-REF>
                                        </ECUC-REFERENCE-VALUE>
                                    </REFERENCE-VALUES>
                                </ECUC-CONTAINER-VALUE>
                            </SUB-CONTAINERS>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                            <SHORT-NAME>Fls</SHORT-NAME>
                            <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Rte/RteBswModuleInstance</DEFINITION-REF>
                            <SUB-CONTAINERS>
                                <ECUC-CONTAINER-VALUE>
                                    <SHORT-NAME>Fls_17_Dmu_MainFunctionTimingEvent0</SHORT-NAME>
                                    <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Rte/RteBswModuleInstance/RteBswEventToTaskMapping</DEFINITION-REF>
                                    <REFERENCE-VALUES>
                                        <ECUC-REFERENCE-VALUE>
                                            <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Rte/RteBswModuleInstance/RteBswEventToTaskMapping/RteBswUsedOsEventRef</DEFINITION-REF>
                                            <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/DaVinci_Project/Os/Rte_Ev_Cyclic2_Default_BSW_Async_Task_0_10ms</VALUE-REF>
                                        </ECUC-REFERENCE-VALUE>
                                        <ECUC-REFERENCE-VALUE>
                                            <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Rte/RteBswModuleInstance/RteBswEventToTaskMapping/RteBswUsedOsAlarmRef</DEFINITION-REF>
                                            <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/DaVinci_Project/Os/Rte_Al_TE2_Default_BSW_Async_Task_0_10ms</VALUE-REF>
                                        </ECUC-REFERENCE-VALUE>
                                    </REFERENCE-VALUES>
                                </ECUC-CONTAINER-VALUE>
                            </SUB-CONTAINERS>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                            <SHORT-NAME>Fr</SHORT-NAME>
                            <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Rte/RteBswModuleInstance</DEFINITION-REF>
                            <SUB-CONTAINERS>
                            </SUB-CONTAINERS>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                            <SHORT-NAME>FrIf</SHORT-NAME>
                            <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Rte/RteBswModuleInstance</DEFINITION-REF>
                            <SUB-CONTAINERS>
                                <ECUC-CONTAINER-VALUE>
                                    <SHORT-NAME>FrIf_MainFunction_0TimingEvent0</SHORT-NAME>
                                    <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Rte/RteBswModuleInstance/RteBswEventToTaskMapping</DEFINITION-REF>
                                    <REFERENCE-VALUES>
                                        <ECUC-REFERENCE-VALUE>
                                            <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Rte/RteBswModuleInstance/RteBswEventToTaskMapping/RteBswUsedOsEventRef</DEFINITION-REF>
                                            <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/DaVinci_Project/Os/Rte_Ev_Cyclic2_Default_BSW_Async_Task_0_5ms</VALUE-REF>
                                        </ECUC-REFERENCE-VALUE>
                                        <ECUC-REFERENCE-VALUE>
                                            <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Rte/RteBswModuleInstance/RteBswEventToTaskMapping/RteBswUsedOsAlarmRef</DEFINITION-REF>
                                            <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/DaVinci_Project/Os/Rte_Al_TE2_Default_BSW_Async_Task_0_5ms</VALUE-REF>
                                        </ECUC-REFERENCE-VALUE>
                                    </REFERENCE-VALUES>
                                </ECUC-CONTAINER-VALUE>
                            </SUB-CONTAINERS>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                            <SHORT-NAME>FrNm</SHORT-NAME>
                            <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Rte/RteBswModuleInstance</DEFINITION-REF>
                            <SUB-CONTAINERS>
                                <ECUC-CONTAINER-VALUE>
                                    <SHORT-NAME>FrNm_MainFunction_0TimingEvent0</SHORT-NAME>
                                    <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Rte/RteBswModuleInstance/RteBswEventToTaskMapping</DEFINITION-REF>
                                    <REFERENCE-VALUES>
                                        <ECUC-REFERENCE-VALUE>
                                            <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Rte/RteBswModuleInstance/RteBswEventToTaskMapping/RteBswUsedOsEventRef</DEFINITION-REF>
                                            <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/DaVinci_Project/Os/Rte_Ev_Cyclic2_Default_BSW_Async_Task_0_5ms</VALUE-REF>
                                        </ECUC-REFERENCE-VALUE>
                                        <ECUC-REFERENCE-VALUE>
                                            <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Rte/RteBswModuleInstance/RteBswEventToTaskMapping/RteBswUsedOsAlarmRef</DEFINITION-REF>
                                            <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/DaVinci_Project/Os/Rte_Al_TE2_Default_BSW_Async_Task_0_5ms</VALUE-REF>
                                        </ECUC-REFERENCE-VALUE>
                                    </REFERENCE-VALUES>
                                </ECUC-CONTAINER-VALUE>
                            </SUB-CONTAINERS>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                            <SHORT-NAME>FrSM</SHORT-NAME>
                            <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Rte/RteBswModuleInstance</DEFINITION-REF>
                            <SUB-CONTAINERS>
                                <ECUC-CONTAINER-VALUE>
                                    <SHORT-NAME>FrSM_MainFunction_0TimingEvent0</SHORT-NAME>
                                    <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Rte/RteBswModuleInstance/RteBswEventToTaskMapping</DEFINITION-REF>
                                    <REFERENCE-VALUES>
                                        <ECUC-REFERENCE-VALUE>
                                            <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Rte/RteBswModuleInstance/RteBswEventToTaskMapping/RteBswUsedOsEventRef</DEFINITION-REF>
                                            <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/DaVinci_Project/Os/Rte_Ev_Cyclic2_Default_BSW_Async_Task_0_5ms</VALUE-REF>
                                        </ECUC-REFERENCE-VALUE>
                                        <ECUC-REFERENCE-VALUE>
                                            <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Rte/RteBswModuleInstance/RteBswEventToTaskMapping/RteBswUsedOsAlarmRef</DEFINITION-REF>
                                            <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/DaVinci_Project/Os/Rte_Al_TE2_Default_BSW_Async_Task_0_5ms</VALUE-REF>
                                        </ECUC-REFERENCE-VALUE>
                                    </REFERENCE-VALUES>
                                </ECUC-CONTAINER-VALUE>
                            </SUB-CONTAINERS>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                            <SHORT-NAME>Lin</SHORT-NAME>
                            <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Rte/RteBswModuleInstance</DEFINITION-REF>
                            <SUB-CONTAINERS>
                            </SUB-CONTAINERS>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                            <SHORT-NAME>LinIf</SHORT-NAME>
                            <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Rte/RteBswModuleInstance</DEFINITION-REF>
                            <SUB-CONTAINERS>
                                <ECUC-CONTAINER-VALUE>
                                    <SHORT-NAME>LinIf_MainFunctionTimingEvent0</SHORT-NAME>
                                    <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Rte/RteBswModuleInstance/RteBswEventToTaskMapping</DEFINITION-REF>
                                    <REFERENCE-VALUES>
                                        <ECUC-REFERENCE-VALUE>
                                            <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Rte/RteBswModuleInstance/RteBswEventToTaskMapping/RteBswUsedOsEventRef</DEFINITION-REF>
                                            <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/DaVinci_Project/Os/Rte_Ev_Run_LinIf_LinIf_MainFunction</VALUE-REF>
                                        </ECUC-REFERENCE-VALUE>
                                        <ECUC-REFERENCE-VALUE>
                                            <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Rte/RteBswModuleInstance/RteBswEventToTaskMapping/RteBswUsedOsAlarmRef</DEFINITION-REF>
                                            <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/DaVinci_Project/Os/Rte_Al_TE_LinIf_LinIf_MainFunction</VALUE-REF>
                                        </ECUC-REFERENCE-VALUE>
                                    </REFERENCE-VALUES>
                                </ECUC-CONTAINER-VALUE>
                            </SUB-CONTAINERS>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                            <SHORT-NAME>LinNm</SHORT-NAME>
                            <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Rte/RteBswModuleInstance</DEFINITION-REF>
                            <SUB-CONTAINERS>
                            </SUB-CONTAINERS>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                            <SHORT-NAME>LinSM</SHORT-NAME>
                            <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Rte/RteBswModuleInstance</DEFINITION-REF>
                            <SUB-CONTAINERS>
                                <ECUC-CONTAINER-VALUE>
                                    <SHORT-NAME>LinSM_MainFunctionTimingEvent0</SHORT-NAME>
                                    <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Rte/RteBswModuleInstance/RteBswEventToTaskMapping</DEFINITION-REF>
                                    <REFERENCE-VALUES>
                                        <ECUC-REFERENCE-VALUE>
                                            <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Rte/RteBswModuleInstance/RteBswEventToTaskMapping/RteBswUsedOsEventRef</DEFINITION-REF>
                                            <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/DaVinci_Project/Os/Rte_Ev_Cyclic2_Default_BSW_Async_Task_0_10ms</VALUE-REF>
                                        </ECUC-REFERENCE-VALUE>
                                        <ECUC-REFERENCE-VALUE>
                                            <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Rte/RteBswModuleInstance/RteBswEventToTaskMapping/RteBswUsedOsAlarmRef</DEFINITION-REF>
                                            <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/DaVinci_Project/Os/Rte_Al_TE2_Default_BSW_Async_Task_0_10ms</VALUE-REF>
                                        </ECUC-REFERENCE-VALUE>
                                    </REFERENCE-VALUES>
                                </ECUC-CONTAINER-VALUE>
                            </SUB-CONTAINERS>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                            <SHORT-NAME>LinTp</SHORT-NAME>
                            <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Rte/RteBswModuleInstance</DEFINITION-REF>
                            <SUB-CONTAINERS>
                            </SUB-CONTAINERS>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                            <SHORT-NAME>McalLib</SHORT-NAME>
                            <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Rte/RteBswModuleInstance</DEFINITION-REF>
                            <SUB-CONTAINERS>
                            </SUB-CONTAINERS>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                            <SHORT-NAME>Mcu</SHORT-NAME>
                            <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Rte/RteBswModuleInstance</DEFINITION-REF>
                            <SUB-CONTAINERS>
                            </SUB-CONTAINERS>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                            <SHORT-NAME>Nm</SHORT-NAME>
                            <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Rte/RteBswModuleInstance</DEFINITION-REF>
                            <SUB-CONTAINERS>
                            </SUB-CONTAINERS>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                            <SHORT-NAME>NvM</SHORT-NAME>
                            <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Rte/RteBswModuleInstance</DEFINITION-REF>
                            <SUB-CONTAINERS>
                                <ECUC-CONTAINER-VALUE>
                                    <SHORT-NAME>NvM_MainFunctionTimingEvent0</SHORT-NAME>
                                    <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Rte/RteBswModuleInstance/RteBswEventToTaskMapping</DEFINITION-REF>
                                    <REFERENCE-VALUES>
                                        <ECUC-REFERENCE-VALUE>
                                            <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Rte/RteBswModuleInstance/RteBswEventToTaskMapping/RteBswUsedOsEventRef</DEFINITION-REF>
                                            <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/DaVinci_Project/Os/Rte_Ev_Cyclic2_Default_BSW_Async_Task_0_10ms</VALUE-REF>
                                        </ECUC-REFERENCE-VALUE>
                                        <ECUC-REFERENCE-VALUE>
                                            <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Rte/RteBswModuleInstance/RteBswEventToTaskMapping/RteBswUsedOsAlarmRef</DEFINITION-REF>
                                            <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/DaVinci_Project/Os/Rte_Al_TE2_Default_BSW_Async_Task_0_10ms</VALUE-REF>
                                        </ECUC-REFERENCE-VALUE>
                                    </REFERENCE-VALUES>
                                </ECUC-CONTAINER-VALUE>
                            </SUB-CONTAINERS>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                            <SHORT-NAME>PduR</SHORT-NAME>
                            <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Rte/RteBswModuleInstance</DEFINITION-REF>
                            <SUB-CONTAINERS>
                            </SUB-CONTAINERS>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                            <SHORT-NAME>Port</SHORT-NAME>
                            <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Rte/RteBswModuleInstance</DEFINITION-REF>
                            <SUB-CONTAINERS>
                            </SUB-CONTAINERS>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                            <SHORT-NAME>BswM_EcuSwComposition</SHORT-NAME>
                            <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Rte/RteSwComponentInstance</DEFINITION-REF>
                            <SUB-CONTAINERS>
                                <ECUC-CONTAINER-VALUE>
                                    <SHORT-NAME>Timer_BswM_MainFunction</SHORT-NAME>
                                    <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Rte/RteSwComponentInstance/RteEventToTaskMapping</DEFINITION-REF>
                                    <REFERENCE-VALUES>
                                        <ECUC-REFERENCE-VALUE>
                                            <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Rte/RteSwComponentInstance/RteEventToTaskMapping/RteUsedOsEventRef</DEFINITION-REF>
                                            <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/DaVinci_Project/Os/Rte_Ev_Cyclic2_Default_BSW_Async_Task_0_10ms</VALUE-REF>
                                        </ECUC-REFERENCE-VALUE>
                                        <ECUC-REFERENCE-VALUE>
                                            <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Rte/RteSwComponentInstance/RteEventToTaskMapping/RteUsedOsAlarmRef</DEFINITION-REF>
                                            <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/DaVinci_Project/Os/Rte_Al_TE2_Default_BSW_Async_Task_0_10ms</VALUE-REF>
                                        </ECUC-REFERENCE-VALUE>
                                    </REFERENCE-VALUES>
                                </ECUC-CONTAINER-VALUE>
                            </SUB-CONTAINERS>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                            <SHORT-NAME>ComM_EcuSwComposition</SHORT-NAME>
                            <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Rte/RteSwComponentInstance</DEFINITION-REF>
                            <SUB-CONTAINERS>
                                <ECUC-CONTAINER-VALUE>
                                    <SHORT-NAME>Timer_ComM_MainFunction_0</SHORT-NAME>
                                    <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Rte/RteSwComponentInstance/RteEventToTaskMapping</DEFINITION-REF>
                                    <REFERENCE-VALUES>
                                        <ECUC-REFERENCE-VALUE>
                                            <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Rte/RteSwComponentInstance/RteEventToTaskMapping/RteUsedOsEventRef</DEFINITION-REF>
                                            <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/DaVinci_Project/Os/Rte_Ev_Cyclic2_Default_BSW_Async_Task_0_20ms</VALUE-REF>
                                        </ECUC-REFERENCE-VALUE>
                                        <ECUC-REFERENCE-VALUE>
                                            <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Rte/RteSwComponentInstance/RteEventToTaskMapping/RteUsedOsAlarmRef</DEFINITION-REF>
                                            <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/DaVinci_Project/Os/Rte_Al_TE2_Default_BSW_Async_Task_0_20ms</VALUE-REF>
                                        </ECUC-REFERENCE-VALUE>
                                    </REFERENCE-VALUES>
                                </ECUC-CONTAINER-VALUE>
                                <ECUC-CONTAINER-VALUE>
                                    <SHORT-NAME>Timer_ComM_MainFunction_1</SHORT-NAME>
                                    <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Rte/RteSwComponentInstance/RteEventToTaskMapping</DEFINITION-REF>
                                    <REFERENCE-VALUES>
                                        <ECUC-REFERENCE-VALUE>
                                            <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Rte/RteSwComponentInstance/RteEventToTaskMapping/RteUsedOsEventRef</DEFINITION-REF>
                                            <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/DaVinci_Project/Os/Rte_Ev_Cyclic2_Default_BSW_Async_Task_0_20ms</VALUE-REF>
                                        </ECUC-REFERENCE-VALUE>
                                        <ECUC-REFERENCE-VALUE>
                                            <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Rte/RteSwComponentInstance/RteEventToTaskMapping/RteUsedOsAlarmRef</DEFINITION-REF>
                                            <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/DaVinci_Project/Os/Rte_Al_TE2_Default_BSW_Async_Task_0_20ms</VALUE-REF>
                                        </ECUC-REFERENCE-VALUE>
                                    </REFERENCE-VALUES>
                                </ECUC-CONTAINER-VALUE>
                                <ECUC-CONTAINER-VALUE>
                                    <SHORT-NAME>Timer_ComM_MainFunction_2</SHORT-NAME>
                                    <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Rte/RteSwComponentInstance/RteEventToTaskMapping</DEFINITION-REF>
                                    <REFERENCE-VALUES>
                                        <ECUC-REFERENCE-VALUE>
                                            <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Rte/RteSwComponentInstance/RteEventToTaskMapping/RteUsedOsEventRef</DEFINITION-REF>
                                            <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/DaVinci_Project/Os/Rte_Ev_Cyclic2_Default_BSW_Async_Task_0_20ms</VALUE-REF>
                                        </ECUC-REFERENCE-VALUE>
                                        <ECUC-REFERENCE-VALUE>
                                            <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Rte/RteSwComponentInstance/RteEventToTaskMapping/RteUsedOsAlarmRef</DEFINITION-REF>
                                            <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/DaVinci_Project/Os/Rte_Al_TE2_Default_BSW_Async_Task_0_20ms</VALUE-REF>
                                        </ECUC-REFERENCE-VALUE>
                                    </REFERENCE-VALUES>
                                </ECUC-CONTAINER-VALUE>
                            </SUB-CONTAINERS>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                            <SHORT-NAME>Dcm_EcuSwComposition</SHORT-NAME>
                            <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Rte/RteSwComponentInstance</DEFINITION-REF>
                            <SUB-CONTAINERS>
                                <ECUC-CONTAINER-VALUE>
                                    <SHORT-NAME>Timer_Dcm_MainFunction</SHORT-NAME>
                                    <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Rte/RteSwComponentInstance/RteEventToTaskMapping</DEFINITION-REF>
                                    <REFERENCE-VALUES>
                                        <ECUC-REFERENCE-VALUE>
                                            <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Rte/RteSwComponentInstance/RteEventToTaskMapping/RteUsedOsEventRef</DEFINITION-REF>
                                            <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/DaVinci_Project/Os/Rte_Ev_Cyclic2_Default_BSW_Async_Task_0_10ms</VALUE-REF>
                                        </ECUC-REFERENCE-VALUE>
                                        <ECUC-REFERENCE-VALUE>
                                            <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Rte/RteSwComponentInstance/RteEventToTaskMapping/RteUsedOsAlarmRef</DEFINITION-REF>
                                            <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/DaVinci_Project/Os/Rte_Al_TE2_Default_BSW_Async_Task_0_10ms</VALUE-REF>
                                        </ECUC-REFERENCE-VALUE>
                                    </REFERENCE-VALUES>
                                </ECUC-CONTAINER-VALUE>
                            </SUB-CONTAINERS>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                            <SHORT-NAME>DemMaster_0_EcuSwComposition</SHORT-NAME>
                            <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Rte/RteSwComponentInstance</DEFINITION-REF>
                            <SUB-CONTAINERS>
                                <ECUC-CONTAINER-VALUE>
                                    <SHORT-NAME>Timer_Dem_MasterMainFunction</SHORT-NAME>
                                    <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Rte/RteSwComponentInstance/RteEventToTaskMapping</DEFINITION-REF>
                                    <REFERENCE-VALUES>
                                        <ECUC-REFERENCE-VALUE>
                                            <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Rte/RteSwComponentInstance/RteEventToTaskMapping/RteUsedOsEventRef</DEFINITION-REF>
                                            <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/DaVinci_Project/Os/Rte_Ev_Cyclic2_Default_BSW_Async_Task_0_10ms</VALUE-REF>
                                        </ECUC-REFERENCE-VALUE>
                                        <ECUC-REFERENCE-VALUE>
                                            <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Rte/RteSwComponentInstance/RteEventToTaskMapping/RteUsedOsAlarmRef</DEFINITION-REF>
                                            <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/DaVinci_Project/Os/Rte_Al_TE2_Default_BSW_Async_Task_0_10ms</VALUE-REF>
                                        </ECUC-REFERENCE-VALUE>
                                    </REFERENCE-VALUES>
                                </ECUC-CONTAINER-VALUE>
                            </SUB-CONTAINERS>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                            <SHORT-NAME>DemSatellite_0_EcuSwComposition</SHORT-NAME>
                            <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Rte/RteSwComponentInstance</DEFINITION-REF>
                            <SUB-CONTAINERS>
                                <ECUC-CONTAINER-VALUE>
                                    <SHORT-NAME>Timer_Dem_SatelliteMainFunction</SHORT-NAME>
                                    <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Rte/RteSwComponentInstance/RteEventToTaskMapping</DEFINITION-REF>
                                    <REFERENCE-VALUES>
                                        <ECUC-REFERENCE-VALUE>
                                            <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Rte/RteSwComponentInstance/RteEventToTaskMapping/RteUsedOsEventRef</DEFINITION-REF>
                                            <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/DaVinci_Project/Os/Rte_Ev_Cyclic2_Default_BSW_Async_Task_0_10ms</VALUE-REF>
                                        </ECUC-REFERENCE-VALUE>
                                        <ECUC-REFERENCE-VALUE>
                                            <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Rte/RteSwComponentInstance/RteEventToTaskMapping/RteUsedOsAlarmRef</DEFINITION-REF>
                                            <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/DaVinci_Project/Os/Rte_Al_TE2_Default_BSW_Async_Task_0_10ms</VALUE-REF>
                                        </ECUC-REFERENCE-VALUE>
                                    </REFERENCE-VALUES>
                                </ECUC-CONTAINER-VALUE>
                            </SUB-CONTAINERS>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                            <SHORT-NAME>Det_EcuSwComposition</SHORT-NAME>
                            <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Rte/RteSwComponentInstance</DEFINITION-REF>
                            <SUB-CONTAINERS>
                            </SUB-CONTAINERS>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                            <SHORT-NAME>EcuM_EcuSwComposition</SHORT-NAME>
                            <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Rte/RteSwComponentInstance</DEFINITION-REF>
                            <SUB-CONTAINERS>
                                <ECUC-CONTAINER-VALUE>
                                    <SHORT-NAME>Timer_EcuM_MainFunction</SHORT-NAME>
                                    <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Rte/RteSwComponentInstance/RteEventToTaskMapping</DEFINITION-REF>
                                    <REFERENCE-VALUES>
                                        <ECUC-REFERENCE-VALUE>
                                            <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Rte/RteSwComponentInstance/RteEventToTaskMapping/RteUsedOsEventRef</DEFINITION-REF>
                                            <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/DaVinci_Project/Os/Rte_Ev_Cyclic2_Default_BSW_Async_Task_0_10ms</VALUE-REF>
                                        </ECUC-REFERENCE-VALUE>
                                        <ECUC-REFERENCE-VALUE>
                                            <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Rte/RteSwComponentInstance/RteEventToTaskMapping/RteUsedOsAlarmRef</DEFINITION-REF>
                                            <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/DaVinci_Project/Os/Rte_Al_TE2_Default_BSW_Async_Task_0_10ms</VALUE-REF>
                                        </ECUC-REFERENCE-VALUE>
                                    </REFERENCE-VALUES>
                                </ECUC-CONTAINER-VALUE>
                            </SUB-CONTAINERS>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                            <SHORT-NAME>NvM_EcuSwComposition</SHORT-NAME>
                            <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Rte/RteSwComponentInstance</DEFINITION-REF>
                            <SUB-CONTAINERS>
                                <ECUC-CONTAINER-VALUE>
                                    <SHORT-NAME>Timer_NvM_MainFunction</SHORT-NAME>
                                    <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Rte/RteSwComponentInstance/RteEventToTaskMapping</DEFINITION-REF>
                                    <REFERENCE-VALUES>
                                        <ECUC-REFERENCE-VALUE>
                                            <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Rte/RteSwComponentInstance/RteEventToTaskMapping/RteUsedOsEventRef</DEFINITION-REF>
                                            <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/DaVinci_Project/Os/Rte_Ev_Cyclic2_Default_BSW_Async_Task_0_10ms</VALUE-REF>
                                        </ECUC-REFERENCE-VALUE>
                                        <ECUC-REFERENCE-VALUE>
                                            <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Rte/RteSwComponentInstance/RteEventToTaskMapping/RteUsedOsAlarmRef</DEFINITION-REF>
                                            <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/DaVinci_Project/Os/Rte_Al_TE2_Default_BSW_Async_Task_0_10ms</VALUE-REF>
                                        </ECUC-REFERENCE-VALUE>
                                    </REFERENCE-VALUES>
                                </ECUC-CONTAINER-VALUE>
                            </SUB-CONTAINERS>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                            <SHORT-NAME>Os_OsCore0_swc_EcuSwComposition</SHORT-NAME>
                            <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Rte/RteSwComponentInstance</DEFINITION-REF>
                            <SUB-CONTAINERS>
                            </SUB-CONTAINERS>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                            <SHORT-NAME>StartApplication_EcuSwComposition</SHORT-NAME>
                            <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Rte/RteSwComponentInstance</DEFINITION-REF>
                            <SUB-CONTAINERS>
                                <ECUC-CONTAINER-VALUE>
                                    <SHORT-NAME>Timer_StartApplication_Cyclic1000ms</SHORT-NAME>
                                    <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Rte/RteSwComponentInstance/RteEventToTaskMapping</DEFINITION-REF>
                                    <REFERENCE-VALUES>
                                        <ECUC-REFERENCE-VALUE>
                                            <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Rte/RteSwComponentInstance/RteEventToTaskMapping/RteUsedOsEventRef</DEFINITION-REF>
                                            <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/DaVinci_Project/Os/Rte_Ev_Run_StartApplication_StartApplication_Cyclic1000ms</VALUE-REF>
                                        </ECUC-REFERENCE-VALUE>
                                        <ECUC-REFERENCE-VALUE>
                                            <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Rte/RteSwComponentInstance/RteEventToTaskMapping/RteUsedOsAlarmRef</DEFINITION-REF>
                                            <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/DaVinci_Project/Os/Rte_Al_TE_StartApplication_StartApplication_Cyclic1000ms</VALUE-REF>
                                        </ECUC-REFERENCE-VALUE>
                                    </REFERENCE-VALUES>
                                </ECUC-CONTAINER-VALUE>
                                <ECUC-CONTAINER-VALUE>
                                    <SHORT-NAME>Timer_StartApplication_Cyclic10ms</SHORT-NAME>
                                    <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Rte/RteSwComponentInstance/RteEventToTaskMapping</DEFINITION-REF>
                                    <REFERENCE-VALUES>
                                        <ECUC-REFERENCE-VALUE>
                                            <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Rte/RteSwComponentInstance/RteEventToTaskMapping/RteUsedOsEventRef</DEFINITION-REF>
                                            <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/DaVinci_Project/Os/Rte_Ev_Run_StartApplication_StartApplication_Cyclic10ms</VALUE-REF>
                                        </ECUC-REFERENCE-VALUE>
                                        <ECUC-REFERENCE-VALUE>
                                            <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Rte/RteSwComponentInstance/RteEventToTaskMapping/RteUsedOsAlarmRef</DEFINITION-REF>
                                            <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/DaVinci_Project/Os/Rte_Al_TE_StartApplication_StartApplication_Cyclic10ms</VALUE-REF>
                                        </ECUC-REFERENCE-VALUE>
                                    </REFERENCE-VALUES>
                                </ECUC-CONTAINER-VALUE>
                                <ECUC-CONTAINER-VALUE>
                                    <SHORT-NAME>Timer_StartApplication_Cyclic1ms</SHORT-NAME>
                                    <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Rte/RteSwComponentInstance/RteEventToTaskMapping</DEFINITION-REF>
                                    <REFERENCE-VALUES>
                                        <ECUC-REFERENCE-VALUE>
                                            <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Rte/RteSwComponentInstance/RteEventToTaskMapping/RteUsedOsEventRef</DEFINITION-REF>
                                            <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/DaVinci_Project/Os/Rte_Ev_Run_StartApplication_StartApplication_Cyclic1ms</VALUE-REF>
                                        </ECUC-REFERENCE-VALUE>
                                        <ECUC-REFERENCE-VALUE>
                                            <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Rte/RteSwComponentInstance/RteEventToTaskMapping/RteUsedOsAlarmRef</DEFINITION-REF>
                                            <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/DaVinci_Project/Os/Rte_Al_TE_StartApplication_StartApplication_Cyclic1ms</VALUE-REF>
                                        </ECUC-REFERENCE-VALUE>
                                    </REFERENCE-VALUES>
                                </ECUC-CONTAINER-VALUE>
                                <ECUC-CONTAINER-VALUE>
                                    <SHORT-NAME>Timer_StartApplication_Cyclic250ms</SHORT-NAME>
                                    <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Rte/RteSwComponentInstance/RteEventToTaskMapping</DEFINITION-REF>
                                    <REFERENCE-VALUES>
                                        <ECUC-REFERENCE-VALUE>
                                            <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Rte/RteSwComponentInstance/RteEventToTaskMapping/RteUsedOsEventRef</DEFINITION-REF>
                                            <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/DaVinci_Project/Os/Rte_Ev_Run_StartApplication_StartApplication_Cyclic250ms</VALUE-REF>
                                        </ECUC-REFERENCE-VALUE>
                                        <ECUC-REFERENCE-VALUE>
                                            <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Rte/RteSwComponentInstance/RteEventToTaskMapping/RteUsedOsAlarmRef</DEFINITION-REF>
                                            <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/DaVinci_Project/Os/Rte_Al_TE_StartApplication_StartApplication_Cyclic250ms</VALUE-REF>
                                        </ECUC-REFERENCE-VALUE>
                                    </REFERENCE-VALUES>
                                </ECUC-CONTAINER-VALUE>
                                <ECUC-CONTAINER-VALUE>
                                    <SHORT-NAME>DataReceivedEvent_StartApplication_OnDataRec_RxCtrl_PpSignal_Rx20bit_Cyclic_omsg_RxCycle500_20_oCAN_983b15aa_Rx_DeSigna_7b663139</SHORT-NAME>
                                    <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Rte/RteSwComponentInstance/RteEventToTaskMapping</DEFINITION-REF>
                                    <REFERENCE-VALUES>
                                        <ECUC-REFERENCE-VALUE>
                                            <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Rte/RteSwComponentInstance/RteEventToTaskMapping/RteUsedOsEventRef</DEFINITION-REF>
                                            <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/DaVinci_Project/Os/Rte_Ev_Run_StartApplication_StartApplication_OnDataRec_RxCtrl</VALUE-REF>
                                        </ECUC-REFERENCE-VALUE>
                                    </REFERENCE-VALUES>
                                </ECUC-CONTAINER-VALUE>
                                <ECUC-CONTAINER-VALUE>
                                    <SHORT-NAME>DataReceivedEvent_StartApplication_OnDataRec_RxData_PpSG_RgRx_Prof1C_RxCycle_omsg_RxCycle_E2eProf1C_500_10_oCAN_430c5af_7f5b60fc</SHORT-NAME>
                                    <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Rte/RteSwComponentInstance/RteEventToTaskMapping</DEFINITION-REF>
                                    <REFERENCE-VALUES>
                                        <ECUC-REFERENCE-VALUE>
                                            <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Rte/RteSwComponentInstance/RteEventToTaskMapping/RteUsedOsEventRef</DEFINITION-REF>
                                            <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/DaVinci_Project/Os/Rte_Ev_Run_StartApplication_StartApplication_OnDataRec_RxData</VALUE-REF>
                                        </ECUC-REFERENCE-VALUE>
                                    </REFERENCE-VALUES>
                                </ECUC-CONTAINER-VALUE>
                            </SUB-CONTAINERS>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                            <SHORT-NAME>RteOsInteraction</SHORT-NAME>
                            <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Rte/RteOsInteraction</DEFINITION-REF>
                            <SUB-CONTAINERS>
                                <ECUC-CONTAINER-VALUE>
                                    <SHORT-NAME>Rte_Al_TE2_Default_BSW_Async_Task_0_10ms</SHORT-NAME>
                                    <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Rte/RteOsInteraction/RteUsedOsActivation</DEFINITION-REF>
                                       <PARAMETER-VALUES>
                                           <ECUC-NUMERICAL-PARAM-VALUE>
                                               <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/MICROSAR/Rte/RteOsInteraction/RteUsedOsActivation/RteExpectedActivationOffset</DEFINITION-REF>
                                               <VALUE>0</VALUE>
                                           </ECUC-NUMERICAL-PARAM-VALUE>
                                       </PARAMETER-VALUES>
                                    <REFERENCE-VALUES>
                                        <ECUC-REFERENCE-VALUE>
                                            <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Rte/RteOsInteraction/RteUsedOsActivation/RteActivationOsAlarmRef</DEFINITION-REF>
                                            <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/DaVinci_Project/Os/Rte_Al_TE2_Default_BSW_Async_Task_0_10ms</VALUE-REF>
                                        </ECUC-REFERENCE-VALUE>
                                           <ECUC-REFERENCE-VALUE>
                                               <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Rte/RteOsInteraction/RteUsedOsActivation/RteActivationOsTaskRef</DEFINITION-REF>
                                               <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/DaVinci_Project/Os/Default_BSW_Async_Task</VALUE-REF>
                                           </ECUC-REFERENCE-VALUE>
                                    </REFERENCE-VALUES>
                                </ECUC-CONTAINER-VALUE>
                                <ECUC-CONTAINER-VALUE>
                                    <SHORT-NAME>Rte_Al_TE2_Default_BSW_Async_Task_0_20ms</SHORT-NAME>
                                    <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Rte/RteOsInteraction/RteUsedOsActivation</DEFINITION-REF>
                                       <PARAMETER-VALUES>
                                           <ECUC-NUMERICAL-PARAM-VALUE>
                                               <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/MICROSAR/Rte/RteOsInteraction/RteUsedOsActivation/RteExpectedActivationOffset</DEFINITION-REF>
                                               <VALUE>0</VALUE>
                                           </ECUC-NUMERICAL-PARAM-VALUE>
                                       </PARAMETER-VALUES>
                                    <REFERENCE-VALUES>
                                        <ECUC-REFERENCE-VALUE>
                                            <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Rte/RteOsInteraction/RteUsedOsActivation/RteActivationOsAlarmRef</DEFINITION-REF>
                                            <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/DaVinci_Project/Os/Rte_Al_TE2_Default_BSW_Async_Task_0_20ms</VALUE-REF>
                                        </ECUC-REFERENCE-VALUE>
                                           <ECUC-REFERENCE-VALUE>
                                               <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Rte/RteOsInteraction/RteUsedOsActivation/RteActivationOsTaskRef</DEFINITION-REF>
                                               <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/DaVinci_Project/Os/Default_BSW_Async_Task</VALUE-REF>
                                           </ECUC-REFERENCE-VALUE>
                                    </REFERENCE-VALUES>
                                </ECUC-CONTAINER-VALUE>
                                <ECUC-CONTAINER-VALUE>
                                    <SHORT-NAME>Rte_Al_TE2_Default_BSW_Async_Task_0_5ms</SHORT-NAME>
                                    <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Rte/RteOsInteraction/RteUsedOsActivation</DEFINITION-REF>
                                       <PARAMETER-VALUES>
                                           <ECUC-NUMERICAL-PARAM-VALUE>
                                               <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/MICROSAR/Rte/RteOsInteraction/RteUsedOsActivation/RteExpectedActivationOffset</DEFINITION-REF>
                                               <VALUE>0</VALUE>
                                           </ECUC-NUMERICAL-PARAM-VALUE>
                                       </PARAMETER-VALUES>
                                    <REFERENCE-VALUES>
                                        <ECUC-REFERENCE-VALUE>
                                            <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Rte/RteOsInteraction/RteUsedOsActivation/RteActivationOsAlarmRef</DEFINITION-REF>
                                            <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/DaVinci_Project/Os/Rte_Al_TE2_Default_BSW_Async_Task_0_5ms</VALUE-REF>
                                        </ECUC-REFERENCE-VALUE>
                                           <ECUC-REFERENCE-VALUE>
                                               <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Rte/RteOsInteraction/RteUsedOsActivation/RteActivationOsTaskRef</DEFINITION-REF>
                                               <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/DaVinci_Project/Os/Default_BSW_Async_Task</VALUE-REF>
                                           </ECUC-REFERENCE-VALUE>
                                    </REFERENCE-VALUES>
                                </ECUC-CONTAINER-VALUE>
                                <ECUC-CONTAINER-VALUE>
                                    <SHORT-NAME>Rte_Al_TE_LinIf_LinIf_MainFunction</SHORT-NAME>
                                    <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Rte/RteOsInteraction/RteUsedOsActivation</DEFINITION-REF>
                                       <PARAMETER-VALUES>
                                           <ECUC-NUMERICAL-PARAM-VALUE>
                                               <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/MICROSAR/Rte/RteOsInteraction/RteUsedOsActivation/RteExpectedActivationOffset</DEFINITION-REF>
                                               <VALUE>0</VALUE>
                                           </ECUC-NUMERICAL-PARAM-VALUE>
                                       </PARAMETER-VALUES>
                                    <REFERENCE-VALUES>
                                        <ECUC-REFERENCE-VALUE>
                                            <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Rte/RteOsInteraction/RteUsedOsActivation/RteActivationOsAlarmRef</DEFINITION-REF>
                                            <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/DaVinci_Project/Os/Rte_Al_TE_LinIf_LinIf_MainFunction</VALUE-REF>
                                        </ECUC-REFERENCE-VALUE>
                                           <ECUC-REFERENCE-VALUE>
                                               <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Rte/RteOsInteraction/RteUsedOsActivation/RteActivationOsTaskRef</DEFINITION-REF>
                                               <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/DaVinci_Project/Os/Default_BSW_Async_Task</VALUE-REF>
                                           </ECUC-REFERENCE-VALUE>
                                    </REFERENCE-VALUES>
                                </ECUC-CONTAINER-VALUE>
                                <ECUC-CONTAINER-VALUE>
                                    <SHORT-NAME>Rte_Al_TE_StartApplication_StartApplication_Cyclic1000ms</SHORT-NAME>
                                    <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Rte/RteOsInteraction/RteUsedOsActivation</DEFINITION-REF>
                                       <PARAMETER-VALUES>
                                           <ECUC-NUMERICAL-PARAM-VALUE>
                                               <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/MICROSAR/Rte/RteOsInteraction/RteUsedOsActivation/RteExpectedActivationOffset</DEFINITION-REF>
                                               <VALUE>0</VALUE>
                                           </ECUC-NUMERICAL-PARAM-VALUE>
                                       </PARAMETER-VALUES>
                                    <REFERENCE-VALUES>
                                        <ECUC-REFERENCE-VALUE>
                                            <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Rte/RteOsInteraction/RteUsedOsActivation/RteActivationOsAlarmRef</DEFINITION-REF>
                                            <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/DaVinci_Project/Os/Rte_Al_TE_StartApplication_StartApplication_Cyclic1000ms</VALUE-REF>
                                        </ECUC-REFERENCE-VALUE>
                                           <ECUC-REFERENCE-VALUE>
                                               <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Rte/RteOsInteraction/RteUsedOsActivation/RteActivationOsTaskRef</DEFINITION-REF>
                                               <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/DaVinci_Project/Os/StartApplication_Appl_Task</VALUE-REF>
                                           </ECUC-REFERENCE-VALUE>
                                    </REFERENCE-VALUES>
                                </ECUC-CONTAINER-VALUE>
                                <ECUC-CONTAINER-VALUE>
                                    <SHORT-NAME>Rte_Al_TE_StartApplication_StartApplication_Cyclic10ms</SHORT-NAME>
                                    <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Rte/RteOsInteraction/RteUsedOsActivation</DEFINITION-REF>
                                       <PARAMETER-VALUES>
                                           <ECUC-NUMERICAL-PARAM-VALUE>
                                               <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/MICROSAR/Rte/RteOsInteraction/RteUsedOsActivation/RteExpectedActivationOffset</DEFINITION-REF>
                                               <VALUE>0</VALUE>
                                           </ECUC-NUMERICAL-PARAM-VALUE>
                                       </PARAMETER-VALUES>
                                    <REFERENCE-VALUES>
                                        <ECUC-REFERENCE-VALUE>
                                            <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Rte/RteOsInteraction/RteUsedOsActivation/RteActivationOsAlarmRef</DEFINITION-REF>
                                            <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/DaVinci_Project/Os/Rte_Al_TE_StartApplication_StartApplication_Cyclic10ms</VALUE-REF>
                                        </ECUC-REFERENCE-VALUE>
                                           <ECUC-REFERENCE-VALUE>
                                               <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Rte/RteOsInteraction/RteUsedOsActivation/RteActivationOsTaskRef</DEFINITION-REF>
                                               <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/DaVinci_Project/Os/StartApplication_Appl_Task</VALUE-REF>
                                           </ECUC-REFERENCE-VALUE>
                                    </REFERENCE-VALUES>
                                </ECUC-CONTAINER-VALUE>
                                <ECUC-CONTAINER-VALUE>
                                    <SHORT-NAME>Rte_Al_TE_StartApplication_StartApplication_Cyclic1ms</SHORT-NAME>
                                    <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Rte/RteOsInteraction/RteUsedOsActivation</DEFINITION-REF>
                                       <PARAMETER-VALUES>
                                           <ECUC-NUMERICAL-PARAM-VALUE>
                                               <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/MICROSAR/Rte/RteOsInteraction/RteUsedOsActivation/RteExpectedActivationOffset</DEFINITION-REF>
                                               <VALUE>0</VALUE>
                                           </ECUC-NUMERICAL-PARAM-VALUE>
                                       </PARAMETER-VALUES>
                                    <REFERENCE-VALUES>
                                        <ECUC-REFERENCE-VALUE>
                                            <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Rte/RteOsInteraction/RteUsedOsActivation/RteActivationOsAlarmRef</DEFINITION-REF>
                                            <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/DaVinci_Project/Os/Rte_Al_TE_StartApplication_StartApplication_Cyclic1ms</VALUE-REF>
                                        </ECUC-REFERENCE-VALUE>
                                           <ECUC-REFERENCE-VALUE>
                                               <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Rte/RteOsInteraction/RteUsedOsActivation/RteActivationOsTaskRef</DEFINITION-REF>
                                               <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/DaVinci_Project/Os/StartApplication_Appl_Task</VALUE-REF>
                                           </ECUC-REFERENCE-VALUE>
                                    </REFERENCE-VALUES>
                                </ECUC-CONTAINER-VALUE>
                                <ECUC-CONTAINER-VALUE>
                                    <SHORT-NAME>Rte_Al_TE_StartApplication_StartApplication_Cyclic250ms</SHORT-NAME>
                                    <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Rte/RteOsInteraction/RteUsedOsActivation</DEFINITION-REF>
                                       <PARAMETER-VALUES>
                                           <ECUC-NUMERICAL-PARAM-VALUE>
                                               <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/MICROSAR/Rte/RteOsInteraction/RteUsedOsActivation/RteExpectedActivationOffset</DEFINITION-REF>
                                               <VALUE>0</VALUE>
                                           </ECUC-NUMERICAL-PARAM-VALUE>
                                       </PARAMETER-VALUES>
                                    <REFERENCE-VALUES>
                                        <ECUC-REFERENCE-VALUE>
                                            <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Rte/RteOsInteraction/RteUsedOsActivation/RteActivationOsAlarmRef</DEFINITION-REF>
                                            <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/DaVinci_Project/Os/Rte_Al_TE_StartApplication_StartApplication_Cyclic250ms</VALUE-REF>
                                        </ECUC-REFERENCE-VALUE>
                                           <ECUC-REFERENCE-VALUE>
                                               <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Rte/RteOsInteraction/RteUsedOsActivation/RteActivationOsTaskRef</DEFINITION-REF>
                                               <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/DaVinci_Project/Os/StartApplication_Appl_Task</VALUE-REF>
                                           </ECUC-REFERENCE-VALUE>
                                    </REFERENCE-VALUES>
                                </ECUC-CONTAINER-VALUE>
                            </SUB-CONTAINERS>
                        </ECUC-CONTAINER-VALUE>
                    </CONTAINERS>
                </ECUC-MODULE-CONFIGURATION-VALUES>
                <ECUC-MODULE-CONFIGURATION-VALUES>
                    <SHORT-NAME>NvM</SHORT-NAME>
                    <DEFINITION-REF DEST="ECUC-MODULE-DEF">/MICROSAR/NvM</DEFINITION-REF>
                    <CONTAINERS>
                    </CONTAINERS>
                </ECUC-MODULE-CONFIGURATION-VALUES>
                <ECUC-MODULE-CONFIGURATION-VALUES>
                    <SHORT-NAME>Os</SHORT-NAME>
                    <DEFINITION-REF DEST="ECUC-MODULE-DEF">/MICROSAR/Os</DEFINITION-REF>
                    <CONTAINERS>
                        <ECUC-CONTAINER-VALUE>
                            <SHORT-NAME>Default_BSW_Async_Task</SHORT-NAME>
                            <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Os/OsTask</DEFINITION-REF>
                            <PARAMETER-VALUES>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                    <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/MICROSAR/Os/OsTask/OsTaskActivation</DEFINITION-REF>
                                    <VALUE>1</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                    <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/MICROSAR/Os/OsTask/OsTaskPriority</DEFINITION-REF>
                                    <VALUE>30</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                    <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/MICROSAR/Os/OsTask/OsTaskSchedule</DEFINITION-REF>
                                    <VALUE>NON</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                            </PARAMETER-VALUES>
                            <REFERENCE-VALUES>
                                <ECUC-REFERENCE-VALUE>
                                    <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Os/OsTask/OsTaskEventRef</DEFINITION-REF>
                                    <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/DaVinci_Project/Os/Rte_Ev_Cyclic2_Default_BSW_Async_Task_0_10ms</VALUE-REF>
                                </ECUC-REFERENCE-VALUE>
                                <ECUC-REFERENCE-VALUE>
                                    <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Os/OsTask/OsTaskEventRef</DEFINITION-REF>
                                    <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/DaVinci_Project/Os/Rte_Ev_Cyclic2_Default_BSW_Async_Task_0_20ms</VALUE-REF>
                                </ECUC-REFERENCE-VALUE>
                                <ECUC-REFERENCE-VALUE>
                                    <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Os/OsTask/OsTaskEventRef</DEFINITION-REF>
                                    <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/DaVinci_Project/Os/Rte_Ev_Cyclic2_Default_BSW_Async_Task_0_5ms</VALUE-REF>
                                </ECUC-REFERENCE-VALUE>
                                <ECUC-REFERENCE-VALUE>
                                    <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Os/OsTask/OsTaskEventRef</DEFINITION-REF>
                                    <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/DaVinci_Project/Os/Rte_Ev_Run_LinIf_LinIf_MainFunction</VALUE-REF>
                                </ECUC-REFERENCE-VALUE>
                            </REFERENCE-VALUES>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                            <SHORT-NAME>Rte_Al_TE2_Default_BSW_Async_Task_0_10ms</SHORT-NAME>
                            <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Os/OsAlarm</DEFINITION-REF>
                            <REFERENCE-VALUES>
                                <ECUC-REFERENCE-VALUE>
                                    <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Os/OsAlarm/OsAlarmCounterRef</DEFINITION-REF>
                                    <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/DaVinci_Project/Os/SystemTimer</VALUE-REF>
                                </ECUC-REFERENCE-VALUE>
                            </REFERENCE-VALUES>
                            <SUB-CONTAINERS>
                                <ECUC-CONTAINER-VALUE>
                                    <SHORT-NAME>OsAlarmAction</SHORT-NAME>
                                    <DEFINITION-REF DEST="ECUC-CHOICE-CONTAINER-DEF">/MICROSAR/Os/OsAlarm/OsAlarmAction</DEFINITION-REF>
                                    <SUB-CONTAINERS>
                                        <ECUC-CONTAINER-VALUE>
                                            <SHORT-NAME>OsAlarmSetEvent</SHORT-NAME>
                                            <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Os/OsAlarm/OsAlarmAction/OsAlarmSetEvent</DEFINITION-REF>
                                            <REFERENCE-VALUES>
                                                <ECUC-REFERENCE-VALUE>
                                                    <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Os/OsAlarm/OsAlarmAction/OsAlarmSetEvent/OsAlarmSetEventRef</DEFINITION-REF>
                                                    <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/DaVinci_Project/Os/Rte_Ev_Cyclic2_Default_BSW_Async_Task_0_10ms</VALUE-REF>
                                                </ECUC-REFERENCE-VALUE>
                                                <ECUC-REFERENCE-VALUE>
                                                    <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Os/OsAlarm/OsAlarmAction/OsAlarmSetEvent/OsAlarmSetEventTaskRef</DEFINITION-REF>
                                                    <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/DaVinci_Project/Os/Default_BSW_Async_Task</VALUE-REF>
                                                </ECUC-REFERENCE-VALUE>
                                            </REFERENCE-VALUES>
                                        </ECUC-CONTAINER-VALUE>
                                    </SUB-CONTAINERS>
                                </ECUC-CONTAINER-VALUE>
                            </SUB-CONTAINERS>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                            <SHORT-NAME>Rte_Al_TE2_Default_BSW_Async_Task_0_20ms</SHORT-NAME>
                            <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Os/OsAlarm</DEFINITION-REF>
                            <REFERENCE-VALUES>
                                <ECUC-REFERENCE-VALUE>
                                    <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Os/OsAlarm/OsAlarmCounterRef</DEFINITION-REF>
                                    <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/DaVinci_Project/Os/SystemTimer</VALUE-REF>
                                </ECUC-REFERENCE-VALUE>
                            </REFERENCE-VALUES>
                            <SUB-CONTAINERS>
                                <ECUC-CONTAINER-VALUE>
                                    <SHORT-NAME>OsAlarmAction</SHORT-NAME>
                                    <DEFINITION-REF DEST="ECUC-CHOICE-CONTAINER-DEF">/MICROSAR/Os/OsAlarm/OsAlarmAction</DEFINITION-REF>
                                    <SUB-CONTAINERS>
                                        <ECUC-CONTAINER-VALUE>
                                            <SHORT-NAME>OsAlarmSetEvent</SHORT-NAME>
                                            <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Os/OsAlarm/OsAlarmAction/OsAlarmSetEvent</DEFINITION-REF>
                                            <REFERENCE-VALUES>
                                                <ECUC-REFERENCE-VALUE>
                                                    <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Os/OsAlarm/OsAlarmAction/OsAlarmSetEvent/OsAlarmSetEventRef</DEFINITION-REF>
                                                    <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/DaVinci_Project/Os/Rte_Ev_Cyclic2_Default_BSW_Async_Task_0_20ms</VALUE-REF>
                                                </ECUC-REFERENCE-VALUE>
                                                <ECUC-REFERENCE-VALUE>
                                                    <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Os/OsAlarm/OsAlarmAction/OsAlarmSetEvent/OsAlarmSetEventTaskRef</DEFINITION-REF>
                                                    <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/DaVinci_Project/Os/Default_BSW_Async_Task</VALUE-REF>
                                                </ECUC-REFERENCE-VALUE>
                                            </REFERENCE-VALUES>
                                        </ECUC-CONTAINER-VALUE>
                                    </SUB-CONTAINERS>
                                </ECUC-CONTAINER-VALUE>
                            </SUB-CONTAINERS>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                            <SHORT-NAME>Rte_Al_TE2_Default_BSW_Async_Task_0_5ms</SHORT-NAME>
                            <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Os/OsAlarm</DEFINITION-REF>
                            <REFERENCE-VALUES>
                                <ECUC-REFERENCE-VALUE>
                                    <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Os/OsAlarm/OsAlarmCounterRef</DEFINITION-REF>
                                    <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/DaVinci_Project/Os/SystemTimer</VALUE-REF>
                                </ECUC-REFERENCE-VALUE>
                            </REFERENCE-VALUES>
                            <SUB-CONTAINERS>
                                <ECUC-CONTAINER-VALUE>
                                    <SHORT-NAME>OsAlarmAction</SHORT-NAME>
                                    <DEFINITION-REF DEST="ECUC-CHOICE-CONTAINER-DEF">/MICROSAR/Os/OsAlarm/OsAlarmAction</DEFINITION-REF>
                                    <SUB-CONTAINERS>
                                        <ECUC-CONTAINER-VALUE>
                                            <SHORT-NAME>OsAlarmSetEvent</SHORT-NAME>
                                            <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Os/OsAlarm/OsAlarmAction/OsAlarmSetEvent</DEFINITION-REF>
                                            <REFERENCE-VALUES>
                                                <ECUC-REFERENCE-VALUE>
                                                    <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Os/OsAlarm/OsAlarmAction/OsAlarmSetEvent/OsAlarmSetEventRef</DEFINITION-REF>
                                                    <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/DaVinci_Project/Os/Rte_Ev_Cyclic2_Default_BSW_Async_Task_0_5ms</VALUE-REF>
                                                </ECUC-REFERENCE-VALUE>
                                                <ECUC-REFERENCE-VALUE>
                                                    <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Os/OsAlarm/OsAlarmAction/OsAlarmSetEvent/OsAlarmSetEventTaskRef</DEFINITION-REF>
                                                    <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/DaVinci_Project/Os/Default_BSW_Async_Task</VALUE-REF>
                                                </ECUC-REFERENCE-VALUE>
                                            </REFERENCE-VALUES>
                                        </ECUC-CONTAINER-VALUE>
                                    </SUB-CONTAINERS>
                                </ECUC-CONTAINER-VALUE>
                            </SUB-CONTAINERS>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                            <SHORT-NAME>Rte_Al_TE_LinIf_LinIf_MainFunction</SHORT-NAME>
                            <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Os/OsAlarm</DEFINITION-REF>
                            <REFERENCE-VALUES>
                                <ECUC-REFERENCE-VALUE>
                                    <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Os/OsAlarm/OsAlarmCounterRef</DEFINITION-REF>
                                    <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/DaVinci_Project/Os/SystemTimer</VALUE-REF>
                                </ECUC-REFERENCE-VALUE>
                            </REFERENCE-VALUES>
                            <SUB-CONTAINERS>
                                <ECUC-CONTAINER-VALUE>
                                    <SHORT-NAME>OsAlarmAction</SHORT-NAME>
                                    <DEFINITION-REF DEST="ECUC-CHOICE-CONTAINER-DEF">/MICROSAR/Os/OsAlarm/OsAlarmAction</DEFINITION-REF>
                                    <SUB-CONTAINERS>
                                        <ECUC-CONTAINER-VALUE>
                                            <SHORT-NAME>OsAlarmSetEvent</SHORT-NAME>
                                            <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Os/OsAlarm/OsAlarmAction/OsAlarmSetEvent</DEFINITION-REF>
                                            <REFERENCE-VALUES>
                                                <ECUC-REFERENCE-VALUE>
                                                    <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Os/OsAlarm/OsAlarmAction/OsAlarmSetEvent/OsAlarmSetEventRef</DEFINITION-REF>
                                                    <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/DaVinci_Project/Os/Rte_Ev_Run_LinIf_LinIf_MainFunction</VALUE-REF>
                                                </ECUC-REFERENCE-VALUE>
                                                <ECUC-REFERENCE-VALUE>
                                                    <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Os/OsAlarm/OsAlarmAction/OsAlarmSetEvent/OsAlarmSetEventTaskRef</DEFINITION-REF>
                                                    <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/DaVinci_Project/Os/Default_BSW_Async_Task</VALUE-REF>
                                                </ECUC-REFERENCE-VALUE>
                                            </REFERENCE-VALUES>
                                        </ECUC-CONTAINER-VALUE>
                                    </SUB-CONTAINERS>
                                </ECUC-CONTAINER-VALUE>
                            </SUB-CONTAINERS>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                            <SHORT-NAME>StartApplication_Appl_Init_Task</SHORT-NAME>
                            <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Os/OsTask</DEFINITION-REF>
                            <PARAMETER-VALUES>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                    <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/MICROSAR/Os/OsTask/OsTaskActivation</DEFINITION-REF>
                                    <VALUE>1</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                    <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/MICROSAR/Os/OsTask/OsTaskPriority</DEFINITION-REF>
                                    <VALUE>45</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                    <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/MICROSAR/Os/OsTask/OsTaskSchedule</DEFINITION-REF>
                                    <VALUE>NON</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                            </PARAMETER-VALUES>
                            <REFERENCE-VALUES>
                            </REFERENCE-VALUES>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                            <SHORT-NAME>StartApplication_Appl_Task</SHORT-NAME>
                            <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Os/OsTask</DEFINITION-REF>
                            <PARAMETER-VALUES>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                    <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/MICROSAR/Os/OsTask/OsTaskActivation</DEFINITION-REF>
                                    <VALUE>1</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                    <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/MICROSAR/Os/OsTask/OsTaskPriority</DEFINITION-REF>
                                    <VALUE>5</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                    <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/MICROSAR/Os/OsTask/OsTaskSchedule</DEFINITION-REF>
                                    <VALUE>NON</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                            </PARAMETER-VALUES>
                            <REFERENCE-VALUES>
                                <ECUC-REFERENCE-VALUE>
                                    <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Os/OsTask/OsTaskEventRef</DEFINITION-REF>
                                    <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/DaVinci_Project/Os/Rte_Ev_Run_StartApplication_StartApplication_Cyclic1000ms</VALUE-REF>
                                </ECUC-REFERENCE-VALUE>
                                <ECUC-REFERENCE-VALUE>
                                    <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Os/OsTask/OsTaskEventRef</DEFINITION-REF>
                                    <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/DaVinci_Project/Os/Rte_Ev_Run_StartApplication_StartApplication_Cyclic10ms</VALUE-REF>
                                </ECUC-REFERENCE-VALUE>
                                <ECUC-REFERENCE-VALUE>
                                    <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Os/OsTask/OsTaskEventRef</DEFINITION-REF>
                                    <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/DaVinci_Project/Os/Rte_Ev_Run_StartApplication_StartApplication_Cyclic1ms</VALUE-REF>
                                </ECUC-REFERENCE-VALUE>
                                <ECUC-REFERENCE-VALUE>
                                    <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Os/OsTask/OsTaskEventRef</DEFINITION-REF>
                                    <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/DaVinci_Project/Os/Rte_Ev_Run_StartApplication_StartApplication_Cyclic250ms</VALUE-REF>
                                </ECUC-REFERENCE-VALUE>
                                <ECUC-REFERENCE-VALUE>
                                    <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Os/OsTask/OsTaskEventRef</DEFINITION-REF>
                                    <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/DaVinci_Project/Os/Rte_Ev_Run_StartApplication_StartApplication_OnDataRec_RxCtrl</VALUE-REF>
                                </ECUC-REFERENCE-VALUE>
                                <ECUC-REFERENCE-VALUE>
                                    <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Os/OsTask/OsTaskEventRef</DEFINITION-REF>
                                    <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/DaVinci_Project/Os/Rte_Ev_Run_StartApplication_StartApplication_OnDataRec_RxData</VALUE-REF>
                                </ECUC-REFERENCE-VALUE>
                            </REFERENCE-VALUES>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                            <SHORT-NAME>Rte_Al_TE_StartApplication_StartApplication_Cyclic1000ms</SHORT-NAME>
                            <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Os/OsAlarm</DEFINITION-REF>
                            <REFERENCE-VALUES>
                                <ECUC-REFERENCE-VALUE>
                                    <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Os/OsAlarm/OsAlarmCounterRef</DEFINITION-REF>
                                    <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/DaVinci_Project/Os/SystemTimer</VALUE-REF>
                                </ECUC-REFERENCE-VALUE>
                            </REFERENCE-VALUES>
                            <SUB-CONTAINERS>
                                <ECUC-CONTAINER-VALUE>
                                    <SHORT-NAME>OsAlarmAction</SHORT-NAME>
                                    <DEFINITION-REF DEST="ECUC-CHOICE-CONTAINER-DEF">/MICROSAR/Os/OsAlarm/OsAlarmAction</DEFINITION-REF>
                                    <SUB-CONTAINERS>
                                        <ECUC-CONTAINER-VALUE>
                                            <SHORT-NAME>OsAlarmSetEvent</SHORT-NAME>
                                            <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Os/OsAlarm/OsAlarmAction/OsAlarmSetEvent</DEFINITION-REF>
                                            <REFERENCE-VALUES>
                                                <ECUC-REFERENCE-VALUE>
                                                    <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Os/OsAlarm/OsAlarmAction/OsAlarmSetEvent/OsAlarmSetEventRef</DEFINITION-REF>
                                                    <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/DaVinci_Project/Os/Rte_Ev_Run_StartApplication_StartApplication_Cyclic1000ms</VALUE-REF>
                                                </ECUC-REFERENCE-VALUE>
                                                <ECUC-REFERENCE-VALUE>
                                                    <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Os/OsAlarm/OsAlarmAction/OsAlarmSetEvent/OsAlarmSetEventTaskRef</DEFINITION-REF>
                                                    <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/DaVinci_Project/Os/StartApplication_Appl_Task</VALUE-REF>
                                                </ECUC-REFERENCE-VALUE>
                                            </REFERENCE-VALUES>
                                        </ECUC-CONTAINER-VALUE>
                                    </SUB-CONTAINERS>
                                </ECUC-CONTAINER-VALUE>
                            </SUB-CONTAINERS>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                            <SHORT-NAME>Rte_Al_TE_StartApplication_StartApplication_Cyclic10ms</SHORT-NAME>
                            <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Os/OsAlarm</DEFINITION-REF>
                            <REFERENCE-VALUES>
                                <ECUC-REFERENCE-VALUE>
                                    <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Os/OsAlarm/OsAlarmCounterRef</DEFINITION-REF>
                                    <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/DaVinci_Project/Os/SystemTimer</VALUE-REF>
                                </ECUC-REFERENCE-VALUE>
                            </REFERENCE-VALUES>
                            <SUB-CONTAINERS>
                                <ECUC-CONTAINER-VALUE>
                                    <SHORT-NAME>OsAlarmAction</SHORT-NAME>
                                    <DEFINITION-REF DEST="ECUC-CHOICE-CONTAINER-DEF">/MICROSAR/Os/OsAlarm/OsAlarmAction</DEFINITION-REF>
                                    <SUB-CONTAINERS>
                                        <ECUC-CONTAINER-VALUE>
                                            <SHORT-NAME>OsAlarmSetEvent</SHORT-NAME>
                                            <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Os/OsAlarm/OsAlarmAction/OsAlarmSetEvent</DEFINITION-REF>
                                            <REFERENCE-VALUES>
                                                <ECUC-REFERENCE-VALUE>
                                                    <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Os/OsAlarm/OsAlarmAction/OsAlarmSetEvent/OsAlarmSetEventRef</DEFINITION-REF>
                                                    <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/DaVinci_Project/Os/Rte_Ev_Run_StartApplication_StartApplication_Cyclic10ms</VALUE-REF>
                                                </ECUC-REFERENCE-VALUE>
                                                <ECUC-REFERENCE-VALUE>
                                                    <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Os/OsAlarm/OsAlarmAction/OsAlarmSetEvent/OsAlarmSetEventTaskRef</DEFINITION-REF>
                                                    <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/DaVinci_Project/Os/StartApplication_Appl_Task</VALUE-REF>
                                                </ECUC-REFERENCE-VALUE>
                                            </REFERENCE-VALUES>
                                        </ECUC-CONTAINER-VALUE>
                                    </SUB-CONTAINERS>
                                </ECUC-CONTAINER-VALUE>
                            </SUB-CONTAINERS>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                            <SHORT-NAME>Rte_Al_TE_StartApplication_StartApplication_Cyclic1ms</SHORT-NAME>
                            <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Os/OsAlarm</DEFINITION-REF>
                            <REFERENCE-VALUES>
                                <ECUC-REFERENCE-VALUE>
                                    <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Os/OsAlarm/OsAlarmCounterRef</DEFINITION-REF>
                                    <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/DaVinci_Project/Os/SystemTimer</VALUE-REF>
                                </ECUC-REFERENCE-VALUE>
                            </REFERENCE-VALUES>
                            <SUB-CONTAINERS>
                                <ECUC-CONTAINER-VALUE>
                                    <SHORT-NAME>OsAlarmAction</SHORT-NAME>
                                    <DEFINITION-REF DEST="ECUC-CHOICE-CONTAINER-DEF">/MICROSAR/Os/OsAlarm/OsAlarmAction</DEFINITION-REF>
                                    <SUB-CONTAINERS>
                                        <ECUC-CONTAINER-VALUE>
                                            <SHORT-NAME>OsAlarmSetEvent</SHORT-NAME>
                                            <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Os/OsAlarm/OsAlarmAction/OsAlarmSetEvent</DEFINITION-REF>
                                            <REFERENCE-VALUES>
                                                <ECUC-REFERENCE-VALUE>
                                                    <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Os/OsAlarm/OsAlarmAction/OsAlarmSetEvent/OsAlarmSetEventRef</DEFINITION-REF>
                                                    <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/DaVinci_Project/Os/Rte_Ev_Run_StartApplication_StartApplication_Cyclic1ms</VALUE-REF>
                                                </ECUC-REFERENCE-VALUE>
                                                <ECUC-REFERENCE-VALUE>
                                                    <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Os/OsAlarm/OsAlarmAction/OsAlarmSetEvent/OsAlarmSetEventTaskRef</DEFINITION-REF>
                                                    <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/DaVinci_Project/Os/StartApplication_Appl_Task</VALUE-REF>
                                                </ECUC-REFERENCE-VALUE>
                                            </REFERENCE-VALUES>
                                        </ECUC-CONTAINER-VALUE>
                                    </SUB-CONTAINERS>
                                </ECUC-CONTAINER-VALUE>
                            </SUB-CONTAINERS>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                            <SHORT-NAME>Rte_Al_TE_StartApplication_StartApplication_Cyclic250ms</SHORT-NAME>
                            <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Os/OsAlarm</DEFINITION-REF>
                            <REFERENCE-VALUES>
                                <ECUC-REFERENCE-VALUE>
                                    <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Os/OsAlarm/OsAlarmCounterRef</DEFINITION-REF>
                                    <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/DaVinci_Project/Os/SystemTimer</VALUE-REF>
                                </ECUC-REFERENCE-VALUE>
                            </REFERENCE-VALUES>
                            <SUB-CONTAINERS>
                                <ECUC-CONTAINER-VALUE>
                                    <SHORT-NAME>OsAlarmAction</SHORT-NAME>
                                    <DEFINITION-REF DEST="ECUC-CHOICE-CONTAINER-DEF">/MICROSAR/Os/OsAlarm/OsAlarmAction</DEFINITION-REF>
                                    <SUB-CONTAINERS>
                                        <ECUC-CONTAINER-VALUE>
                                            <SHORT-NAME>OsAlarmSetEvent</SHORT-NAME>
                                            <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Os/OsAlarm/OsAlarmAction/OsAlarmSetEvent</DEFINITION-REF>
                                            <REFERENCE-VALUES>
                                                <ECUC-REFERENCE-VALUE>
                                                    <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Os/OsAlarm/OsAlarmAction/OsAlarmSetEvent/OsAlarmSetEventRef</DEFINITION-REF>
                                                    <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/DaVinci_Project/Os/Rte_Ev_Run_StartApplication_StartApplication_Cyclic250ms</VALUE-REF>
                                                </ECUC-REFERENCE-VALUE>
                                                <ECUC-REFERENCE-VALUE>
                                                    <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Os/OsAlarm/OsAlarmAction/OsAlarmSetEvent/OsAlarmSetEventTaskRef</DEFINITION-REF>
                                                    <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/DaVinci_Project/Os/StartApplication_Appl_Task</VALUE-REF>
                                                </ECUC-REFERENCE-VALUE>
                                            </REFERENCE-VALUES>
                                        </ECUC-CONTAINER-VALUE>
                                    </SUB-CONTAINERS>
                                </ECUC-CONTAINER-VALUE>
                            </SUB-CONTAINERS>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                            <SHORT-NAME>Rte_Ev_Cyclic2_Default_BSW_Async_Task_0_10ms</SHORT-NAME>
                            <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Os/OsEvent</DEFINITION-REF>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                            <SHORT-NAME>Rte_Ev_Cyclic2_Default_BSW_Async_Task_0_20ms</SHORT-NAME>
                            <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Os/OsEvent</DEFINITION-REF>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                            <SHORT-NAME>Rte_Ev_Cyclic2_Default_BSW_Async_Task_0_5ms</SHORT-NAME>
                            <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Os/OsEvent</DEFINITION-REF>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                            <SHORT-NAME>Rte_Ev_Run_LinIf_LinIf_MainFunction</SHORT-NAME>
                            <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Os/OsEvent</DEFINITION-REF>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                            <SHORT-NAME>Rte_Ev_Run_StartApplication_StartApplication_Cyclic1000ms</SHORT-NAME>
                            <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Os/OsEvent</DEFINITION-REF>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                            <SHORT-NAME>Rte_Ev_Run_StartApplication_StartApplication_Cyclic10ms</SHORT-NAME>
                            <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Os/OsEvent</DEFINITION-REF>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                            <SHORT-NAME>Rte_Ev_Run_StartApplication_StartApplication_Cyclic1ms</SHORT-NAME>
                            <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Os/OsEvent</DEFINITION-REF>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                            <SHORT-NAME>Rte_Ev_Run_StartApplication_StartApplication_Cyclic250ms</SHORT-NAME>
                            <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Os/OsEvent</DEFINITION-REF>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                            <SHORT-NAME>Rte_Ev_Run_StartApplication_StartApplication_OnDataRec_RxCtrl</SHORT-NAME>
                            <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Os/OsEvent</DEFINITION-REF>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                            <SHORT-NAME>Rte_Ev_Run_StartApplication_StartApplication_OnDataRec_RxData</SHORT-NAME>
                            <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Os/OsEvent</DEFINITION-REF>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                            <SHORT-NAME>OsApplication_NonTrusted_Core0</SHORT-NAME>
                            <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/MICROSAR/Os/OsApplication</DEFINITION-REF>
                            <PARAMETER-VALUES>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                    <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/MICROSAR/Os/OsApplication/OsTrusted</DEFINITION-REF>
                                    <VALUE>true</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                            </PARAMETER-VALUES>
                            <REFERENCE-VALUES>
                                <ECUC-REFERENCE-VALUE>
                                    <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Os/OsApplication/OsAppTaskRef</DEFINITION-REF>
                                    <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/DaVinci_Project/Os/Default_BSW_Async_Task</VALUE-REF>
                                </ECUC-REFERENCE-VALUE>
                                <ECUC-REFERENCE-VALUE>
                                    <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Os/OsApplication/OsAppAlarmRef</DEFINITION-REF>
                                    <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/DaVinci_Project/Os/Rte_Al_TE2_Default_BSW_Async_Task_0_10ms</VALUE-REF>
                                </ECUC-REFERENCE-VALUE>
                                <ECUC-REFERENCE-VALUE>
                                    <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Os/OsApplication/OsAppAlarmRef</DEFINITION-REF>
                                    <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/DaVinci_Project/Os/Rte_Al_TE2_Default_BSW_Async_Task_0_20ms</VALUE-REF>
                                </ECUC-REFERENCE-VALUE>
                                <ECUC-REFERENCE-VALUE>
                                    <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Os/OsApplication/OsAppAlarmRef</DEFINITION-REF>
                                    <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/DaVinci_Project/Os/Rte_Al_TE2_Default_BSW_Async_Task_0_5ms</VALUE-REF>
                                </ECUC-REFERENCE-VALUE>
                                <ECUC-REFERENCE-VALUE>
                                    <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Os/OsApplication/OsAppAlarmRef</DEFINITION-REF>
                                    <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/DaVinci_Project/Os/Rte_Al_TE_LinIf_LinIf_MainFunction</VALUE-REF>
                                </ECUC-REFERENCE-VALUE>
                                <ECUC-REFERENCE-VALUE>
                                    <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Os/OsApplication/OsAppTaskRef</DEFINITION-REF>
                                    <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/DaVinci_Project/Os/StartApplication_Appl_Init_Task</VALUE-REF>
                                </ECUC-REFERENCE-VALUE>
                                <ECUC-REFERENCE-VALUE>
                                    <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Os/OsApplication/OsAppTaskRef</DEFINITION-REF>
                                    <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/DaVinci_Project/Os/StartApplication_Appl_Task</VALUE-REF>
                                </ECUC-REFERENCE-VALUE>
                                <ECUC-REFERENCE-VALUE>
                                    <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Os/OsApplication/OsAppAlarmRef</DEFINITION-REF>
                                    <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/DaVinci_Project/Os/Rte_Al_TE_StartApplication_StartApplication_Cyclic1000ms</VALUE-REF>
                                </ECUC-REFERENCE-VALUE>
                                <ECUC-REFERENCE-VALUE>
                                    <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Os/OsApplication/OsAppAlarmRef</DEFINITION-REF>
                                    <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/DaVinci_Project/Os/Rte_Al_TE_StartApplication_StartApplication_Cyclic10ms</VALUE-REF>
                                </ECUC-REFERENCE-VALUE>
                                <ECUC-REFERENCE-VALUE>
                                    <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Os/OsApplication/OsAppAlarmRef</DEFINITION-REF>
                                    <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/DaVinci_Project/Os/Rte_Al_TE_StartApplication_StartApplication_Cyclic1ms</VALUE-REF>
                                </ECUC-REFERENCE-VALUE>
                                <ECUC-REFERENCE-VALUE>
                                    <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/MICROSAR/Os/OsApplication/OsAppAlarmRef</DEFINITION-REF>
                                    <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/DaVinci_Project/Os/Rte_Al_TE_StartApplication_StartApplication_Cyclic250ms</VALUE-REF>
                                </ECUC-REFERENCE-VALUE>
                            </REFERENCE-VALUES>
                        </ECUC-CONTAINER-VALUE>
                    </CONTAINERS>
                </ECUC-MODULE-CONFIGURATION-VALUES>
                <ECUC-MODULE-CONFIGURATION-VALUES>
                    <SHORT-NAME>EcuC</SHORT-NAME>
                    <DEFINITION-REF DEST="ECUC-MODULE-DEF">/MICROSAR/EcuC</DEFINITION-REF>
                </ECUC-MODULE-CONFIGURATION-VALUES>
                <ECUC-VALUE-COLLECTION>
                    <SHORT-NAME>DaVinci_Project</SHORT-NAME>
                    <ECUC-VALUES>
                        <ECUC-MODULE-CONFIGURATION-VALUES-REF-CONDITIONAL>
                            <ECUC-MODULE-CONFIGURATION-VALUES-REF DEST="ECUC-MODULE-CONFIGURATION-VALUES">/DaVinci_Project/Com</ECUC-MODULE-CONFIGURATION-VALUES-REF>
                        </ECUC-MODULE-CONFIGURATION-VALUES-REF-CONDITIONAL>
                        <ECUC-MODULE-CONFIGURATION-VALUES-REF-CONDITIONAL>
                            <ECUC-MODULE-CONFIGURATION-VALUES-REF DEST="ECUC-MODULE-CONFIGURATION-VALUES">/DaVinci_Project/Rte</ECUC-MODULE-CONFIGURATION-VALUES-REF>
                        </ECUC-MODULE-CONFIGURATION-VALUES-REF-CONDITIONAL>
                        <ECUC-MODULE-CONFIGURATION-VALUES-REF-CONDITIONAL>
                            <ECUC-MODULE-CONFIGURATION-VALUES-REF DEST="ECUC-MODULE-CONFIGURATION-VALUES">/DaVinci_Project/Os</ECUC-MODULE-CONFIGURATION-VALUES-REF>
                        </ECUC-MODULE-CONFIGURATION-VALUES-REF-CONDITIONAL>
                        <ECUC-MODULE-CONFIGURATION-VALUES-REF-CONDITIONAL>
                            <ECUC-MODULE-CONFIGURATION-VALUES-REF DEST="ECUC-MODULE-CONFIGURATION-VALUES">/DaVinci_Project/EcuC</ECUC-MODULE-CONFIGURATION-VALUES-REF>
                        </ECUC-MODULE-CONFIGURATION-VALUES-REF-CONDITIONAL>
                        <ECUC-MODULE-CONFIGURATION-VALUES-REF-CONDITIONAL>
                            <ECUC-MODULE-CONFIGURATION-VALUES-REF DEST="ECUC-MODULE-CONFIGURATION-VALUES">/DaVinci_Project/NvM</ECUC-MODULE-CONFIGURATION-VALUES-REF>
                        </ECUC-MODULE-CONFIGURATION-VALUES-REF-CONDITIONAL>
                    </ECUC-VALUES>
                </ECUC-VALUE-COLLECTION>
            </ELEMENTS>
        </AR-PACKAGE>
    </AR-PACKAGES>
</AUTOSAR>
