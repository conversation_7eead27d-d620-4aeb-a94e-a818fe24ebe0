
#------------------------------------------------------------------------------
# Version of generated hardware configuration
#------------------------------------------------------------------------------
BRS_GENERATED_HW_CONFIG_VERSION = 343

#------------------------------------------------------------------------------
# Clock frequency of main oscillator (MHz)
#------------------------------------------------------------------------------
MAIN_OSC_CLK = 20

#------------------------------------------------------------------------------
# BRS time base clock (MHz)
# This frequency is the CPU clock of the cores.
#------------------------------------------------------------------------------
TIMEBASE_CLOCK = 300

#------------------------------------------------------------------------------
# Peripheral clock (MHz)
# This frequency is the clock for the on-chip timer used for BRS ms flag.
#------------------------------------------------------------------------------
PERIPH_CLOCK = 100

#------------------------------------------------------------------------------
# Tested Derivative: Infineon Aurix 2G TC37x Family
#------------------------------------------------------------------------------
DERIVATIVE = TC37x

#------------------------------------------------------------------------------
# CPU Core
#------------------------------------------------------------------------------
CPU_CORE = TC162

#------------------------------------------------------------------------------
# Evaluation Board: This Eval board is used for customer hardware. All pin configuration have to be specify manually in the BrsHw_Ports.h.
#------------------------------------------------------------------------------
EVA_BOARD = CUSTOMER_HARDWARE

#------------------------------------------------------------------------------
# Derivative Group
#------------------------------------------------------------------------------
DERIVATIVE_GROUP = TC3xx

#------------------------------------------------------------------------------
# Pll Group
#------------------------------------------------------------------------------
PLL_GROUP = B

#------------------------------------------------------------------------------
# Port Group
#------------------------------------------------------------------------------
PORT_GROUP = B

#------------------------------------------------------------------------------
# Reset Group
#------------------------------------------------------------------------------
RESET_GROUP = B

#------------------------------------------------------------------------------
# Watchdog Group
#------------------------------------------------------------------------------
WATCHDOG_GROUP = B

#------------------------------------------------------------------------------
# Support of Hardware Security Module (HSM)
#------------------------------------------------------------------------------
BRS_ENABLE_HSM_SUPPORT = 0


