<?xml version="1.0" encoding="UTF-8"?>

<!--
/*******************************************************************************
**                                                                            **
** Copyright (C) Infineon Technologies (2020)                                 **
**                                                                            **
** All rights reserved.                                                       **
**                                                                            **
** This document contains proprietary information belonging to Infineon       **
** Technologies. Passing on and copying of this document, and communication   **
** of its contents is not permitted without prior written authorization.      **
**                                                                            **
********************************************************************************
**                                                                            **
**  FILENAME  : Port_Bswmd.arxml                                              **
**                                                                            **
**  VERSION   : 1.40.1_19.0.0                                                 **
**                                                                            **
**  DATE, TIME: 2025-08-05, 10:50:54             !!!IGNORE-LINE!!!            **
**                                                                            **
**  GENERATOR : Build b191017-0938              !!!IGNORE-LINE!!!             **
**                                                                            **
**  VARIANT   : Variant PB                                                    **
**                                                                            **
**  PLATFORM  : Infineon AURIX2G                                              **
**                                                                            **
**  AUTHOR    : DL-AUTOSAR-Engineering                                        **
**                                                                            **
**  VENDOR    : Infineon Technologies                                         **
**                                                                            **
**  DESCRIPTION  : Basic Software Module Description for PORT                 **
**                                                                            **
**  SPECIFICATION(S) : Specification of Port Driver, AUTOSAR Release 4.2.2    **
**                                                                            **
**  MAY BE CHANGED BY USER : no                                               **
**                                                                            **
*******************************************************************************/
-->
<AUTOSAR xmlns="http://autosar.org/schema/r4.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://autosar.org/schema/r4.0 AUTOSAR_4-2-2.xsd">
  <AR-PACKAGES>
    <AR-PACKAGE>
      <SHORT-NAME>AUTOSAR_Port</SHORT-NAME>
      <AR-PACKAGES>
        <AR-PACKAGE>
          <SHORT-NAME>BswModuleDescriptions</SHORT-NAME>
          <CATEGORY>STANDARD</CATEGORY>
          <ELEMENTS>
            <BSW-MODULE-DESCRIPTION>
              <SHORT-NAME>Port</SHORT-NAME>
              <LONG-NAME>
                <L-4 L="EN">PORT Driver</L-4>
              </LONG-NAME>
              <CATEGORY>BSW_MODULE</CATEGORY>
              <MODULE-ID>124</MODULE-ID>
              <PROVIDED-ENTRYS>
                <BSW-MODULE-ENTRY-REF-CONDITIONAL>
                  <BSW-MODULE-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Port/BswModuleEntrys/Port_Init</BSW-MODULE-ENTRY-REF>
                </BSW-MODULE-ENTRY-REF-CONDITIONAL>
                <BSW-MODULE-ENTRY-REF-CONDITIONAL>
                  <BSW-MODULE-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Port/BswModuleEntrys/Port_SetPinDirection</BSW-MODULE-ENTRY-REF>
                </BSW-MODULE-ENTRY-REF-CONDITIONAL>
                <BSW-MODULE-ENTRY-REF-CONDITIONAL>
                  <BSW-MODULE-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Port/BswModuleEntrys/Port_RefreshPortDirection</BSW-MODULE-ENTRY-REF>
                </BSW-MODULE-ENTRY-REF-CONDITIONAL>
                <BSW-MODULE-ENTRY-REF-CONDITIONAL>
                  <BSW-MODULE-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Port/BswModuleEntrys/Port_SetPinMode</BSW-MODULE-ENTRY-REF>
                </BSW-MODULE-ENTRY-REF-CONDITIONAL>
              </PROVIDED-ENTRYS>
              <BSW-MODULE-DEPENDENCYS>
                <BSW-MODULE-DEPENDENCY>
                  <SHORT-NAME>DetDependency</SHORT-NAME>
                  <TARGET-MODULE-ID>15</TARGET-MODULE-ID>
                  <REQUIRED-ENTRYS>
                    <BSW-MODULE-ENTRY-REF-CONDITIONAL>
                      <BSW-MODULE-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Det/BswModuleEntrys/Det_ReportError</BSW-MODULE-ENTRY-REF>
                    </BSW-MODULE-ENTRY-REF-CONDITIONAL>
                  </REQUIRED-ENTRYS>
                </BSW-MODULE-DEPENDENCY>
                <BSW-MODULE-DEPENDENCY>
                  <SHORT-NAME>McalLibDependency</SHORT-NAME>
                  <TARGET-MODULE-ID>255</TARGET-MODULE-ID>
                  <REQUIRED-ENTRYS>
                    <BSW-MODULE-ENTRY-REF-CONDITIONAL>
                      <BSW-MODULE-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AURIX2G_McalLib/BswModuleEntrys/Mcal_WritePeripEndInitProtReg</BSW-MODULE-ENTRY-REF>
                    </BSW-MODULE-ENTRY-REF-CONDITIONAL>
                    <BSW-MODULE-ENTRY-REF-CONDITIONAL>
                      <BSW-MODULE-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AURIX2G_McalLib/BswModuleEntrys/Mcal_WriteSafetyEndInitProtRegMask</BSW-MODULE-ENTRY-REF>
                    </BSW-MODULE-ENTRY-REF-CONDITIONAL>
                    <BSW-MODULE-ENTRY-REF-CONDITIONAL>
                      <BSW-MODULE-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AURIX2G_McalLib/BswModuleEntrys/Mcal_SetBitAtomic</BSW-MODULE-ENTRY-REF>
                    </BSW-MODULE-ENTRY-REF-CONDITIONAL>
                  </REQUIRED-ENTRYS>
                </BSW-MODULE-DEPENDENCY>
              </BSW-MODULE-DEPENDENCYS>
              <INTERNAL-BEHAVIORS>
                <BSW-INTERNAL-BEHAVIOR>
                  <SHORT-NAME>PortBehavior</SHORT-NAME>
                  <ENTITYS>
                    <BSW-CALLED-ENTITY>
                      <SHORT-NAME>Port_Init</SHORT-NAME>
                      <REENTRANCY-LEVEL>NON-REENTRANT</REENTRANCY-LEVEL>
                      <IMPLEMENTED-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Port/BswModuleEntrys/Port_Init</IMPLEMENTED-ENTRY-REF>
                    </BSW-CALLED-ENTITY>
                    <BSW-CALLED-ENTITY>
                      <SHORT-NAME>Port_SetPinDirection</SHORT-NAME>
                      <REENTRANCY-LEVEL>MULTICORE-REENTRANT</REENTRANCY-LEVEL>
                      <IMPLEMENTED-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Port/BswModuleEntrys/Port_SetPinDirection</IMPLEMENTED-ENTRY-REF>
                    </BSW-CALLED-ENTITY>
                    <BSW-CALLED-ENTITY>
                      <SHORT-NAME>Port_RefreshPortDirection</SHORT-NAME>
                      <REENTRANCY-LEVEL>NON-REENTRANT</REENTRANCY-LEVEL>
                      <IMPLEMENTED-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Port/BswModuleEntrys/Port_RefreshPortDirection</IMPLEMENTED-ENTRY-REF>
                    </BSW-CALLED-ENTITY>
                    <BSW-CALLED-ENTITY>
                      <SHORT-NAME>Port_SetPinMode</SHORT-NAME>
                      <REENTRANCY-LEVEL>MULTICORE-REENTRANT</REENTRANCY-LEVEL>
                      <IMPLEMENTED-ENTRY-REF DEST="BSW-MODULE-ENTRY">/AUTOSAR_Port/BswModuleEntrys/Port_SetPinMode</IMPLEMENTED-ENTRY-REF>
                    </BSW-CALLED-ENTITY>
                  </ENTITYS>
                </BSW-INTERNAL-BEHAVIOR>
              </INTERNAL-BEHAVIORS>
            </BSW-MODULE-DESCRIPTION>
          </ELEMENTS>
        </AR-PACKAGE>
        <AR-PACKAGE>
          <SHORT-NAME>BswModuleEntrys</SHORT-NAME>
          <CATEGORY>STANDARD</CATEGORY>
          <ELEMENTS>
            <BSW-MODULE-ENTRY>
              <SHORT-NAME>Port_Init</SHORT-NAME>
              <SERVICE-ID>0</SERVICE-ID>
              <IS-REENTRANT>false</IS-REENTRANT>
              <IS-SYNCHRONOUS>true</IS-SYNCHRONOUS>
              <CALL-TYPE>REGULAR</CALL-TYPE>
              <EXECUTION-CONTEXT>TASK</EXECUTION-CONTEXT>
              <SW-SERVICE-IMPL-POLICY>STANDARD</SW-SERVICE-IMPL-POLICY>
              <ARGUMENTS>
                <SW-SERVICE-ARG>
                  <SHORT-NAME>ConfigPtr</SHORT-NAME>
                  <CATEGORY>DATA_REFERENCE</CATEGORY>
                  <SW-DATA-DEF-PROPS>
                    <SW-DATA-DEF-PROPS-VARIANTS>
                      <SW-DATA-DEF-PROPS-CONDITIONAL>
                        <SW-POINTER-TARGET-PROPS>
                          <TARGET-CATEGORY>TYPE_REFERENCE</TARGET-CATEGORY>
                          <SW-DATA-DEF-PROPS>
                            <SW-DATA-DEF-PROPS-VARIANTS>
                              <SW-DATA-DEF-PROPS-CONDITIONAL>
                                <IMPLEMENTATION-DATA-TYPE-REF DEST = "IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Port/ImplementationDataTypes/Port_ConfigType</IMPLEMENTATION-DATA-TYPE-REF>
                              </SW-DATA-DEF-PROPS-CONDITIONAL>
                            </SW-DATA-DEF-PROPS-VARIANTS>
                          </SW-DATA-DEF-PROPS>
                        </SW-POINTER-TARGET-PROPS>
                      </SW-DATA-DEF-PROPS-CONDITIONAL>
                    </SW-DATA-DEF-PROPS-VARIANTS>
                  </SW-DATA-DEF-PROPS>
                </SW-SERVICE-ARG>
              </ARGUMENTS>
            </BSW-MODULE-ENTRY>
            <BSW-MODULE-ENTRY>
              <SHORT-NAME>Port_SetPinDirection</SHORT-NAME>
              <SERVICE-ID>1</SERVICE-ID>
              <IS-REENTRANT>true</IS-REENTRANT>
              <IS-SYNCHRONOUS>true</IS-SYNCHRONOUS>
              <CALL-TYPE>REGULAR</CALL-TYPE>
              <EXECUTION-CONTEXT>TASK</EXECUTION-CONTEXT>
              <SW-SERVICE-IMPL-POLICY>STANDARD</SW-SERVICE-IMPL-POLICY>
              <ARGUMENTS>
                <SW-SERVICE-ARG>
                  <SHORT-NAME>Pin</SHORT-NAME>
                  <CATEGORY>TYPE_REFERENCE</CATEGORY>
                  <SW-DATA-DEF-PROPS>
                    <SW-DATA-DEF-PROPS-VARIANTS>
                      <SW-DATA-DEF-PROPS-CONDITIONAL>
                        <IMPLEMENTATION-DATA-TYPE-REF DEST = "IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Port/ImplementationDataTypes/Port_PinType</IMPLEMENTATION-DATA-TYPE-REF>
                      </SW-DATA-DEF-PROPS-CONDITIONAL>
                    </SW-DATA-DEF-PROPS-VARIANTS>
                  </SW-DATA-DEF-PROPS>
                </SW-SERVICE-ARG>
                <SW-SERVICE-ARG>
                  <SHORT-NAME>Direction</SHORT-NAME>
                  <CATEGORY>TYPE_REFERENCE</CATEGORY>
                  <SW-DATA-DEF-PROPS>
                    <SW-DATA-DEF-PROPS-VARIANTS>
                      <SW-DATA-DEF-PROPS-CONDITIONAL>
                        <IMPLEMENTATION-DATA-TYPE-REF DEST = "IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Port/ImplementationDataTypes/Port_PinDirectionType</IMPLEMENTATION-DATA-TYPE-REF>
                      </SW-DATA-DEF-PROPS-CONDITIONAL>
                    </SW-DATA-DEF-PROPS-VARIANTS>
                  </SW-DATA-DEF-PROPS>
                </SW-SERVICE-ARG>
              </ARGUMENTS>
            </BSW-MODULE-ENTRY>
            <BSW-MODULE-ENTRY>
              <SHORT-NAME>Port_RefreshPortDirection</SHORT-NAME>
              <SERVICE-ID>2</SERVICE-ID>
              <IS-REENTRANT>false</IS-REENTRANT>
              <IS-SYNCHRONOUS>true</IS-SYNCHRONOUS>
              <CALL-TYPE>REGULAR</CALL-TYPE>
              <EXECUTION-CONTEXT>TASK</EXECUTION-CONTEXT>
              <SW-SERVICE-IMPL-POLICY>STANDARD</SW-SERVICE-IMPL-POLICY>
            </BSW-MODULE-ENTRY>
            <BSW-MODULE-ENTRY>
              <SHORT-NAME>Port_SetPinMode</SHORT-NAME>
              <SERVICE-ID>4</SERVICE-ID>
              <IS-REENTRANT>true</IS-REENTRANT>
              <IS-SYNCHRONOUS>true</IS-SYNCHRONOUS>
              <CALL-TYPE>REGULAR</CALL-TYPE>
              <EXECUTION-CONTEXT>TASK</EXECUTION-CONTEXT>
              <SW-SERVICE-IMPL-POLICY>STANDARD</SW-SERVICE-IMPL-POLICY>
              <ARGUMENTS>
                <SW-SERVICE-ARG>
                  <SHORT-NAME>Pin</SHORT-NAME>
                  <CATEGORY>TYPE_REFERENCE</CATEGORY>
                  <SW-DATA-DEF-PROPS>
                    <SW-DATA-DEF-PROPS-VARIANTS>
                      <SW-DATA-DEF-PROPS-CONDITIONAL>
                        <IMPLEMENTATION-DATA-TYPE-REF DEST = "IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Port/ImplementationDataTypes/Port_PinType</IMPLEMENTATION-DATA-TYPE-REF>
                      </SW-DATA-DEF-PROPS-CONDITIONAL>
                    </SW-DATA-DEF-PROPS-VARIANTS>
                  </SW-DATA-DEF-PROPS>
                </SW-SERVICE-ARG>
                <SW-SERVICE-ARG>
                  <SHORT-NAME>Mode</SHORT-NAME>
                  <CATEGORY>TYPE_REFERENCE</CATEGORY>
                  <SW-DATA-DEF-PROPS>
                    <SW-DATA-DEF-PROPS-VARIANTS>
                      <SW-DATA-DEF-PROPS-CONDITIONAL>
                        <IMPLEMENTATION-DATA-TYPE-REF DEST = "IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Port/ImplementationDataTypes/Port_PinModeType</IMPLEMENTATION-DATA-TYPE-REF>
                      </SW-DATA-DEF-PROPS-CONDITIONAL>
                    </SW-DATA-DEF-PROPS-VARIANTS>
                  </SW-DATA-DEF-PROPS>
                </SW-SERVICE-ARG>
              </ARGUMENTS>
            </BSW-MODULE-ENTRY>
          </ELEMENTS>
        </AR-PACKAGE>
        <AR-PACKAGE>
          <SHORT-NAME>Implementations</SHORT-NAME>
          <ELEMENTS>
            <BSW-IMPLEMENTATION>
              <SHORT-NAME>Port</SHORT-NAME>
              <CODE-DESCRIPTORS>
                <CODE>
                  <SHORT-NAME>Files</SHORT-NAME>
                  <ARTIFACT-DESCRIPTORS>
                    <AUTOSAR-ENGINEERING-OBJECT>
                      <SHORT-LABEL>ssc::src::Port.c</SHORT-LABEL>
                      <CATEGORY>SWSRC</CATEGORY>
                    </AUTOSAR-ENGINEERING-OBJECT>
                    <AUTOSAR-ENGINEERING-OBJECT>
                      <SHORT-LABEL>ssc::inc::Port.h</SHORT-LABEL>
                      <CATEGORY>SWHDR</CATEGORY>
                    </AUTOSAR-ENGINEERING-OBJECT>
                  </ARTIFACT-DESCRIPTORS>
                </CODE>
              </CODE-DESCRIPTORS>
              <GENERATED-ARTIFACTS>
                <DEPENDENCY-ON-ARTIFACT>
                  <SHORT-NAME>Port_PBCfg_c</SHORT-NAME>
                  <ARTIFACT-DESCRIPTOR>
                    <SHORT-LABEL>generate::template::src::Port_PBCfg.c</SHORT-LABEL>
                    <CATEGORY>SWTEMPLATE</CATEGORY>
                  </ARTIFACT-DESCRIPTOR>
                  <USAGES>
                    <USAGE>EXECUTE</USAGE>
                  </USAGES>
                </DEPENDENCY-ON-ARTIFACT>
                <DEPENDENCY-ON-ARTIFACT>
                  <SHORT-NAME>Port_PBCfg_h</SHORT-NAME>
                  <ARTIFACT-DESCRIPTOR>
                    <SHORT-LABEL>generate::template::inc::Port_PBcfg.h</SHORT-LABEL>
                    <CATEGORY>SWTEMPLATE</CATEGORY>
                  </ARTIFACT-DESCRIPTOR>
                  <USAGES>
                    <USAGE>EXECUTE</USAGE>
                  </USAGES>
                </DEPENDENCY-ON-ARTIFACT>
                <DEPENDENCY-ON-ARTIFACT>
                  <SHORT-NAME>Port_xdm</SHORT-NAME>
                  <ARTIFACT-DESCRIPTOR>
                    <SHORT-LABEL>config::Port.xdm</SHORT-LABEL>
                    <CATEGORY>SWTOOL</CATEGORY>
                  </ARTIFACT-DESCRIPTOR>
                  <USAGES>
                    <USAGE>CODEGENERATION</USAGE>
                  </USAGES>
                </DEPENDENCY-ON-ARTIFACT>
                <DEPENDENCY-ON-ARTIFACT>
                  <SHORT-NAME>Port_Cfg_h</SHORT-NAME>
                  <ARTIFACT-DESCRIPTOR>
                    <SHORT-LABEL>generate::template::inc::Port_Cfg.h</SHORT-LABEL>
                    <CATEGORY>SWTEMPLATE</CATEGORY>
                  </ARTIFACT-DESCRIPTOR>
                  <USAGES>
                    <USAGE>COMPILE</USAGE>
                  </USAGES>
                </DEPENDENCY-ON-ARTIFACT>
                <DEPENDENCY-ON-ARTIFACT>
                  
                  <SHORT-NAME>Port_bmd</SHORT-NAME>
                  <ARTIFACT-DESCRIPTOR>
                    <SHORT-LABEL>autosar::TC37x::TC377x_ED_EX::Port.bmd</SHORT-LABEL>
                    <CATEGORY>SWTOOL</CATEGORY>
                  </ARTIFACT-DESCRIPTOR>
                  <USAGES>
                    <USAGE>CODEGENERATION</USAGE>
                  </USAGES>
                </DEPENDENCY-ON-ARTIFACT>
              </GENERATED-ARTIFACTS>
              <PROGRAMMING-LANGUAGE>C</PROGRAMMING-LANGUAGE>
              <RESOURCE-CONSUMPTION>
                <SHORT-NAME>ResourceConsumption</SHORT-NAME>
                <MEMORY-SECTIONS>
                  <MEMORY-SECTION>
                    <SHORT-NAME>VAR_CLEARED_ASIL_B_GLOBAL_8</SHORT-NAME>
                    <ALIGNMENT>8</ALIGNMENT>
                    <SW-ADDRMETHOD-REF DEST="SW-ADDR-METHOD">/AUTOSAR_MemMap/SwAddrMethods/VAR_CLEARED</SW-ADDRMETHOD-REF>
                  </MEMORY-SECTION>
                  <MEMORY-SECTION>
                    <SHORT-NAME>VAR_CLEARED_ASIL_B_GLOBAL_32</SHORT-NAME>
                    <ALIGNMENT>32</ALIGNMENT>
                    <SW-ADDRMETHOD-REF DEST="SW-ADDR-METHOD">/AUTOSAR_MemMap/SwAddrMethods/VAR_CLEARED</SW-ADDRMETHOD-REF>
                  </MEMORY-SECTION>
                  <MEMORY-SECTION>
                    <SHORT-NAME>CONST_ASIL_B_GLOBAL_16</SHORT-NAME>
                    <ALIGNMENT>16</ALIGNMENT>
                    <SW-ADDRMETHOD-REF DEST="SW-ADDR-METHOD">/AUTOSAR_MemMap/SwAddrMethods/CONST</SW-ADDRMETHOD-REF>
                  </MEMORY-SECTION>
                  <MEMORY-SECTION>
                    <SHORT-NAME>CONFIG_DATA_ASIL_B_GLOBAL_UNSPECIFIED</SHORT-NAME>
                    <ALIGNMENT>UNSPECIFIED</ALIGNMENT>
                    <SW-ADDRMETHOD-REF DEST="SW-ADDR-METHOD">/AUTOSAR_MemMap/SwAddrMethods/CONFIG_DATA</SW-ADDRMETHOD-REF>
                  </MEMORY-SECTION>
                  <MEMORY-SECTION>
                    <SHORT-NAME>CODE_ASIL_B_GLOBAL</SHORT-NAME>
                    <ALIGNMENT>UNSPECIFIED</ALIGNMENT>
                    <SW-ADDRMETHOD-REF DEST="SW-ADDR-METHOD">/AUTOSAR_MemMap/SwAddrMethods/CODE</SW-ADDRMETHOD-REF>
                  </MEMORY-SECTION>
                </MEMORY-SECTIONS>
                <SECTION-NAME-PREFIXS>
                  <SECTION-NAME-PREFIX>
                    <SHORT-NAME>PORT</SHORT-NAME>
                    <SYMBOL>PORT</SYMBOL>
                  </SECTION-NAME-PREFIX>
                </SECTION-NAME-PREFIXS>
              </RESOURCE-CONSUMPTION>
              <SW-VERSION>10.40.1</SW-VERSION>
              <VENDOR-ID>17</VENDOR-ID>
              <AR-RELEASE-VERSION>4.2.2</AR-RELEASE-VERSION>
              <BEHAVIOR-REF DEST="BSW-INTERNAL-BEHAVIOR">/AUTOSAR_Port/BswModuleDescriptions/Port/PortBehavior</BEHAVIOR-REF>
              <VENDOR-SPECIFIC-MODULE-DEF-REFS>
                <VENDOR-SPECIFIC-MODULE-DEF-REF DEST="ECUC-MODULE-DEF">/AURIX2G/EcucDefs/Port</VENDOR-SPECIFIC-MODULE-DEF-REF>
              </VENDOR-SPECIFIC-MODULE-DEF-REFS>
            </BSW-IMPLEMENTATION>
          </ELEMENTS>
        </AR-PACKAGE>
        <AR-PACKAGE>
          <SHORT-NAME>ImplementationDataTypes</SHORT-NAME>
          <CATEGORY>STANDARD</CATEGORY>
          <ELEMENTS>
            <IMPLEMENTATION-DATA-TYPE>
              <SHORT-NAME>Port_PinDirectionType</SHORT-NAME>
              <CATEGORY>TYPE_REFERENCE</CATEGORY>
              <SW-DATA-DEF-PROPS>
                <SW-DATA-DEF-PROPS-VARIANTS>
                  <SW-DATA-DEF-PROPS-CONDITIONAL>
                    <COMPU-METHOD-REF DEST="COMPU-METHOD">/AUTOSAR_Port/CompuMethods/Port_PinDirectionType</COMPU-METHOD-REF>
                    <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/sint32</IMPLEMENTATION-DATA-TYPE-REF>
                    <SW-IMPL-POLICY>STANDARD</SW-IMPL-POLICY>
                    <ADDITIONAL-NATIVE-TYPE-QUALIFIER>enum</ADDITIONAL-NATIVE-TYPE-QUALIFIER>
                  </SW-DATA-DEF-PROPS-CONDITIONAL>
                </SW-DATA-DEF-PROPS-VARIANTS>
              </SW-DATA-DEF-PROPS>
              <TYPE-EMITTER>BSW</TYPE-EMITTER>
            </IMPLEMENTATION-DATA-TYPE>
            <IMPLEMENTATION-DATA-TYPE>
              <SHORT-NAME>Port_PinModeType</SHORT-NAME>
              <CATEGORY>TYPE_REFERENCE</CATEGORY>
              <SW-DATA-DEF-PROPS>
                <SW-DATA-DEF-PROPS-VARIANTS>
                  <SW-DATA-DEF-PROPS-CONDITIONAL>
                    <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</IMPLEMENTATION-DATA-TYPE-REF>
                    <SW-IMPL-POLICY>STANDARD</SW-IMPL-POLICY>
                  </SW-DATA-DEF-PROPS-CONDITIONAL>
                </SW-DATA-DEF-PROPS-VARIANTS>
              </SW-DATA-DEF-PROPS>
              <TYPE-EMITTER>BSW</TYPE-EMITTER>
            </IMPLEMENTATION-DATA-TYPE>
            <IMPLEMENTATION-DATA-TYPE>
              <SHORT-NAME>Port_PinType</SHORT-NAME>
              <CATEGORY>TYPE_REFERENCE</CATEGORY>
              <SW-DATA-DEF-PROPS>
                <SW-DATA-DEF-PROPS-VARIANTS>
                  <SW-DATA-DEF-PROPS-CONDITIONAL>
                    <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint16</IMPLEMENTATION-DATA-TYPE-REF>
                    <SW-IMPL-POLICY>STANDARD</SW-IMPL-POLICY>
                  </SW-DATA-DEF-PROPS-CONDITIONAL>
                </SW-DATA-DEF-PROPS-VARIANTS>
              </SW-DATA-DEF-PROPS>
              <TYPE-EMITTER>BSW</TYPE-EMITTER>
            </IMPLEMENTATION-DATA-TYPE>
            <IMPLEMENTATION-DATA-TYPE>
              <SHORT-NAME>Port_ConfigType</SHORT-NAME>
              <CATEGORY>STRUCTURE</CATEGORY>
              <SUB-ELEMENTS>
                <IMPLEMENTATION-DATA-TYPE-ELEMENT>
                  <SHORT-NAME>PortConfigSetPtr</SHORT-NAME>
                  <CATEGORY>DATA_REFERENCE</CATEGORY>
                  <SW-DATA-DEF-PROPS>
                    <SW-DATA-DEF-PROPS-VARIANTS>
                      <SW-DATA-DEF-PROPS-CONDITIONAL>
                        <SW-POINTER-TARGET-PROPS>
                          <TARGET-CATEGORY>TYPE_REFERENCE</TARGET-CATEGORY>
                          <SW-DATA-DEF-PROPS>
                            <SW-DATA-DEF-PROPS-VARIANTS>
                              <SW-DATA-DEF-PROPS-CONDITIONAL>
                                <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Port/ImplementationDataTypes/Port_n_ConfigType</IMPLEMENTATION-DATA-TYPE-REF>
                              </SW-DATA-DEF-PROPS-CONDITIONAL>
                            </SW-DATA-DEF-PROPS-VARIANTS>
                          </SW-DATA-DEF-PROPS>
                        </SW-POINTER-TARGET-PROPS>
                      </SW-DATA-DEF-PROPS-CONDITIONAL>
                    </SW-DATA-DEF-PROPS-VARIANTS>
                  </SW-DATA-DEF-PROPS>
                </IMPLEMENTATION-DATA-TYPE-ELEMENT>
                <IMPLEMENTATION-DATA-TYPE-ELEMENT>
                  <SHORT-NAME>PDiscSet</SHORT-NAME>
                  <CATEGORY>DATA_REFERENCE</CATEGORY>
                  <SW-DATA-DEF-PROPS>
                    <SW-DATA-DEF-PROPS-VARIANTS>
                      <SW-DATA-DEF-PROPS-CONDITIONAL>
                        <SW-POINTER-TARGET-PROPS>
                          <TARGET-CATEGORY>TYPE_REFERENCE</TARGET-CATEGORY>
                          <SW-DATA-DEF-PROPS>
                            <SW-DATA-DEF-PROPS-VARIANTS>
                              <SW-DATA-DEF-PROPS-CONDITIONAL>
                                <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint32</IMPLEMENTATION-DATA-TYPE-REF>
                              </SW-DATA-DEF-PROPS-CONDITIONAL>
                            </SW-DATA-DEF-PROPS-VARIANTS>
                          </SW-DATA-DEF-PROPS>
                        </SW-POINTER-TARGET-PROPS>
                      </SW-DATA-DEF-PROPS-CONDITIONAL>
                    </SW-DATA-DEF-PROPS-VARIANTS>
                  </SW-DATA-DEF-PROPS>
                </IMPLEMENTATION-DATA-TYPE-ELEMENT>
                <IMPLEMENTATION-DATA-TYPE-ELEMENT>
                  <SHORT-NAME>PortPinHwSupportedModes</SHORT-NAME>
                  <CATEGORY>DATA_REFERENCE</CATEGORY>
                  <SW-DATA-DEF-PROPS>
                    <SW-DATA-DEF-PROPS-VARIANTS>
                      <SW-DATA-DEF-PROPS-CONDITIONAL>
                        <SW-POINTER-TARGET-PROPS>
                          <TARGET-CATEGORY>TYPE_REFERENCE</TARGET-CATEGORY>
                          <SW-DATA-DEF-PROPS>
                            <SW-DATA-DEF-PROPS-VARIANTS>
                              <SW-DATA-DEF-PROPS-CONDITIONAL>
                                <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Port/ImplementationDataTypes/Port_n_ModeType</IMPLEMENTATION-DATA-TYPE-REF>
                              </SW-DATA-DEF-PROPS-CONDITIONAL>
                            </SW-DATA-DEF-PROPS-VARIANTS>
                          </SW-DATA-DEF-PROPS>
                        </SW-POINTER-TARGET-PROPS>
                      </SW-DATA-DEF-PROPS-CONDITIONAL>
                    </SW-DATA-DEF-PROPS-VARIANTS>
                  </SW-DATA-DEF-PROPS>
                </IMPLEMENTATION-DATA-TYPE-ELEMENT>
                <IMPLEMENTATION-DATA-TYPE-ELEMENT>
                  <SHORT-NAME>Port_LVDSConfigTypePtr</SHORT-NAME>
                  <CATEGORY>DATA_REFERENCE</CATEGORY>
                  <SW-DATA-DEF-PROPS>
                    <SW-DATA-DEF-PROPS-VARIANTS>
                      <SW-DATA-DEF-PROPS-CONDITIONAL>
                        <SW-POINTER-TARGET-PROPS>
                          <TARGET-CATEGORY>TYPE_REFERENCE</TARGET-CATEGORY>
                          <SW-DATA-DEF-PROPS>
                            <SW-DATA-DEF-PROPS-VARIANTS>
                              <SW-DATA-DEF-PROPS-CONDITIONAL>
                                <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Port/ImplementationDataTypes/Port_n_LVDSConfigType</IMPLEMENTATION-DATA-TYPE-REF>
                              </SW-DATA-DEF-PROPS-CONDITIONAL>
                            </SW-DATA-DEF-PROPS-VARIANTS>
                          </SW-DATA-DEF-PROPS>
                        </SW-POINTER-TARGET-PROPS>
                      </SW-DATA-DEF-PROPS-CONDITIONAL>
                    </SW-DATA-DEF-PROPS-VARIANTS>
                  </SW-DATA-DEF-PROPS>
                </IMPLEMENTATION-DATA-TYPE-ELEMENT>
                <IMPLEMENTATION-DATA-TYPE-ELEMENT>
                  <SHORT-NAME>Port_PCSRConfigTypePtr</SHORT-NAME>
                  <CATEGORY>DATA_REFERENCE</CATEGORY>
                  <SW-DATA-DEF-PROPS>
                    <SW-DATA-DEF-PROPS-VARIANTS>
                      <SW-DATA-DEF-PROPS-CONDITIONAL>
                        <SW-POINTER-TARGET-PROPS>
                          <TARGET-CATEGORY>TYPE_REFERENCE</TARGET-CATEGORY>
                          <SW-DATA-DEF-PROPS>
                            <SW-DATA-DEF-PROPS-VARIANTS>
                              <SW-DATA-DEF-PROPS-CONDITIONAL>
                                <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint32</IMPLEMENTATION-DATA-TYPE-REF>
                              </SW-DATA-DEF-PROPS-CONDITIONAL>
                            </SW-DATA-DEF-PROPS-VARIANTS>
                          </SW-DATA-DEF-PROPS>
                        </SW-POINTER-TARGET-PROPS>
                      </SW-DATA-DEF-PROPS-CONDITIONAL>
                    </SW-DATA-DEF-PROPS-VARIANTS>
                  </SW-DATA-DEF-PROPS>
                </IMPLEMENTATION-DATA-TYPE-ELEMENT>
              </SUB-ELEMENTS>
              <TYPE-EMITTER>BSW</TYPE-EMITTER>
            </IMPLEMENTATION-DATA-TYPE>
            <IMPLEMENTATION-DATA-TYPE>
              <SHORT-NAME>Port_n_ConfigType</SHORT-NAME>
              <CATEGORY>STRUCTURE</CATEGORY>
              <SUB-ELEMENTS>
                <IMPLEMENTATION-DATA-TYPE-ELEMENT>
                  <SHORT-NAME>PinControl</SHORT-NAME>
                  <CATEGORY>TYPE_REFERENCE</CATEGORY>
                  <SW-DATA-DEF-PROPS>
                    <SW-DATA-DEF-PROPS-VARIANTS>
                      <SW-DATA-DEF-PROPS-CONDITIONAL>
                        <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Port/ImplementationDataTypes/Port_n_ControlType</IMPLEMENTATION-DATA-TYPE-REF>
                      </SW-DATA-DEF-PROPS-CONDITIONAL>
                    </SW-DATA-DEF-PROPS-VARIANTS>
                  </SW-DATA-DEF-PROPS>
                </IMPLEMENTATION-DATA-TYPE-ELEMENT>
                <IMPLEMENTATION-DATA-TYPE-ELEMENT>
                  <SHORT-NAME>PinLevel</SHORT-NAME>
                  <CATEGORY>TYPE_REFERENCE</CATEGORY>
                  <SW-DATA-DEF-PROPS>
                    <SW-DATA-DEF-PROPS-VARIANTS>
                      <SW-DATA-DEF-PROPS-CONDITIONAL>
                        <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Port/ImplementationDataTypes/Port_n_PinType</IMPLEMENTATION-DATA-TYPE-REF>
                      </SW-DATA-DEF-PROPS-CONDITIONAL>
                    </SW-DATA-DEF-PROPS-VARIANTS>
                  </SW-DATA-DEF-PROPS>
                </IMPLEMENTATION-DATA-TYPE-ELEMENT>
                <IMPLEMENTATION-DATA-TYPE-ELEMENT>
                  <SHORT-NAME>DriverStrength0</SHORT-NAME>
                  <CATEGORY>TYPE_REFERENCE</CATEGORY>
                  <SW-DATA-DEF-PROPS>
                    <SW-DATA-DEF-PROPS-VARIANTS>
                      <SW-DATA-DEF-PROPS-CONDITIONAL>
                        <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint32</IMPLEMENTATION-DATA-TYPE-REF>
                      </SW-DATA-DEF-PROPS-CONDITIONAL>
                    </SW-DATA-DEF-PROPS-VARIANTS>
                  </SW-DATA-DEF-PROPS>
                </IMPLEMENTATION-DATA-TYPE-ELEMENT>
                <IMPLEMENTATION-DATA-TYPE-ELEMENT>
                  <SHORT-NAME>DriverStrength1</SHORT-NAME>
                  <CATEGORY>TYPE_REFERENCE</CATEGORY>
                  <SW-DATA-DEF-PROPS>
                    <SW-DATA-DEF-PROPS-VARIANTS>
                      <SW-DATA-DEF-PROPS-CONDITIONAL>
                        <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint32</IMPLEMENTATION-DATA-TYPE-REF>
                      </SW-DATA-DEF-PROPS-CONDITIONAL>
                    </SW-DATA-DEF-PROPS-VARIANTS>
                  </SW-DATA-DEF-PROPS>
                </IMPLEMENTATION-DATA-TYPE-ELEMENT>
                <IMPLEMENTATION-DATA-TYPE-ELEMENT>
                  <SHORT-NAME>ModeChangeControl</SHORT-NAME>
                  <CATEGORY>TYPE_REFERENCE</CATEGORY>
                  <SW-DATA-DEF-PROPS>
                    <SW-DATA-DEF-PROPS-VARIANTS>
                      <SW-DATA-DEF-PROPS-CONDITIONAL>
                        <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Port/ImplementationDataTypes/Port_n_PinType</IMPLEMENTATION-DATA-TYPE-REF>
                      </SW-DATA-DEF-PROPS-CONDITIONAL>
                    </SW-DATA-DEF-PROPS-VARIANTS>
                  </SW-DATA-DEF-PROPS>
                </IMPLEMENTATION-DATA-TYPE-ELEMENT>
                <IMPLEMENTATION-DATA-TYPE-ELEMENT>
                  <SHORT-NAME>DirChangeControl</SHORT-NAME>
                  <CATEGORY>TYPE_REFERENCE</CATEGORY>
                  <SW-DATA-DEF-PROPS>
                    <SW-DATA-DEF-PROPS-VARIANTS>
                      <SW-DATA-DEF-PROPS-CONDITIONAL>
                        <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Port/ImplementationDataTypes/Port_n_PinType</IMPLEMENTATION-DATA-TYPE-REF>
                      </SW-DATA-DEF-PROPS-CONDITIONAL>
                    </SW-DATA-DEF-PROPS-VARIANTS>
                  </SW-DATA-DEF-PROPS>
                </IMPLEMENTATION-DATA-TYPE-ELEMENT>
                <IMPLEMENTATION-DATA-TYPE-ELEMENT>
                  <SHORT-NAME>PinControl2</SHORT-NAME>
                  <CATEGORY>TYPE_REFERENCE</CATEGORY>
                  <SW-DATA-DEF-PROPS>
                    <SW-DATA-DEF-PROPS-VARIANTS>
                      <SW-DATA-DEF-PROPS-CONDITIONAL>
                        <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Port/ImplementationDataTypes/Port_n_ControlType</IMPLEMENTATION-DATA-TYPE-REF>
                      </SW-DATA-DEF-PROPS-CONDITIONAL>
                    </SW-DATA-DEF-PROPS-VARIANTS>
                  </SW-DATA-DEF-PROPS>
                </IMPLEMENTATION-DATA-TYPE-ELEMENT>
                <IMPLEMENTATION-DATA-TYPE-ELEMENT>
                  <SHORT-NAME>EmergencyStopConf</SHORT-NAME>
                  <CATEGORY>TYPE_REFERENCE</CATEGORY>
                  <SW-DATA-DEF-PROPS>
                    <SW-DATA-DEF-PROPS-VARIANTS>
                      <SW-DATA-DEF-PROPS-CONDITIONAL>
                        <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Port/ImplementationDataTypes/Port_n_PinType</IMPLEMENTATION-DATA-TYPE-REF>
                      </SW-DATA-DEF-PROPS-CONDITIONAL>
                    </SW-DATA-DEF-PROPS-VARIANTS>
                  </SW-DATA-DEF-PROPS>
                </IMPLEMENTATION-DATA-TYPE-ELEMENT>
              </SUB-ELEMENTS>
              <TYPE-EMITTER>BSW</TYPE-EMITTER>
            </IMPLEMENTATION-DATA-TYPE>
            <IMPLEMENTATION-DATA-TYPE>
              <SHORT-NAME>Port_n_ControlType</SHORT-NAME>
              <CATEGORY>STRUCTURE</CATEGORY>
              <SUB-ELEMENTS>
                <IMPLEMENTATION-DATA-TYPE-ELEMENT>
                  <SHORT-NAME>PCx</SHORT-NAME>
                  <CATEGORY>TYPE_REFERENCE</CATEGORY>
                  <SW-DATA-DEF-PROPS>
                    <SW-DATA-DEF-PROPS-VARIANTS>
                      <SW-DATA-DEF-PROPS-CONDITIONAL>
                        <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</IMPLEMENTATION-DATA-TYPE-REF>
                      </SW-DATA-DEF-PROPS-CONDITIONAL>
                    </SW-DATA-DEF-PROPS-VARIANTS>
                  </SW-DATA-DEF-PROPS>
                </IMPLEMENTATION-DATA-TYPE-ELEMENT>
              </SUB-ELEMENTS>
              <TYPE-EMITTER>BSW</TYPE-EMITTER>
            </IMPLEMENTATION-DATA-TYPE>
            <IMPLEMENTATION-DATA-TYPE>
              <SHORT-NAME>Port_n_LVDSConfigType</SHORT-NAME>
              <CATEGORY>STRUCTURE</CATEGORY>
              <SUB-ELEMENTS>
                <IMPLEMENTATION-DATA-TYPE-ELEMENT>
                  <SHORT-NAME>LPCRx</SHORT-NAME>
                  <CATEGORY>TYPE_REFERENCE</CATEGORY>
                  <SW-DATA-DEF-PROPS>
                    <SW-DATA-DEF-PROPS-VARIANTS>
                      <SW-DATA-DEF-PROPS-CONDITIONAL>
                        <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint32</IMPLEMENTATION-DATA-TYPE-REF>
                      </SW-DATA-DEF-PROPS-CONDITIONAL>
                    </SW-DATA-DEF-PROPS-VARIANTS>
                  </SW-DATA-DEF-PROPS>
                </IMPLEMENTATION-DATA-TYPE-ELEMENT>
              </SUB-ELEMENTS>
              <TYPE-EMITTER>BSW</TYPE-EMITTER>
            </IMPLEMENTATION-DATA-TYPE>
            <IMPLEMENTATION-DATA-TYPE>
              <SHORT-NAME>Port_n_ModeType</SHORT-NAME>
              <CATEGORY>STRUCTURE</CATEGORY>
              <SUB-ELEMENTS>
                <IMPLEMENTATION-DATA-TYPE-ELEMENT>
                  <SHORT-NAME>U</SHORT-NAME>
                  <CATEGORY>TYPE_REFERENCE</CATEGORY>
                  <ARRAY-SIZE>16</ARRAY-SIZE>
                  <ARRAY-SIZE-SEMANTICS>FIXED-SIZE</ARRAY-SIZE-SEMANTICS>
                  <SW-DATA-DEF-PROPS>
                    <SW-DATA-DEF-PROPS-VARIANTS>
                      <SW-DATA-DEF-PROPS-CONDITIONAL>
                        <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint8</IMPLEMENTATION-DATA-TYPE-REF>
                      </SW-DATA-DEF-PROPS-CONDITIONAL>
                    </SW-DATA-DEF-PROPS-VARIANTS>
                  </SW-DATA-DEF-PROPS>
                </IMPLEMENTATION-DATA-TYPE-ELEMENT>
              </SUB-ELEMENTS>
              <TYPE-EMITTER>BSW</TYPE-EMITTER>
            </IMPLEMENTATION-DATA-TYPE>
            <IMPLEMENTATION-DATA-TYPE>
              <SHORT-NAME>Port_n_PinType</SHORT-NAME>
              <CATEGORY>STRUCTURE</CATEGORY>
              <SUB-ELEMENTS>
                <IMPLEMENTATION-DATA-TYPE-ELEMENT>
                  <SHORT-NAME>Px</SHORT-NAME>
                  <CATEGORY>TYPE_REFERENCE</CATEGORY>
                  <SW-DATA-DEF-PROPS>
                    <SW-DATA-DEF-PROPS-VARIANTS>
                      <SW-DATA-DEF-PROPS-CONDITIONAL>
                        <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">/AUTOSAR_Platform/ImplementationDataTypes/uint32</IMPLEMENTATION-DATA-TYPE-REF>
                      </SW-DATA-DEF-PROPS-CONDITIONAL>
                    </SW-DATA-DEF-PROPS-VARIANTS>
                  </SW-DATA-DEF-PROPS>
                </IMPLEMENTATION-DATA-TYPE-ELEMENT>
              </SUB-ELEMENTS>
              <TYPE-EMITTER>BSW</TYPE-EMITTER>
            </IMPLEMENTATION-DATA-TYPE>
          </ELEMENTS>
        </AR-PACKAGE>
        <AR-PACKAGE>
          <SHORT-NAME>CompuMethods</SHORT-NAME>
          <CATEGORY>STANDARD</CATEGORY>
          <ELEMENTS>
            <COMPU-METHOD>
              <SHORT-NAME>Port_PinDirectionType</SHORT-NAME>
              <CATEGORY>TEXTTABLE</CATEGORY>
              <COMPU-INTERNAL-TO-PHYS>
                <COMPU-SCALES>
                  <COMPU-SCALE>
                    <LOWER-LIMIT INTERVAL-TYPE="CLOSED">128</LOWER-LIMIT>
                    <UPPER-LIMIT INTERVAL-TYPE="CLOSED">128</UPPER-LIMIT>
                    <COMPU-CONST>
                      <VT>PORT_PIN_IN</VT>
                    </COMPU-CONST>
                  </COMPU-SCALE>
                  <COMPU-SCALE>
                    <LOWER-LIMIT INTERVAL-TYPE="CLOSED">0</LOWER-LIMIT>
                    <UPPER-LIMIT INTERVAL-TYPE="CLOSED">0</UPPER-LIMIT>
                    <COMPU-CONST>
                      <VT>PORT_PIN_OUT</VT>
                    </COMPU-CONST>
                  </COMPU-SCALE>
                </COMPU-SCALES>
              </COMPU-INTERNAL-TO-PHYS>
            </COMPU-METHOD>
          </ELEMENTS>
        </AR-PACKAGE>
      </AR-PACKAGES>
    </AR-PACKAGE>
  </AR-PACKAGES>
</AUTOSAR>
