<?xml version="1.0" encoding="UTF-8" standalone="no"?><xmi:XMI xmlns:xmi="http://schema.omg.org/spec/XMI/2.1" xmlns:uml="http://schema.omg.org/spec/UML/2.1" xmi:version="2.1"><xmi:Documentation exporter="Enterprise Architect" exporterVersion="6.5"/><xmi:Extension extender="Enterprise Architect" extenderID="6.5"><elements><element name="ActionLists:ConstStruct" scope="public" xmi:idref="EAID_8fffe8b4c6b0bb3cff10a8f4dda834f9" xmi:type="uml:Object"><constraints/><attributes><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_bb9720b59ceeeb5f1155cfa161972e9f"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute><attribute name="FctPtrOfActionLists" scope="Private" xmi:idref="EAID_80c0087599ffdc2979eda6bed99954e6"><Constraints/><documentation value="Pointer to the array list function."/><properties changeability="frozen" collection="true" type="BswM_ActionListFuncType"/></attribute></attributes></element><element name="CanSMChannelMapping:ConstStruct" scope="public" xmi:idref="EAID_a21ade727c7ce9e2b2c47a38b3f50d4c" xmi:type="uml:Object"><properties documentation="Maps the external id of BswMCanSMIndication to an internal id and references immediate request ports." isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><constraints/><attributes><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_47f8c909da2c82326619483879aebb43"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute><attribute name="ExternalIdOfCanSMChannelMapping" scope="Private" xmi:idref="EAID_54a2ba27a48bb2292d71ee85f33f9769"><Constraints/><documentation value="External id of BswMCanSMIndication."/><properties changeability="frozen" collection="true" type="BswM_ExternalIdOfCanSMChannelMappingType"/></attribute><attribute name="InitValueOfCanSMChannelMapping" scope="Private" xmi:idref="EAID_d08eb3d449aea088be30d13007ab8bee"><Constraints/><documentation value="Initialization value of port."/><properties changeability="frozen" collection="true" type="CanSM_BswMCurrentStateType"/></attribute></attributes></element><element name="ComMChannelMapping:ConstStruct" scope="public" xmi:idref="EAID_449bc0813b49dcbf8d734ffd0f7ceae1" xmi:type="uml:Object"><properties documentation="Maps the external id of BswMComMIndication to an internal id and references immediate request ports." isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><constraints/><attributes><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_cf031f9b93ac88063f77287cb734c134"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute><attribute name="ExternalIdOfComMChannelMapping" scope="Private" xmi:idref="EAID_4fe412d5efc25a3d58b2a7712e772546"><Constraints/><documentation value="External id of BswMComMIndication."/><properties changeability="frozen" collection="true" type="BswM_ExternalIdOfComMChannelMappingType"/></attribute><attribute name="InitValueOfComMChannelMapping" scope="Private" xmi:idref="EAID_d89d7f12dc8487080a01d3573ebb5e42"><Constraints/><documentation value="Initialization value of port."/><properties changeability="frozen" collection="true" type="ComM_ModeType"/></attribute></attributes></element><element name="ComMInitiateResetMapping:ConstStruct" scope="public" xmi:idref="EAID_b034e05ba7519845f628fef9bdd2a277" xmi:type="uml:Object"><properties documentation="Maps the external id of BswMComMInitiateReset to an internal id and references immediate request ports." isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><constraints><constraint description="No Mode Request for BswMComMInitiateReset configured." name="IndirectableFeatureDeactivationCondition" status="Implemented" type="Pre-condition"/></constraints><attributes><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_cabce6d296e8104432dd514e817e9e95"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute><attribute name="InitValueOfComMInitiateResetMapping" scope="Private" xmi:idref="EAID_23d51eef31128cf7983edad81fb75113"><Constraints/><documentation value="Initialization value of port."/><properties changeability="frozen" collection="true" type="BswM_InitValueOfComMInitiateResetMappingType"/></attribute></attributes></element><element name="ComMPncMapping:ConstStruct" scope="public" xmi:idref="EAID_c6af116831d0a86da42785bf281317c3" xmi:type="uml:Object"><properties documentation="Maps the external id of BswMComMPncRequest to an internal id and references immediate request ports." isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><constraints><constraint description="No Mode Request for BswMComMPncRequest configured." name="IndirectableFeatureDeactivationCondition" status="Implemented" type="Pre-condition"/></constraints><attributes><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_2e52670d102987d4a53299496d9f29e5"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute><attribute name="ExternalIdOfComMPncMapping" scope="Private" xmi:idref="EAID_fe2732ba343b1e3c6c0f783341d7f21c"><Constraints/><documentation value="External id of BswMComMPncRequest."/><properties changeability="frozen" collection="true" type="BswM_ExternalIdOfComMPncMappingType"/></attribute><attribute name="InitValueOfComMPncMapping" scope="Private" xmi:idref="EAID_9bdc9c29bffbfac5aa488e35c001ff62"><Constraints/><documentation value="Initialization value of port."/><properties changeability="frozen" collection="true" type="ComM_PncModeType"/></attribute></attributes></element><element name="DcmApplUpdateMapping:ConstStruct" scope="public" xmi:idref="EAID_7bf126176b70ddd82d12cae610196108" xmi:type="uml:Object"><properties documentation="Maps the external id of BswMDcmApplicationUpdatedIndication to an internal id and references immediate request ports." isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><constraints><constraint description="No Mode Request for BswMDcmApplicationUpdatedIndication configured." name="IndirectableFeatureDeactivationCondition" status="Implemented" type="Pre-condition"/></constraints><attributes><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_ee29f02697662282f7cff567eaab2f20"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute><attribute name="InitValueOfDcmApplUpdateMapping" scope="Private" xmi:idref="EAID_0c084260fd413189737052b42cda3cf5"><Constraints/><documentation value="Initialization value of port."/><properties changeability="frozen" collection="true" type="BswM_InitValueOfDcmApplUpdateMappingType"/></attribute></attributes></element><element name="DcmComMapping:ConstStruct" scope="public" xmi:idref="EAID_07966b43865a440f0013855288098f51" xmi:type="uml:Object"><properties documentation="Maps the external id of BswMDcmComModeRequest to an internal id and references immediate request ports." isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><constraints><constraint description="No Mode Request for BswMDcmComModeRequest configured." name="IndirectableFeatureDeactivationCondition" status="Implemented" type="Pre-condition"/></constraints><attributes><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_ebccd0c9a9f0f411d81f6ceb25bec8af"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute><attribute name="ExternalIdOfDcmComMapping" scope="Private" xmi:idref="EAID_892dfd40d8f024eb64258a7fae102b4b"><Constraints/><documentation value="External id of BswMDcmComModeRequest."/><properties changeability="frozen" collection="true" type="BswM_ExternalIdOfDcmComMappingType"/></attribute><attribute name="InitValueOfDcmComMapping" scope="Private" xmi:idref="EAID_9dd9c8e7714b894e447f180efcfb36ae"><Constraints/><documentation value="Initialization value of port."/><properties changeability="frozen" collection="true" type="Dcm_CommunicationModeType"/></attribute></attributes></element><element name="DeferredRules:ConstStruct" scope="public" xmi:idref="EAID_73416e6903dc3163e0c6f257e0043235" xmi:type="uml:Object"><constraints><constraint description="No Deferred Rule in configuration. Therefore, Deferred Rules can not be created." name="IndirectableFeatureDeactivationCondition" status="Implemented" type="Pre-condition"/><constraint description="No Logical Expressions in configuration. Therefore, rules can not be created." name="IndirectableFeatureDeactivationCondition" status="Implemented" type="Pre-condition"/></constraints><attributes><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_72cc9a8b42499fea507c4c0295877fc8"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute></attributes></element><element name="EcuMModeMapping:ConstStruct" scope="public" xmi:idref="EAID_426c54d97548b9f37f228e3979caddc5" xmi:type="uml:Object"><properties documentation="Maps the external id of BswMEcuMIndication to an internal id and references immediate request ports." isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><constraints><constraint description="No Mode Request for BswMEcuMIndication configured." name="IndirectableFeatureDeactivationCondition" status="Implemented" type="Pre-condition"/></constraints><attributes><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_3ebb961c528059b7c36507222501f9de"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute><attribute name="InitValueOfEcuMModeMapping" scope="Private" xmi:idref="EAID_3f6959cb7d234ad5bcc54eb8e59b5a6a"><Constraints/><documentation value="Initialization value of port."/><properties changeability="frozen" collection="true" type="BswM_InitValueOfEcuMModeMappingType"/></attribute></attributes></element><element name="EcuMRunRequestMapping:ConstStruct" scope="public" xmi:idref="EAID_a9a1f120ae1ea463237fc9e424063a9a" xmi:type="uml:Object"><properties documentation="Maps the external id of BswMEcuMRUNRequestIndication to an internal id and references immediate request ports." isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><constraints><constraint description="No Mode Request for BswMEcuMRUNRequestIndication configured." name="IndirectableFeatureDeactivationCondition" status="Implemented" type="Pre-condition"/></constraints><attributes><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_588bc15c2cb56eb8bd72e407c7ca9df8"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute><attribute name="ExternalIdOfEcuMRunRequestMapping" scope="Private" xmi:idref="EAID_96a235a87f14b187407b011bcd2b28d8"><Constraints/><documentation value="External id of BswMEcuMRUNRequestIndication."/><properties changeability="frozen" collection="true" type="BswM_ExternalIdOfEcuMRunRequestMappingType"/></attribute><attribute name="InitValueOfEcuMRunRequestMapping" scope="Private" xmi:idref="EAID_94d6bab50d8607632a50a9a987f1e930"><Constraints/><documentation value="Initialization value of port."/><properties changeability="frozen" collection="true" type="EcuM_RunStatusType"/></attribute></attributes></element><element name="EcuMWakeupMapping:ConstStruct" scope="public" xmi:idref="EAID_2d308396852199787b2c9d011d2ef9dd" xmi:type="uml:Object"><properties documentation="Maps the external id of BswMEcuMWakeupSource to an internal id and references immediate request ports." isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><constraints><constraint description="No Mode Request for BswMEcuMWakeupSource configured." name="IndirectableFeatureDeactivationCondition" status="Implemented" type="Pre-condition"/></constraints><attributes><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_20551f10697a3b935474aa05d3cd53c3"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute><attribute name="ExternalIdOfEcuMWakeupMapping" scope="Private" xmi:idref="EAID_46dc6e6f1d37481a01e2caddd2f6e69a"><Constraints/><documentation value="External id of BswMEcuMWakeupSource."/><properties changeability="frozen" collection="true" type="BswM_ExternalIdOfEcuMWakeupMappingType"/></attribute><attribute name="InitValueOfEcuMWakeupMapping" scope="Private" xmi:idref="EAID_ba0e7e75324d8e7f6ce04c2e8fadd430"><Constraints/><documentation value="Initialization value of port."/><properties changeability="frozen" collection="true" type="EcuM_WakeupStateType"/></attribute></attributes></element><element name="EthIfPortMapping:ConstStruct" scope="public" xmi:idref="EAID_71a7eb72f8e3d8920cbc1acebd26c718" xmi:type="uml:Object"><properties documentation="Maps the external id of BswMEthIfPortGroupLinkStateChg to an internal id and references immediate request ports." isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><constraints><constraint description="No Mode Request for BswMEthIfPortGroupLinkStateChg configured." name="IndirectableFeatureDeactivationCondition" status="Implemented" type="Pre-condition"/></constraints><attributes><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_2e4ffd6b91e82113c12049a778424f2c"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute><attribute name="ExternalIdOfEthIfPortMapping" scope="Private" xmi:idref="EAID_22016b1fc4af2ee7cc29c5f1cca7df49"><Constraints/><documentation value="External id of BswMEthIfPortGroupLinkStateChg."/><properties changeability="frozen" collection="true" type="BswM_ExternalIdOfEthIfPortMappingType"/></attribute><attribute name="InitValueOfEthIfPortMapping" scope="Private" xmi:idref="EAID_e8a3ddd159f38d0ecab71a33d401d6be"><Constraints/><documentation value="Initialization value of port."/><properties changeability="frozen" collection="true" type="EthIf_SwitchPortGroupIdxType"/></attribute></attributes></element><element name="EthSMMapping:ConstStruct" scope="public" xmi:idref="EAID_aca37cd1d4b0d25f4584736607047a3f" xmi:type="uml:Object"><properties documentation="Maps the external id of BswMEthSMIndication to an internal id and references immediate request ports." isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><constraints><constraint description="No Mode Request for BswMEthSMIndication configured." name="IndirectableFeatureDeactivationCondition" status="Implemented" type="Pre-condition"/></constraints><attributes><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_c6734a559d664f489628daa6611e74c0"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute><attribute name="ExternalIdOfEthSMMapping" scope="Private" xmi:idref="EAID_2eb29936c1fe1b0a33c5d7921f084906"><Constraints/><documentation value="External id of BswMEthSMIndication."/><properties changeability="frozen" collection="true" type="BswM_ExternalIdOfEthSMMappingType"/></attribute><attribute name="InitValueOfEthSMMapping" scope="Private" xmi:idref="EAID_7ce556f0bc7462f5d59ae5907ddf43c0"><Constraints/><documentation value="Initialization value of port."/><properties changeability="frozen" collection="true" type="EthSM_NetworkModeStateType"/></attribute></attributes></element><element name="FrSMMapping:ConstStruct" scope="public" xmi:idref="EAID_8da2460f8e273616060821161f0facc6" xmi:type="uml:Object"><properties documentation="Maps the external id of BswMFrSMIndication to an internal id and references immediate request ports." isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><constraints/><attributes><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_a8684bb9b0175d44dc72e8c3d70a2471"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute><attribute name="ExternalIdOfFrSMMapping" scope="Private" xmi:idref="EAID_e736c307567f4dc1e82c13bc7d5e4bfc"><Constraints/><documentation value="External id of BswMFrSMIndication."/><properties changeability="frozen" collection="true" type="BswM_ExternalIdOfFrSMMappingType"/></attribute><attribute name="InitValueOfFrSMMapping" scope="Private" xmi:idref="EAID_65cc5ea22d52617d7fd095875d537347"><Constraints/><documentation value="Initialization value of port."/><properties changeability="frozen" collection="true" type="FrSM_BswM_StateType"/></attribute></attributes></element><element name="GenericMapping:ConstStruct" scope="public" xmi:idref="EAID_0ef8bd3922191bfc24fa7689a2d52cab" xmi:type="uml:Object"><properties documentation="Maps the external id of BswMGenericRequest to an internal id and references immediate request ports." isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><constraints/><attributes><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_f971b4626674eef7c3f756f63208d644"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute><attribute name="ExternalIdOfGenericMapping" scope="Private" xmi:idref="EAID_814dc459466aef63231a6628f6e4db34"><Constraints/><documentation value="External id of BswMGenericRequest."/><properties changeability="frozen" collection="true" type="BswM_ExternalIdOfGenericMappingType"/></attribute><attribute name="InitValueOfGenericMapping" scope="Private" xmi:idref="EAID_4c98a14b8bf628fc80b4b006c499ceb0"><Constraints/><documentation value="Initialization value of port."/><properties changeability="frozen" collection="true" type="BswM_ModeType"/></attribute></attributes></element><element name="ImmediateUser:ConstStruct" scope="public" xmi:idref="EAID_736b23f87e4f84ab6f4f112a4c275d56" xmi:type="uml:Object"><properties documentation="Contains all immediate request ports." isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><constraints><constraint description="No immediate user in configuration." name="IndirectableFeatureDeactivationCondition" status="Implemented" type="Pre-condition"/></constraints><attributes><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_96ad64085f28fe1618770160b5cc9d0e"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute><attribute name="ForcedOfImmediateUser" scope="Private" xmi:idref="EAID_d843996cd10771786eb9bc792a132cea"><Constraints><Constraint name="CBoolArrayGenCondition" notes="deactivate the BswM_ForcedOfImmediateUser if all values are false" type="Pre-condition"/></Constraints><documentation value="Forced immediate request (will never be queued)."/><properties changeability="frozen" collection="true" type="BswM_ForcedOfImmediateUserType"/></attribute><attribute name="OnInitOfImmediateUser" scope="Private" xmi:idref="EAID_55a964e7c8b06d6e63aa99d40a78778a"><Constraints><Constraint name="CBoolArrayGenCondition" notes="deactivate the BswM_OnInitOfImmediateUser if all values are false" type="Pre-condition"/></Constraints><documentation value="Arbitrate depending rules on initialization."/><properties changeability="frozen" collection="true" type="BswM_OnInitOfImmediateUserType"/></attribute></attributes></element><element name="J1939DcmMapping:ConstStruct" scope="public" xmi:idref="EAID_5c9b557e25f4453a916526c349760ba3" xmi:type="uml:Object"><properties documentation="Maps the external id of BswMJ1939DcmBroadcastStatus to an internal id and references immediate request ports." isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><constraints><constraint description="No Mode Request for BswMJ1939DcmBroadcastStatus configured." name="IndirectableFeatureDeactivationCondition" status="Implemented" type="Pre-condition"/></constraints><attributes><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_d8d48031b75ccc85ce88e857dca6ddb0"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute><attribute name="InitValueOfJ1939DcmMapping" scope="Private" xmi:idref="EAID_beef8104b73a0e0ba15a90c1fb84fb5b"><Constraints/><documentation value="Initialization value of port."/><properties changeability="frozen" collection="true" type="BswM_InitValueOfJ1939DcmMappingType"/></attribute></attributes></element><element name="J1939NmMapping:ConstStruct" scope="public" xmi:idref="EAID_f564c0c37c31d9acb7cfa606b606be94" xmi:type="uml:Object"><properties documentation="Maps the external id of BswMJ1939NmIndication to an internal id and references immediate request ports." isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><constraints><constraint description="No Mode Request for BswMJ1939NmIndication configured." name="IndirectableFeatureDeactivationCondition" status="Implemented" type="Pre-condition"/></constraints><attributes><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_c3c0afca3f7cee4bc4aab1f8d59b4ccd"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute><attribute name="ExternalIdOfJ1939NmMapping" scope="Private" xmi:idref="EAID_c8f8467142da4828f91a1a1ee2d8e04a"><Constraints/><documentation value="External id of BswMJ1939NmIndication."/><properties changeability="frozen" collection="true" type="BswM_ExternalIdOfJ1939NmMappingType"/></attribute><attribute name="InitValueOfJ1939NmMapping" scope="Private" xmi:idref="EAID_21541b1f810a2c3c6e4bfdf649328b12"><Constraints/><documentation value="Initialization value of port."/><properties changeability="frozen" collection="true" type="Nm_StateType"/></attribute></attributes></element><element name="LinSMMapping:ConstStruct" scope="public" xmi:idref="EAID_2fdddeed51c0a69f3bede5f95c808bfc" xmi:type="uml:Object"><properties documentation="Maps the external id of BswMLinSMIndication to an internal id and references immediate request ports." isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><constraints/><attributes><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_701560f9fab0f227096ae7c6e2a62bd2"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute><attribute name="ExternalIdOfLinSMMapping" scope="Private" xmi:idref="EAID_d79c7e68e16118566d7ab0939375c870"><Constraints/><documentation value="External id of BswMLinSMIndication."/><properties changeability="frozen" collection="true" type="BswM_ExternalIdOfLinSMMappingType"/></attribute><attribute name="InitValueOfLinSMMapping" scope="Private" xmi:idref="EAID_e45a12c9b6a326bef2aafd64acfa3a36"><Constraints/><documentation value="Initialization value of port."/><properties changeability="frozen" collection="true" type="LinSM_ModeType"/></attribute></attributes></element><element name="LinScheduleEndMapping:ConstStruct" scope="public" xmi:idref="EAID_2ddd09a93c8171fba85908a632445594" xmi:type="uml:Object"><properties documentation="Maps the external id of BswMLinScheduleEndNotification to an internal id and references immediate request ports." isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><constraints><constraint description="No Mode Request for BswMLinScheduleEndNotification configured." name="IndirectableFeatureDeactivationCondition" status="Implemented" type="Pre-condition"/></constraints><attributes><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_df2295d99e3f21f6990299ec8a6e688c"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute><attribute name="ExternalIdOfLinScheduleEndMapping" scope="Private" xmi:idref="EAID_257daf09445cf89dae03f4c16ae34f8b"><Constraints/><documentation value="External id of BswMLinScheduleEndNotification."/><properties changeability="frozen" collection="true" type="BswM_ExternalIdOfLinScheduleEndMappingType"/></attribute><attribute name="InitValueOfLinScheduleEndMapping" scope="Private" xmi:idref="EAID_c21c3459ddd04da22b7bcce77892f8bd"><Constraints/><documentation value="Initialization value of port."/><properties changeability="frozen" collection="true" type="LinIf_SchHandleType"/></attribute></attributes></element><element name="LinScheduleMapping:ConstStruct" scope="public" xmi:idref="EAID_c825ab2f2c373c59418cc35185a0d67d" xmi:type="uml:Object"><properties documentation="Maps the external id of BswMLinScheduleIndication to an internal id and references immediate request ports." isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><constraints/><attributes><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_ebf5cb5d66b698e0238910f107cd0361"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute><attribute name="ExternalIdOfLinScheduleMapping" scope="Private" xmi:idref="EAID_f201ba075b83d9931cff3d5426c62fee"><Constraints/><documentation value="External id of BswMLinScheduleIndication."/><properties changeability="frozen" collection="true" type="BswM_ExternalIdOfLinScheduleMappingType"/></attribute><attribute name="InitValueOfLinScheduleMapping" scope="Private" xmi:idref="EAID_a448e792acfefb37103d572b700cce52"><Constraints/><documentation value="Initialization value of port."/><properties changeability="frozen" collection="true" type="LinIf_SchHandleType"/></attribute></attributes></element><element name="LinTPMapping:ConstStruct" scope="public" xmi:idref="EAID_2f5822e2084d2acc197e7ef5e1e88125" xmi:type="uml:Object"><properties documentation="Maps the external id of BswMLinTpModeRequest to an internal id and references immediate request ports." isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><constraints><constraint description="No Mode Request for BswMLinTpModeRequest configured." name="IndirectableFeatureDeactivationCondition" status="Implemented" type="Pre-condition"/></constraints><attributes><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_77891986a6a5df06a57042d1d649be81"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute><attribute name="ExternalIdOfLinTPMapping" scope="Private" xmi:idref="EAID_29124cc978f723bbe010a6e38a65cc16"><Constraints/><documentation value="External id of BswMLinTpModeRequest."/><properties changeability="frozen" collection="true" type="BswM_ExternalIdOfLinTPMappingType"/></attribute><attribute name="InitValueOfLinTPMapping" scope="Private" xmi:idref="EAID_4346081b4f6a0a727aa298e91e17609e"><Constraints/><documentation value="Initialization value of port."/><properties changeability="frozen" collection="true" type="LinTp_Mode"/></attribute></attributes></element><element name="ModeNotificationMapping:ConstStruct" scope="public" xmi:idref="EAID_4c25e97bd5cb480accd82325110a2a96" xmi:type="uml:Object"><properties documentation="Maps the external id of BswMSwcModeNotification to an internal id and references immediate request ports." isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><constraints/><attributes><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_35f0cdb91804d07cb1430cc66b164071"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute></attributes></element><element name="ModeRequestMapping:ConstStruct" scope="public" xmi:idref="EAID_6a57bf2c090b65ce442c84f95529fef3" xmi:type="uml:Object"><properties documentation="Maps the external id of BswMSwcModeRequest to an internal id and references immediate request ports." isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><constraints/><attributes><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_426d55bc65f365c86317095921d8271c"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute></attributes></element><element name="NmMapping:ConstStruct" scope="public" xmi:idref="EAID_a60f4f05f55df5ec2e7c53c020d9a47d" xmi:type="uml:Object"><properties documentation="Maps the external id of BswMNmIndication to an internal id and references immediate request ports." isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><constraints><constraint description="No Mode Request for BswMNmIndication configured." name="IndirectableFeatureDeactivationCondition" status="Implemented" type="Pre-condition"/></constraints><attributes><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_df27714241cbf2839098d37a3c1586d9"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute><attribute name="ExternalIdOfNmMapping" scope="Private" xmi:idref="EAID_0e4ca8c167342c1f36c4ffcb893e14e3"><Constraints/><documentation value="External id of BswMNmIndication."/><properties changeability="frozen" collection="true" type="BswM_ExternalIdOfNmMappingType"/></attribute><attribute name="InitValueOfNmMapping" scope="Private" xmi:idref="EAID_032e2835610a07ec23c28577e9085d9b"><Constraints/><documentation value="Initialization value of port."/><properties changeability="frozen" collection="true" type="Nm_StateType"/></attribute></attributes></element><element name="NvMBlockMapping:ConstStruct" scope="public" xmi:idref="EAID_8054289471090aaf93f03b02401f1ab3" xmi:type="uml:Object"><properties documentation="Maps the external id of BswMNvMRequest to an internal id and references immediate request ports." isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><constraints><constraint description="No Mode Request for BswMNvMRequest configured." name="IndirectableFeatureDeactivationCondition" status="Implemented" type="Pre-condition"/></constraints><attributes><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_536c780a828639e2dfb9caee3d85953d"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute><attribute name="ExternalIdOfNvMBlockMapping" scope="Private" xmi:idref="EAID_5fc58179d3899a2a9769bf924e797a6a"><Constraints/><documentation value="External id of BswMNvMRequest."/><properties changeability="frozen" collection="true" type="BswM_ExternalIdOfNvMBlockMappingType"/></attribute><attribute name="InitValueOfNvMBlockMapping" scope="Private" xmi:idref="EAID_3d72a80b36df66c7e1edae9bbc2cf829"><Constraints/><documentation value="Initialization value of port."/><properties changeability="frozen" collection="true" type="NvM_RequestResultType"/></attribute></attributes></element><element name="NvMJobMapping:ConstStruct" scope="public" xmi:idref="EAID_6b0c2255d55263a1c9ca7e897f2ff4ed" xmi:type="uml:Object"><properties documentation="Maps the external id of BswMNvMJobModeIndication to an internal id and references immediate request ports." isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><constraints/><attributes><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_ebd953b8e8186b4c3822b70dd4047862"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute><attribute name="ExternalIdOfNvMJobMapping" scope="Private" xmi:idref="EAID_9762e8b88b51f78a740b870834b19e21"><Constraints/><documentation value="External id of BswMNvMJobModeIndication."/><properties changeability="frozen" collection="true" type="BswM_ExternalIdOfNvMJobMappingType"/></attribute><attribute name="InitValueOfNvMJobMapping" scope="Private" xmi:idref="EAID_69afd70dbbc452b6bec87316c3a91ccc"><Constraints/><documentation value="Initialization value of port."/><properties changeability="frozen" collection="true" type="NvM_RequestResultType"/></attribute></attributes></element><element name="PduRRxIndicationMapping:ConstStruct" scope="public" xmi:idref="EAID_dca23268bf9314a308078fdd0f25566f" xmi:type="uml:Object"><properties documentation="Maps the external id of BswMPduRRxIndication to an internal id and references immediate request ports." isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><constraints><constraint description="No Mode Request for BswMPduRRxIndication configured." name="IndirectableFeatureDeactivationCondition" status="Implemented" type="Pre-condition"/></constraints><attributes><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_a51b6761f39e2c0c843025e65738910c"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute><attribute name="ExternalIdOfPduRRxIndicationMapping" scope="Private" xmi:idref="EAID_89e84c577892b5692ee65f373cc02c33"><Constraints/><documentation value="External id of BswMPduRRxIndication."/><properties changeability="frozen" collection="true" type="BswM_ExternalIdOfPduRRxIndicationMappingType"/></attribute><attribute name="InitValueOfPduRRxIndicationMapping" scope="Private" xmi:idref="EAID_a5f79ffd4ddc325e529ddeffa8c67131"><Constraints/><documentation value="Initialization value of port."/><properties changeability="frozen" collection="true" type="BswM_InitValueOfPduRRxIndicationMappingType"/></attribute></attributes></element><element name="PduRTpRxIndicationMapping:ConstStruct" scope="public" xmi:idref="EAID_4570df5896ab42d50e3cb8875a98f345" xmi:type="uml:Object"><properties documentation="Maps the external id of BswMPduRTpRxIndication to an internal id and references immediate request ports." isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><constraints><constraint description="No Mode Request for BswMPduRTpRxIndication configured." name="IndirectableFeatureDeactivationCondition" status="Implemented" type="Pre-condition"/></constraints><attributes><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_58d09d97412ab33cf10f501ce4812828"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute><attribute name="ExternalIdOfPduRTpRxIndicationMapping" scope="Private" xmi:idref="EAID_aa0e41fb911ac3a364c98c602b6dc3f5"><Constraints/><documentation value="External id of BswMPduRTpRxIndication."/><properties changeability="frozen" collection="true" type="BswM_ExternalIdOfPduRTpRxIndicationMappingType"/></attribute><attribute name="InitValueOfPduRTpRxIndicationMapping" scope="Private" xmi:idref="EAID_56a221474b5375e480b7d4d8e0433c31"><Constraints/><documentation value="Initialization value of port."/><properties changeability="frozen" collection="true" type="BswM_InitValueOfPduRTpRxIndicationMappingType"/></attribute></attributes></element><element name="PduRTpStartOfReceptionMapping:ConstStruct" scope="public" xmi:idref="EAID_d9d96d1680ba023d123166ec09c0ad26" xmi:type="uml:Object"><properties documentation="Maps the external id of BswMPduRTpStartOfReception to an internal id and references immediate request ports." isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><constraints><constraint description="No Mode Request for BswMPduRTpStartOfReception configured." name="IndirectableFeatureDeactivationCondition" status="Implemented" type="Pre-condition"/></constraints><attributes><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_fe76154064cd7d7102cc27fde1a701a7"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute><attribute name="ExternalIdOfPduRTpStartOfReceptionMapping" scope="Private" xmi:idref="EAID_d68a50f7709155500309ac934d73179b"><Constraints/><documentation value="External id of BswMPduRTpStartOfReception."/><properties changeability="frozen" collection="true" type="BswM_ExternalIdOfPduRTpStartOfReceptionMappingType"/></attribute><attribute name="InitValueOfPduRTpStartOfReceptionMapping" scope="Private" xmi:idref="EAID_8085afd1239615a2be8d696b9ea84abe"><Constraints/><documentation value="Initialization value of port."/><properties changeability="frozen" collection="true" type="BswM_InitValueOfPduRTpStartOfReceptionMappingType"/></attribute></attributes></element><element name="PduRTpTxConfirmationMapping:ConstStruct" scope="public" xmi:idref="EAID_25c789e736b4f0674042f030daef27a5" xmi:type="uml:Object"><properties documentation="Maps the external id of BswMPduRTpTxConfirmation to an internal id and references immediate request ports." isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><constraints><constraint description="No Mode Request for BswMPduRTpTxConfirmation configured." name="IndirectableFeatureDeactivationCondition" status="Implemented" type="Pre-condition"/></constraints><attributes><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_eb71e2cc96edbb1ea160ebd519dbd9c0"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute><attribute name="ExternalIdOfPduRTpTxConfirmationMapping" scope="Private" xmi:idref="EAID_a1eed56b7ddbe732c74f8e6a0bef3b35"><Constraints/><documentation value="External id of BswMPduRTpTxConfirmation."/><properties changeability="frozen" collection="true" type="BswM_ExternalIdOfPduRTpTxConfirmationMappingType"/></attribute><attribute name="InitValueOfPduRTpTxConfirmationMapping" scope="Private" xmi:idref="EAID_b9765b9292e3b32568051c5e965ff7f3"><Constraints/><documentation value="Initialization value of port."/><properties changeability="frozen" collection="true" type="BswM_InitValueOfPduRTpTxConfirmationMappingType"/></attribute></attributes></element><element name="PduRTransmitMapping:ConstStruct" scope="public" xmi:idref="EAID_de0b701f4135993e03cf8835354a29a1" xmi:type="uml:Object"><properties documentation="Maps the external id of BswMPduRTransmit to an internal id and references immediate request ports." isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><constraints><constraint description="No Mode Request for BswMPduRTransmit configured." name="IndirectableFeatureDeactivationCondition" status="Implemented" type="Pre-condition"/></constraints><attributes><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_982f5bf173416a660fc1a70a4ac555ff"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute><attribute name="ExternalIdOfPduRTransmitMapping" scope="Private" xmi:idref="EAID_b63651538286b82d4d52d6bc959a8d54"><Constraints/><documentation value="External id of BswMPduRTransmit."/><properties changeability="frozen" collection="true" type="BswM_ExternalIdOfPduRTransmitMappingType"/></attribute><attribute name="InitValueOfPduRTransmitMapping" scope="Private" xmi:idref="EAID_2a1dd8c7aa6a92ef8b6c9007a9f19463"><Constraints/><documentation value="Initialization value of port."/><properties changeability="frozen" collection="true" type="BswM_InitValueOfPduRTransmitMappingType"/></attribute></attributes></element><element name="PduRTxConfirmationMapping:ConstStruct" scope="public" xmi:idref="EAID_a54b478336d897c37a8f7e4377062f5a" xmi:type="uml:Object"><properties documentation="Maps the external id of BswMPduRTxConfirmation to an internal id and references immediate request ports." isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><constraints><constraint description="No Mode Request for BswMPduRTxConfirmation configured." name="IndirectableFeatureDeactivationCondition" status="Implemented" type="Pre-condition"/></constraints><attributes><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_bfad4c3a4393d0fd28f17f4a52d81bd8"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute><attribute name="ExternalIdOfPduRTxConfirmationMapping" scope="Private" xmi:idref="EAID_ac577c4649cb4fb888deebb89f033c2e"><Constraints/><documentation value="External id of BswMPduRTxConfirmation."/><properties changeability="frozen" collection="true" type="BswM_ExternalIdOfPduRTxConfirmationMappingType"/></attribute><attribute name="InitValueOfPduRTxConfirmationMapping" scope="Private" xmi:idref="EAID_dca4f8fc80eaa9364ac420ee218562fb"><Constraints/><documentation value="Initialization value of port."/><properties changeability="frozen" collection="true" type="BswM_InitValueOfPduRTxConfirmationMappingType"/></attribute></attributes></element><element name="Rules:ConstStruct" scope="public" xmi:idref="EAID_6d3ab65f9bb3e78db2ddcf0fd6277073" xmi:type="uml:Object"><constraints/><attributes><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_c9213260854ddc97087b72a8bb61fa5c"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute><attribute name="FctPtrOfRules" scope="Private" xmi:idref="EAID_e24c29c1df2d3990b0b3cf2ed6d95f8b"><Constraints/><documentation value="Pointer to the rule function which does the arbitration."/><properties changeability="frozen" collection="true" type="BswM_RuleTableFctPtrType"/></attribute><attribute name="IdOfRules" scope="Private" xmi:idref="EAID_32c1f058835e2ea8f64a41910cf3a944"><Constraints/><documentation value="External id of rule."/><properties changeability="frozen" collection="true" type="BswM_IdOfRulesType"/></attribute><attribute name="InitOfRules" scope="Private" xmi:idref="EAID_f6b02125dd2979e682d40d5f10d459d8"><Constraints/><documentation value="Initialization value of rule state (TRUE, FALSE, UNDEFINED or DEACTIVATED)."/><properties changeability="frozen" collection="true" type="BswM_InitOfRulesType"/></attribute></attributes></element><element name="SdClientServiceMapping:ConstStruct" scope="public" xmi:idref="EAID_73ba171504d9bc327fa65885ba2f3e46" xmi:type="uml:Object"><properties documentation="Maps the external id of BswMSdClientServiceCurrentState to an internal id and references immediate request ports." isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><constraints><constraint description="No Mode Request for BswMSdClientServiceCurrentState configured." name="IndirectableFeatureDeactivationCondition" status="Implemented" type="Pre-condition"/></constraints><attributes><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_c62e6a6b3e87d77ee6af200b9ebda95f"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute><attribute name="ExternalIdOfSdClientServiceMapping" scope="Private" xmi:idref="EAID_5c613161bc83dd177c7d6c1f6239f4f8"><Constraints/><documentation value="External id of BswMSdClientServiceCurrentState."/><properties changeability="frozen" collection="true" type="BswM_ExternalIdOfSdClientServiceMappingType"/></attribute><attribute name="InitValueOfSdClientServiceMapping" scope="Private" xmi:idref="EAID_985b2916bf1d408f5a8ef028546c63cb"><Constraints/><documentation value="Initialization value of port."/><properties changeability="frozen" collection="true" type="Sd_ClientServiceCurrentStateType"/></attribute></attributes></element><element name="SdConsumedEventMapping:ConstStruct" scope="public" xmi:idref="EAID_1a406a988d77b67daad6f863e0d267d6" xmi:type="uml:Object"><properties documentation="Maps the external id of BswMSdConsumedEventGroupCurrentState to an internal id and references immediate request ports." isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><constraints><constraint description="No Mode Request for BswMSdConsumedEventGroupCurrentState configured." name="IndirectableFeatureDeactivationCondition" status="Implemented" type="Pre-condition"/></constraints><attributes><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_d5881b429199cdbbc1082cf5fa892621"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute><attribute name="ExternalIdOfSdConsumedEventMapping" scope="Private" xmi:idref="EAID_e2731927dcf4a444f8d056cccbf9ac93"><Constraints/><documentation value="External id of BswMSdConsumedEventGroupCurrentState."/><properties changeability="frozen" collection="true" type="BswM_ExternalIdOfSdConsumedEventMappingType"/></attribute><attribute name="InitValueOfSdConsumedEventMapping" scope="Private" xmi:idref="EAID_2613df17ef294f4495d3330b9840f991"><Constraints/><documentation value="Initialization value of port."/><properties changeability="frozen" collection="true" type="Sd_ConsumedEventGroupCurrentStateType"/></attribute></attributes></element><element name="SdEventHandlerMapping:ConstStruct" scope="public" xmi:idref="EAID_8bcd80b7d0c042d31449cd6be786b997" xmi:type="uml:Object"><properties documentation="Maps the external id of BswMSdEventHandlerCurrentState to an internal id and references immediate request ports." isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><constraints><constraint description="No Mode Request for BswMSdEventHandlerCurrentState configured." name="IndirectableFeatureDeactivationCondition" status="Implemented" type="Pre-condition"/></constraints><attributes><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_b56a855de34d14ac341f4ab6ca33d0a3"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute><attribute name="ExternalIdOfSdEventHandlerMapping" scope="Private" xmi:idref="EAID_f3902ae2585e430403f35f00e24c953a"><Constraints/><documentation value="External id of BswMSdEventHandlerCurrentState."/><properties changeability="frozen" collection="true" type="BswM_ExternalIdOfSdEventHandlerMappingType"/></attribute><attribute name="InitValueOfSdEventHandlerMapping" scope="Private" xmi:idref="EAID_dba46369ea3ce7a2cbba6620ec7adb38"><Constraints/><documentation value="Initialization value of port."/><properties changeability="frozen" collection="true" type="Sd_EventHandlerCurrentStateType"/></attribute></attributes></element><element name="WdgMMapping:ConstStruct" scope="public" xmi:idref="EAID_a366686ddfeaf0470ea2b08764eb9e18" xmi:type="uml:Object"><properties documentation="Maps the external id of BswMWdgMRequestPartitionReset to an internal id and references immediate request ports." isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><constraints><constraint description="No Mode Request for BswMWdgMRequestPartitionReset configured." name="IndirectableFeatureDeactivationCondition" status="Implemented" type="Pre-condition"/></constraints><attributes><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_8c02c0200f5824f7ccbafcfa9d1fc39d"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute><attribute name="ExternalIdOfWdgMMapping" scope="Private" xmi:idref="EAID_ab02f79942d25ad0129d5ae2aed7db8f"><Constraints/><documentation value="External id of BswMWdgMRequestPartitionReset."/><properties changeability="frozen" collection="true" type="BswM_ExternalIdOfWdgMMappingType"/></attribute><attribute name="InitValueOfWdgMMapping" scope="Private" xmi:idref="EAID_e6cf5a971465a3464551b2f55ed0f21e"><Constraints/><documentation value="Initialization value of port."/><properties changeability="frozen" collection="true" type="BswM_InitValueOfWdgMMappingType"/></attribute></attributes></element><element name="InitGenVarAndInitAL:ConstArray" scope="public" xmi:idref="EAID_13bce6af878e8272764dd53353384f97" xmi:type="uml:Object"><attributes><attribute name="InitGenVarAndInitAL" scope="Private" xmi:idref="EAID_956853748cb1c26acc8249ad6c718aba"><Constraints/><properties changeability="frozen" collection="true" type="BswM_InitGenVarAndInitALType"/></attribute><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_642e5b1ad9c20a465e68b8de4230aee1"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute></attributes></element><element name="ModeNotificationFct:ConstArray" scope="public" xmi:idref="EAID_4be79be67ff68b1a41e4829ab431c6ab" xmi:type="uml:Object"><attributes><attribute name="ModeNotificationFct" scope="Private" xmi:idref="EAID_9179091f7cfb85daeb183c5c97f57f54"><Constraints/><properties changeability="frozen" collection="true" type="BswM_PartitionFunctionType"/></attribute><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_816c662ea169248b29e97286962e8d63"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute></attributes></element><element name="SwcModeRequestUpdateFct:ConstArray" scope="public" xmi:idref="EAID_b591a756b0430239f853601eb6ff1d34" xmi:type="uml:Object"><attributes><attribute name="SwcModeRequestUpdateFct" scope="Private" xmi:idref="EAID_658297afebde517a25660e33121229ac"><Constraints/><properties changeability="frozen" collection="true" type="BswM_PartitionFunctionType"/></attribute><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_c1611f684565b05f0860a221e7a45d45"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute></attributes></element><element name="ActionListPriorityQueue:VarArray" scope="public" xmi:idref="EAID_8d980ae5825cc02ef281dc2498ff8276" xmi:type="uml:Object"><properties documentation="Heap of priority queue that stores the indexes of the used Action List Queue indexes" isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><attributes><attribute name="ActionListPriorityQueue" scope="Private" xmi:idref="EAID_4bbf9d298858b7b919ecefc0dc647f02"><Constraints><Constraint name="IndirectableFeatureDeactivationCondition" notes="Action List Queue Search Algorithm is not equal to PRIORITY_QUEUE" type="Pre-condition"/></Constraints><documentation value="Heap of priority queue that stores the indexes of the used Action List Queue indexes"/><properties collection="true"/></attribute><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_9b052d16948f3ef9d6a5f9923b72fcce"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute></attributes></element><element name="ActionListQueue:VarArray" scope="public" xmi:idref="EAID_77f1f5474de5c097c5f46090d7b95e22" xmi:type="uml:Object"><properties documentation="Variable to store action lists which shall be executed." isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><attributes><attribute name="ActionListQueue" scope="Private" xmi:idref="EAID_21b1ce0e40a3274fe8cc2d78b0d4f8db"><Constraints/><documentation value="Variable to store action lists which shall be executed."/><properties collection="true"/></attribute><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_5027e3d4815398e0e6500aa500f0953a"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute></attributes></element><element name="CanSMChannelState:VarArray" scope="public" xmi:idref="EAID_3c10edb7f4de06c74906aaf0c8aff755" xmi:type="uml:Object"><properties documentation="Variable to store current mode of BswMCanSMIndication mode request ports." isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><attributes><attribute name="CanSMChannelState" scope="Private" xmi:idref="EAID_5cb7003c55c463a40c57582a945db34e"><Constraints/><documentation value="Variable to store current mode of BswMCanSMIndication mode request ports."/><properties collection="true"/></attribute><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_2a8a4783ac003dbd4d583bae4a0182f5"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute></attributes></element><element name="ComMChannelState:VarArray" scope="public" xmi:idref="EAID_d89319db780de5953be15da15b29fea4" xmi:type="uml:Object"><properties documentation="Variable to store current mode of BswMComMIndication mode request ports." isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><attributes><attribute name="ComMChannelState" scope="Private" xmi:idref="EAID_5dc900626ca8378b0054fd780f7d82ce"><Constraints/><documentation value="Variable to store current mode of BswMComMIndication mode request ports."/><properties collection="true"/></attribute><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_32a9c51546a3f43f6d39b06a979f1ccc"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute></attributes></element><element name="ComMPncState:VarArray" scope="public" xmi:idref="EAID_aab8a9894c175591bb2ed223682446d2" xmi:type="uml:Object"><properties documentation="Variable to store current mode of BswMComMPncRequest mode request ports." isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><attributes><attribute name="ComMPncState" scope="Private" xmi:idref="EAID_20c24c0e62a3734531dac7776ccc6655"><Constraints/><documentation value="Variable to store current mode of BswMComMPncRequest mode request ports."/><properties collection="true"/></attribute><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_545ab8996056bf203f0b30ef15a92f7c"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute></attributes></element><element name="DcmComState:VarArray" scope="public" xmi:idref="EAID_d05992d737490f1c22607b265339f321" xmi:type="uml:Object"><properties documentation="Variable to store current mode of BswMDcmComModeRequest mode request ports." isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><attributes><attribute name="DcmComState" scope="Private" xmi:idref="EAID_8d3e4a631ae24cd7a2b8016a6ba3fa16"><Constraints/><documentation value="Variable to store current mode of BswMDcmComModeRequest mode request ports."/><properties collection="true"/></attribute><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_ecddea465a9edde0d371a825f325ad26"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute></attributes></element><element name="EcuMRunRequestState:VarArray" scope="public" xmi:idref="EAID_d6b19081a1717e96aca720d19c549d0f" xmi:type="uml:Object"><properties documentation="Variable to store current mode of BswMEcuMRUNRequestIndication mode request ports." isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><attributes><attribute name="EcuMRunRequestState" scope="Private" xmi:idref="EAID_158f5b7eb194543d16c826c66bde82ea"><Constraints/><documentation value="Variable to store current mode of BswMEcuMRUNRequestIndication mode request ports."/><properties collection="true"/></attribute><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_274823114382bdbe619d9bf2ba381833"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute></attributes></element><element name="EcuMWakeupState:VarArray" scope="public" xmi:idref="EAID_ed09cf7dc5ff034db3934bce408ab274" xmi:type="uml:Object"><properties documentation="Variable to store current mode of BswMEcuMWakeupSource mode request ports." isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><attributes><attribute name="EcuMWakeupState" scope="Private" xmi:idref="EAID_fec797b6bc83a77e323566f2c3b2db37"><Constraints/><documentation value="Variable to store current mode of BswMEcuMWakeupSource mode request ports."/><properties collection="true"/></attribute><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_9a6adbef80e41bfd5d4e6056eeba06c6"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute></attributes></element><element name="EthIfPortState:VarArray" scope="public" xmi:idref="EAID_290f7efd4a165002388b9d74983797ed" xmi:type="uml:Object"><properties documentation="Variable to store current mode of BswMEthIfPortGroupLinkStateChg mode request ports." isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><attributes><attribute name="EthIfPortState" scope="Private" xmi:idref="EAID_05cd7bd033043173f83e4787f73fec37"><Constraints/><documentation value="Variable to store current mode of BswMEthIfPortGroupLinkStateChg mode request ports."/><properties collection="true"/></attribute><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_13d11c722438a19a26350fc986270c44"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute></attributes></element><element name="EthSMState:VarArray" scope="public" xmi:idref="EAID_99158b66258e6db32e26958d24c2f457" xmi:type="uml:Object"><properties documentation="Variable to store current mode of BswMEthSMIndication mode request ports." isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><attributes><attribute name="EthSMState" scope="Private" xmi:idref="EAID_d355d61d91ef1e9f11417f681abec809"><Constraints/><documentation value="Variable to store current mode of BswMEthSMIndication mode request ports."/><properties collection="true"/></attribute><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_c7155b82c22258123c91f5a43db43d4f"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute></attributes></element><element name="FrSMState:VarArray" scope="public" xmi:idref="EAID_6c9ff6840305b8093fcb24c199298b6a" xmi:type="uml:Object"><properties documentation="Variable to store current mode of BswMFrSMIndication mode request ports." isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><attributes><attribute name="FrSMState" scope="Private" xmi:idref="EAID_3dc850990c4db12e7332afeb7dfb2b0c"><Constraints/><documentation value="Variable to store current mode of BswMFrSMIndication mode request ports."/><properties collection="true"/></attribute><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_89003460c255dd61abdc350a66fd8cf2"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute></attributes></element><element name="GenericState:VarArray" scope="public" xmi:idref="EAID_171701682fc470d3b63f7c2ca2cb7d41" xmi:type="uml:Object"><properties documentation="Variable to store current mode of BswMGenericRequest mode request ports." isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><attributes><attribute name="GenericState" scope="Private" xmi:idref="EAID_02bdb295c81b6761bd3a334d9b2301a3"><Constraints/><documentation value="Variable to store current mode of BswMGenericRequest mode request ports."/><properties collection="true"/></attribute><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_e1270c61bb1840657f654d24c21f1fdd"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute></attributes></element><element name="J1939NmState:VarArray" scope="public" xmi:idref="EAID_8cfbe97fdd31b179538802e9fe4bc42f" xmi:type="uml:Object"><properties documentation="Variable to store current mode of BswMJ1939NmIndication mode request ports." isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><attributes><attribute name="J1939NmState" scope="Private" xmi:idref="EAID_66154f0c59f133ce1188245cd6ee1469"><Constraints/><documentation value="Variable to store current mode of BswMJ1939NmIndication mode request ports."/><properties collection="true"/></attribute><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_80f6c2d6e1656dc85038800345025cf2"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute></attributes></element><element name="LinSMState:VarArray" scope="public" xmi:idref="EAID_31dc458ee6f35226a71bc9888c5f229e" xmi:type="uml:Object"><properties documentation="Variable to store current mode of BswMLinSMIndication mode request ports." isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><attributes><attribute name="LinSMState" scope="Private" xmi:idref="EAID_b9049a650a91f5bc5661ca2f20f73546"><Constraints/><documentation value="Variable to store current mode of BswMLinSMIndication mode request ports."/><properties collection="true"/></attribute><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_37e739e1135a28fc10ab92c6c96aca0b"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute></attributes></element><element name="LinScheduleEndState:VarArray" scope="public" xmi:idref="EAID_a25b859f884aa8eaeea88b06db52a2cf" xmi:type="uml:Object"><properties documentation="Variable to store current mode of BswMLinScheduleEndNotification mode request ports." isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><attributes><attribute name="LinScheduleEndState" scope="Private" xmi:idref="EAID_23430036ffdad02453433b0e6a78824f"><Constraints/><documentation value="Variable to store current mode of BswMLinScheduleEndNotification mode request ports."/><properties collection="true"/></attribute><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_7cf80c254dffd636c6bff1809b6cfb8f"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute></attributes></element><element name="LinScheduleState:VarArray" scope="public" xmi:idref="EAID_335562ed44a8bc4ec2ab7220067315e8" xmi:type="uml:Object"><properties documentation="Variable to store current mode of BswMLinScheduleIndication mode request ports." isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><attributes><attribute name="LinScheduleState" scope="Private" xmi:idref="EAID_b487e1de0ceeb1a7c84137296be6971b"><Constraints/><documentation value="Variable to store current mode of BswMLinScheduleIndication mode request ports."/><properties collection="true"/></attribute><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_2d21236b1a9baf9fd07e10438a4a08f5"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute></attributes></element><element name="LinTPState:VarArray" scope="public" xmi:idref="EAID_8e541ea950e51db2c18e4e0dce6688c5" xmi:type="uml:Object"><properties documentation="Variable to store current mode of BswMLinTpModeRequest mode request ports." isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><attributes><attribute name="LinTPState" scope="Private" xmi:idref="EAID_5c156ba7bced8c3e2949583b6a9008ca"><Constraints/><documentation value="Variable to store current mode of BswMLinTpModeRequest mode request ports."/><properties collection="true"/></attribute><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_24a4c453355c1f9362e6b501773c8b45"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute></attributes></element><element name="ModeRequestQueue:VarArray" scope="public" xmi:idref="EAID_1a4bfcce0d9b189ee94241961ae7d4c4" xmi:type="uml:Object"><properties documentation="Variable to store an immediate mode request which must be queued because of a current active arbitration." isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><attributes><attribute name="ModeRequestQueue" scope="Private" xmi:idref="EAID_0b35538d728f8187bb167fce024116be"><Constraints><Constraint name="IndirectableFeatureDeactivationCondition" notes="No immediate user in configuration." type="Pre-condition"/></Constraints><documentation value="Variable to store an immediate mode request which must be queued because of a current active arbitration."/><properties collection="true"/></attribute><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_409f16d7795357f76be8bf580b76fbac"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute></attributes></element><element name="NmState:VarArray" scope="public" xmi:idref="EAID_d8394a9f0893f71b130b48a13c06d820" xmi:type="uml:Object"><properties documentation="Variable to store current mode of BswMNmIndication mode request ports." isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><attributes><attribute name="NmState" scope="Private" xmi:idref="EAID_404bdeae554e1e95039bcf4f450f49d1"><Constraints/><documentation value="Variable to store current mode of BswMNmIndication mode request ports."/><properties collection="true"/></attribute><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_c4b477b4779f238564ab1881eb9890c8"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute></attributes></element><element name="NvMBlockState:VarArray" scope="public" xmi:idref="EAID_c9a206cfb74bd3118a17f6a808a5ab7a" xmi:type="uml:Object"><properties documentation="Variable to store current mode of BswMNvMRequest mode request ports." isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><attributes><attribute name="NvMBlockState" scope="Private" xmi:idref="EAID_8fea0c1539f7b98d2f2d09fdbe451098"><Constraints/><documentation value="Variable to store current mode of BswMNvMRequest mode request ports."/><properties collection="true"/></attribute><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_57dba598984142df0de8339b0e8fb7b1"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute></attributes></element><element name="NvMJobState:VarArray" scope="public" xmi:idref="EAID_d0d31ae91933181268654c928f2fb086" xmi:type="uml:Object"><properties documentation="Variable to store current mode of BswMNvMJobModeIndication mode request ports." isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><attributes><attribute name="NvMJobState" scope="Private" xmi:idref="EAID_47882734ef57dc9075b95343675e8e1f"><Constraints/><documentation value="Variable to store current mode of BswMNvMJobModeIndication mode request ports."/><properties collection="true"/></attribute><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_1f2f6eddf3098ed446412bd469e2181e"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute></attributes></element><element name="PduRRxIndicationState:VarArray" scope="public" xmi:idref="EAID_f8c887515503bedbdce88a68931a2ac6" xmi:type="uml:Object"><properties documentation="Variable to store current mode of BswMPduRRxIndication mode request ports." isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><attributes><attribute name="PduRRxIndicationState" scope="Private" xmi:idref="EAID_2386fece77a316ced048343d45458a6e"><Constraints/><documentation value="Variable to store current mode of BswMPduRRxIndication mode request ports."/><properties collection="true"/></attribute><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_9584b96cdd97bd73550211698084eb8f"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute></attributes></element><element name="PduRTpRxIndicationState:VarArray" scope="public" xmi:idref="EAID_b9aabb503e0211b4d456ce4eb38eb8fa" xmi:type="uml:Object"><properties documentation="Variable to store current mode of BswMPduRTpRxIndication mode request ports." isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><attributes><attribute name="PduRTpRxIndicationState" scope="Private" xmi:idref="EAID_3db805439e5cd48dfcdfc535cd91da46"><Constraints/><documentation value="Variable to store current mode of BswMPduRTpRxIndication mode request ports."/><properties collection="true"/></attribute><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_d98a20f5ccae08bad2a43fba9b71bfc0"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute></attributes></element><element name="PduRTpStartOfReceptionState:VarArray" scope="public" xmi:idref="EAID_3c497359d34b2ff8c0e078f087cf6eac" xmi:type="uml:Object"><properties documentation="Variable to store current mode of BswMPduRTpStartOfReception mode request ports." isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><attributes><attribute name="PduRTpStartOfReceptionState" scope="Private" xmi:idref="EAID_ce9cd2307b59c2dc3c14ec98cc30f0eb"><Constraints/><documentation value="Variable to store current mode of BswMPduRTpStartOfReception mode request ports."/><properties collection="true"/></attribute><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_c67eb608085ee7c286440a66c4a003b7"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute></attributes></element><element name="PduRTpTxConfirmationState:VarArray" scope="public" xmi:idref="EAID_17e562fb8c0fb0e0271ef6133a83dd5e" xmi:type="uml:Object"><properties documentation="Variable to store current mode of BswMPduRTpTxConfirmation mode request ports." isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><attributes><attribute name="PduRTpTxConfirmationState" scope="Private" xmi:idref="EAID_c846cac01228f1c9d7f49011d1e1c153"><Constraints/><documentation value="Variable to store current mode of BswMPduRTpTxConfirmation mode request ports."/><properties collection="true"/></attribute><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_1791bf043ee6791c22ff811a46cc2a46"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute></attributes></element><element name="PduRTransmitState:VarArray" scope="public" xmi:idref="EAID_3fe61c0afd7ae951b47f2fe4ed7a45fe" xmi:type="uml:Object"><properties documentation="Variable to store current mode of BswMPduRTransmit mode request ports." isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><attributes><attribute name="PduRTransmitState" scope="Private" xmi:idref="EAID_a3f475fdd52dc315e1fb3c924ae5872b"><Constraints/><documentation value="Variable to store current mode of BswMPduRTransmit mode request ports."/><properties collection="true"/></attribute><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_807135d6e253f9930ef107414742a657"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute></attributes></element><element name="PduRTxConfirmationState:VarArray" scope="public" xmi:idref="EAID_bf3a554c297cd87ac58a84572b9c8e26" xmi:type="uml:Object"><properties documentation="Variable to store current mode of BswMPduRTxConfirmation mode request ports." isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><attributes><attribute name="PduRTxConfirmationState" scope="Private" xmi:idref="EAID_367493bef07a7182459334f27a07af87"><Constraints/><documentation value="Variable to store current mode of BswMPduRTxConfirmation mode request ports."/><properties collection="true"/></attribute><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_32d270b2434d85dcc2fbee46f08d84f0"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute></attributes></element><element name="RuleStates:VarArray" scope="public" xmi:idref="EAID_deb4cd790a8d925299ba0870fdbd0d09" xmi:type="uml:Object"><properties documentation="Stores the last execution state of the rule." isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><attributes><attribute name="RuleStates" scope="Private" xmi:idref="EAID_7a0a6f69c06a3e990d581ef1470b5dd6"><Constraints/><documentation value="Stores the last execution state of the rule."/><properties collection="true"/></attribute><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_fd19700b3d0d4efd2ce23e0d63c96aae"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute></attributes></element><element name="SdClientServiceState:VarArray" scope="public" xmi:idref="EAID_98c4b1bf6f3f551c791611e1ad670c91" xmi:type="uml:Object"><properties documentation="Variable to store current mode of BswMSdClientServiceCurrentState mode request ports." isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><attributes><attribute name="SdClientServiceState" scope="Private" xmi:idref="EAID_2282a34c521e5d581ed4fd5574bfaa38"><Constraints/><documentation value="Variable to store current mode of BswMSdClientServiceCurrentState mode request ports."/><properties collection="true"/></attribute><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_39df12dec102310a8648ff59ddf7fd4d"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute></attributes></element><element name="SdConsumedEventState:VarArray" scope="public" xmi:idref="EAID_f47838c62158a1f397168af542485878" xmi:type="uml:Object"><properties documentation="Variable to store current mode of BswMSdConsumedEventGroupCurrentState mode request ports." isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><attributes><attribute name="SdConsumedEventState" scope="Private" xmi:idref="EAID_5475db494826471b73c6b0c783098728"><Constraints/><documentation value="Variable to store current mode of BswMSdConsumedEventGroupCurrentState mode request ports."/><properties collection="true"/></attribute><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_53eaa7c834c277d14083d50e1c23f7c3"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute></attributes></element><element name="SdEventHandlerState:VarArray" scope="public" xmi:idref="EAID_94ee2d14d35f6272f20cd647c3e6d7c8" xmi:type="uml:Object"><properties documentation="Variable to store current mode of BswMSdEventHandlerCurrentState mode request ports." isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><attributes><attribute name="SdEventHandlerState" scope="Private" xmi:idref="EAID_c5751b8f05c0fcee5d0c3422e04b8056"><Constraints/><documentation value="Variable to store current mode of BswMSdEventHandlerCurrentState mode request ports."/><properties collection="true"/></attribute><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_6e264bded67fc14e01974e80ad1196d3"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute></attributes></element><element name="TimerState:VarArray" scope="public" xmi:idref="EAID_258d98afc41927a84cb6eb39ca23cef6" xmi:type="uml:Object"><properties documentation="Variable to store current state of BswMTimer (STARTED, STOPPER OR EXPIRED)." isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><attributes><attribute name="TimerState" scope="Private" xmi:idref="EAID_22337e387f627985bed5d380cf4589fd"><Constraints/><documentation value="Variable to store current state of BswMTimer (STARTED, STOPPER OR EXPIRED)."/><properties collection="true"/></attribute><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_858ec43734642d804ea6112d25a44977"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute></attributes></element><element name="TimerValue:VarArray" scope="public" xmi:idref="EAID_727f53355f4ed7c375697933a1a1f303" xmi:type="uml:Object"><properties documentation="Variable to store current timer values." isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><attributes><attribute name="TimerValue" scope="Private" xmi:idref="EAID_dedfe4178c8295dd6401d1eb6131936c"><Constraints/><documentation value="Variable to store current timer values."/><properties collection="true"/></attribute><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_b20536c2d1533b1415d99aaa7537005d"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute></attributes></element><element name="WdgMState:VarArray" scope="public" xmi:idref="EAID_d2a37ea767b79ca1d06fc8f6ada51ea6" xmi:type="uml:Object"><properties documentation="Variable to store current mode of BswMWdgMRequestPartitionReset mode request ports." isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><attributes><attribute name="WdgMState" scope="Private" xmi:idref="EAID_9352d9e7b087dffdc368e82b9743adc9"><Constraints/><documentation value="Variable to store current mode of BswMWdgMRequestPartitionReset mode request ports."/><properties collection="true"/></attribute><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_09b79f99c2e5cb93f9bfe63912bfb440"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute></attributes></element><element name="ComMInitiateResetState:Var" scope="public" xmi:idref="EAID_7871b549ee45cf280b89538355694439" xmi:type="uml:Object"><properties documentation="Variable to store the current mode of the BswMComMInitiateReset mode request port." isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><attributes><attribute name="ComMInitiateResetState" scope="Private" xmi:idref="EAID_488183c759d349cc3a6de2292fc85fdf"><Constraints><Constraint name="IndirectableFeatureDeactivationCondition" notes="No Mode Request for BswMComMInitiateReset configured." type="Pre-condition"/></Constraints><documentation value="Variable to store the current mode of the BswMComMInitiateReset mode request port."/><properties/></attribute><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_f9e3afc9677eff9561a024d108b21528"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute></attributes></element><element name="DcmApplUpdateState:Var" scope="public" xmi:idref="EAID_779fcecad28f9de2b7d4541983f52458" xmi:type="uml:Object"><properties documentation="Variable to store the current mode of the BswMDcmApplicationUpdatedIndication mode request port." isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><attributes><attribute name="DcmApplUpdateState" scope="Private" xmi:idref="EAID_454ed94cc9d73a499a77d58976b549f1"><Constraints><Constraint name="IndirectableFeatureDeactivationCondition" notes="No Mode Request for BswMDcmApplicationUpdatedIndication configured." type="Pre-condition"/></Constraints><documentation value="Variable to store the current mode of the BswMDcmApplicationUpdatedIndication mode request port."/><properties/></attribute><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_6d8e546821c2214bb8f79b66748cd6cb"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute></attributes></element><element name="EcuMModeState:Var" scope="public" xmi:idref="EAID_c830edd274a79de99a8206406ff45b8e" xmi:type="uml:Object"><properties documentation="Variable to store the current mode of the BswMEcuMIndication mode request port." isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><attributes><attribute name="EcuMModeState" scope="Private" xmi:idref="EAID_74173f13641ee5eb4e328888fae01048"><Constraints><Constraint name="IndirectableFeatureDeactivationCondition" notes="No Mode Request for BswMEcuMIndication configured." type="Pre-condition"/></Constraints><documentation value="Variable to store the current mode of the BswMEcuMIndication mode request port."/><properties/></attribute><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_d564edea556ce08e6cf3721325702f41"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute></attributes></element><element name="ForcedActionListPriority:Var" scope="public" xmi:idref="EAID_33a8db8054dbd4b983e678109e55b496" xmi:type="uml:Object"><attributes><attribute name="ForcedActionListPriority" scope="Private" xmi:idref="EAID_3c692450a9850c2596ad1c65f50b72cc"><Constraints/><properties/></attribute><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_af065967029129e3a8906e5cdd585718"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute></attributes></element><element name="Initialized:Var" scope="public" xmi:idref="EAID_045c79f73ab47665923017576a0b630d" xmi:type="uml:Object"><attributes><attribute name="Initialized" scope="Private" xmi:idref="EAID_8be96278268205bdeaa9aac5b1366ea3"><Constraints/><properties/></attribute><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_1cd452f35061813df58fa6a4b1cf3e69"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute></attributes></element><element name="J1939DcmState:Var" scope="public" xmi:idref="EAID_062280957fae9e6d518edca224ebd25e" xmi:type="uml:Object"><properties documentation="Variable to store the current mode of the BswMJ1939DcmBroadcastStatus mode request port." isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><attributes><attribute name="J1939DcmState" scope="Private" xmi:idref="EAID_c02c9e53169445767776da2cae9e543e"><Constraints><Constraint name="IndirectableFeatureDeactivationCondition" notes="No Mode Request for BswMJ1939DcmBroadcastStatus configured." type="Pre-condition"/></Constraints><documentation value="Variable to store the current mode of the BswMJ1939DcmBroadcastStatus mode request port."/><properties/></attribute><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_254a9cb76543a5dd5e34307638ce8959"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute></attributes></element><element name="LengthOfActionListPriorityQueue:Var" scope="public" xmi:idref="EAID_05996e1f9307c65223f7fb1b48ec8f0e" xmi:type="uml:Object"><attributes><attribute name="LengthOfActionListPriorityQueue" scope="Private" xmi:idref="EAID_1399df6e1e0ac17a2e8d8d4ba281d437"><Constraints><Constraint name="IndirectableFeatureDeactivationCondition" notes="ActionListPriorityQueue not enabled" type="Pre-condition"/></Constraints><properties/></attribute><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_3a061cab576bce4411b61732a8e3bbf1"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute></attributes></element><element name="QueueSemaphore:Var" scope="public" xmi:idref="EAID_55369514abc8d09c66423d57be99e7f1" xmi:type="uml:Object"><attributes><attribute name="QueueSemaphore" scope="Private" xmi:idref="EAID_8f1d4b7059b44c79682a136223383c55"><Constraints/><properties/></attribute><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_87696b56ef3ebc73573a462c9b18d91c"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute></attributes></element><element name="QueueWritten:Var" scope="public" xmi:idref="EAID_3d47a5d06b2acc76d9a20050e88405e7" xmi:type="uml:Object"><attributes><attribute name="QueueWritten" scope="Private" xmi:idref="EAID_7a26db709c0375b0ca1035b389fee503"><Constraints/><properties/></attribute><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_43f013fdd20910b5b2ed8085ac188cee"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute></attributes></element></elements><connectors><connector xmi:idref="EAID_b84998561e2b88ca10e09aeb03bf5add"><source xmi:idref="EAID_a21ade727c7ce9e2b2c47a38b3f50d4c"><model name="CanSMChannelMapping" type="Object"/><type aggregation="none"/></source><target xmi:idref="EAID_736b23f87e4f84ab6f4f112a4c275d56"><model name="ImmediateUser" type="Object"/><type aggregation="none" multiplicity="0..*"/></target><properties direction="Source -&gt; Destination" ea_type="Association"/><labels mt="indirection" rb="0..*"/></connector><connector xmi:idref="EAID_9fabb08ba59f6a04b64dee1bbd12fc01"><source xmi:idref="EAID_449bc0813b49dcbf8d734ffd0f7ceae1"><model name="ComMChannelMapping" type="Object"/><type aggregation="none"/></source><target xmi:idref="EAID_736b23f87e4f84ab6f4f112a4c275d56"><model name="ImmediateUser" type="Object"/><type aggregation="none" multiplicity="0..*"/></target><properties direction="Source -&gt; Destination" ea_type="Association"/><labels mt="indirection" rb="0..*"/></connector><connector xmi:idref="EAID_e14d4545f45ddb55247f0a61eca51391"><source xmi:idref="EAID_b034e05ba7519845f628fef9bdd2a277"><model name="ComMInitiateResetMapping" type="Object"/><type aggregation="none"/></source><target xmi:idref="EAID_736b23f87e4f84ab6f4f112a4c275d56"><model name="ImmediateUser" type="Object"/><type aggregation="none" multiplicity="0..*"/></target><properties direction="Source -&gt; Destination" ea_type="Association"/><labels mt="indirection" rb="0..*"/></connector><connector xmi:idref="EAID_bcd306f659856a45c8698e428d07a2d3"><source xmi:idref="EAID_c6af116831d0a86da42785bf281317c3"><model name="ComMPncMapping" type="Object"/><type aggregation="none"/></source><target xmi:idref="EAID_736b23f87e4f84ab6f4f112a4c275d56"><model name="ImmediateUser" type="Object"/><type aggregation="none" multiplicity="0..*"/></target><properties direction="Source -&gt; Destination" ea_type="Association"/><labels mt="indirection" rb="0..*"/></connector><connector xmi:idref="EAID_f7f1ee45893b76309a765f4cbd3d7994"><source xmi:idref="EAID_7bf126176b70ddd82d12cae610196108"><model name="DcmApplUpdateMapping" type="Object"/><type aggregation="none"/></source><target xmi:idref="EAID_736b23f87e4f84ab6f4f112a4c275d56"><model name="ImmediateUser" type="Object"/><type aggregation="none" multiplicity="0..*"/></target><properties direction="Source -&gt; Destination" ea_type="Association"/><labels mt="indirection" rb="0..*"/></connector><connector xmi:idref="EAID_b1a183270eeeecbf953a02eb0831999f"><source xmi:idref="EAID_07966b43865a440f0013855288098f51"><model name="DcmComMapping" type="Object"/><type aggregation="none"/></source><target xmi:idref="EAID_736b23f87e4f84ab6f4f112a4c275d56"><model name="ImmediateUser" type="Object"/><type aggregation="none" multiplicity="0..*"/></target><properties direction="Source -&gt; Destination" ea_type="Association"/><labels mt="indirection" rb="0..*"/></connector><connector xmi:idref="EAID_b80879ec699ed32da3fab6fc079bd379"><source xmi:idref="EAID_73416e6903dc3163e0c6f257e0043235"><model name="DeferredRules" type="Object"/><type aggregation="none"/></source><target xmi:idref="EAID_6d3ab65f9bb3e78db2ddcf0fd6277073"><model name="Rules" type="Object"/><type aggregation="none" multiplicity="1..1"/></target><properties direction="Source -&gt; Destination" ea_type="Association"/><labels mt="indirection" rb="1..1"/></connector><connector xmi:idref="EAID_7080a0c1cff5520ed5c8b38832e6bb78"><source xmi:idref="EAID_426c54d97548b9f37f228e3979caddc5"><model name="EcuMModeMapping" type="Object"/><type aggregation="none"/></source><target xmi:idref="EAID_736b23f87e4f84ab6f4f112a4c275d56"><model name="ImmediateUser" type="Object"/><type aggregation="none" multiplicity="0..*"/></target><properties direction="Source -&gt; Destination" ea_type="Association"/><labels mt="indirection" rb="0..*"/></connector><connector xmi:idref="EAID_ee7ac79fb81200e18d4fee9c45e8caf7"><source xmi:idref="EAID_a9a1f120ae1ea463237fc9e424063a9a"><model name="EcuMRunRequestMapping" type="Object"/><type aggregation="none"/></source><target xmi:idref="EAID_736b23f87e4f84ab6f4f112a4c275d56"><model name="ImmediateUser" type="Object"/><type aggregation="none" multiplicity="0..*"/></target><properties direction="Source -&gt; Destination" ea_type="Association"/><labels mt="indirection" rb="0..*"/></connector><connector xmi:idref="EAID_699748f3387f40a1a20c6f44ad46fe2a"><source xmi:idref="EAID_2d308396852199787b2c9d011d2ef9dd"><model name="EcuMWakeupMapping" type="Object"/><type aggregation="none"/></source><target xmi:idref="EAID_736b23f87e4f84ab6f4f112a4c275d56"><model name="ImmediateUser" type="Object"/><type aggregation="none" multiplicity="0..*"/></target><properties direction="Source -&gt; Destination" ea_type="Association"/><labels mt="indirection" rb="0..*"/></connector><connector xmi:idref="EAID_8e0fae51e5cf2f4bd465899b5976bb09"><source xmi:idref="EAID_71a7eb72f8e3d8920cbc1acebd26c718"><model name="EthIfPortMapping" type="Object"/><type aggregation="none"/></source><target xmi:idref="EAID_736b23f87e4f84ab6f4f112a4c275d56"><model name="ImmediateUser" type="Object"/><type aggregation="none" multiplicity="0..*"/></target><properties direction="Source -&gt; Destination" ea_type="Association"/><labels mt="indirection" rb="0..*"/></connector><connector xmi:idref="EAID_9a8b481f99c2beb1facf7fc4652228e6"><source xmi:idref="EAID_aca37cd1d4b0d25f4584736607047a3f"><model name="EthSMMapping" type="Object"/><type aggregation="none"/></source><target xmi:idref="EAID_736b23f87e4f84ab6f4f112a4c275d56"><model name="ImmediateUser" type="Object"/><type aggregation="none" multiplicity="0..*"/></target><properties direction="Source -&gt; Destination" ea_type="Association"/><labels mt="indirection" rb="0..*"/></connector><connector xmi:idref="EAID_05798b5cb848308b9967f7cb995fa63a"><source xmi:idref="EAID_8da2460f8e273616060821161f0facc6"><model name="FrSMMapping" type="Object"/><type aggregation="none"/></source><target xmi:idref="EAID_736b23f87e4f84ab6f4f112a4c275d56"><model name="ImmediateUser" type="Object"/><type aggregation="none" multiplicity="0..*"/></target><properties direction="Source -&gt; Destination" ea_type="Association"/><labels mt="indirection" rb="0..*"/></connector><connector xmi:idref="EAID_ea7c6a8da4e65dc08ff7ecd7e5496e0f"><source xmi:idref="EAID_0ef8bd3922191bfc24fa7689a2d52cab"><model name="GenericMapping" type="Object"/><type aggregation="none"/></source><target xmi:idref="EAID_736b23f87e4f84ab6f4f112a4c275d56"><model name="ImmediateUser" type="Object"/><type aggregation="none" multiplicity="0..*"/></target><properties direction="Source -&gt; Destination" ea_type="Association"/><labels mt="indirection" rb="0..*"/></connector><connector xmi:idref="EAID_9285f972fd67129b5089bd5e0da8b278"><source xmi:idref="EAID_736b23f87e4f84ab6f4f112a4c275d56"><model name="ImmediateUser" type="Object"/><type aggregation="none"/></source><target xmi:idref="EAID_0d076b193bd4dca769a6178cd2d277e9"><model name="RulesInd" type="Object"/><type aggregation="none" multiplicity="0..*"/></target><properties direction="Source -&gt; Destination" ea_type="Association"/><labels mt="indirection" rb="0..*"/></connector><connector xmi:idref="EAID_b1c6ccceda419ed367fd7290db40bc07"><source xmi:idref="EAID_736b23f87e4f84ab6f4f112a4c275d56"><model name="ImmediateUser" type="Object"/><type aggregation="none"/></source><target xmi:idref="EAID_6d3ab65f9bb3e78db2ddcf0fd6277073"><model name="Rules" type="Object"/><type aggregation="none" multiplicity="1..*"/></target><properties direction="Source -&gt; Destination" ea_type="Association"/><labels mt="indirection" rb="1..*"/></connector><connector xmi:idref="EAID_2e636905f5737bac3b0d9ec0c73c48a8"><source xmi:idref="EAID_5c9b557e25f4453a916526c349760ba3"><model name="J1939DcmMapping" type="Object"/><type aggregation="none"/></source><target xmi:idref="EAID_736b23f87e4f84ab6f4f112a4c275d56"><model name="ImmediateUser" type="Object"/><type aggregation="none" multiplicity="0..*"/></target><properties direction="Source -&gt; Destination" ea_type="Association"/><labels mt="indirection" rb="0..*"/></connector><connector xmi:idref="EAID_99720c9765e4ce40a3e5f8000da19078"><source xmi:idref="EAID_f564c0c37c31d9acb7cfa606b606be94"><model name="J1939NmMapping" type="Object"/><type aggregation="none"/></source><target xmi:idref="EAID_736b23f87e4f84ab6f4f112a4c275d56"><model name="ImmediateUser" type="Object"/><type aggregation="none" multiplicity="0..*"/></target><properties direction="Source -&gt; Destination" ea_type="Association"/><labels mt="indirection" rb="0..*"/></connector><connector xmi:idref="EAID_3a8ee9088a69ebf59d30e7aa6b6772ab"><source xmi:idref="EAID_2fdddeed51c0a69f3bede5f95c808bfc"><model name="LinSMMapping" type="Object"/><type aggregation="none"/></source><target xmi:idref="EAID_736b23f87e4f84ab6f4f112a4c275d56"><model name="ImmediateUser" type="Object"/><type aggregation="none" multiplicity="0..*"/></target><properties direction="Source -&gt; Destination" ea_type="Association"/><labels mt="indirection" rb="0..*"/></connector><connector xmi:idref="EAID_09f6e54fc2911490337d1bf26376ee18"><source xmi:idref="EAID_2ddd09a93c8171fba85908a632445594"><model name="LinScheduleEndMapping" type="Object"/><type aggregation="none"/></source><target xmi:idref="EAID_736b23f87e4f84ab6f4f112a4c275d56"><model name="ImmediateUser" type="Object"/><type aggregation="none" multiplicity="0..*"/></target><properties direction="Source -&gt; Destination" ea_type="Association"/><labels mt="indirection" rb="0..*"/></connector><connector xmi:idref="EAID_a2fd97f859a9ff97b20442bb8e4bb315"><source xmi:idref="EAID_c825ab2f2c373c59418cc35185a0d67d"><model name="LinScheduleMapping" type="Object"/><type aggregation="none"/></source><target xmi:idref="EAID_736b23f87e4f84ab6f4f112a4c275d56"><model name="ImmediateUser" type="Object"/><type aggregation="none" multiplicity="0..*"/></target><properties direction="Source -&gt; Destination" ea_type="Association"/><labels mt="indirection" rb="0..*"/></connector><connector xmi:idref="EAID_cfb118fb5ff29a5a92e49289b2564882"><source xmi:idref="EAID_2f5822e2084d2acc197e7ef5e1e88125"><model name="LinTPMapping" type="Object"/><type aggregation="none"/></source><target xmi:idref="EAID_736b23f87e4f84ab6f4f112a4c275d56"><model name="ImmediateUser" type="Object"/><type aggregation="none" multiplicity="0..*"/></target><properties direction="Source -&gt; Destination" ea_type="Association"/><labels mt="indirection" rb="0..*"/></connector><connector xmi:idref="EAID_ba03c70195bce6f28207f22f1cd4edd8"><source xmi:idref="EAID_4c25e97bd5cb480accd82325110a2a96"><model name="ModeNotificationMapping" type="Object"/><type aggregation="none"/></source><target xmi:idref="EAID_736b23f87e4f84ab6f4f112a4c275d56"><model name="ImmediateUser" type="Object"/><type aggregation="none" multiplicity="0..*"/></target><properties direction="Source -&gt; Destination" ea_type="Association"/><labels mt="indirection" rb="0..*"/></connector><connector xmi:idref="EAID_a0bacd43bd11475d2ac4e3858318b932"><source xmi:idref="EAID_6a57bf2c090b65ce442c84f95529fef3"><model name="ModeRequestMapping" type="Object"/><type aggregation="none"/></source><target xmi:idref="EAID_736b23f87e4f84ab6f4f112a4c275d56"><model name="ImmediateUser" type="Object"/><type aggregation="none" multiplicity="0..*"/></target><properties direction="Source -&gt; Destination" ea_type="Association"/><labels mt="indirection" rb="0..*"/></connector><connector xmi:idref="EAID_1afb3c71059f2765c61e4ec03ce60f5a"><source xmi:idref="EAID_a60f4f05f55df5ec2e7c53c020d9a47d"><model name="NmMapping" type="Object"/><type aggregation="none"/></source><target xmi:idref="EAID_736b23f87e4f84ab6f4f112a4c275d56"><model name="ImmediateUser" type="Object"/><type aggregation="none" multiplicity="0..*"/></target><properties direction="Source -&gt; Destination" ea_type="Association"/><labels mt="indirection" rb="0..*"/></connector><connector xmi:idref="EAID_358c0e8a9164ca79300fef910e086604"><source xmi:idref="EAID_8054289471090aaf93f03b02401f1ab3"><model name="NvMBlockMapping" type="Object"/><type aggregation="none"/></source><target xmi:idref="EAID_736b23f87e4f84ab6f4f112a4c275d56"><model name="ImmediateUser" type="Object"/><type aggregation="none" multiplicity="0..*"/></target><properties direction="Source -&gt; Destination" ea_type="Association"/><labels mt="indirection" rb="0..*"/></connector><connector xmi:idref="EAID_b67aeef8bfb03841c4068ec3842df3de"><source xmi:idref="EAID_6b0c2255d55263a1c9ca7e897f2ff4ed"><model name="NvMJobMapping" type="Object"/><type aggregation="none"/></source><target xmi:idref="EAID_736b23f87e4f84ab6f4f112a4c275d56"><model name="ImmediateUser" type="Object"/><type aggregation="none" multiplicity="0..*"/></target><properties direction="Source -&gt; Destination" ea_type="Association"/><labels mt="indirection" rb="0..*"/></connector><connector xmi:idref="EAID_5470b4ccba472aa92c0b65b750b0e50c"><source xmi:idref="EAID_dca23268bf9314a308078fdd0f25566f"><model name="PduRRxIndicationMapping" type="Object"/><type aggregation="none"/></source><target xmi:idref="EAID_f8c887515503bedbdce88a68931a2ac6"><model name="PduRRxIndicationState" type="Object"/><type aggregation="none" multiplicity="1..1"/></target><properties direction="Source -&gt; Destination" ea_type="Association"/><labels mt="indirection" rb="1..1"/></connector><connector xmi:idref="EAID_7686b2a95b75de889a5ff6c7688bf7f3"><source xmi:idref="EAID_dca23268bf9314a308078fdd0f25566f"><model name="PduRRxIndicationMapping" type="Object"/><type aggregation="none"/></source><target xmi:idref="EAID_736b23f87e4f84ab6f4f112a4c275d56"><model name="ImmediateUser" type="Object"/><type aggregation="none" multiplicity="0..*"/></target><properties direction="Source -&gt; Destination" ea_type="Association"/><labels mt="indirection" rb="0..*"/></connector><connector xmi:idref="EAID_d4ebdf62d49e65f21d979d8209d14470"><source xmi:idref="EAID_4570df5896ab42d50e3cb8875a98f345"><model name="PduRTpRxIndicationMapping" type="Object"/><type aggregation="none"/></source><target xmi:idref="EAID_b9aabb503e0211b4d456ce4eb38eb8fa"><model name="PduRTpRxIndicationState" type="Object"/><type aggregation="none" multiplicity="1..1"/></target><properties direction="Source -&gt; Destination" ea_type="Association"/><labels mt="indirection" rb="1..1"/></connector><connector xmi:idref="EAID_7e0c2e30a0497b8e9d68bf9000a22f6d"><source xmi:idref="EAID_4570df5896ab42d50e3cb8875a98f345"><model name="PduRTpRxIndicationMapping" type="Object"/><type aggregation="none"/></source><target xmi:idref="EAID_736b23f87e4f84ab6f4f112a4c275d56"><model name="ImmediateUser" type="Object"/><type aggregation="none" multiplicity="0..*"/></target><properties direction="Source -&gt; Destination" ea_type="Association"/><labels mt="indirection" rb="0..*"/></connector><connector xmi:idref="EAID_73c8d511ce71be2c587d1ee9f7301e6f"><source xmi:idref="EAID_d9d96d1680ba023d123166ec09c0ad26"><model name="PduRTpStartOfReceptionMapping" type="Object"/><type aggregation="none"/></source><target xmi:idref="EAID_3c497359d34b2ff8c0e078f087cf6eac"><model name="PduRTpStartOfReceptionState" type="Object"/><type aggregation="none" multiplicity="1..1"/></target><properties direction="Source -&gt; Destination" ea_type="Association"/><labels mt="indirection" rb="1..1"/></connector><connector xmi:idref="EAID_6c99ab46d843271bcd4cb69b3138077c"><source xmi:idref="EAID_d9d96d1680ba023d123166ec09c0ad26"><model name="PduRTpStartOfReceptionMapping" type="Object"/><type aggregation="none"/></source><target xmi:idref="EAID_736b23f87e4f84ab6f4f112a4c275d56"><model name="ImmediateUser" type="Object"/><type aggregation="none" multiplicity="0..*"/></target><properties direction="Source -&gt; Destination" ea_type="Association"/><labels mt="indirection" rb="0..*"/></connector><connector xmi:idref="EAID_55be133e605a025f2946e391b7bb2a1a"><source xmi:idref="EAID_25c789e736b4f0674042f030daef27a5"><model name="PduRTpTxConfirmationMapping" type="Object"/><type aggregation="none"/></source><target xmi:idref="EAID_17e562fb8c0fb0e0271ef6133a83dd5e"><model name="PduRTpTxConfirmationState" type="Object"/><type aggregation="none" multiplicity="1..1"/></target><properties direction="Source -&gt; Destination" ea_type="Association"/><labels mt="indirection" rb="1..1"/></connector><connector xmi:idref="EAID_066d9dcc663aa623cb684276ecdd8e63"><source xmi:idref="EAID_25c789e736b4f0674042f030daef27a5"><model name="PduRTpTxConfirmationMapping" type="Object"/><type aggregation="none"/></source><target xmi:idref="EAID_736b23f87e4f84ab6f4f112a4c275d56"><model name="ImmediateUser" type="Object"/><type aggregation="none" multiplicity="0..*"/></target><properties direction="Source -&gt; Destination" ea_type="Association"/><labels mt="indirection" rb="0..*"/></connector><connector xmi:idref="EAID_6f0de7c21e53547a95b0fd12ed1a2fcf"><source xmi:idref="EAID_de0b701f4135993e03cf8835354a29a1"><model name="PduRTransmitMapping" type="Object"/><type aggregation="none"/></source><target xmi:idref="EAID_3fe61c0afd7ae951b47f2fe4ed7a45fe"><model name="PduRTransmitState" type="Object"/><type aggregation="none" multiplicity="1..1"/></target><properties direction="Source -&gt; Destination" ea_type="Association"/><labels mt="indirection" rb="1..1"/></connector><connector xmi:idref="EAID_bbaf27b22b6375cf72602a162b9cad90"><source xmi:idref="EAID_de0b701f4135993e03cf8835354a29a1"><model name="PduRTransmitMapping" type="Object"/><type aggregation="none"/></source><target xmi:idref="EAID_736b23f87e4f84ab6f4f112a4c275d56"><model name="ImmediateUser" type="Object"/><type aggregation="none" multiplicity="0..*"/></target><properties direction="Source -&gt; Destination" ea_type="Association"/><labels mt="indirection" rb="0..*"/></connector><connector xmi:idref="EAID_31bd0de8465464b7db2ddce493eecbd3"><source xmi:idref="EAID_a54b478336d897c37a8f7e4377062f5a"><model name="PduRTxConfirmationMapping" type="Object"/><type aggregation="none"/></source><target xmi:idref="EAID_bf3a554c297cd87ac58a84572b9c8e26"><model name="PduRTxConfirmationState" type="Object"/><type aggregation="none" multiplicity="1..1"/></target><properties direction="Source -&gt; Destination" ea_type="Association"/><labels mt="indirection" rb="1..1"/></connector><connector xmi:idref="EAID_1a9cdae7a6111b4ee5771ce0342ce5e0"><source xmi:idref="EAID_a54b478336d897c37a8f7e4377062f5a"><model name="PduRTxConfirmationMapping" type="Object"/><type aggregation="none"/></source><target xmi:idref="EAID_736b23f87e4f84ab6f4f112a4c275d56"><model name="ImmediateUser" type="Object"/><type aggregation="none" multiplicity="0..*"/></target><properties direction="Source -&gt; Destination" ea_type="Association"/><labels mt="indirection" rb="0..*"/></connector><connector xmi:idref="EAID_1961a9588bac8529350a3e7bf68e6f17"><source xmi:idref="EAID_6d3ab65f9bb3e78db2ddcf0fd6277073"><model name="Rules" type="Object"/><type aggregation="none"/></source><target xmi:idref="EAID_deb4cd790a8d925299ba0870fdbd0d09"><model name="RuleStates" type="Object"/><type aggregation="none" multiplicity="1..1"/></target><properties direction="Source -&gt; Destination" ea_type="Association"/><labels mt="indirection" rb="1..1"/></connector><connector xmi:idref="EAID_01b6b6ad9865d7d80390263b83428796"><source xmi:idref="EAID_73ba171504d9bc327fa65885ba2f3e46"><model name="SdClientServiceMapping" type="Object"/><type aggregation="none"/></source><target xmi:idref="EAID_736b23f87e4f84ab6f4f112a4c275d56"><model name="ImmediateUser" type="Object"/><type aggregation="none" multiplicity="0..*"/></target><properties direction="Source -&gt; Destination" ea_type="Association"/><labels mt="indirection" rb="0..*"/></connector><connector xmi:idref="EAID_d875ecf9a67e8b2f0adaf0ea90374bab"><source xmi:idref="EAID_1a406a988d77b67daad6f863e0d267d6"><model name="SdConsumedEventMapping" type="Object"/><type aggregation="none"/></source><target xmi:idref="EAID_736b23f87e4f84ab6f4f112a4c275d56"><model name="ImmediateUser" type="Object"/><type aggregation="none" multiplicity="0..*"/></target><properties direction="Source -&gt; Destination" ea_type="Association"/><labels mt="indirection" rb="0..*"/></connector><connector xmi:idref="EAID_e5e90b12d5bfcacbfe7730e912178c59"><source xmi:idref="EAID_8bcd80b7d0c042d31449cd6be786b997"><model name="SdEventHandlerMapping" type="Object"/><type aggregation="none"/></source><target xmi:idref="EAID_736b23f87e4f84ab6f4f112a4c275d56"><model name="ImmediateUser" type="Object"/><type aggregation="none" multiplicity="0..*"/></target><properties direction="Source -&gt; Destination" ea_type="Association"/><labels mt="indirection" rb="0..*"/></connector><connector xmi:idref="EAID_75cb43aa74a87fbc9a5db1b7443a554c"><source xmi:idref="EAID_a366686ddfeaf0470ea2b08764eb9e18"><model name="WdgMMapping" type="Object"/><type aggregation="none"/></source><target xmi:idref="EAID_736b23f87e4f84ab6f4f112a4c275d56"><model name="ImmediateUser" type="Object"/><type aggregation="none" multiplicity="0..*"/></target><properties direction="Source -&gt; Destination" ea_type="Association"/><labels mt="indirection" rb="0..*"/></connector><connector xmi:idref="EAID_68dd67f33eaf08f260da73c0111d5696"><source xmi:idref="EAID_8d980ae5825cc02ef281dc2498ff8276"><model name="ActionListPriorityQueue" type="Object"/><type aggregation="none"/></source><target xmi:idref="EAID_8fffe8b4c6b0bb3cff10a8f4dda834f9"><model name="ActionLists" type="Object"/><type aggregation="none"/></target><properties direction="Unspecified" ea_type="Association"/><labels mt="VarIndirectableSizeByConstIndirecableRelation"/></connector><connector xmi:idref="EAID_a2f61eda6fbafa28e9e9341b2e52d2c1"><source xmi:idref="EAID_77f1f5474de5c097c5f46090d7b95e22"><model name="ActionListQueue" type="Object"/><type aggregation="none"/></source><target xmi:idref="EAID_8fffe8b4c6b0bb3cff10a8f4dda834f9"><model name="ActionLists" type="Object"/><type aggregation="none"/></target><properties direction="Unspecified" ea_type="Association"/><labels mt="VarIndirectableSizeByConstIndirecableRelation"/></connector><connector xmi:idref="EAID_90b1c11163b6886d3f8df75d0222b12b"><source xmi:idref="EAID_1a4bfcce0d9b189ee94241961ae7d4c4"><model name="ModeRequestQueue" type="Object"/><type aggregation="none"/></source><target xmi:idref="EAID_736b23f87e4f84ab6f4f112a4c275d56"><model name="ImmediateUser" type="Object"/><type aggregation="none"/></target><properties direction="Unspecified" ea_type="Association"/><labels mt="VarIndirectableSizeByConstIndirecableRelation"/></connector></connectors><diagrams><diagram xmi:id="EAID_cd418e6ee0abdf000dfbf7d140bc8811"><elements><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_8fffe8b4c6b0bb3cff10a8f4dda834f9"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_a21ade727c7ce9e2b2c47a38b3f50d4c"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_b84998561e2b88ca10e09aeb03bf5add"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_449bc0813b49dcbf8d734ffd0f7ceae1"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_9fabb08ba59f6a04b64dee1bbd12fc01"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_b034e05ba7519845f628fef9bdd2a277"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_e14d4545f45ddb55247f0a61eca51391"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_c6af116831d0a86da42785bf281317c3"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_bcd306f659856a45c8698e428d07a2d3"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_7bf126176b70ddd82d12cae610196108"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_f7f1ee45893b76309a765f4cbd3d7994"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_07966b43865a440f0013855288098f51"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_b1a183270eeeecbf953a02eb0831999f"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_73416e6903dc3163e0c6f257e0043235"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_b80879ec699ed32da3fab6fc079bd379"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_426c54d97548b9f37f228e3979caddc5"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_7080a0c1cff5520ed5c8b38832e6bb78"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_a9a1f120ae1ea463237fc9e424063a9a"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_ee7ac79fb81200e18d4fee9c45e8caf7"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_2d308396852199787b2c9d011d2ef9dd"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_699748f3387f40a1a20c6f44ad46fe2a"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_71a7eb72f8e3d8920cbc1acebd26c718"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_8e0fae51e5cf2f4bd465899b5976bb09"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_aca37cd1d4b0d25f4584736607047a3f"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_9a8b481f99c2beb1facf7fc4652228e6"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_8da2460f8e273616060821161f0facc6"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_05798b5cb848308b9967f7cb995fa63a"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_0ef8bd3922191bfc24fa7689a2d52cab"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_ea7c6a8da4e65dc08ff7ecd7e5496e0f"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_736b23f87e4f84ab6f4f112a4c275d56"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_9285f972fd67129b5089bd5e0da8b278"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_b1c6ccceda419ed367fd7290db40bc07"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_5c9b557e25f4453a916526c349760ba3"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_2e636905f5737bac3b0d9ec0c73c48a8"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_f564c0c37c31d9acb7cfa606b606be94"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_99720c9765e4ce40a3e5f8000da19078"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_2fdddeed51c0a69f3bede5f95c808bfc"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_3a8ee9088a69ebf59d30e7aa6b6772ab"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_2ddd09a93c8171fba85908a632445594"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_09f6e54fc2911490337d1bf26376ee18"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_c825ab2f2c373c59418cc35185a0d67d"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_a2fd97f859a9ff97b20442bb8e4bb315"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_2f5822e2084d2acc197e7ef5e1e88125"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_cfb118fb5ff29a5a92e49289b2564882"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_4c25e97bd5cb480accd82325110a2a96"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_ba03c70195bce6f28207f22f1cd4edd8"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_6a57bf2c090b65ce442c84f95529fef3"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_a0bacd43bd11475d2ac4e3858318b932"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_a60f4f05f55df5ec2e7c53c020d9a47d"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_1afb3c71059f2765c61e4ec03ce60f5a"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_8054289471090aaf93f03b02401f1ab3"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_358c0e8a9164ca79300fef910e086604"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_6b0c2255d55263a1c9ca7e897f2ff4ed"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_b67aeef8bfb03841c4068ec3842df3de"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_dca23268bf9314a308078fdd0f25566f"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_5470b4ccba472aa92c0b65b750b0e50c"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_7686b2a95b75de889a5ff6c7688bf7f3"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_4570df5896ab42d50e3cb8875a98f345"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_d4ebdf62d49e65f21d979d8209d14470"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_7e0c2e30a0497b8e9d68bf9000a22f6d"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_d9d96d1680ba023d123166ec09c0ad26"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_73c8d511ce71be2c587d1ee9f7301e6f"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_6c99ab46d843271bcd4cb69b3138077c"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_25c789e736b4f0674042f030daef27a5"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_55be133e605a025f2946e391b7bb2a1a"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_066d9dcc663aa623cb684276ecdd8e63"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_de0b701f4135993e03cf8835354a29a1"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_6f0de7c21e53547a95b0fd12ed1a2fcf"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_bbaf27b22b6375cf72602a162b9cad90"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_a54b478336d897c37a8f7e4377062f5a"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_31bd0de8465464b7db2ddce493eecbd3"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_1a9cdae7a6111b4ee5771ce0342ce5e0"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_6d3ab65f9bb3e78db2ddcf0fd6277073"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_1961a9588bac8529350a3e7bf68e6f17"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_73ba171504d9bc327fa65885ba2f3e46"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_01b6b6ad9865d7d80390263b83428796"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_1a406a988d77b67daad6f863e0d267d6"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_d875ecf9a67e8b2f0adaf0ea90374bab"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_8bcd80b7d0c042d31449cd6be786b997"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_e5e90b12d5bfcacbfe7730e912178c59"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_a366686ddfeaf0470ea2b08764eb9e18"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_75cb43aa74a87fbc9a5db1b7443a554c"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_13bce6af878e8272764dd53353384f97"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_4be79be67ff68b1a41e4829ab431c6ab"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_b591a756b0430239f853601eb6ff1d34"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_8d980ae5825cc02ef281dc2498ff8276"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_68dd67f33eaf08f260da73c0111d5696"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_77f1f5474de5c097c5f46090d7b95e22"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_a2f61eda6fbafa28e9e9341b2e52d2c1"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_3c10edb7f4de06c74906aaf0c8aff755"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_d89319db780de5953be15da15b29fea4"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_aab8a9894c175591bb2ed223682446d2"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_d05992d737490f1c22607b265339f321"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_d6b19081a1717e96aca720d19c549d0f"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_ed09cf7dc5ff034db3934bce408ab274"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_290f7efd4a165002388b9d74983797ed"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_99158b66258e6db32e26958d24c2f457"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_6c9ff6840305b8093fcb24c199298b6a"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_171701682fc470d3b63f7c2ca2cb7d41"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_8cfbe97fdd31b179538802e9fe4bc42f"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_31dc458ee6f35226a71bc9888c5f229e"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_a25b859f884aa8eaeea88b06db52a2cf"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_335562ed44a8bc4ec2ab7220067315e8"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_8e541ea950e51db2c18e4e0dce6688c5"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_1a4bfcce0d9b189ee94241961ae7d4c4"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_90b1c11163b6886d3f8df75d0222b12b"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_d8394a9f0893f71b130b48a13c06d820"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_c9a206cfb74bd3118a17f6a808a5ab7a"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_d0d31ae91933181268654c928f2fb086"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_f8c887515503bedbdce88a68931a2ac6"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_b9aabb503e0211b4d456ce4eb38eb8fa"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_3c497359d34b2ff8c0e078f087cf6eac"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_17e562fb8c0fb0e0271ef6133a83dd5e"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_3fe61c0afd7ae951b47f2fe4ed7a45fe"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_bf3a554c297cd87ac58a84572b9c8e26"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_deb4cd790a8d925299ba0870fdbd0d09"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_98c4b1bf6f3f551c791611e1ad670c91"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_f47838c62158a1f397168af542485878"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_94ee2d14d35f6272f20cd647c3e6d7c8"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_258d98afc41927a84cb6eb39ca23cef6"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_727f53355f4ed7c375697933a1a1f303"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_d2a37ea767b79ca1d06fc8f6ada51ea6"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_7871b549ee45cf280b89538355694439"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_779fcecad28f9de2b7d4541983f52458"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_c830edd274a79de99a8206406ff45b8e"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_33a8db8054dbd4b983e678109e55b496"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_045c79f73ab47665923017576a0b630d"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_062280957fae9e6d518edca224ebd25e"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_05996e1f9307c65223f7fb1b48ec8f0e"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_55369514abc8d09c66423d57be99e7f1"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_3d47a5d06b2acc76d9a20050e88405e7"/></elements><model owner="EAPK884b8635344808af88b78d1522f8b3f7" package="EAPK884b8635344808af88b78d1522f8b3f7"/><properties name="All Data and Relations" type="Object"/><style1 value="ShowPrivate=1;ShowProtected=1;ShowPublic=1;HideRelationships=0;Locked=0;Border=1;HighlightForeign=1;PackageContents=1;SequenceNotes=0;ScalePrintImage=0;PPgs.cx=0;PPgs.cy=0;DocSize.cx=791;DocSize.cy=1134;ShowDetails=0;Orientation=P;Zoom=100;ShowTags=0;OpParams=1;VisibleAttributeDetail=0;ShowOpRetType=1;ShowIcons=1;CollabNums=0;HideProps=0;ShowReqs=0;ShowCons=0;PaperSize=9;HideParents=0;UseAlias=0;HideAtts=0;HideOps=0;HideStereo=0;HideElemStereo=0;ShowTests=0;ShowMaint=0;ConnectorNotation=UML 2.1;ExplicitNavigability=0;AdvancedElementProps=1;AdvancedFeatureProps=1;AdvancedConnectorProps=1;ShowNotes=0;SuppressBrackets=0;SuppConnectorLabels=0;PrintPageHeadFoot=0;ShowAsList=0;"/><style2 value="ExcludeRTF=0;DocAll=0;HideQuals=0;AttPkg=1;ShowTests=0;ShowMaint=0;SuppressFOC=1;MatrixActive=0;SwimlanesActive=0;MatrixLineWidth=1;MatrixLocked=0;TConnectorNotation=UML 2.1;TExplicitNavigability=0;AdvancedElementProps=1;AdvancedFeatureProps=1;AdvancedConnectorProps=1;ProfileData=;MDGDgm=;STBLDgm=;ShowNotes=0;VisibleAttributeDetail=0;ShowOpRetType=1;SuppressBrackets=0;SuppConnectorLabels=0;PrintPageHeadFoot=0;ShowAsList=0;"/><swimlanes value="locked=false;orientation=0;width=0;inbar=false;names=false;color=0;bold=false;fcol=0;;cls=0;"/><matrixitems value="locked=false;matrixactive=false;swimlanesactive=false;width=1;"/><extendedProperties/></diagram><diagram xmi:id="EAID_792beb30c3962265132df67cb1a2fdff"><elements><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_8fffe8b4c6b0bb3cff10a8f4dda834f9"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_a21ade727c7ce9e2b2c47a38b3f50d4c"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_b84998561e2b88ca10e09aeb03bf5add"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_449bc0813b49dcbf8d734ffd0f7ceae1"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_9fabb08ba59f6a04b64dee1bbd12fc01"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_b034e05ba7519845f628fef9bdd2a277"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_e14d4545f45ddb55247f0a61eca51391"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_c6af116831d0a86da42785bf281317c3"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_bcd306f659856a45c8698e428d07a2d3"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_7bf126176b70ddd82d12cae610196108"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_f7f1ee45893b76309a765f4cbd3d7994"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_07966b43865a440f0013855288098f51"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_b1a183270eeeecbf953a02eb0831999f"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_73416e6903dc3163e0c6f257e0043235"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_b80879ec699ed32da3fab6fc079bd379"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_426c54d97548b9f37f228e3979caddc5"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_7080a0c1cff5520ed5c8b38832e6bb78"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_a9a1f120ae1ea463237fc9e424063a9a"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_ee7ac79fb81200e18d4fee9c45e8caf7"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_2d308396852199787b2c9d011d2ef9dd"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_699748f3387f40a1a20c6f44ad46fe2a"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_71a7eb72f8e3d8920cbc1acebd26c718"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_8e0fae51e5cf2f4bd465899b5976bb09"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_aca37cd1d4b0d25f4584736607047a3f"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_9a8b481f99c2beb1facf7fc4652228e6"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_8da2460f8e273616060821161f0facc6"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_05798b5cb848308b9967f7cb995fa63a"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_0ef8bd3922191bfc24fa7689a2d52cab"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_ea7c6a8da4e65dc08ff7ecd7e5496e0f"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_736b23f87e4f84ab6f4f112a4c275d56"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_9285f972fd67129b5089bd5e0da8b278"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_b1c6ccceda419ed367fd7290db40bc07"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_5c9b557e25f4453a916526c349760ba3"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_2e636905f5737bac3b0d9ec0c73c48a8"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_f564c0c37c31d9acb7cfa606b606be94"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_99720c9765e4ce40a3e5f8000da19078"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_2fdddeed51c0a69f3bede5f95c808bfc"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_3a8ee9088a69ebf59d30e7aa6b6772ab"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_2ddd09a93c8171fba85908a632445594"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_09f6e54fc2911490337d1bf26376ee18"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_c825ab2f2c373c59418cc35185a0d67d"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_a2fd97f859a9ff97b20442bb8e4bb315"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_2f5822e2084d2acc197e7ef5e1e88125"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_cfb118fb5ff29a5a92e49289b2564882"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_4c25e97bd5cb480accd82325110a2a96"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_ba03c70195bce6f28207f22f1cd4edd8"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_6a57bf2c090b65ce442c84f95529fef3"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_a0bacd43bd11475d2ac4e3858318b932"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_a60f4f05f55df5ec2e7c53c020d9a47d"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_1afb3c71059f2765c61e4ec03ce60f5a"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_8054289471090aaf93f03b02401f1ab3"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_358c0e8a9164ca79300fef910e086604"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_6b0c2255d55263a1c9ca7e897f2ff4ed"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_b67aeef8bfb03841c4068ec3842df3de"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_dca23268bf9314a308078fdd0f25566f"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_5470b4ccba472aa92c0b65b750b0e50c"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_7686b2a95b75de889a5ff6c7688bf7f3"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_4570df5896ab42d50e3cb8875a98f345"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_d4ebdf62d49e65f21d979d8209d14470"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_7e0c2e30a0497b8e9d68bf9000a22f6d"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_d9d96d1680ba023d123166ec09c0ad26"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_73c8d511ce71be2c587d1ee9f7301e6f"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_6c99ab46d843271bcd4cb69b3138077c"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_25c789e736b4f0674042f030daef27a5"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_55be133e605a025f2946e391b7bb2a1a"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_066d9dcc663aa623cb684276ecdd8e63"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_de0b701f4135993e03cf8835354a29a1"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_6f0de7c21e53547a95b0fd12ed1a2fcf"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_bbaf27b22b6375cf72602a162b9cad90"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_a54b478336d897c37a8f7e4377062f5a"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_31bd0de8465464b7db2ddce493eecbd3"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_1a9cdae7a6111b4ee5771ce0342ce5e0"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_6d3ab65f9bb3e78db2ddcf0fd6277073"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_1961a9588bac8529350a3e7bf68e6f17"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_73ba171504d9bc327fa65885ba2f3e46"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_01b6b6ad9865d7d80390263b83428796"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_1a406a988d77b67daad6f863e0d267d6"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_d875ecf9a67e8b2f0adaf0ea90374bab"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_8bcd80b7d0c042d31449cd6be786b997"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_e5e90b12d5bfcacbfe7730e912178c59"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_a366686ddfeaf0470ea2b08764eb9e18"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_75cb43aa74a87fbc9a5db1b7443a554c"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_13bce6af878e8272764dd53353384f97"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_4be79be67ff68b1a41e4829ab431c6ab"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_b591a756b0430239f853601eb6ff1d34"/></elements><model owner="EAPK884b8635344808af88b78d1522f8b3f7" package="EAPK884b8635344808af88b78d1522f8b3f7"/><properties name="CONST with Struct Elements" type="Object"/><style1 value="ShowPrivate=1;ShowProtected=1;ShowPublic=1;HideRelationships=0;Locked=0;Border=1;HighlightForeign=1;PackageContents=1;SequenceNotes=0;ScalePrintImage=0;PPgs.cx=0;PPgs.cy=0;DocSize.cx=791;DocSize.cy=1134;ShowDetails=0;Orientation=P;Zoom=100;ShowTags=0;OpParams=1;VisibleAttributeDetail=0;ShowOpRetType=1;ShowIcons=1;CollabNums=0;HideProps=0;ShowReqs=0;ShowCons=0;PaperSize=9;HideParents=0;UseAlias=0;HideAtts=0;HideOps=0;HideStereo=0;HideElemStereo=0;ShowTests=0;ShowMaint=0;ConnectorNotation=UML 2.1;ExplicitNavigability=0;AdvancedElementProps=1;AdvancedFeatureProps=1;AdvancedConnectorProps=1;ShowNotes=0;SuppressBrackets=0;SuppConnectorLabels=0;PrintPageHeadFoot=0;ShowAsList=0;"/><style2 value="ExcludeRTF=0;DocAll=0;HideQuals=0;AttPkg=1;ShowTests=0;ShowMaint=0;SuppressFOC=1;MatrixActive=0;SwimlanesActive=0;MatrixLineWidth=1;MatrixLocked=0;TConnectorNotation=UML 2.1;TExplicitNavigability=0;AdvancedElementProps=1;AdvancedFeatureProps=1;AdvancedConnectorProps=1;ProfileData=;MDGDgm=;STBLDgm=;ShowNotes=0;VisibleAttributeDetail=0;ShowOpRetType=1;SuppressBrackets=0;SuppConnectorLabels=0;PrintPageHeadFoot=0;ShowAsList=0;"/><swimlanes value="locked=false;orientation=0;width=0;inbar=false;names=false;color=0;bold=false;fcol=0;;cls=0;"/><matrixitems value="locked=false;matrixactive=false;swimlanesactive=false;width=1;"/><extendedProperties/></diagram><diagram xmi:id="EAID_02dec0ffde21758a86774d9c5be0c3a7"><elements><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_8fffe8b4c6b0bb3cff10a8f4dda834f9"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_a21ade727c7ce9e2b2c47a38b3f50d4c"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_b84998561e2b88ca10e09aeb03bf5add"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_449bc0813b49dcbf8d734ffd0f7ceae1"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_9fabb08ba59f6a04b64dee1bbd12fc01"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_b034e05ba7519845f628fef9bdd2a277"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_e14d4545f45ddb55247f0a61eca51391"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_c6af116831d0a86da42785bf281317c3"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_bcd306f659856a45c8698e428d07a2d3"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_7bf126176b70ddd82d12cae610196108"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_f7f1ee45893b76309a765f4cbd3d7994"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_07966b43865a440f0013855288098f51"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_b1a183270eeeecbf953a02eb0831999f"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_73416e6903dc3163e0c6f257e0043235"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_b80879ec699ed32da3fab6fc079bd379"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_426c54d97548b9f37f228e3979caddc5"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_7080a0c1cff5520ed5c8b38832e6bb78"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_a9a1f120ae1ea463237fc9e424063a9a"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_ee7ac79fb81200e18d4fee9c45e8caf7"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_2d308396852199787b2c9d011d2ef9dd"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_699748f3387f40a1a20c6f44ad46fe2a"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_71a7eb72f8e3d8920cbc1acebd26c718"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_8e0fae51e5cf2f4bd465899b5976bb09"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_aca37cd1d4b0d25f4584736607047a3f"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_9a8b481f99c2beb1facf7fc4652228e6"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_8da2460f8e273616060821161f0facc6"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_05798b5cb848308b9967f7cb995fa63a"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_0ef8bd3922191bfc24fa7689a2d52cab"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_ea7c6a8da4e65dc08ff7ecd7e5496e0f"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_736b23f87e4f84ab6f4f112a4c275d56"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_9285f972fd67129b5089bd5e0da8b278"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_b1c6ccceda419ed367fd7290db40bc07"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_5c9b557e25f4453a916526c349760ba3"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_2e636905f5737bac3b0d9ec0c73c48a8"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_f564c0c37c31d9acb7cfa606b606be94"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_99720c9765e4ce40a3e5f8000da19078"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_2fdddeed51c0a69f3bede5f95c808bfc"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_3a8ee9088a69ebf59d30e7aa6b6772ab"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_2ddd09a93c8171fba85908a632445594"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_09f6e54fc2911490337d1bf26376ee18"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_c825ab2f2c373c59418cc35185a0d67d"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_a2fd97f859a9ff97b20442bb8e4bb315"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_2f5822e2084d2acc197e7ef5e1e88125"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_cfb118fb5ff29a5a92e49289b2564882"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_4c25e97bd5cb480accd82325110a2a96"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_ba03c70195bce6f28207f22f1cd4edd8"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_6a57bf2c090b65ce442c84f95529fef3"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_a0bacd43bd11475d2ac4e3858318b932"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_a60f4f05f55df5ec2e7c53c020d9a47d"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_1afb3c71059f2765c61e4ec03ce60f5a"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_8054289471090aaf93f03b02401f1ab3"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_358c0e8a9164ca79300fef910e086604"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_6b0c2255d55263a1c9ca7e897f2ff4ed"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_b67aeef8bfb03841c4068ec3842df3de"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_dca23268bf9314a308078fdd0f25566f"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_5470b4ccba472aa92c0b65b750b0e50c"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_7686b2a95b75de889a5ff6c7688bf7f3"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_4570df5896ab42d50e3cb8875a98f345"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_d4ebdf62d49e65f21d979d8209d14470"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_7e0c2e30a0497b8e9d68bf9000a22f6d"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_d9d96d1680ba023d123166ec09c0ad26"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_73c8d511ce71be2c587d1ee9f7301e6f"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_6c99ab46d843271bcd4cb69b3138077c"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_25c789e736b4f0674042f030daef27a5"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_55be133e605a025f2946e391b7bb2a1a"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_066d9dcc663aa623cb684276ecdd8e63"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_de0b701f4135993e03cf8835354a29a1"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_6f0de7c21e53547a95b0fd12ed1a2fcf"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_bbaf27b22b6375cf72602a162b9cad90"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_a54b478336d897c37a8f7e4377062f5a"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_31bd0de8465464b7db2ddce493eecbd3"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_1a9cdae7a6111b4ee5771ce0342ce5e0"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_6d3ab65f9bb3e78db2ddcf0fd6277073"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_1961a9588bac8529350a3e7bf68e6f17"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_73ba171504d9bc327fa65885ba2f3e46"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_01b6b6ad9865d7d80390263b83428796"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_1a406a988d77b67daad6f863e0d267d6"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_d875ecf9a67e8b2f0adaf0ea90374bab"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_8bcd80b7d0c042d31449cd6be786b997"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_e5e90b12d5bfcacbfe7730e912178c59"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_a366686ddfeaf0470ea2b08764eb9e18"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_75cb43aa74a87fbc9a5db1b7443a554c"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_13bce6af878e8272764dd53353384f97"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_4be79be67ff68b1a41e4829ab431c6ab"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_b591a756b0430239f853601eb6ff1d34"/></elements><model owner="EAPK884b8635344808af88b78d1522f8b3f7" package="EAPK884b8635344808af88b78d1522f8b3f7"/><properties name="CONST without Struct Elements" type="Object"/><style1 value="ShowPrivate=1;ShowProtected=1;ShowPublic=1;HideRelationships=0;Locked=0;Border=1;HighlightForeign=1;PackageContents=1;SequenceNotes=0;ScalePrintImage=0;PPgs.cx=0;PPgs.cy=0;DocSize.cx=791;DocSize.cy=1134;ShowDetails=0;Orientation=P;Zoom=100;ShowTags=0;OpParams=1;VisibleAttributeDetail=0;ShowOpRetType=1;ShowIcons=1;CollabNums=0;HideProps=0;ShowReqs=0;ShowCons=0;PaperSize=9;HideParents=0;UseAlias=0;HideAtts=1;HideOps=1;HideStereo=0;HideElemStereo=0;ShowTests=0;ShowMaint=0;ConnectorNotation=UML 2.1;ExplicitNavigability=0;AdvancedElementProps=1;AdvancedFeatureProps=1;AdvancedConnectorProps=1;ShowNotes=0;SuppressBrackets=0;SuppConnectorLabels=0;PrintPageHeadFoot=0;ShowAsList=0;"/><style2 value="ExcludeRTF=0;DocAll=0;HideQuals=0;AttPkg=1;ShowTests=0;ShowMaint=0;SuppressFOC=1;MatrixActive=0;SwimlanesActive=0;MatrixLineWidth=1;MatrixLocked=0;TConnectorNotation=UML 2.1;TExplicitNavigability=0;AdvancedElementProps=1;AdvancedFeatureProps=1;AdvancedConnectorProps=1;ProfileData=;MDGDgm=;STBLDgm=;ShowNotes=1;VisibleAttributeDetail=0;ShowOpRetType=1;SuppressBrackets=0;SuppConnectorLabels=0;PrintPageHeadFoot=0;ShowAsList=0;"/><swimlanes value="locked=false;orientation=0;width=0;inbar=false;names=false;color=0;bold=false;fcol=0;;cls=0;"/><matrixitems value="locked=false;matrixactive=false;swimlanesactive=false;width=1;"/><extendedProperties/></diagram><diagram xmi:id="EAID_935cdde7aa50e5ca538d03b4f8b1ce12"><elements><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_8d980ae5825cc02ef281dc2498ff8276"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_8fffe8b4c6b0bb3cff10a8f4dda834f9"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_77f1f5474de5c097c5f46090d7b95e22"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_8fffe8b4c6b0bb3cff10a8f4dda834f9"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_3c10edb7f4de06c74906aaf0c8aff755"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_d89319db780de5953be15da15b29fea4"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_aab8a9894c175591bb2ed223682446d2"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_d05992d737490f1c22607b265339f321"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_d6b19081a1717e96aca720d19c549d0f"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_ed09cf7dc5ff034db3934bce408ab274"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_290f7efd4a165002388b9d74983797ed"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_99158b66258e6db32e26958d24c2f457"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_6c9ff6840305b8093fcb24c199298b6a"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_171701682fc470d3b63f7c2ca2cb7d41"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_8cfbe97fdd31b179538802e9fe4bc42f"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_31dc458ee6f35226a71bc9888c5f229e"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_a25b859f884aa8eaeea88b06db52a2cf"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_335562ed44a8bc4ec2ab7220067315e8"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_8e541ea950e51db2c18e4e0dce6688c5"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_1a4bfcce0d9b189ee94241961ae7d4c4"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_736b23f87e4f84ab6f4f112a4c275d56"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_d8394a9f0893f71b130b48a13c06d820"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_c9a206cfb74bd3118a17f6a808a5ab7a"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_d0d31ae91933181268654c928f2fb086"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_f8c887515503bedbdce88a68931a2ac6"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_b9aabb503e0211b4d456ce4eb38eb8fa"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_3c497359d34b2ff8c0e078f087cf6eac"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_17e562fb8c0fb0e0271ef6133a83dd5e"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_3fe61c0afd7ae951b47f2fe4ed7a45fe"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_bf3a554c297cd87ac58a84572b9c8e26"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_deb4cd790a8d925299ba0870fdbd0d09"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_98c4b1bf6f3f551c791611e1ad670c91"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_f47838c62158a1f397168af542485878"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_94ee2d14d35f6272f20cd647c3e6d7c8"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_258d98afc41927a84cb6eb39ca23cef6"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_727f53355f4ed7c375697933a1a1f303"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_d2a37ea767b79ca1d06fc8f6ada51ea6"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_7871b549ee45cf280b89538355694439"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_779fcecad28f9de2b7d4541983f52458"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_c830edd274a79de99a8206406ff45b8e"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_33a8db8054dbd4b983e678109e55b496"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_045c79f73ab47665923017576a0b630d"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_062280957fae9e6d518edca224ebd25e"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_05996e1f9307c65223f7fb1b48ec8f0e"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_55369514abc8d09c66423d57be99e7f1"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_3d47a5d06b2acc76d9a20050e88405e7"/></elements><model owner="EAPK884b8635344808af88b78d1522f8b3f7" package="EAPK884b8635344808af88b78d1522f8b3f7"/><properties name="VAR and Relations" type="Object"/><style1 value="ShowPrivate=1;ShowProtected=1;ShowPublic=1;HideRelationships=0;Locked=0;Border=1;HighlightForeign=1;PackageContents=1;SequenceNotes=0;ScalePrintImage=0;PPgs.cx=0;PPgs.cy=0;DocSize.cx=791;DocSize.cy=1134;ShowDetails=0;Orientation=P;Zoom=100;ShowTags=0;OpParams=1;VisibleAttributeDetail=0;ShowOpRetType=1;ShowIcons=1;CollabNums=0;HideProps=0;ShowReqs=0;ShowCons=0;PaperSize=9;HideParents=0;UseAlias=0;HideAtts=0;HideOps=0;HideStereo=0;HideElemStereo=0;ShowTests=0;ShowMaint=0;ConnectorNotation=UML 2.1;ExplicitNavigability=0;AdvancedElementProps=1;AdvancedFeatureProps=1;AdvancedConnectorProps=1;ShowNotes=0;SuppressBrackets=0;SuppConnectorLabels=0;PrintPageHeadFoot=0;ShowAsList=0;"/><style2 value="ExcludeRTF=0;DocAll=0;HideQuals=0;AttPkg=1;ShowTests=0;ShowMaint=0;SuppressFOC=1;MatrixActive=0;SwimlanesActive=0;MatrixLineWidth=1;MatrixLocked=0;TConnectorNotation=UML 2.1;TExplicitNavigability=0;AdvancedElementProps=1;AdvancedFeatureProps=1;AdvancedConnectorProps=1;ProfileData=;MDGDgm=;STBLDgm=;ShowNotes=1;VisibleAttributeDetail=0;ShowOpRetType=1;SuppressBrackets=0;SuppConnectorLabels=0;PrintPageHeadFoot=0;ShowAsList=0;"/><swimlanes value="locked=false;orientation=0;width=0;inbar=false;names=false;color=0;bold=false;fcol=0;;cls=0;"/><matrixitems value="locked=false;matrixactive=false;swimlanesactive=false;width=1;"/><extendedProperties/></diagram><diagram xmi:id="EAID_00cd30fb79b136be70f25b6a9cf12753"><elements/><model owner="EAPK884b8635344808af88b78d1522f8b3f7" package="EAPK884b8635344808af88b78d1522f8b3f7"/><properties name="Data Accessed by Adress Operator" type="Object"/><style1 value="ShowPrivate=1;ShowProtected=1;ShowPublic=1;HideRelationships=0;Locked=0;Border=1;HighlightForeign=1;PackageContents=1;SequenceNotes=0;ScalePrintImage=0;PPgs.cx=0;PPgs.cy=0;DocSize.cx=791;DocSize.cy=1134;ShowDetails=0;Orientation=P;Zoom=100;ShowTags=0;OpParams=1;VisibleAttributeDetail=0;ShowOpRetType=1;ShowIcons=1;CollabNums=0;HideProps=0;ShowReqs=0;ShowCons=0;PaperSize=9;HideParents=0;UseAlias=0;HideAtts=1;HideOps=1;HideStereo=0;HideElemStereo=0;ShowTests=0;ShowMaint=0;ConnectorNotation=UML 2.1;ExplicitNavigability=0;AdvancedElementProps=1;AdvancedFeatureProps=1;AdvancedConnectorProps=1;ShowNotes=0;SuppressBrackets=0;SuppConnectorLabels=0;PrintPageHeadFoot=0;ShowAsList=0;"/><style2 value="ExcludeRTF=0;DocAll=0;HideQuals=0;AttPkg=1;ShowTests=0;ShowMaint=0;SuppressFOC=1;MatrixActive=0;SwimlanesActive=0;MatrixLineWidth=1;MatrixLocked=0;TConnectorNotation=UML 2.1;TExplicitNavigability=0;AdvancedElementProps=1;AdvancedFeatureProps=1;AdvancedConnectorProps=1;ProfileData=;MDGDgm=;STBLDgm=;ShowNotes=1;VisibleAttributeDetail=0;ShowOpRetType=1;SuppressBrackets=0;SuppConnectorLabels=0;PrintPageHeadFoot=0;ShowAsList=0;"/><swimlanes value="locked=false;orientation=0;width=0;inbar=false;names=false;color=0;bold=false;fcol=0;;cls=0;"/><matrixitems value="locked=false;matrixactive=false;swimlanesactive=false;width=1;"/><extendedProperties/></diagram><diagram xmi:id="EAID_1f5181b83c15a2bb2c10060e506429d4"><elements/><model owner="EAPK884b8635344808af88b78d1522f8b3f7" package="EAPK884b8635344808af88b78d1522f8b3f7"/><properties name="Data Accessed by Interface Handles" type="Object"/><style1 value="ShowPrivate=1;ShowProtected=1;ShowPublic=1;HideRelationships=0;Locked=0;Border=1;HighlightForeign=1;PackageContents=1;SequenceNotes=0;ScalePrintImage=0;PPgs.cx=0;PPgs.cy=0;DocSize.cx=791;DocSize.cy=1134;ShowDetails=0;Orientation=P;Zoom=100;ShowTags=0;OpParams=1;VisibleAttributeDetail=0;ShowOpRetType=1;ShowIcons=1;CollabNums=0;HideProps=0;ShowReqs=0;ShowCons=0;PaperSize=9;HideParents=0;UseAlias=0;HideAtts=1;HideOps=1;HideStereo=0;HideElemStereo=0;ShowTests=0;ShowMaint=0;ConnectorNotation=UML 2.1;ExplicitNavigability=0;AdvancedElementProps=1;AdvancedFeatureProps=1;AdvancedConnectorProps=1;ShowNotes=0;SuppressBrackets=0;SuppConnectorLabels=0;PrintPageHeadFoot=0;ShowAsList=0;"/><style2 value="ExcludeRTF=0;DocAll=0;HideQuals=0;AttPkg=1;ShowTests=0;ShowMaint=0;SuppressFOC=1;MatrixActive=0;SwimlanesActive=0;MatrixLineWidth=1;MatrixLocked=0;TConnectorNotation=UML 2.1;TExplicitNavigability=0;AdvancedElementProps=1;AdvancedFeatureProps=1;AdvancedConnectorProps=1;ProfileData=;MDGDgm=;STBLDgm=;ShowNotes=1;VisibleAttributeDetail=0;ShowOpRetType=1;SuppressBrackets=0;SuppConnectorLabels=0;PrintPageHeadFoot=0;ShowAsList=0;"/><swimlanes value="locked=false;orientation=0;width=0;inbar=false;names=false;color=0;bold=false;fcol=0;;cls=0;"/><matrixitems value="locked=false;matrixactive=false;swimlanesactive=false;width=1;"/><extendedProperties/></diagram><diagram xmi:id="EAID_46adddaa7d81165600ddade589cd28d8"><elements><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_6d3ab65f9bb3e78db2ddcf0fd6277073"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_13bce6af878e8272764dd53353384f97"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_4be79be67ff68b1a41e4829ab431c6ab"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_b591a756b0430239f853601eb6ff1d34"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_3c10edb7f4de06c74906aaf0c8aff755"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_d89319db780de5953be15da15b29fea4"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_aab8a9894c175591bb2ed223682446d2"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_d05992d737490f1c22607b265339f321"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_d6b19081a1717e96aca720d19c549d0f"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_ed09cf7dc5ff034db3934bce408ab274"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_290f7efd4a165002388b9d74983797ed"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_99158b66258e6db32e26958d24c2f457"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_6c9ff6840305b8093fcb24c199298b6a"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_171701682fc470d3b63f7c2ca2cb7d41"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_8cfbe97fdd31b179538802e9fe4bc42f"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_31dc458ee6f35226a71bc9888c5f229e"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_a25b859f884aa8eaeea88b06db52a2cf"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_335562ed44a8bc4ec2ab7220067315e8"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_8e541ea950e51db2c18e4e0dce6688c5"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_1a4bfcce0d9b189ee94241961ae7d4c4"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_d8394a9f0893f71b130b48a13c06d820"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_c9a206cfb74bd3118a17f6a808a5ab7a"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_d0d31ae91933181268654c928f2fb086"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_f8c887515503bedbdce88a68931a2ac6"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_b9aabb503e0211b4d456ce4eb38eb8fa"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_3c497359d34b2ff8c0e078f087cf6eac"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_17e562fb8c0fb0e0271ef6133a83dd5e"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_3fe61c0afd7ae951b47f2fe4ed7a45fe"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_bf3a554c297cd87ac58a84572b9c8e26"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_deb4cd790a8d925299ba0870fdbd0d09"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_98c4b1bf6f3f551c791611e1ad670c91"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_f47838c62158a1f397168af542485878"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_94ee2d14d35f6272f20cd647c3e6d7c8"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_258d98afc41927a84cb6eb39ca23cef6"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_727f53355f4ed7c375697933a1a1f303"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_d2a37ea767b79ca1d06fc8f6ada51ea6"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_7871b549ee45cf280b89538355694439"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_779fcecad28f9de2b7d4541983f52458"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_c830edd274a79de99a8206406ff45b8e"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_33a8db8054dbd4b983e678109e55b496"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_045c79f73ab47665923017576a0b630d"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_062280957fae9e6d518edca224ebd25e"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_05996e1f9307c65223f7fb1b48ec8f0e"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_55369514abc8d09c66423d57be99e7f1"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_3d47a5d06b2acc76d9a20050e88405e7"/></elements><model owner="EAPK884b8635344808af88b78d1522f8b3f7" package="EAPK884b8635344808af88b78d1522f8b3f7"/><properties name="Max Precompile Configurable Data" type="Object"/><style1 value="ShowPrivate=1;ShowProtected=1;ShowPublic=1;HideRelationships=0;Locked=0;Border=1;HighlightForeign=1;PackageContents=1;SequenceNotes=0;ScalePrintImage=0;PPgs.cx=0;PPgs.cy=0;DocSize.cx=791;DocSize.cy=1134;ShowDetails=0;Orientation=P;Zoom=100;ShowTags=0;OpParams=1;VisibleAttributeDetail=0;ShowOpRetType=1;ShowIcons=1;CollabNums=0;HideProps=0;ShowReqs=0;ShowCons=0;PaperSize=9;HideParents=0;UseAlias=0;HideAtts=1;HideOps=1;HideStereo=0;HideElemStereo=0;ShowTests=0;ShowMaint=0;ConnectorNotation=UML 2.1;ExplicitNavigability=0;AdvancedElementProps=1;AdvancedFeatureProps=1;AdvancedConnectorProps=1;ShowNotes=0;SuppressBrackets=0;SuppConnectorLabels=0;PrintPageHeadFoot=0;ShowAsList=0;"/><style2 value="ExcludeRTF=0;DocAll=0;HideQuals=0;AttPkg=1;ShowTests=0;ShowMaint=0;SuppressFOC=1;MatrixActive=0;SwimlanesActive=0;MatrixLineWidth=1;MatrixLocked=0;TConnectorNotation=UML 2.1;TExplicitNavigability=0;AdvancedElementProps=1;AdvancedFeatureProps=1;AdvancedConnectorProps=1;ProfileData=;MDGDgm=;STBLDgm=;ShowNotes=1;VisibleAttributeDetail=0;ShowOpRetType=1;SuppressBrackets=0;SuppConnectorLabels=0;PrintPageHeadFoot=0;ShowAsList=0;"/><swimlanes value="locked=false;orientation=0;width=0;inbar=false;names=false;color=0;bold=false;fcol=0;;cls=0;"/><matrixitems value="locked=false;matrixactive=false;swimlanesactive=false;width=1;"/><extendedProperties/></diagram><diagram xmi:id="EAID_30ad13b048953102c07974c3c5b763b5"><elements/><model owner="EAPK884b8635344808af88b78d1522f8b3f7" package="EAPK884b8635344808af88b78d1522f8b3f7"/><properties name="Max Linktime Configurable Data" type="Object"/><style1 value="ShowPrivate=1;ShowProtected=1;ShowPublic=1;HideRelationships=0;Locked=0;Border=1;HighlightForeign=1;PackageContents=1;SequenceNotes=0;ScalePrintImage=0;PPgs.cx=0;PPgs.cy=0;DocSize.cx=791;DocSize.cy=1134;ShowDetails=0;Orientation=P;Zoom=100;ShowTags=0;OpParams=1;VisibleAttributeDetail=0;ShowOpRetType=1;ShowIcons=1;CollabNums=0;HideProps=0;ShowReqs=0;ShowCons=0;PaperSize=9;HideParents=0;UseAlias=0;HideAtts=1;HideOps=1;HideStereo=0;HideElemStereo=0;ShowTests=0;ShowMaint=0;ConnectorNotation=UML 2.1;ExplicitNavigability=0;AdvancedElementProps=1;AdvancedFeatureProps=1;AdvancedConnectorProps=1;ShowNotes=0;SuppressBrackets=0;SuppConnectorLabels=0;PrintPageHeadFoot=0;ShowAsList=0;"/><style2 value="ExcludeRTF=0;DocAll=0;HideQuals=0;AttPkg=1;ShowTests=0;ShowMaint=0;SuppressFOC=1;MatrixActive=0;SwimlanesActive=0;MatrixLineWidth=1;MatrixLocked=0;TConnectorNotation=UML 2.1;TExplicitNavigability=0;AdvancedElementProps=1;AdvancedFeatureProps=1;AdvancedConnectorProps=1;ProfileData=;MDGDgm=;STBLDgm=;ShowNotes=1;VisibleAttributeDetail=0;ShowOpRetType=1;SuppressBrackets=0;SuppConnectorLabels=0;PrintPageHeadFoot=0;ShowAsList=0;"/><swimlanes value="locked=false;orientation=0;width=0;inbar=false;names=false;color=0;bold=false;fcol=0;;cls=0;"/><matrixitems value="locked=false;matrixactive=false;swimlanesactive=false;width=1;"/><extendedProperties/></diagram><diagram xmi:id="EAID_f9a5e074d21ca91391f5fa4af578596f"><elements><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_8fffe8b4c6b0bb3cff10a8f4dda834f9"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_a21ade727c7ce9e2b2c47a38b3f50d4c"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_449bc0813b49dcbf8d734ffd0f7ceae1"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_b034e05ba7519845f628fef9bdd2a277"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_c6af116831d0a86da42785bf281317c3"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_7bf126176b70ddd82d12cae610196108"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_07966b43865a440f0013855288098f51"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_73416e6903dc3163e0c6f257e0043235"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_426c54d97548b9f37f228e3979caddc5"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_a9a1f120ae1ea463237fc9e424063a9a"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_2d308396852199787b2c9d011d2ef9dd"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_71a7eb72f8e3d8920cbc1acebd26c718"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_aca37cd1d4b0d25f4584736607047a3f"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_8da2460f8e273616060821161f0facc6"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_0ef8bd3922191bfc24fa7689a2d52cab"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_736b23f87e4f84ab6f4f112a4c275d56"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_5c9b557e25f4453a916526c349760ba3"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_f564c0c37c31d9acb7cfa606b606be94"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_2fdddeed51c0a69f3bede5f95c808bfc"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_2ddd09a93c8171fba85908a632445594"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_c825ab2f2c373c59418cc35185a0d67d"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_2f5822e2084d2acc197e7ef5e1e88125"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_4c25e97bd5cb480accd82325110a2a96"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_6a57bf2c090b65ce442c84f95529fef3"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_a60f4f05f55df5ec2e7c53c020d9a47d"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_8054289471090aaf93f03b02401f1ab3"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_6b0c2255d55263a1c9ca7e897f2ff4ed"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_dca23268bf9314a308078fdd0f25566f"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_4570df5896ab42d50e3cb8875a98f345"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_d9d96d1680ba023d123166ec09c0ad26"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_25c789e736b4f0674042f030daef27a5"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_de0b701f4135993e03cf8835354a29a1"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_a54b478336d897c37a8f7e4377062f5a"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_73ba171504d9bc327fa65885ba2f3e46"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_1a406a988d77b67daad6f863e0d267d6"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_8bcd80b7d0c042d31449cd6be786b997"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_a366686ddfeaf0470ea2b08764eb9e18"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_8d980ae5825cc02ef281dc2498ff8276"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_77f1f5474de5c097c5f46090d7b95e22"/></elements><model owner="EAPK884b8635344808af88b78d1522f8b3f7" package="EAPK884b8635344808af88b78d1522f8b3f7"/><properties name="Max Postbuild Configurable Data" type="Object"/><style1 value="ShowPrivate=1;ShowProtected=1;ShowPublic=1;HideRelationships=0;Locked=0;Border=1;HighlightForeign=1;PackageContents=1;SequenceNotes=0;ScalePrintImage=0;PPgs.cx=0;PPgs.cy=0;DocSize.cx=791;DocSize.cy=1134;ShowDetails=0;Orientation=P;Zoom=100;ShowTags=0;OpParams=1;VisibleAttributeDetail=0;ShowOpRetType=1;ShowIcons=1;CollabNums=0;HideProps=0;ShowReqs=0;ShowCons=0;PaperSize=9;HideParents=0;UseAlias=0;HideAtts=1;HideOps=1;HideStereo=0;HideElemStereo=0;ShowTests=0;ShowMaint=0;ConnectorNotation=UML 2.1;ExplicitNavigability=0;AdvancedElementProps=1;AdvancedFeatureProps=1;AdvancedConnectorProps=1;ShowNotes=0;SuppressBrackets=0;SuppConnectorLabels=0;PrintPageHeadFoot=0;ShowAsList=0;"/><style2 value="ExcludeRTF=0;DocAll=0;HideQuals=0;AttPkg=1;ShowTests=0;ShowMaint=0;SuppressFOC=1;MatrixActive=0;SwimlanesActive=0;MatrixLineWidth=1;MatrixLocked=0;TConnectorNotation=UML 2.1;TExplicitNavigability=0;AdvancedElementProps=1;AdvancedFeatureProps=1;AdvancedConnectorProps=1;ProfileData=;MDGDgm=;STBLDgm=;ShowNotes=1;VisibleAttributeDetail=0;ShowOpRetType=1;SuppressBrackets=0;SuppConnectorLabels=0;PrintPageHeadFoot=0;ShowAsList=0;"/><swimlanes value="locked=false;orientation=0;width=0;inbar=false;names=false;color=0;bold=false;fcol=0;;cls=0;"/><matrixitems value="locked=false;matrixactive=false;swimlanesactive=false;width=1;"/><extendedProperties/></diagram><diagram xmi:id="EAID_8563485c2956390cb119266a7b932f52"><elements/><model owner="EAPK884b8635344808af88b78d1522f8b3f7" package="EAPK884b8635344808af88b78d1522f8b3f7"/><properties name="Calibration Lite Data" type="Object"/><style1 value="ShowPrivate=1;ShowProtected=1;ShowPublic=1;HideRelationships=0;Locked=0;Border=1;HighlightForeign=1;PackageContents=1;SequenceNotes=0;ScalePrintImage=0;PPgs.cx=0;PPgs.cy=0;DocSize.cx=791;DocSize.cy=1134;ShowDetails=0;Orientation=P;Zoom=100;ShowTags=0;OpParams=1;VisibleAttributeDetail=0;ShowOpRetType=1;ShowIcons=1;CollabNums=0;HideProps=0;ShowReqs=0;ShowCons=0;PaperSize=9;HideParents=0;UseAlias=0;HideAtts=1;HideOps=1;HideStereo=0;HideElemStereo=0;ShowTests=0;ShowMaint=0;ConnectorNotation=UML 2.1;ExplicitNavigability=0;AdvancedElementProps=1;AdvancedFeatureProps=1;AdvancedConnectorProps=1;ShowNotes=0;SuppressBrackets=0;SuppConnectorLabels=0;PrintPageHeadFoot=0;ShowAsList=0;"/><style2 value="ExcludeRTF=0;DocAll=0;HideQuals=0;AttPkg=1;ShowTests=0;ShowMaint=0;SuppressFOC=1;MatrixActive=0;SwimlanesActive=0;MatrixLineWidth=1;MatrixLocked=0;TConnectorNotation=UML 2.1;TExplicitNavigability=0;AdvancedElementProps=1;AdvancedFeatureProps=1;AdvancedConnectorProps=1;ProfileData=;MDGDgm=;STBLDgm=;ShowNotes=1;VisibleAttributeDetail=0;ShowOpRetType=1;SuppressBrackets=0;SuppConnectorLabels=0;PrintPageHeadFoot=0;ShowAsList=0;"/><swimlanes value="locked=false;orientation=0;width=0;inbar=false;names=false;color=0;bold=false;fcol=0;;cls=0;"/><matrixitems value="locked=false;matrixactive=false;swimlanesactive=false;width=1;"/><extendedProperties/></diagram><diagram xmi:id="EAID_d475fbe68b33240983cb062819584f1b"><elements/><model owner="EAPK884b8635344808af88b78d1522f8b3f7" package="EAPK884b8635344808af88b78d1522f8b3f7"/><properties name="Sandbox with Details" type="Object"/><style1 value="ShowPrivate=1;ShowProtected=1;ShowPublic=1;HideRelationships=0;Locked=0;Border=1;HighlightForeign=1;PackageContents=1;SequenceNotes=0;ScalePrintImage=0;PPgs.cx=0;PPgs.cy=0;DocSize.cx=791;DocSize.cy=1134;ShowDetails=0;Orientation=P;Zoom=100;ShowTags=0;OpParams=1;VisibleAttributeDetail=0;ShowOpRetType=1;ShowIcons=1;CollabNums=0;HideProps=0;ShowReqs=0;ShowCons=0;PaperSize=9;HideParents=0;UseAlias=0;HideAtts=0;HideOps=0;HideStereo=0;HideElemStereo=0;ShowTests=0;ShowMaint=0;ConnectorNotation=UML 2.1;ExplicitNavigability=0;AdvancedElementProps=1;AdvancedFeatureProps=1;AdvancedConnectorProps=1;ShowNotes=0;SuppressBrackets=0;SuppConnectorLabels=0;PrintPageHeadFoot=0;ShowAsList=0;"/><style2 value="ExcludeRTF=0;DocAll=0;HideQuals=0;AttPkg=1;ShowTests=0;ShowMaint=0;SuppressFOC=1;MatrixActive=0;SwimlanesActive=0;MatrixLineWidth=1;MatrixLocked=0;TConnectorNotation=UML 2.1;TExplicitNavigability=0;AdvancedElementProps=1;AdvancedFeatureProps=1;AdvancedConnectorProps=1;ProfileData=;MDGDgm=;STBLDgm=;ShowNotes=1;VisibleAttributeDetail=0;ShowOpRetType=1;SuppressBrackets=0;SuppConnectorLabels=0;PrintPageHeadFoot=0;ShowAsList=0;"/><swimlanes value="locked=false;orientation=0;width=0;inbar=false;names=false;color=0;bold=false;fcol=0;;cls=0;"/><matrixitems value="locked=false;matrixactive=false;swimlanesactive=false;width=1;"/><extendedProperties/></diagram><diagram xmi:id="EAID_a0d3d3cdc3fa80d859b5a32b5b4f367d"><elements/><model owner="EAPK884b8635344808af88b78d1522f8b3f7" package="EAPK884b8635344808af88b78d1522f8b3f7"/><properties name="Sandbox without Details" type="Object"/><style1 value="ShowPrivate=1;ShowProtected=1;ShowPublic=1;HideRelationships=0;Locked=0;Border=1;HighlightForeign=1;PackageContents=1;SequenceNotes=0;ScalePrintImage=0;PPgs.cx=0;PPgs.cy=0;DocSize.cx=791;DocSize.cy=1134;ShowDetails=0;Orientation=P;Zoom=100;ShowTags=0;OpParams=1;VisibleAttributeDetail=0;ShowOpRetType=1;ShowIcons=1;CollabNums=0;HideProps=0;ShowReqs=0;ShowCons=0;PaperSize=9;HideParents=0;UseAlias=0;HideAtts=1;HideOps=1;HideStereo=0;HideElemStereo=0;ShowTests=0;ShowMaint=0;ConnectorNotation=UML 2.1;ExplicitNavigability=0;AdvancedElementProps=1;AdvancedFeatureProps=1;AdvancedConnectorProps=1;ShowNotes=0;SuppressBrackets=0;SuppConnectorLabels=0;PrintPageHeadFoot=0;ShowAsList=0;"/><style2 value="ExcludeRTF=0;DocAll=0;HideQuals=0;AttPkg=1;ShowTests=0;ShowMaint=0;SuppressFOC=1;MatrixActive=0;SwimlanesActive=0;MatrixLineWidth=1;MatrixLocked=0;TConnectorNotation=UML 2.1;TExplicitNavigability=0;AdvancedElementProps=1;AdvancedFeatureProps=1;AdvancedConnectorProps=1;ProfileData=;MDGDgm=;STBLDgm=;ShowNotes=0;VisibleAttributeDetail=0;ShowOpRetType=1;SuppressBrackets=0;SuppConnectorLabels=0;PrintPageHeadFoot=0;ShowAsList=0;"/><swimlanes value="locked=false;orientation=0;width=0;inbar=false;names=false;color=0;bold=false;fcol=0;;cls=0;"/><matrixitems value="locked=false;matrixactive=false;swimlanesactive=false;width=1;"/><extendedProperties/></diagram></diagrams></xmi:Extension><uml:Model name="BswM Abstract Data Model" visibility="public" xmi:id="EAPK884b8635344808af88b78d1522f8b3f7" xmi:type="uml:Package"><packagedElement name="ActionLists:ConstStruct" visibility="public" xmi:id="EAID_8fffe8b4c6b0bb3cff10a8f4dda834f9" xmi:type="uml:InstanceSpecification"/><packagedElement name="CanSMChannelMapping:ConstStruct" visibility="public" xmi:id="EAID_a21ade727c7ce9e2b2c47a38b3f50d4c" xmi:type="uml:InstanceSpecification"><ownedAttribute aggregation="none" association="EAID_b84998561e2b88ca10e09aeb03bf5add" isDerived="false" isDerivedUnion="false" isOrdered="true" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_dstb84998561e2b88ca10e09aeb03bf5add" xmi:type="uml:Property"><lowerValue value="0" xmi:id="EAID_f0177cc839d128c406d918a699b54f2c" xmi:type="uml:LiteralInteger"/><upperValue value="-1" xmi:id="EAID_114da649ca5cad73646bd4f3d5da8917" xmi:type="uml:LiteralUnlimitedNatural"/><type xmi:idref="EAID_736b23f87e4f84ab6f4f112a4c275d56"/></ownedAttribute></packagedElement><packagedElement name="indirection" visibility="public" xmi:id="EAID_b84998561e2b88ca10e09aeb03bf5add" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dstb84998561e2b88ca10e09aeb03bf5add"/><memberEnd xmi:idref="EAID_srcb84998561e2b88ca10e09aeb03bf5add"/><ownedEnd aggregation="none" association="EAID_b84998561e2b88ca10e09aeb03bf5add" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_srcb84998561e2b88ca10e09aeb03bf5add" xmi:type="uml:Property"><type xmi:idref="EAID_a21ade727c7ce9e2b2c47a38b3f50d4c"/></ownedEnd></packagedElement><packagedElement name="ComMChannelMapping:ConstStruct" visibility="public" xmi:id="EAID_449bc0813b49dcbf8d734ffd0f7ceae1" xmi:type="uml:InstanceSpecification"><ownedAttribute aggregation="none" association="EAID_9fabb08ba59f6a04b64dee1bbd12fc01" isDerived="false" isDerivedUnion="false" isOrdered="true" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_dst9fabb08ba59f6a04b64dee1bbd12fc01" xmi:type="uml:Property"><lowerValue value="0" xmi:id="EAID_0b81b09dc239e4974a1daa2d524db2c3" xmi:type="uml:LiteralInteger"/><upperValue value="-1" xmi:id="EAID_faff9f0614c8f0f8bf1d44b9e518b88d" xmi:type="uml:LiteralUnlimitedNatural"/><type xmi:idref="EAID_736b23f87e4f84ab6f4f112a4c275d56"/></ownedAttribute></packagedElement><packagedElement name="indirection" visibility="public" xmi:id="EAID_9fabb08ba59f6a04b64dee1bbd12fc01" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dst9fabb08ba59f6a04b64dee1bbd12fc01"/><memberEnd xmi:idref="EAID_src9fabb08ba59f6a04b64dee1bbd12fc01"/><ownedEnd aggregation="none" association="EAID_9fabb08ba59f6a04b64dee1bbd12fc01" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_src9fabb08ba59f6a04b64dee1bbd12fc01" xmi:type="uml:Property"><type xmi:idref="EAID_449bc0813b49dcbf8d734ffd0f7ceae1"/></ownedEnd></packagedElement><packagedElement name="ComMInitiateResetMapping:ConstStruct" visibility="public" xmi:id="EAID_b034e05ba7519845f628fef9bdd2a277" xmi:type="uml:InstanceSpecification"><ownedAttribute aggregation="none" association="EAID_e14d4545f45ddb55247f0a61eca51391" isDerived="false" isDerivedUnion="false" isOrdered="true" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_dste14d4545f45ddb55247f0a61eca51391" xmi:type="uml:Property"><lowerValue value="0" xmi:id="EAID_b5fa8b472f795421fa2b9122551e6b19" xmi:type="uml:LiteralInteger"/><upperValue value="-1" xmi:id="EAID_ff7cb0b58bd25ab87792dae3f6a8abc0" xmi:type="uml:LiteralUnlimitedNatural"/><type xmi:idref="EAID_736b23f87e4f84ab6f4f112a4c275d56"/></ownedAttribute></packagedElement><packagedElement name="indirection" visibility="public" xmi:id="EAID_e14d4545f45ddb55247f0a61eca51391" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dste14d4545f45ddb55247f0a61eca51391"/><memberEnd xmi:idref="EAID_srce14d4545f45ddb55247f0a61eca51391"/><ownedEnd aggregation="none" association="EAID_e14d4545f45ddb55247f0a61eca51391" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_srce14d4545f45ddb55247f0a61eca51391" xmi:type="uml:Property"><type xmi:idref="EAID_b034e05ba7519845f628fef9bdd2a277"/></ownedEnd></packagedElement><packagedElement name="ComMPncMapping:ConstStruct" visibility="public" xmi:id="EAID_c6af116831d0a86da42785bf281317c3" xmi:type="uml:InstanceSpecification"><ownedAttribute aggregation="none" association="EAID_bcd306f659856a45c8698e428d07a2d3" isDerived="false" isDerivedUnion="false" isOrdered="true" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_dstbcd306f659856a45c8698e428d07a2d3" xmi:type="uml:Property"><lowerValue value="0" xmi:id="EAID_62a7686112ee9d7e768a3260fa31e0bd" xmi:type="uml:LiteralInteger"/><upperValue value="-1" xmi:id="EAID_3f81155a135c159a93e77178e9b4ec9c" xmi:type="uml:LiteralUnlimitedNatural"/><type xmi:idref="EAID_736b23f87e4f84ab6f4f112a4c275d56"/></ownedAttribute></packagedElement><packagedElement name="indirection" visibility="public" xmi:id="EAID_bcd306f659856a45c8698e428d07a2d3" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dstbcd306f659856a45c8698e428d07a2d3"/><memberEnd xmi:idref="EAID_srcbcd306f659856a45c8698e428d07a2d3"/><ownedEnd aggregation="none" association="EAID_bcd306f659856a45c8698e428d07a2d3" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_srcbcd306f659856a45c8698e428d07a2d3" xmi:type="uml:Property"><type xmi:idref="EAID_c6af116831d0a86da42785bf281317c3"/></ownedEnd></packagedElement><packagedElement name="DcmApplUpdateMapping:ConstStruct" visibility="public" xmi:id="EAID_7bf126176b70ddd82d12cae610196108" xmi:type="uml:InstanceSpecification"><ownedAttribute aggregation="none" association="EAID_f7f1ee45893b76309a765f4cbd3d7994" isDerived="false" isDerivedUnion="false" isOrdered="true" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_dstf7f1ee45893b76309a765f4cbd3d7994" xmi:type="uml:Property"><lowerValue value="0" xmi:id="EAID_f7231374a21b40ed0c3c181657b244f3" xmi:type="uml:LiteralInteger"/><upperValue value="-1" xmi:id="EAID_7b1ad05e447d58cf8593f6c5a520742b" xmi:type="uml:LiteralUnlimitedNatural"/><type xmi:idref="EAID_736b23f87e4f84ab6f4f112a4c275d56"/></ownedAttribute></packagedElement><packagedElement name="indirection" visibility="public" xmi:id="EAID_f7f1ee45893b76309a765f4cbd3d7994" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dstf7f1ee45893b76309a765f4cbd3d7994"/><memberEnd xmi:idref="EAID_srcf7f1ee45893b76309a765f4cbd3d7994"/><ownedEnd aggregation="none" association="EAID_f7f1ee45893b76309a765f4cbd3d7994" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_srcf7f1ee45893b76309a765f4cbd3d7994" xmi:type="uml:Property"><type xmi:idref="EAID_7bf126176b70ddd82d12cae610196108"/></ownedEnd></packagedElement><packagedElement name="DcmComMapping:ConstStruct" visibility="public" xmi:id="EAID_07966b43865a440f0013855288098f51" xmi:type="uml:InstanceSpecification"><ownedAttribute aggregation="none" association="EAID_b1a183270eeeecbf953a02eb0831999f" isDerived="false" isDerivedUnion="false" isOrdered="true" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_dstb1a183270eeeecbf953a02eb0831999f" xmi:type="uml:Property"><lowerValue value="0" xmi:id="EAID_43c99fb5d25b68f3f040c99fe439b50e" xmi:type="uml:LiteralInteger"/><upperValue value="-1" xmi:id="EAID_e1fa9ea6dbeaba84532a8a6f999dae9a" xmi:type="uml:LiteralUnlimitedNatural"/><type xmi:idref="EAID_736b23f87e4f84ab6f4f112a4c275d56"/></ownedAttribute></packagedElement><packagedElement name="indirection" visibility="public" xmi:id="EAID_b1a183270eeeecbf953a02eb0831999f" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dstb1a183270eeeecbf953a02eb0831999f"/><memberEnd xmi:idref="EAID_srcb1a183270eeeecbf953a02eb0831999f"/><ownedEnd aggregation="none" association="EAID_b1a183270eeeecbf953a02eb0831999f" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_srcb1a183270eeeecbf953a02eb0831999f" xmi:type="uml:Property"><type xmi:idref="EAID_07966b43865a440f0013855288098f51"/></ownedEnd></packagedElement><packagedElement name="DeferredRules:ConstStruct" visibility="public" xmi:id="EAID_73416e6903dc3163e0c6f257e0043235" xmi:type="uml:InstanceSpecification"><ownedAttribute aggregation="none" association="EAID_b80879ec699ed32da3fab6fc079bd379" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_dstb80879ec699ed32da3fab6fc079bd379" xmi:type="uml:Property"><lowerValue value="1" xmi:id="EAID_985f2adff827d1a112a198d68ba8bb2c" xmi:type="uml:LiteralInteger"/><upperValue value="1" xmi:id="EAID_25bccd1ca19284e926c7c34096421418" xmi:type="uml:LiteralInteger"/><type xmi:idref="EAID_6d3ab65f9bb3e78db2ddcf0fd6277073"/></ownedAttribute></packagedElement><packagedElement name="indirection" visibility="public" xmi:id="EAID_b80879ec699ed32da3fab6fc079bd379" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dstb80879ec699ed32da3fab6fc079bd379"/><memberEnd xmi:idref="EAID_srcb80879ec699ed32da3fab6fc079bd379"/><ownedEnd aggregation="none" association="EAID_b80879ec699ed32da3fab6fc079bd379" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_srcb80879ec699ed32da3fab6fc079bd379" xmi:type="uml:Property"><type xmi:idref="EAID_73416e6903dc3163e0c6f257e0043235"/></ownedEnd></packagedElement><packagedElement name="EcuMModeMapping:ConstStruct" visibility="public" xmi:id="EAID_426c54d97548b9f37f228e3979caddc5" xmi:type="uml:InstanceSpecification"><ownedAttribute aggregation="none" association="EAID_7080a0c1cff5520ed5c8b38832e6bb78" isDerived="false" isDerivedUnion="false" isOrdered="true" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_dst7080a0c1cff5520ed5c8b38832e6bb78" xmi:type="uml:Property"><lowerValue value="0" xmi:id="EAID_8c190f06a90f73ea8bd71e9fad827131" xmi:type="uml:LiteralInteger"/><upperValue value="-1" xmi:id="EAID_bf18eb826a9044d9188e66d37fb2e105" xmi:type="uml:LiteralUnlimitedNatural"/><type xmi:idref="EAID_736b23f87e4f84ab6f4f112a4c275d56"/></ownedAttribute></packagedElement><packagedElement name="indirection" visibility="public" xmi:id="EAID_7080a0c1cff5520ed5c8b38832e6bb78" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dst7080a0c1cff5520ed5c8b38832e6bb78"/><memberEnd xmi:idref="EAID_src7080a0c1cff5520ed5c8b38832e6bb78"/><ownedEnd aggregation="none" association="EAID_7080a0c1cff5520ed5c8b38832e6bb78" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_src7080a0c1cff5520ed5c8b38832e6bb78" xmi:type="uml:Property"><type xmi:idref="EAID_426c54d97548b9f37f228e3979caddc5"/></ownedEnd></packagedElement><packagedElement name="EcuMRunRequestMapping:ConstStruct" visibility="public" xmi:id="EAID_a9a1f120ae1ea463237fc9e424063a9a" xmi:type="uml:InstanceSpecification"><ownedAttribute aggregation="none" association="EAID_ee7ac79fb81200e18d4fee9c45e8caf7" isDerived="false" isDerivedUnion="false" isOrdered="true" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_dstee7ac79fb81200e18d4fee9c45e8caf7" xmi:type="uml:Property"><lowerValue value="0" xmi:id="EAID_25b06d5549cf029c2d516dd59d162629" xmi:type="uml:LiteralInteger"/><upperValue value="-1" xmi:id="EAID_3b8ef7f71a33d9acda2853522ab26991" xmi:type="uml:LiteralUnlimitedNatural"/><type xmi:idref="EAID_736b23f87e4f84ab6f4f112a4c275d56"/></ownedAttribute></packagedElement><packagedElement name="indirection" visibility="public" xmi:id="EAID_ee7ac79fb81200e18d4fee9c45e8caf7" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dstee7ac79fb81200e18d4fee9c45e8caf7"/><memberEnd xmi:idref="EAID_srcee7ac79fb81200e18d4fee9c45e8caf7"/><ownedEnd aggregation="none" association="EAID_ee7ac79fb81200e18d4fee9c45e8caf7" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_srcee7ac79fb81200e18d4fee9c45e8caf7" xmi:type="uml:Property"><type xmi:idref="EAID_a9a1f120ae1ea463237fc9e424063a9a"/></ownedEnd></packagedElement><packagedElement name="EcuMWakeupMapping:ConstStruct" visibility="public" xmi:id="EAID_2d308396852199787b2c9d011d2ef9dd" xmi:type="uml:InstanceSpecification"><ownedAttribute aggregation="none" association="EAID_699748f3387f40a1a20c6f44ad46fe2a" isDerived="false" isDerivedUnion="false" isOrdered="true" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_dst699748f3387f40a1a20c6f44ad46fe2a" xmi:type="uml:Property"><lowerValue value="0" xmi:id="EAID_6c1c5c2ba814e18a7eff74f8a58f2e9b" xmi:type="uml:LiteralInteger"/><upperValue value="-1" xmi:id="EAID_6bb9729f94b1a90511f1484282a12cb6" xmi:type="uml:LiteralUnlimitedNatural"/><type xmi:idref="EAID_736b23f87e4f84ab6f4f112a4c275d56"/></ownedAttribute></packagedElement><packagedElement name="indirection" visibility="public" xmi:id="EAID_699748f3387f40a1a20c6f44ad46fe2a" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dst699748f3387f40a1a20c6f44ad46fe2a"/><memberEnd xmi:idref="EAID_src699748f3387f40a1a20c6f44ad46fe2a"/><ownedEnd aggregation="none" association="EAID_699748f3387f40a1a20c6f44ad46fe2a" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_src699748f3387f40a1a20c6f44ad46fe2a" xmi:type="uml:Property"><type xmi:idref="EAID_2d308396852199787b2c9d011d2ef9dd"/></ownedEnd></packagedElement><packagedElement name="EthIfPortMapping:ConstStruct" visibility="public" xmi:id="EAID_71a7eb72f8e3d8920cbc1acebd26c718" xmi:type="uml:InstanceSpecification"><ownedAttribute aggregation="none" association="EAID_8e0fae51e5cf2f4bd465899b5976bb09" isDerived="false" isDerivedUnion="false" isOrdered="true" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_dst8e0fae51e5cf2f4bd465899b5976bb09" xmi:type="uml:Property"><lowerValue value="0" xmi:id="EAID_5832225b1525ee16aa513b53b1c14c18" xmi:type="uml:LiteralInteger"/><upperValue value="-1" xmi:id="EAID_6aae43fdc86ff66e8c048e7160633338" xmi:type="uml:LiteralUnlimitedNatural"/><type xmi:idref="EAID_736b23f87e4f84ab6f4f112a4c275d56"/></ownedAttribute></packagedElement><packagedElement name="indirection" visibility="public" xmi:id="EAID_8e0fae51e5cf2f4bd465899b5976bb09" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dst8e0fae51e5cf2f4bd465899b5976bb09"/><memberEnd xmi:idref="EAID_src8e0fae51e5cf2f4bd465899b5976bb09"/><ownedEnd aggregation="none" association="EAID_8e0fae51e5cf2f4bd465899b5976bb09" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_src8e0fae51e5cf2f4bd465899b5976bb09" xmi:type="uml:Property"><type xmi:idref="EAID_71a7eb72f8e3d8920cbc1acebd26c718"/></ownedEnd></packagedElement><packagedElement name="EthSMMapping:ConstStruct" visibility="public" xmi:id="EAID_aca37cd1d4b0d25f4584736607047a3f" xmi:type="uml:InstanceSpecification"><ownedAttribute aggregation="none" association="EAID_9a8b481f99c2beb1facf7fc4652228e6" isDerived="false" isDerivedUnion="false" isOrdered="true" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_dst9a8b481f99c2beb1facf7fc4652228e6" xmi:type="uml:Property"><lowerValue value="0" xmi:id="EAID_d4aaecf23bf807ace4580ed905d7d5b5" xmi:type="uml:LiteralInteger"/><upperValue value="-1" xmi:id="EAID_6fa1613aee132637e89bd414d257f14a" xmi:type="uml:LiteralUnlimitedNatural"/><type xmi:idref="EAID_736b23f87e4f84ab6f4f112a4c275d56"/></ownedAttribute></packagedElement><packagedElement name="indirection" visibility="public" xmi:id="EAID_9a8b481f99c2beb1facf7fc4652228e6" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dst9a8b481f99c2beb1facf7fc4652228e6"/><memberEnd xmi:idref="EAID_src9a8b481f99c2beb1facf7fc4652228e6"/><ownedEnd aggregation="none" association="EAID_9a8b481f99c2beb1facf7fc4652228e6" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_src9a8b481f99c2beb1facf7fc4652228e6" xmi:type="uml:Property"><type xmi:idref="EAID_aca37cd1d4b0d25f4584736607047a3f"/></ownedEnd></packagedElement><packagedElement name="FrSMMapping:ConstStruct" visibility="public" xmi:id="EAID_8da2460f8e273616060821161f0facc6" xmi:type="uml:InstanceSpecification"><ownedAttribute aggregation="none" association="EAID_05798b5cb848308b9967f7cb995fa63a" isDerived="false" isDerivedUnion="false" isOrdered="true" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_dst05798b5cb848308b9967f7cb995fa63a" xmi:type="uml:Property"><lowerValue value="0" xmi:id="EAID_b2e0bcd04a520c21360934f40487b542" xmi:type="uml:LiteralInteger"/><upperValue value="-1" xmi:id="EAID_30550d3d4954f20d9831cb5dc0e7027a" xmi:type="uml:LiteralUnlimitedNatural"/><type xmi:idref="EAID_736b23f87e4f84ab6f4f112a4c275d56"/></ownedAttribute></packagedElement><packagedElement name="indirection" visibility="public" xmi:id="EAID_05798b5cb848308b9967f7cb995fa63a" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dst05798b5cb848308b9967f7cb995fa63a"/><memberEnd xmi:idref="EAID_src05798b5cb848308b9967f7cb995fa63a"/><ownedEnd aggregation="none" association="EAID_05798b5cb848308b9967f7cb995fa63a" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_src05798b5cb848308b9967f7cb995fa63a" xmi:type="uml:Property"><type xmi:idref="EAID_8da2460f8e273616060821161f0facc6"/></ownedEnd></packagedElement><packagedElement name="GenericMapping:ConstStruct" visibility="public" xmi:id="EAID_0ef8bd3922191bfc24fa7689a2d52cab" xmi:type="uml:InstanceSpecification"><ownedAttribute aggregation="none" association="EAID_ea7c6a8da4e65dc08ff7ecd7e5496e0f" isDerived="false" isDerivedUnion="false" isOrdered="true" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_dstea7c6a8da4e65dc08ff7ecd7e5496e0f" xmi:type="uml:Property"><lowerValue value="0" xmi:id="EAID_f4f3665c60a738e01496cf83674b1c76" xmi:type="uml:LiteralInteger"/><upperValue value="-1" xmi:id="EAID_910b399af118053e6cb4fc5145700563" xmi:type="uml:LiteralUnlimitedNatural"/><type xmi:idref="EAID_736b23f87e4f84ab6f4f112a4c275d56"/></ownedAttribute></packagedElement><packagedElement name="indirection" visibility="public" xmi:id="EAID_ea7c6a8da4e65dc08ff7ecd7e5496e0f" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dstea7c6a8da4e65dc08ff7ecd7e5496e0f"/><memberEnd xmi:idref="EAID_srcea7c6a8da4e65dc08ff7ecd7e5496e0f"/><ownedEnd aggregation="none" association="EAID_ea7c6a8da4e65dc08ff7ecd7e5496e0f" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_srcea7c6a8da4e65dc08ff7ecd7e5496e0f" xmi:type="uml:Property"><type xmi:idref="EAID_0ef8bd3922191bfc24fa7689a2d52cab"/></ownedEnd></packagedElement><packagedElement name="ImmediateUser:ConstStruct" visibility="public" xmi:id="EAID_736b23f87e4f84ab6f4f112a4c275d56" xmi:type="uml:InstanceSpecification"><ownedAttribute aggregation="none" association="EAID_9285f972fd67129b5089bd5e0da8b278" isDerived="false" isDerivedUnion="false" isOrdered="true" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_dst9285f972fd67129b5089bd5e0da8b278" xmi:type="uml:Property"><lowerValue value="0" xmi:id="EAID_3770bd8c3e34438c96f8c7a99fd72fd8" xmi:type="uml:LiteralInteger"/><upperValue value="-1" xmi:id="EAID_910273aa171fedbdbd9fa2e46144c42e" xmi:type="uml:LiteralUnlimitedNatural"/><type xmi:idref="EAID_0d076b193bd4dca769a6178cd2d277e9"/></ownedAttribute><ownedAttribute aggregation="none" association="EAID_b1c6ccceda419ed367fd7290db40bc07" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_dstb1c6ccceda419ed367fd7290db40bc07" xmi:type="uml:Property"><lowerValue value="1" xmi:id="EAID_dcc8bd5738439e223950408597fa0876" xmi:type="uml:LiteralInteger"/><upperValue value="-1" xmi:id="EAID_ae2b1a883a63bdae859ee76c025d1e71" xmi:type="uml:LiteralUnlimitedNatural"/><type xmi:idref="EAID_6d3ab65f9bb3e78db2ddcf0fd6277073"/></ownedAttribute></packagedElement><packagedElement name="indirection" visibility="public" xmi:id="EAID_9285f972fd67129b5089bd5e0da8b278" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dst9285f972fd67129b5089bd5e0da8b278"/><memberEnd xmi:idref="EAID_src9285f972fd67129b5089bd5e0da8b278"/><ownedEnd aggregation="none" association="EAID_9285f972fd67129b5089bd5e0da8b278" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_src9285f972fd67129b5089bd5e0da8b278" xmi:type="uml:Property"><type xmi:idref="EAID_736b23f87e4f84ab6f4f112a4c275d56"/></ownedEnd></packagedElement><packagedElement name="indirection" visibility="public" xmi:id="EAID_b1c6ccceda419ed367fd7290db40bc07" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dstb1c6ccceda419ed367fd7290db40bc07"/><memberEnd xmi:idref="EAID_srcb1c6ccceda419ed367fd7290db40bc07"/><ownedEnd aggregation="none" association="EAID_b1c6ccceda419ed367fd7290db40bc07" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_srcb1c6ccceda419ed367fd7290db40bc07" xmi:type="uml:Property"><type xmi:idref="EAID_736b23f87e4f84ab6f4f112a4c275d56"/></ownedEnd></packagedElement><packagedElement name="J1939DcmMapping:ConstStruct" visibility="public" xmi:id="EAID_5c9b557e25f4453a916526c349760ba3" xmi:type="uml:InstanceSpecification"><ownedAttribute aggregation="none" association="EAID_2e636905f5737bac3b0d9ec0c73c48a8" isDerived="false" isDerivedUnion="false" isOrdered="true" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_dst2e636905f5737bac3b0d9ec0c73c48a8" xmi:type="uml:Property"><lowerValue value="0" xmi:id="EAID_a2ba74fa06f6da320a770b2c7357ead4" xmi:type="uml:LiteralInteger"/><upperValue value="-1" xmi:id="EAID_244a4581f8a2c68877f96c868e5d5d8a" xmi:type="uml:LiteralUnlimitedNatural"/><type xmi:idref="EAID_736b23f87e4f84ab6f4f112a4c275d56"/></ownedAttribute></packagedElement><packagedElement name="indirection" visibility="public" xmi:id="EAID_2e636905f5737bac3b0d9ec0c73c48a8" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dst2e636905f5737bac3b0d9ec0c73c48a8"/><memberEnd xmi:idref="EAID_src2e636905f5737bac3b0d9ec0c73c48a8"/><ownedEnd aggregation="none" association="EAID_2e636905f5737bac3b0d9ec0c73c48a8" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_src2e636905f5737bac3b0d9ec0c73c48a8" xmi:type="uml:Property"><type xmi:idref="EAID_5c9b557e25f4453a916526c349760ba3"/></ownedEnd></packagedElement><packagedElement name="J1939NmMapping:ConstStruct" visibility="public" xmi:id="EAID_f564c0c37c31d9acb7cfa606b606be94" xmi:type="uml:InstanceSpecification"><ownedAttribute aggregation="none" association="EAID_99720c9765e4ce40a3e5f8000da19078" isDerived="false" isDerivedUnion="false" isOrdered="true" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_dst99720c9765e4ce40a3e5f8000da19078" xmi:type="uml:Property"><lowerValue value="0" xmi:id="EAID_f7235e048ae02d6e4d689a7631945289" xmi:type="uml:LiteralInteger"/><upperValue value="-1" xmi:id="EAID_f4dbdb891ec7e156fc399784e85d4554" xmi:type="uml:LiteralUnlimitedNatural"/><type xmi:idref="EAID_736b23f87e4f84ab6f4f112a4c275d56"/></ownedAttribute></packagedElement><packagedElement name="indirection" visibility="public" xmi:id="EAID_99720c9765e4ce40a3e5f8000da19078" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dst99720c9765e4ce40a3e5f8000da19078"/><memberEnd xmi:idref="EAID_src99720c9765e4ce40a3e5f8000da19078"/><ownedEnd aggregation="none" association="EAID_99720c9765e4ce40a3e5f8000da19078" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_src99720c9765e4ce40a3e5f8000da19078" xmi:type="uml:Property"><type xmi:idref="EAID_f564c0c37c31d9acb7cfa606b606be94"/></ownedEnd></packagedElement><packagedElement name="LinSMMapping:ConstStruct" visibility="public" xmi:id="EAID_2fdddeed51c0a69f3bede5f95c808bfc" xmi:type="uml:InstanceSpecification"><ownedAttribute aggregation="none" association="EAID_3a8ee9088a69ebf59d30e7aa6b6772ab" isDerived="false" isDerivedUnion="false" isOrdered="true" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_dst3a8ee9088a69ebf59d30e7aa6b6772ab" xmi:type="uml:Property"><lowerValue value="0" xmi:id="EAID_3f086b1ceadef3d131a8561eaebfebd5" xmi:type="uml:LiteralInteger"/><upperValue value="-1" xmi:id="EAID_f2357bf70f432d8d1ce0cbb80a33e7de" xmi:type="uml:LiteralUnlimitedNatural"/><type xmi:idref="EAID_736b23f87e4f84ab6f4f112a4c275d56"/></ownedAttribute></packagedElement><packagedElement name="indirection" visibility="public" xmi:id="EAID_3a8ee9088a69ebf59d30e7aa6b6772ab" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dst3a8ee9088a69ebf59d30e7aa6b6772ab"/><memberEnd xmi:idref="EAID_src3a8ee9088a69ebf59d30e7aa6b6772ab"/><ownedEnd aggregation="none" association="EAID_3a8ee9088a69ebf59d30e7aa6b6772ab" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_src3a8ee9088a69ebf59d30e7aa6b6772ab" xmi:type="uml:Property"><type xmi:idref="EAID_2fdddeed51c0a69f3bede5f95c808bfc"/></ownedEnd></packagedElement><packagedElement name="LinScheduleEndMapping:ConstStruct" visibility="public" xmi:id="EAID_2ddd09a93c8171fba85908a632445594" xmi:type="uml:InstanceSpecification"><ownedAttribute aggregation="none" association="EAID_09f6e54fc2911490337d1bf26376ee18" isDerived="false" isDerivedUnion="false" isOrdered="true" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_dst09f6e54fc2911490337d1bf26376ee18" xmi:type="uml:Property"><lowerValue value="0" xmi:id="EAID_2397517c03ec28c6097faec362f17f28" xmi:type="uml:LiteralInteger"/><upperValue value="-1" xmi:id="EAID_c87e76ad6c6622f883268a9967d7591d" xmi:type="uml:LiteralUnlimitedNatural"/><type xmi:idref="EAID_736b23f87e4f84ab6f4f112a4c275d56"/></ownedAttribute></packagedElement><packagedElement name="indirection" visibility="public" xmi:id="EAID_09f6e54fc2911490337d1bf26376ee18" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dst09f6e54fc2911490337d1bf26376ee18"/><memberEnd xmi:idref="EAID_src09f6e54fc2911490337d1bf26376ee18"/><ownedEnd aggregation="none" association="EAID_09f6e54fc2911490337d1bf26376ee18" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_src09f6e54fc2911490337d1bf26376ee18" xmi:type="uml:Property"><type xmi:idref="EAID_2ddd09a93c8171fba85908a632445594"/></ownedEnd></packagedElement><packagedElement name="LinScheduleMapping:ConstStruct" visibility="public" xmi:id="EAID_c825ab2f2c373c59418cc35185a0d67d" xmi:type="uml:InstanceSpecification"><ownedAttribute aggregation="none" association="EAID_a2fd97f859a9ff97b20442bb8e4bb315" isDerived="false" isDerivedUnion="false" isOrdered="true" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_dsta2fd97f859a9ff97b20442bb8e4bb315" xmi:type="uml:Property"><lowerValue value="0" xmi:id="EAID_96550c87242c19c4eab6cebfa9b9a0c6" xmi:type="uml:LiteralInteger"/><upperValue value="-1" xmi:id="EAID_6d7385d6a02e1908e6d59021b848f3d7" xmi:type="uml:LiteralUnlimitedNatural"/><type xmi:idref="EAID_736b23f87e4f84ab6f4f112a4c275d56"/></ownedAttribute></packagedElement><packagedElement name="indirection" visibility="public" xmi:id="EAID_a2fd97f859a9ff97b20442bb8e4bb315" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dsta2fd97f859a9ff97b20442bb8e4bb315"/><memberEnd xmi:idref="EAID_srca2fd97f859a9ff97b20442bb8e4bb315"/><ownedEnd aggregation="none" association="EAID_a2fd97f859a9ff97b20442bb8e4bb315" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_srca2fd97f859a9ff97b20442bb8e4bb315" xmi:type="uml:Property"><type xmi:idref="EAID_c825ab2f2c373c59418cc35185a0d67d"/></ownedEnd></packagedElement><packagedElement name="LinTPMapping:ConstStruct" visibility="public" xmi:id="EAID_2f5822e2084d2acc197e7ef5e1e88125" xmi:type="uml:InstanceSpecification"><ownedAttribute aggregation="none" association="EAID_cfb118fb5ff29a5a92e49289b2564882" isDerived="false" isDerivedUnion="false" isOrdered="true" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_dstcfb118fb5ff29a5a92e49289b2564882" xmi:type="uml:Property"><lowerValue value="0" xmi:id="EAID_9d2d928dd057bdc2b539df20c9f14ea2" xmi:type="uml:LiteralInteger"/><upperValue value="-1" xmi:id="EAID_a663bfc22b1ab6a99858b375dba3180b" xmi:type="uml:LiteralUnlimitedNatural"/><type xmi:idref="EAID_736b23f87e4f84ab6f4f112a4c275d56"/></ownedAttribute></packagedElement><packagedElement name="indirection" visibility="public" xmi:id="EAID_cfb118fb5ff29a5a92e49289b2564882" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dstcfb118fb5ff29a5a92e49289b2564882"/><memberEnd xmi:idref="EAID_srccfb118fb5ff29a5a92e49289b2564882"/><ownedEnd aggregation="none" association="EAID_cfb118fb5ff29a5a92e49289b2564882" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_srccfb118fb5ff29a5a92e49289b2564882" xmi:type="uml:Property"><type xmi:idref="EAID_2f5822e2084d2acc197e7ef5e1e88125"/></ownedEnd></packagedElement><packagedElement name="ModeNotificationMapping:ConstStruct" visibility="public" xmi:id="EAID_4c25e97bd5cb480accd82325110a2a96" xmi:type="uml:InstanceSpecification"><ownedAttribute aggregation="none" association="EAID_ba03c70195bce6f28207f22f1cd4edd8" isDerived="false" isDerivedUnion="false" isOrdered="true" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_dstba03c70195bce6f28207f22f1cd4edd8" xmi:type="uml:Property"><lowerValue value="0" xmi:id="EAID_266d8bddd517090659fa63060540e293" xmi:type="uml:LiteralInteger"/><upperValue value="-1" xmi:id="EAID_f34542cacbc0e8c24581e447b7e0dc8d" xmi:type="uml:LiteralUnlimitedNatural"/><type xmi:idref="EAID_736b23f87e4f84ab6f4f112a4c275d56"/></ownedAttribute></packagedElement><packagedElement name="indirection" visibility="public" xmi:id="EAID_ba03c70195bce6f28207f22f1cd4edd8" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dstba03c70195bce6f28207f22f1cd4edd8"/><memberEnd xmi:idref="EAID_srcba03c70195bce6f28207f22f1cd4edd8"/><ownedEnd aggregation="none" association="EAID_ba03c70195bce6f28207f22f1cd4edd8" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_srcba03c70195bce6f28207f22f1cd4edd8" xmi:type="uml:Property"><type xmi:idref="EAID_4c25e97bd5cb480accd82325110a2a96"/></ownedEnd></packagedElement><packagedElement name="ModeRequestMapping:ConstStruct" visibility="public" xmi:id="EAID_6a57bf2c090b65ce442c84f95529fef3" xmi:type="uml:InstanceSpecification"><ownedAttribute aggregation="none" association="EAID_a0bacd43bd11475d2ac4e3858318b932" isDerived="false" isDerivedUnion="false" isOrdered="true" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_dsta0bacd43bd11475d2ac4e3858318b932" xmi:type="uml:Property"><lowerValue value="0" xmi:id="EAID_92c253e581d89112c144a34c55f7c72a" xmi:type="uml:LiteralInteger"/><upperValue value="-1" xmi:id="EAID_6e7522f922288b928fe9a659b844731f" xmi:type="uml:LiteralUnlimitedNatural"/><type xmi:idref="EAID_736b23f87e4f84ab6f4f112a4c275d56"/></ownedAttribute></packagedElement><packagedElement name="indirection" visibility="public" xmi:id="EAID_a0bacd43bd11475d2ac4e3858318b932" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dsta0bacd43bd11475d2ac4e3858318b932"/><memberEnd xmi:idref="EAID_srca0bacd43bd11475d2ac4e3858318b932"/><ownedEnd aggregation="none" association="EAID_a0bacd43bd11475d2ac4e3858318b932" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_srca0bacd43bd11475d2ac4e3858318b932" xmi:type="uml:Property"><type xmi:idref="EAID_6a57bf2c090b65ce442c84f95529fef3"/></ownedEnd></packagedElement><packagedElement name="NmMapping:ConstStruct" visibility="public" xmi:id="EAID_a60f4f05f55df5ec2e7c53c020d9a47d" xmi:type="uml:InstanceSpecification"><ownedAttribute aggregation="none" association="EAID_1afb3c71059f2765c61e4ec03ce60f5a" isDerived="false" isDerivedUnion="false" isOrdered="true" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_dst1afb3c71059f2765c61e4ec03ce60f5a" xmi:type="uml:Property"><lowerValue value="0" xmi:id="EAID_beacba898257784a26076bda7f011ede" xmi:type="uml:LiteralInteger"/><upperValue value="-1" xmi:id="EAID_aa01ba30b2bf9f11d6b12bbd8ade3750" xmi:type="uml:LiteralUnlimitedNatural"/><type xmi:idref="EAID_736b23f87e4f84ab6f4f112a4c275d56"/></ownedAttribute></packagedElement><packagedElement name="indirection" visibility="public" xmi:id="EAID_1afb3c71059f2765c61e4ec03ce60f5a" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dst1afb3c71059f2765c61e4ec03ce60f5a"/><memberEnd xmi:idref="EAID_src1afb3c71059f2765c61e4ec03ce60f5a"/><ownedEnd aggregation="none" association="EAID_1afb3c71059f2765c61e4ec03ce60f5a" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_src1afb3c71059f2765c61e4ec03ce60f5a" xmi:type="uml:Property"><type xmi:idref="EAID_a60f4f05f55df5ec2e7c53c020d9a47d"/></ownedEnd></packagedElement><packagedElement name="NvMBlockMapping:ConstStruct" visibility="public" xmi:id="EAID_8054289471090aaf93f03b02401f1ab3" xmi:type="uml:InstanceSpecification"><ownedAttribute aggregation="none" association="EAID_358c0e8a9164ca79300fef910e086604" isDerived="false" isDerivedUnion="false" isOrdered="true" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_dst358c0e8a9164ca79300fef910e086604" xmi:type="uml:Property"><lowerValue value="0" xmi:id="EAID_aa14456006d5fea8d365ad35d1a7429d" xmi:type="uml:LiteralInteger"/><upperValue value="-1" xmi:id="EAID_263a5d5b3806b1564af1797bddd90262" xmi:type="uml:LiteralUnlimitedNatural"/><type xmi:idref="EAID_736b23f87e4f84ab6f4f112a4c275d56"/></ownedAttribute></packagedElement><packagedElement name="indirection" visibility="public" xmi:id="EAID_358c0e8a9164ca79300fef910e086604" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dst358c0e8a9164ca79300fef910e086604"/><memberEnd xmi:idref="EAID_src358c0e8a9164ca79300fef910e086604"/><ownedEnd aggregation="none" association="EAID_358c0e8a9164ca79300fef910e086604" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_src358c0e8a9164ca79300fef910e086604" xmi:type="uml:Property"><type xmi:idref="EAID_8054289471090aaf93f03b02401f1ab3"/></ownedEnd></packagedElement><packagedElement name="NvMJobMapping:ConstStruct" visibility="public" xmi:id="EAID_6b0c2255d55263a1c9ca7e897f2ff4ed" xmi:type="uml:InstanceSpecification"><ownedAttribute aggregation="none" association="EAID_b67aeef8bfb03841c4068ec3842df3de" isDerived="false" isDerivedUnion="false" isOrdered="true" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_dstb67aeef8bfb03841c4068ec3842df3de" xmi:type="uml:Property"><lowerValue value="0" xmi:id="EAID_6d8970acd99e789f4ae06adb9c3b0c9c" xmi:type="uml:LiteralInteger"/><upperValue value="-1" xmi:id="EAID_1aa21dd26d305f94fe8d845a46745f48" xmi:type="uml:LiteralUnlimitedNatural"/><type xmi:idref="EAID_736b23f87e4f84ab6f4f112a4c275d56"/></ownedAttribute></packagedElement><packagedElement name="indirection" visibility="public" xmi:id="EAID_b67aeef8bfb03841c4068ec3842df3de" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dstb67aeef8bfb03841c4068ec3842df3de"/><memberEnd xmi:idref="EAID_srcb67aeef8bfb03841c4068ec3842df3de"/><ownedEnd aggregation="none" association="EAID_b67aeef8bfb03841c4068ec3842df3de" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_srcb67aeef8bfb03841c4068ec3842df3de" xmi:type="uml:Property"><type xmi:idref="EAID_6b0c2255d55263a1c9ca7e897f2ff4ed"/></ownedEnd></packagedElement><packagedElement name="PduRRxIndicationMapping:ConstStruct" visibility="public" xmi:id="EAID_dca23268bf9314a308078fdd0f25566f" xmi:type="uml:InstanceSpecification"><ownedAttribute aggregation="none" association="EAID_5470b4ccba472aa92c0b65b750b0e50c" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_dst5470b4ccba472aa92c0b65b750b0e50c" xmi:type="uml:Property"><lowerValue value="1" xmi:id="EAID_2f102ef8911ff3ea292656afc6a0db93" xmi:type="uml:LiteralInteger"/><upperValue value="1" xmi:id="EAID_154c1805ad370324756f2d3d925de8a7" xmi:type="uml:LiteralInteger"/><type xmi:idref="EAID_f8c887515503bedbdce88a68931a2ac6"/></ownedAttribute><ownedAttribute aggregation="none" association="EAID_7686b2a95b75de889a5ff6c7688bf7f3" isDerived="false" isDerivedUnion="false" isOrdered="true" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_dst7686b2a95b75de889a5ff6c7688bf7f3" xmi:type="uml:Property"><lowerValue value="0" xmi:id="EAID_2d71b87e447a4cafa187b3bd10296f88" xmi:type="uml:LiteralInteger"/><upperValue value="-1" xmi:id="EAID_96a4923a4b5cfab4de72dadd2e452df5" xmi:type="uml:LiteralUnlimitedNatural"/><type xmi:idref="EAID_736b23f87e4f84ab6f4f112a4c275d56"/></ownedAttribute></packagedElement><packagedElement name="indirection" visibility="public" xmi:id="EAID_5470b4ccba472aa92c0b65b750b0e50c" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dst5470b4ccba472aa92c0b65b750b0e50c"/><memberEnd xmi:idref="EAID_src5470b4ccba472aa92c0b65b750b0e50c"/><ownedEnd aggregation="none" association="EAID_5470b4ccba472aa92c0b65b750b0e50c" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_src5470b4ccba472aa92c0b65b750b0e50c" xmi:type="uml:Property"><type xmi:idref="EAID_dca23268bf9314a308078fdd0f25566f"/></ownedEnd></packagedElement><packagedElement name="indirection" visibility="public" xmi:id="EAID_7686b2a95b75de889a5ff6c7688bf7f3" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dst7686b2a95b75de889a5ff6c7688bf7f3"/><memberEnd xmi:idref="EAID_src7686b2a95b75de889a5ff6c7688bf7f3"/><ownedEnd aggregation="none" association="EAID_7686b2a95b75de889a5ff6c7688bf7f3" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_src7686b2a95b75de889a5ff6c7688bf7f3" xmi:type="uml:Property"><type xmi:idref="EAID_dca23268bf9314a308078fdd0f25566f"/></ownedEnd></packagedElement><packagedElement name="PduRTpRxIndicationMapping:ConstStruct" visibility="public" xmi:id="EAID_4570df5896ab42d50e3cb8875a98f345" xmi:type="uml:InstanceSpecification"><ownedAttribute aggregation="none" association="EAID_d4ebdf62d49e65f21d979d8209d14470" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_dstd4ebdf62d49e65f21d979d8209d14470" xmi:type="uml:Property"><lowerValue value="1" xmi:id="EAID_2ad69fca42a6f9ebfd7e46e3bf2d9dd4" xmi:type="uml:LiteralInteger"/><upperValue value="1" xmi:id="EAID_896b097f1778827af9e8e8ddfe71737b" xmi:type="uml:LiteralInteger"/><type xmi:idref="EAID_b9aabb503e0211b4d456ce4eb38eb8fa"/></ownedAttribute><ownedAttribute aggregation="none" association="EAID_7e0c2e30a0497b8e9d68bf9000a22f6d" isDerived="false" isDerivedUnion="false" isOrdered="true" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_dst7e0c2e30a0497b8e9d68bf9000a22f6d" xmi:type="uml:Property"><lowerValue value="0" xmi:id="EAID_e455e37929eb0bce4fe142338ba3aacb" xmi:type="uml:LiteralInteger"/><upperValue value="-1" xmi:id="EAID_56fce4bfb874342749561ecb2e6c9df7" xmi:type="uml:LiteralUnlimitedNatural"/><type xmi:idref="EAID_736b23f87e4f84ab6f4f112a4c275d56"/></ownedAttribute></packagedElement><packagedElement name="indirection" visibility="public" xmi:id="EAID_d4ebdf62d49e65f21d979d8209d14470" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dstd4ebdf62d49e65f21d979d8209d14470"/><memberEnd xmi:idref="EAID_srcd4ebdf62d49e65f21d979d8209d14470"/><ownedEnd aggregation="none" association="EAID_d4ebdf62d49e65f21d979d8209d14470" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_srcd4ebdf62d49e65f21d979d8209d14470" xmi:type="uml:Property"><type xmi:idref="EAID_4570df5896ab42d50e3cb8875a98f345"/></ownedEnd></packagedElement><packagedElement name="indirection" visibility="public" xmi:id="EAID_7e0c2e30a0497b8e9d68bf9000a22f6d" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dst7e0c2e30a0497b8e9d68bf9000a22f6d"/><memberEnd xmi:idref="EAID_src7e0c2e30a0497b8e9d68bf9000a22f6d"/><ownedEnd aggregation="none" association="EAID_7e0c2e30a0497b8e9d68bf9000a22f6d" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_src7e0c2e30a0497b8e9d68bf9000a22f6d" xmi:type="uml:Property"><type xmi:idref="EAID_4570df5896ab42d50e3cb8875a98f345"/></ownedEnd></packagedElement><packagedElement name="PduRTpStartOfReceptionMapping:ConstStruct" visibility="public" xmi:id="EAID_d9d96d1680ba023d123166ec09c0ad26" xmi:type="uml:InstanceSpecification"><ownedAttribute aggregation="none" association="EAID_73c8d511ce71be2c587d1ee9f7301e6f" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_dst73c8d511ce71be2c587d1ee9f7301e6f" xmi:type="uml:Property"><lowerValue value="1" xmi:id="EAID_e36d20baecd677d0f14e15a01a449c0a" xmi:type="uml:LiteralInteger"/><upperValue value="1" xmi:id="EAID_777be8b30877514c635e7954e145e983" xmi:type="uml:LiteralInteger"/><type xmi:idref="EAID_3c497359d34b2ff8c0e078f087cf6eac"/></ownedAttribute><ownedAttribute aggregation="none" association="EAID_6c99ab46d843271bcd4cb69b3138077c" isDerived="false" isDerivedUnion="false" isOrdered="true" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_dst6c99ab46d843271bcd4cb69b3138077c" xmi:type="uml:Property"><lowerValue value="0" xmi:id="EAID_a751e53ecc8c0fb0a936750b03f34257" xmi:type="uml:LiteralInteger"/><upperValue value="-1" xmi:id="EAID_6a8ea97f6a4434b1ae7c9c56566434f5" xmi:type="uml:LiteralUnlimitedNatural"/><type xmi:idref="EAID_736b23f87e4f84ab6f4f112a4c275d56"/></ownedAttribute></packagedElement><packagedElement name="indirection" visibility="public" xmi:id="EAID_73c8d511ce71be2c587d1ee9f7301e6f" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dst73c8d511ce71be2c587d1ee9f7301e6f"/><memberEnd xmi:idref="EAID_src73c8d511ce71be2c587d1ee9f7301e6f"/><ownedEnd aggregation="none" association="EAID_73c8d511ce71be2c587d1ee9f7301e6f" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_src73c8d511ce71be2c587d1ee9f7301e6f" xmi:type="uml:Property"><type xmi:idref="EAID_d9d96d1680ba023d123166ec09c0ad26"/></ownedEnd></packagedElement><packagedElement name="indirection" visibility="public" xmi:id="EAID_6c99ab46d843271bcd4cb69b3138077c" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dst6c99ab46d843271bcd4cb69b3138077c"/><memberEnd xmi:idref="EAID_src6c99ab46d843271bcd4cb69b3138077c"/><ownedEnd aggregation="none" association="EAID_6c99ab46d843271bcd4cb69b3138077c" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_src6c99ab46d843271bcd4cb69b3138077c" xmi:type="uml:Property"><type xmi:idref="EAID_d9d96d1680ba023d123166ec09c0ad26"/></ownedEnd></packagedElement><packagedElement name="PduRTpTxConfirmationMapping:ConstStruct" visibility="public" xmi:id="EAID_25c789e736b4f0674042f030daef27a5" xmi:type="uml:InstanceSpecification"><ownedAttribute aggregation="none" association="EAID_55be133e605a025f2946e391b7bb2a1a" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_dst55be133e605a025f2946e391b7bb2a1a" xmi:type="uml:Property"><lowerValue value="1" xmi:id="EAID_5997c5945e87f3e7eb153db940f549cd" xmi:type="uml:LiteralInteger"/><upperValue value="1" xmi:id="EAID_5558149cf20ca6ce042c38b66f8b4649" xmi:type="uml:LiteralInteger"/><type xmi:idref="EAID_17e562fb8c0fb0e0271ef6133a83dd5e"/></ownedAttribute><ownedAttribute aggregation="none" association="EAID_066d9dcc663aa623cb684276ecdd8e63" isDerived="false" isDerivedUnion="false" isOrdered="true" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_dst066d9dcc663aa623cb684276ecdd8e63" xmi:type="uml:Property"><lowerValue value="0" xmi:id="EAID_737a58d0c7b9046c6e7b19943b992347" xmi:type="uml:LiteralInteger"/><upperValue value="-1" xmi:id="EAID_92dbaa1ca46c783e91cea9eae2c3a194" xmi:type="uml:LiteralUnlimitedNatural"/><type xmi:idref="EAID_736b23f87e4f84ab6f4f112a4c275d56"/></ownedAttribute></packagedElement><packagedElement name="indirection" visibility="public" xmi:id="EAID_55be133e605a025f2946e391b7bb2a1a" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dst55be133e605a025f2946e391b7bb2a1a"/><memberEnd xmi:idref="EAID_src55be133e605a025f2946e391b7bb2a1a"/><ownedEnd aggregation="none" association="EAID_55be133e605a025f2946e391b7bb2a1a" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_src55be133e605a025f2946e391b7bb2a1a" xmi:type="uml:Property"><type xmi:idref="EAID_25c789e736b4f0674042f030daef27a5"/></ownedEnd></packagedElement><packagedElement name="indirection" visibility="public" xmi:id="EAID_066d9dcc663aa623cb684276ecdd8e63" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dst066d9dcc663aa623cb684276ecdd8e63"/><memberEnd xmi:idref="EAID_src066d9dcc663aa623cb684276ecdd8e63"/><ownedEnd aggregation="none" association="EAID_066d9dcc663aa623cb684276ecdd8e63" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_src066d9dcc663aa623cb684276ecdd8e63" xmi:type="uml:Property"><type xmi:idref="EAID_25c789e736b4f0674042f030daef27a5"/></ownedEnd></packagedElement><packagedElement name="PduRTransmitMapping:ConstStruct" visibility="public" xmi:id="EAID_de0b701f4135993e03cf8835354a29a1" xmi:type="uml:InstanceSpecification"><ownedAttribute aggregation="none" association="EAID_6f0de7c21e53547a95b0fd12ed1a2fcf" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_dst6f0de7c21e53547a95b0fd12ed1a2fcf" xmi:type="uml:Property"><lowerValue value="1" xmi:id="EAID_82804815c0682d6fd359604a4bc46c85" xmi:type="uml:LiteralInteger"/><upperValue value="1" xmi:id="EAID_9a3b13353865874cfa14919c5868ad6c" xmi:type="uml:LiteralInteger"/><type xmi:idref="EAID_3fe61c0afd7ae951b47f2fe4ed7a45fe"/></ownedAttribute><ownedAttribute aggregation="none" association="EAID_bbaf27b22b6375cf72602a162b9cad90" isDerived="false" isDerivedUnion="false" isOrdered="true" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_dstbbaf27b22b6375cf72602a162b9cad90" xmi:type="uml:Property"><lowerValue value="0" xmi:id="EAID_32639c781bb3dbc4e1d2fddfb8ed1ebd" xmi:type="uml:LiteralInteger"/><upperValue value="-1" xmi:id="EAID_c38e25f025b5757ad9e1bde9d245f351" xmi:type="uml:LiteralUnlimitedNatural"/><type xmi:idref="EAID_736b23f87e4f84ab6f4f112a4c275d56"/></ownedAttribute></packagedElement><packagedElement name="indirection" visibility="public" xmi:id="EAID_6f0de7c21e53547a95b0fd12ed1a2fcf" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dst6f0de7c21e53547a95b0fd12ed1a2fcf"/><memberEnd xmi:idref="EAID_src6f0de7c21e53547a95b0fd12ed1a2fcf"/><ownedEnd aggregation="none" association="EAID_6f0de7c21e53547a95b0fd12ed1a2fcf" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_src6f0de7c21e53547a95b0fd12ed1a2fcf" xmi:type="uml:Property"><type xmi:idref="EAID_de0b701f4135993e03cf8835354a29a1"/></ownedEnd></packagedElement><packagedElement name="indirection" visibility="public" xmi:id="EAID_bbaf27b22b6375cf72602a162b9cad90" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dstbbaf27b22b6375cf72602a162b9cad90"/><memberEnd xmi:idref="EAID_srcbbaf27b22b6375cf72602a162b9cad90"/><ownedEnd aggregation="none" association="EAID_bbaf27b22b6375cf72602a162b9cad90" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_srcbbaf27b22b6375cf72602a162b9cad90" xmi:type="uml:Property"><type xmi:idref="EAID_de0b701f4135993e03cf8835354a29a1"/></ownedEnd></packagedElement><packagedElement name="PduRTxConfirmationMapping:ConstStruct" visibility="public" xmi:id="EAID_a54b478336d897c37a8f7e4377062f5a" xmi:type="uml:InstanceSpecification"><ownedAttribute aggregation="none" association="EAID_31bd0de8465464b7db2ddce493eecbd3" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_dst31bd0de8465464b7db2ddce493eecbd3" xmi:type="uml:Property"><lowerValue value="1" xmi:id="EAID_bd204d1bd7a663732b253ea4451d42a6" xmi:type="uml:LiteralInteger"/><upperValue value="1" xmi:id="EAID_61713353aa70eb630ecc9f306bd8ca54" xmi:type="uml:LiteralInteger"/><type xmi:idref="EAID_bf3a554c297cd87ac58a84572b9c8e26"/></ownedAttribute><ownedAttribute aggregation="none" association="EAID_1a9cdae7a6111b4ee5771ce0342ce5e0" isDerived="false" isDerivedUnion="false" isOrdered="true" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_dst1a9cdae7a6111b4ee5771ce0342ce5e0" xmi:type="uml:Property"><lowerValue value="0" xmi:id="EAID_2d29dfd545cae3c45cd9fbd532775d74" xmi:type="uml:LiteralInteger"/><upperValue value="-1" xmi:id="EAID_ce9c368d6f25280acf79637733eeb6a6" xmi:type="uml:LiteralUnlimitedNatural"/><type xmi:idref="EAID_736b23f87e4f84ab6f4f112a4c275d56"/></ownedAttribute></packagedElement><packagedElement name="indirection" visibility="public" xmi:id="EAID_31bd0de8465464b7db2ddce493eecbd3" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dst31bd0de8465464b7db2ddce493eecbd3"/><memberEnd xmi:idref="EAID_src31bd0de8465464b7db2ddce493eecbd3"/><ownedEnd aggregation="none" association="EAID_31bd0de8465464b7db2ddce493eecbd3" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_src31bd0de8465464b7db2ddce493eecbd3" xmi:type="uml:Property"><type xmi:idref="EAID_a54b478336d897c37a8f7e4377062f5a"/></ownedEnd></packagedElement><packagedElement name="indirection" visibility="public" xmi:id="EAID_1a9cdae7a6111b4ee5771ce0342ce5e0" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dst1a9cdae7a6111b4ee5771ce0342ce5e0"/><memberEnd xmi:idref="EAID_src1a9cdae7a6111b4ee5771ce0342ce5e0"/><ownedEnd aggregation="none" association="EAID_1a9cdae7a6111b4ee5771ce0342ce5e0" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_src1a9cdae7a6111b4ee5771ce0342ce5e0" xmi:type="uml:Property"><type xmi:idref="EAID_a54b478336d897c37a8f7e4377062f5a"/></ownedEnd></packagedElement><packagedElement name="Rules:ConstStruct" visibility="public" xmi:id="EAID_6d3ab65f9bb3e78db2ddcf0fd6277073" xmi:type="uml:InstanceSpecification"><ownedAttribute aggregation="none" association="EAID_1961a9588bac8529350a3e7bf68e6f17" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_dst1961a9588bac8529350a3e7bf68e6f17" xmi:type="uml:Property"><lowerValue value="1" xmi:id="EAID_af2ad8a24d2ed8291918722e2104b8e7" xmi:type="uml:LiteralInteger"/><upperValue value="1" xmi:id="EAID_f995abb55fe3d7b643d7147720c30c61" xmi:type="uml:LiteralInteger"/><type xmi:idref="EAID_deb4cd790a8d925299ba0870fdbd0d09"/></ownedAttribute></packagedElement><packagedElement name="indirection" visibility="public" xmi:id="EAID_1961a9588bac8529350a3e7bf68e6f17" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dst1961a9588bac8529350a3e7bf68e6f17"/><memberEnd xmi:idref="EAID_src1961a9588bac8529350a3e7bf68e6f17"/><ownedEnd aggregation="none" association="EAID_1961a9588bac8529350a3e7bf68e6f17" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_src1961a9588bac8529350a3e7bf68e6f17" xmi:type="uml:Property"><type xmi:idref="EAID_6d3ab65f9bb3e78db2ddcf0fd6277073"/></ownedEnd></packagedElement><packagedElement name="SdClientServiceMapping:ConstStruct" visibility="public" xmi:id="EAID_73ba171504d9bc327fa65885ba2f3e46" xmi:type="uml:InstanceSpecification"><ownedAttribute aggregation="none" association="EAID_01b6b6ad9865d7d80390263b83428796" isDerived="false" isDerivedUnion="false" isOrdered="true" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_dst01b6b6ad9865d7d80390263b83428796" xmi:type="uml:Property"><lowerValue value="0" xmi:id="EAID_ba13459059600d2860d1f678c383613f" xmi:type="uml:LiteralInteger"/><upperValue value="-1" xmi:id="EAID_d9ba781ca6c131090a10a3d7b39ea17e" xmi:type="uml:LiteralUnlimitedNatural"/><type xmi:idref="EAID_736b23f87e4f84ab6f4f112a4c275d56"/></ownedAttribute></packagedElement><packagedElement name="indirection" visibility="public" xmi:id="EAID_01b6b6ad9865d7d80390263b83428796" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dst01b6b6ad9865d7d80390263b83428796"/><memberEnd xmi:idref="EAID_src01b6b6ad9865d7d80390263b83428796"/><ownedEnd aggregation="none" association="EAID_01b6b6ad9865d7d80390263b83428796" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_src01b6b6ad9865d7d80390263b83428796" xmi:type="uml:Property"><type xmi:idref="EAID_73ba171504d9bc327fa65885ba2f3e46"/></ownedEnd></packagedElement><packagedElement name="SdConsumedEventMapping:ConstStruct" visibility="public" xmi:id="EAID_1a406a988d77b67daad6f863e0d267d6" xmi:type="uml:InstanceSpecification"><ownedAttribute aggregation="none" association="EAID_d875ecf9a67e8b2f0adaf0ea90374bab" isDerived="false" isDerivedUnion="false" isOrdered="true" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_dstd875ecf9a67e8b2f0adaf0ea90374bab" xmi:type="uml:Property"><lowerValue value="0" xmi:id="EAID_75baf1b20a3f72e7988cc9fd7f45bc98" xmi:type="uml:LiteralInteger"/><upperValue value="-1" xmi:id="EAID_d0f8ca7bb4027352a67f8725361cddec" xmi:type="uml:LiteralUnlimitedNatural"/><type xmi:idref="EAID_736b23f87e4f84ab6f4f112a4c275d56"/></ownedAttribute></packagedElement><packagedElement name="indirection" visibility="public" xmi:id="EAID_d875ecf9a67e8b2f0adaf0ea90374bab" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dstd875ecf9a67e8b2f0adaf0ea90374bab"/><memberEnd xmi:idref="EAID_srcd875ecf9a67e8b2f0adaf0ea90374bab"/><ownedEnd aggregation="none" association="EAID_d875ecf9a67e8b2f0adaf0ea90374bab" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_srcd875ecf9a67e8b2f0adaf0ea90374bab" xmi:type="uml:Property"><type xmi:idref="EAID_1a406a988d77b67daad6f863e0d267d6"/></ownedEnd></packagedElement><packagedElement name="SdEventHandlerMapping:ConstStruct" visibility="public" xmi:id="EAID_8bcd80b7d0c042d31449cd6be786b997" xmi:type="uml:InstanceSpecification"><ownedAttribute aggregation="none" association="EAID_e5e90b12d5bfcacbfe7730e912178c59" isDerived="false" isDerivedUnion="false" isOrdered="true" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_dste5e90b12d5bfcacbfe7730e912178c59" xmi:type="uml:Property"><lowerValue value="0" xmi:id="EAID_843f41fb69b38bd060436d8c6bd8082d" xmi:type="uml:LiteralInteger"/><upperValue value="-1" xmi:id="EAID_6341a24c2dfb3231fae3c7e73cdbf411" xmi:type="uml:LiteralUnlimitedNatural"/><type xmi:idref="EAID_736b23f87e4f84ab6f4f112a4c275d56"/></ownedAttribute></packagedElement><packagedElement name="indirection" visibility="public" xmi:id="EAID_e5e90b12d5bfcacbfe7730e912178c59" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dste5e90b12d5bfcacbfe7730e912178c59"/><memberEnd xmi:idref="EAID_srce5e90b12d5bfcacbfe7730e912178c59"/><ownedEnd aggregation="none" association="EAID_e5e90b12d5bfcacbfe7730e912178c59" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_srce5e90b12d5bfcacbfe7730e912178c59" xmi:type="uml:Property"><type xmi:idref="EAID_8bcd80b7d0c042d31449cd6be786b997"/></ownedEnd></packagedElement><packagedElement name="WdgMMapping:ConstStruct" visibility="public" xmi:id="EAID_a366686ddfeaf0470ea2b08764eb9e18" xmi:type="uml:InstanceSpecification"><ownedAttribute aggregation="none" association="EAID_75cb43aa74a87fbc9a5db1b7443a554c" isDerived="false" isDerivedUnion="false" isOrdered="true" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_dst75cb43aa74a87fbc9a5db1b7443a554c" xmi:type="uml:Property"><lowerValue value="0" xmi:id="EAID_99bc4fead5f3e9da5df4150e250bfb83" xmi:type="uml:LiteralInteger"/><upperValue value="-1" xmi:id="EAID_16b086340c25ffe363df707201de147f" xmi:type="uml:LiteralUnlimitedNatural"/><type xmi:idref="EAID_736b23f87e4f84ab6f4f112a4c275d56"/></ownedAttribute></packagedElement><packagedElement name="indirection" visibility="public" xmi:id="EAID_75cb43aa74a87fbc9a5db1b7443a554c" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dst75cb43aa74a87fbc9a5db1b7443a554c"/><memberEnd xmi:idref="EAID_src75cb43aa74a87fbc9a5db1b7443a554c"/><ownedEnd aggregation="none" association="EAID_75cb43aa74a87fbc9a5db1b7443a554c" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_src75cb43aa74a87fbc9a5db1b7443a554c" xmi:type="uml:Property"><type xmi:idref="EAID_a366686ddfeaf0470ea2b08764eb9e18"/></ownedEnd></packagedElement><packagedElement name="InitGenVarAndInitAL:ConstArray" visibility="public" xmi:id="EAID_13bce6af878e8272764dd53353384f97" xmi:type="uml:InstanceSpecification"/><packagedElement name="ModeNotificationFct:ConstArray" visibility="public" xmi:id="EAID_4be79be67ff68b1a41e4829ab431c6ab" xmi:type="uml:InstanceSpecification"/><packagedElement name="SwcModeRequestUpdateFct:ConstArray" visibility="public" xmi:id="EAID_b591a756b0430239f853601eb6ff1d34" xmi:type="uml:InstanceSpecification"/><packagedElement name="ActionListPriorityQueue:VarArray" visibility="public" xmi:id="EAID_8d980ae5825cc02ef281dc2498ff8276" xmi:type="uml:InstanceSpecification"/><packagedElement name="VarIndirectableSizeByConstIndirecableRelation" visibility="public" xmi:id="EAID_68dd67f33eaf08f260da73c0111d5696" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dst68dd67f33eaf08f260da73c0111d5696"/><ownedEnd aggregation="none" association="EAID_68dd67f33eaf08f260da73c0111d5696" visibility="public" xmi:id="EAID_dst68dd67f33eaf08f260da73c0111d5696" xmi:type="uml:Property"><type xmi:idref="EAID_8fffe8b4c6b0bb3cff10a8f4dda834f9"/></ownedEnd><memberEnd xmi:idref="EAID_src68dd67f33eaf08f260da73c0111d5696"/><ownedEnd aggregation="none" association="EAID_68dd67f33eaf08f260da73c0111d5696" visibility="public" xmi:id="EAID_src68dd67f33eaf08f260da73c0111d5696" xmi:type="uml:Property"><type xmi:idref="EAID_8d980ae5825cc02ef281dc2498ff8276"/></ownedEnd></packagedElement><packagedElement name="ActionListQueue:VarArray" visibility="public" xmi:id="EAID_77f1f5474de5c097c5f46090d7b95e22" xmi:type="uml:InstanceSpecification"/><packagedElement name="VarIndirectableSizeByConstIndirecableRelation" visibility="public" xmi:id="EAID_a2f61eda6fbafa28e9e9341b2e52d2c1" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dsta2f61eda6fbafa28e9e9341b2e52d2c1"/><ownedEnd aggregation="none" association="EAID_a2f61eda6fbafa28e9e9341b2e52d2c1" visibility="public" xmi:id="EAID_dsta2f61eda6fbafa28e9e9341b2e52d2c1" xmi:type="uml:Property"><type xmi:idref="EAID_8fffe8b4c6b0bb3cff10a8f4dda834f9"/></ownedEnd><memberEnd xmi:idref="EAID_srca2f61eda6fbafa28e9e9341b2e52d2c1"/><ownedEnd aggregation="none" association="EAID_a2f61eda6fbafa28e9e9341b2e52d2c1" visibility="public" xmi:id="EAID_srca2f61eda6fbafa28e9e9341b2e52d2c1" xmi:type="uml:Property"><type xmi:idref="EAID_77f1f5474de5c097c5f46090d7b95e22"/></ownedEnd></packagedElement><packagedElement name="CanSMChannelState:VarArray" visibility="public" xmi:id="EAID_3c10edb7f4de06c74906aaf0c8aff755" xmi:type="uml:InstanceSpecification"/><packagedElement name="ComMChannelState:VarArray" visibility="public" xmi:id="EAID_d89319db780de5953be15da15b29fea4" xmi:type="uml:InstanceSpecification"/><packagedElement name="ComMPncState:VarArray" visibility="public" xmi:id="EAID_aab8a9894c175591bb2ed223682446d2" xmi:type="uml:InstanceSpecification"/><packagedElement name="DcmComState:VarArray" visibility="public" xmi:id="EAID_d05992d737490f1c22607b265339f321" xmi:type="uml:InstanceSpecification"/><packagedElement name="EcuMRunRequestState:VarArray" visibility="public" xmi:id="EAID_d6b19081a1717e96aca720d19c549d0f" xmi:type="uml:InstanceSpecification"/><packagedElement name="EcuMWakeupState:VarArray" visibility="public" xmi:id="EAID_ed09cf7dc5ff034db3934bce408ab274" xmi:type="uml:InstanceSpecification"/><packagedElement name="EthIfPortState:VarArray" visibility="public" xmi:id="EAID_290f7efd4a165002388b9d74983797ed" xmi:type="uml:InstanceSpecification"/><packagedElement name="EthSMState:VarArray" visibility="public" xmi:id="EAID_99158b66258e6db32e26958d24c2f457" xmi:type="uml:InstanceSpecification"/><packagedElement name="FrSMState:VarArray" visibility="public" xmi:id="EAID_6c9ff6840305b8093fcb24c199298b6a" xmi:type="uml:InstanceSpecification"/><packagedElement name="GenericState:VarArray" visibility="public" xmi:id="EAID_171701682fc470d3b63f7c2ca2cb7d41" xmi:type="uml:InstanceSpecification"/><packagedElement name="J1939NmState:VarArray" visibility="public" xmi:id="EAID_8cfbe97fdd31b179538802e9fe4bc42f" xmi:type="uml:InstanceSpecification"/><packagedElement name="LinSMState:VarArray" visibility="public" xmi:id="EAID_31dc458ee6f35226a71bc9888c5f229e" xmi:type="uml:InstanceSpecification"/><packagedElement name="LinScheduleEndState:VarArray" visibility="public" xmi:id="EAID_a25b859f884aa8eaeea88b06db52a2cf" xmi:type="uml:InstanceSpecification"/><packagedElement name="LinScheduleState:VarArray" visibility="public" xmi:id="EAID_335562ed44a8bc4ec2ab7220067315e8" xmi:type="uml:InstanceSpecification"/><packagedElement name="LinTPState:VarArray" visibility="public" xmi:id="EAID_8e541ea950e51db2c18e4e0dce6688c5" xmi:type="uml:InstanceSpecification"/><packagedElement name="ModeRequestQueue:VarArray" visibility="public" xmi:id="EAID_1a4bfcce0d9b189ee94241961ae7d4c4" xmi:type="uml:InstanceSpecification"/><packagedElement name="VarIndirectableSizeByConstIndirecableRelation" visibility="public" xmi:id="EAID_90b1c11163b6886d3f8df75d0222b12b" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dst90b1c11163b6886d3f8df75d0222b12b"/><ownedEnd aggregation="none" association="EAID_90b1c11163b6886d3f8df75d0222b12b" visibility="public" xmi:id="EAID_dst90b1c11163b6886d3f8df75d0222b12b" xmi:type="uml:Property"><type xmi:idref="EAID_736b23f87e4f84ab6f4f112a4c275d56"/></ownedEnd><memberEnd xmi:idref="EAID_src90b1c11163b6886d3f8df75d0222b12b"/><ownedEnd aggregation="none" association="EAID_90b1c11163b6886d3f8df75d0222b12b" visibility="public" xmi:id="EAID_src90b1c11163b6886d3f8df75d0222b12b" xmi:type="uml:Property"><type xmi:idref="EAID_1a4bfcce0d9b189ee94241961ae7d4c4"/></ownedEnd></packagedElement><packagedElement name="NmState:VarArray" visibility="public" xmi:id="EAID_d8394a9f0893f71b130b48a13c06d820" xmi:type="uml:InstanceSpecification"/><packagedElement name="NvMBlockState:VarArray" visibility="public" xmi:id="EAID_c9a206cfb74bd3118a17f6a808a5ab7a" xmi:type="uml:InstanceSpecification"/><packagedElement name="NvMJobState:VarArray" visibility="public" xmi:id="EAID_d0d31ae91933181268654c928f2fb086" xmi:type="uml:InstanceSpecification"/><packagedElement name="PduRRxIndicationState:VarArray" visibility="public" xmi:id="EAID_f8c887515503bedbdce88a68931a2ac6" xmi:type="uml:InstanceSpecification"/><packagedElement name="PduRTpRxIndicationState:VarArray" visibility="public" xmi:id="EAID_b9aabb503e0211b4d456ce4eb38eb8fa" xmi:type="uml:InstanceSpecification"/><packagedElement name="PduRTpStartOfReceptionState:VarArray" visibility="public" xmi:id="EAID_3c497359d34b2ff8c0e078f087cf6eac" xmi:type="uml:InstanceSpecification"/><packagedElement name="PduRTpTxConfirmationState:VarArray" visibility="public" xmi:id="EAID_17e562fb8c0fb0e0271ef6133a83dd5e" xmi:type="uml:InstanceSpecification"/><packagedElement name="PduRTransmitState:VarArray" visibility="public" xmi:id="EAID_3fe61c0afd7ae951b47f2fe4ed7a45fe" xmi:type="uml:InstanceSpecification"/><packagedElement name="PduRTxConfirmationState:VarArray" visibility="public" xmi:id="EAID_bf3a554c297cd87ac58a84572b9c8e26" xmi:type="uml:InstanceSpecification"/><packagedElement name="RuleStates:VarArray" visibility="public" xmi:id="EAID_deb4cd790a8d925299ba0870fdbd0d09" xmi:type="uml:InstanceSpecification"/><packagedElement name="SdClientServiceState:VarArray" visibility="public" xmi:id="EAID_98c4b1bf6f3f551c791611e1ad670c91" xmi:type="uml:InstanceSpecification"/><packagedElement name="SdConsumedEventState:VarArray" visibility="public" xmi:id="EAID_f47838c62158a1f397168af542485878" xmi:type="uml:InstanceSpecification"/><packagedElement name="SdEventHandlerState:VarArray" visibility="public" xmi:id="EAID_94ee2d14d35f6272f20cd647c3e6d7c8" xmi:type="uml:InstanceSpecification"/><packagedElement name="TimerState:VarArray" visibility="public" xmi:id="EAID_258d98afc41927a84cb6eb39ca23cef6" xmi:type="uml:InstanceSpecification"/><packagedElement name="TimerValue:VarArray" visibility="public" xmi:id="EAID_727f53355f4ed7c375697933a1a1f303" xmi:type="uml:InstanceSpecification"/><packagedElement name="WdgMState:VarArray" visibility="public" xmi:id="EAID_d2a37ea767b79ca1d06fc8f6ada51ea6" xmi:type="uml:InstanceSpecification"/><packagedElement name="ComMInitiateResetState:Var" visibility="public" xmi:id="EAID_7871b549ee45cf280b89538355694439" xmi:type="uml:InstanceSpecification"/><packagedElement name="DcmApplUpdateState:Var" visibility="public" xmi:id="EAID_779fcecad28f9de2b7d4541983f52458" xmi:type="uml:InstanceSpecification"/><packagedElement name="EcuMModeState:Var" visibility="public" xmi:id="EAID_c830edd274a79de99a8206406ff45b8e" xmi:type="uml:InstanceSpecification"/><packagedElement name="ForcedActionListPriority:Var" visibility="public" xmi:id="EAID_33a8db8054dbd4b983e678109e55b496" xmi:type="uml:InstanceSpecification"/><packagedElement name="Initialized:Var" visibility="public" xmi:id="EAID_045c79f73ab47665923017576a0b630d" xmi:type="uml:InstanceSpecification"/><packagedElement name="J1939DcmState:Var" visibility="public" xmi:id="EAID_062280957fae9e6d518edca224ebd25e" xmi:type="uml:InstanceSpecification"/><packagedElement name="LengthOfActionListPriorityQueue:Var" visibility="public" xmi:id="EAID_05996e1f9307c65223f7fb1b48ec8f0e" xmi:type="uml:InstanceSpecification"/><packagedElement name="QueueSemaphore:Var" visibility="public" xmi:id="EAID_55369514abc8d09c66423d57be99e7f1" xmi:type="uml:InstanceSpecification"/><packagedElement name="QueueWritten:Var" visibility="public" xmi:id="EAID_3d47a5d06b2acc76d9a20050e88405e7" xmi:type="uml:InstanceSpecification"/></uml:Model></xmi:XMI>
