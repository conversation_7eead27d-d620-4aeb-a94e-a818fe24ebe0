/**********************************************************************************************************************
 *  COPYRIGHT
 *  -------------------------------------------------------------------------------------------------------------------
 *  \verbatim
 *
 *                This software is copyright protected and proprietary to Vector Informatik GmbH.
 *                Vector Informatik GmbH grants to you only those rights as set out in the license conditions.
 *                All other rights remain with Vector Informatik GmbH.
 *  \endverbatim
 *  -------------------------------------------------------------------------------------------------------------------
 *  FILE DESCRIPTION
 *  -------------------------------------------------------------------------------------------------------------------
 *             File:  Rte_DemMaster_0_Type.h
 *           Config:  Demo.dpa
 *      ECU-Project:  Demo
 *
 *        Generator:  MICROSAR RTE Generator Version 4.22.1
 *                    RTE Core Version 1.22.1
 *          License:  CBD2000456
 *
 *      Description:  Application types header file for SW-C <DemMaster_0>
 *********************************************************************************************************************/

/* double include prevention */
#ifndef RTE_DEMMASTER_0_TYPE_H
# define RTE_DEMMASTER_0_TYPE_H

# ifdef __cplusplus
extern "C"
{
# endif /* __cplusplus */

# include "Rte_Type.h"

# ifndef RTE_CORE

/**********************************************************************************************************************
 * Range, Invalidation, Enumeration and Bit Field Definitions
 *********************************************************************************************************************/

#  ifndef DEM_DTC_FORMAT_OBD
#   define DEM_DTC_FORMAT_OBD (0U)
#  endif

#  ifndef DEM_DTC_FORMAT_UDS
#   define DEM_DTC_FORMAT_UDS (1U)
#  endif

#  ifndef DEM_DTC_FORMAT_J1939
#   define DEM_DTC_FORMAT_J1939 (2U)
#  endif

#  ifndef DEM_DTC_KIND_ALL_DTCS
#   define DEM_DTC_KIND_ALL_DTCS (1U)
#  endif

#  ifndef DEM_DTC_KIND_EMISSION_REL_DTCS
#   define DEM_DTC_KIND_EMISSION_REL_DTCS (2U)
#  endif

#  ifndef DEM_DTC_ORIGIN_PRIMARY_MEMORY
#   define DEM_DTC_ORIGIN_PRIMARY_MEMORY (1U)
#  endif

#  ifndef DEM_DTC_ORIGIN_MIRROR_MEMORY
#   define DEM_DTC_ORIGIN_MIRROR_MEMORY (2U)
#  endif

#  ifndef DEM_DTC_ORIGIN_PERMANENT_MEMORY
#   define DEM_DTC_ORIGIN_PERMANENT_MEMORY (3U)
#  endif

#  ifndef DEM_DTC_ORIGIN_OBD_RELEVANT_MEMORY
#   define DEM_DTC_ORIGIN_OBD_RELEVANT_MEMORY (4U)
#  endif

#  ifndef DEM_DTC_ORIGIN_SECONDARY_MEMORY
#   define DEM_DTC_ORIGIN_SECONDARY_MEMORY (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_10
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_10 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_11
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_11 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_12
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_12 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_13
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_13 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_14
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_14 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_15
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_15 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_16
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_16 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_17
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_17 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_18
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_18 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_19
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_19 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_1A
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_1A (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_1B
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_1B (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_1C
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_1C (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_1D
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_1D (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_1E
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_1E (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_1F
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_1F (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_20
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_20 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_21
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_21 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_22
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_22 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_23
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_23 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_24
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_24 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_25
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_25 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_26
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_26 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_27
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_27 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_28
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_28 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_29
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_29 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_2A
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_2A (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_2B
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_2B (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_2C
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_2C (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_2D
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_2D (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_2E
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_2E (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_2F
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_2F (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_30
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_30 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_31
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_31 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_32
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_32 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_33
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_33 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_34
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_34 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_35
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_35 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_36
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_36 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_37
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_37 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_38
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_38 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_39
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_39 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_3A
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_3A (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_3B
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_3B (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_3C
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_3C (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_3D
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_3D (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_3E
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_3E (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_3F
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_3F (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_40
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_40 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_41
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_41 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_42
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_42 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_43
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_43 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_44
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_44 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_45
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_45 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_46
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_46 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_47
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_47 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_48
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_48 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_49
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_49 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_4A
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_4A (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_4B
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_4B (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_4C
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_4C (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_4D
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_4D (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_4E
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_4E (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_4F
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_4F (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_50
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_50 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_51
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_51 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_52
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_52 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_53
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_53 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_54
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_54 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_55
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_55 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_56
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_56 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_57
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_57 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_58
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_58 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_59
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_59 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_5A
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_5A (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_5B
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_5B (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_5C
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_5C (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_5D
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_5D (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_5E
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_5E (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_5F
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_5F (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_60
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_60 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_61
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_61 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_62
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_62 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_63
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_63 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_64
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_64 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_65
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_65 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_66
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_66 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_67
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_67 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_68
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_68 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_69
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_69 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_6A
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_6A (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_6B
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_6B (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_6C
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_6C (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_6D
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_6D (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_6E
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_6E (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_6F
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_6F (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_70
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_70 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_71
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_71 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_72
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_72 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_73
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_73 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_74
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_74 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_75
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_75 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_76
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_76 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_77
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_77 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_78
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_78 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_79
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_79 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_7A
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_7A (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_7B
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_7B (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_7C
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_7C (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_7D
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_7D (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_7E
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_7E (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_7F
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_7F (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_80
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_80 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_81
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_81 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_82
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_82 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_83
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_83 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_84
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_84 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_85
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_85 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_86
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_86 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_87
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_87 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_88
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_88 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_89
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_89 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_8A
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_8A (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_8B
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_8B (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_8C
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_8C (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_8D
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_8D (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_8E
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_8E (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_8F
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_8F (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_90
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_90 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_91
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_91 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_92
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_92 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_93
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_93 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_94
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_94 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_95
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_95 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_96
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_96 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_97
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_97 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_98
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_98 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_99
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_99 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_9A
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_9A (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_9B
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_9B (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_9C
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_9C (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_9D
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_9D (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_9E
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_9E (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_9F
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_9F (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_A0
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_A0 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_A1
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_A1 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_A2
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_A2 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_A3
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_A3 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_A4
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_A4 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_A5
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_A5 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_A6
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_A6 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_A7
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_A7 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_A8
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_A8 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_A9
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_A9 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_AA
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_AA (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_AB
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_AB (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_AC
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_AC (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_AD
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_AD (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_AE
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_AE (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_AF
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_AF (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_B0
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_B0 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_B1
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_B1 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_B2
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_B2 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_B3
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_B3 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_B4
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_B4 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_B5
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_B5 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_B6
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_B6 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_B7
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_B7 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_B8
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_B8 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_B9
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_B9 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_BA
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_BA (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_BB
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_BB (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_BC
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_BC (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_BD
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_BD (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_BE
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_BE (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_BF
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_BF (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_C0
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_C0 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_C1
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_C1 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_C2
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_C2 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_C3
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_C3 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_C4
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_C4 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_C5
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_C5 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_C6
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_C6 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_C7
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_C7 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_C8
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_C8 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_C9
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_C9 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_CA
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_CA (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_CB
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_CB (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_CC
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_CC (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_CD
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_CD (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_CE
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_CE (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_CF
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_CF (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_D0
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_D0 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_D1
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_D1 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_D2
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_D2 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_D3
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_D3 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_D4
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_D4 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_D5
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_D5 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_D6
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_D6 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_D7
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_D7 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_D8
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_D8 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_D9
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_D9 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_DA
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_DA (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_DB
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_DB (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_DC
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_DC (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_DD
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_DD (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_DE
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_DE (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_DF
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_DF (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_E0
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_E0 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_E1
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_E1 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_E2
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_E2 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_E3
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_E3 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_E4
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_E4 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_E5
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_E5 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_E6
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_E6 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_E7
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_E7 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_E8
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_E8 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_E9
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_E9 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_EA
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_EA (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_EB
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_EB (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_EC
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_EC (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_ED
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_ED (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_EE
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_EE (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_EF
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_EF (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_F0
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_F0 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_F1
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_F1 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_F2
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_F2 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_F3
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_F3 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_F4
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_F4 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_F5
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_F5 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_F6
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_F6 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_F7
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_F7 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_F8
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_F8 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_F9
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_F9 (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_FA
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_FA (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_FB
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_FB (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_FC
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_FC (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_FD
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_FD (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_FE
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_FE (5U)
#  endif

#  ifndef DEM_DTC_ORIGIN_USERDEFINED_MEMORY_FF
#   define DEM_DTC_ORIGIN_USERDEFINED_MEMORY_FF (5U)
#  endif

#  ifndef DEM_SEVERITY_NO_SEVERITY
#   define DEM_SEVERITY_NO_SEVERITY (0U)
#  endif

#  ifndef DTC_NO_SEVERITY_BflMask
#   define DTC_NO_SEVERITY_BflMask 255U
#  endif

#  ifndef DTC_NO_SEVERITY_BflPn
#   define DTC_NO_SEVERITY_BflPn 0U
#  endif

#  ifndef DTC_NO_SEVERITY_BflLn
#   define DTC_NO_SEVERITY_BflLn 8U
#  endif

#  ifndef DEM_SEVERITY_WWHOBD_CLASS_NO_CLASS
#   define DEM_SEVERITY_WWHOBD_CLASS_NO_CLASS (1U)
#  endif

#  ifndef DTC_CLASS_BflMask
#   define DTC_CLASS_BflMask 31U
#  endif

#  ifndef DTC_CLASS_BflPn
#   define DTC_CLASS_BflPn 0U
#  endif

#  ifndef DTC_CLASS_BflLn
#   define DTC_CLASS_BflLn 5U
#  endif

#  ifndef DEM_SEVERITY_WWHOBD_CLASS_A
#   define DEM_SEVERITY_WWHOBD_CLASS_A (2U)
#  endif

#  ifndef DEM_SEVERITY_WWHOBD_CLASS_B1
#   define DEM_SEVERITY_WWHOBD_CLASS_B1 (4U)
#  endif

#  ifndef DEM_SEVERITY_WWHOBD_CLASS_B2
#   define DEM_SEVERITY_WWHOBD_CLASS_B2 (8U)
#  endif

#  ifndef DEM_SEVERITY_WWHOBD_CLASS_C
#   define DEM_SEVERITY_WWHOBD_CLASS_C (16U)
#  endif

#  ifndef DEM_SEVERITY_MAINTENANCE_ONLY
#   define DEM_SEVERITY_MAINTENANCE_ONLY (32U)
#  endif

#  ifndef DTC_SEVERITY_BflMask
#   define DTC_SEVERITY_BflMask 224U
#  endif

#  ifndef DTC_SEVERITY_BflPn
#   define DTC_SEVERITY_BflPn 5U
#  endif

#  ifndef DTC_SEVERITY_BflLn
#   define DTC_SEVERITY_BflLn 3U
#  endif

#  ifndef DEM_SEVERITY_CHECK_AT_NEXT_HALT
#   define DEM_SEVERITY_CHECK_AT_NEXT_HALT (64U)
#  endif

#  ifndef DEM_SEVERITY_CHECK_IMMEDIATELY
#   define DEM_SEVERITY_CHECK_IMMEDIATELY (128U)
#  endif

#  ifndef DEM_DTR_CTL_NORMAL
#   define DEM_DTR_CTL_NORMAL (0U)
#  endif

#  ifndef DEM_DTR_CTL_NO_MAX
#   define DEM_DTR_CTL_NO_MAX (1U)
#  endif

#  ifndef DEM_DTR_CTL_NO_MIN
#   define DEM_DTR_CTL_NO_MIN (2U)
#  endif

#  ifndef DEM_DTR_CTL_RESET
#   define DEM_DTR_CTL_RESET (3U)
#  endif

#  ifndef DEM_DTR_CTL_INVISIBLE
#   define DEM_DTR_CTL_INVISIBLE (4U)
#  endif

#  ifndef DEM_DEBOUNCE_STATUS_FREEZE
#   define DEM_DEBOUNCE_STATUS_FREEZE (0U)
#  endif

#  ifndef DEM_DEBOUNCE_STATUS_RESET
#   define DEM_DEBOUNCE_STATUS_RESET (1U)
#  endif

#  ifndef DEM_TEMPORARILY_DEFECTIVE
#   define DEM_TEMPORARILY_DEFECTIVE (1U)
#  endif

#  ifndef DEM_TEMPORARILY_DEFECTIVE_BflMask
#   define DEM_TEMPORARILY_DEFECTIVE_BflMask 1U
#  endif

#  ifndef DEM_TEMPORARILY_DEFECTIVE_BflPn
#   define DEM_TEMPORARILY_DEFECTIVE_BflPn 0U
#  endif

#  ifndef DEM_TEMPORARILY_DEFECTIVE_BflLn
#   define DEM_TEMPORARILY_DEFECTIVE_BflLn 1U
#  endif

#  ifndef DEM_FINALLY_DEFECTIVE
#   define DEM_FINALLY_DEFECTIVE (2U)
#  endif

#  ifndef DEM_FINALLY_DEFECTIVE_BflMask
#   define DEM_FINALLY_DEFECTIVE_BflMask 2U
#  endif

#  ifndef DEM_FINALLY_DEFECTIVE_BflPn
#   define DEM_FINALLY_DEFECTIVE_BflPn 1U
#  endif

#  ifndef DEM_FINALLY_DEFECTIVE_BflLn
#   define DEM_FINALLY_DEFECTIVE_BflLn 1U
#  endif

#  ifndef DEM_TEMPORARILY_HEALED
#   define DEM_TEMPORARILY_HEALED (4U)
#  endif

#  ifndef DEM_TEMPORARILY_HEALED_BflMask
#   define DEM_TEMPORARILY_HEALED_BflMask 4U
#  endif

#  ifndef DEM_TEMPORARILY_HEALED_BflPn
#   define DEM_TEMPORARILY_HEALED_BflPn 2U
#  endif

#  ifndef DEM_TEMPORARILY_HEALED_BflLn
#   define DEM_TEMPORARILY_HEALED_BflLn 1U
#  endif

#  ifndef DEM_TEST_COMPLETE
#   define DEM_TEST_COMPLETE (8U)
#  endif

#  ifndef DEM_TEST_COMPLETE_BflMask
#   define DEM_TEST_COMPLETE_BflMask 8U
#  endif

#  ifndef DEM_TEST_COMPLETE_BflPn
#   define DEM_TEST_COMPLETE_BflPn 3U
#  endif

#  ifndef DEM_TEST_COMPLETE_BflLn
#   define DEM_TEST_COMPLETE_BflLn 1U
#  endif

#  ifndef DEM_DTR_UPDATE
#   define DEM_DTR_UPDATE (16U)
#  endif

#  ifndef DEM_DTR_UPDATE_BflMask
#   define DEM_DTR_UPDATE_BflMask 16U
#  endif

#  ifndef DEM_DTR_UPDATE_BflPn
#   define DEM_DTR_UPDATE_BflPn 4U
#  endif

#  ifndef DEM_DTR_UPDATE_BflLn
#   define DEM_DTR_UPDATE_BflLn 1U
#  endif

#  ifndef DEM_EVENT_STATUS_PASSED
#   define DEM_EVENT_STATUS_PASSED (0U)
#  endif

#  ifndef DEM_EVENT_STATUS_FAILED
#   define DEM_EVENT_STATUS_FAILED (1U)
#  endif

#  ifndef DEM_EVENT_STATUS_PREPASSED
#   define DEM_EVENT_STATUS_PREPASSED (2U)
#  endif

#  ifndef DEM_EVENT_STATUS_PREFAILED
#   define DEM_EVENT_STATUS_PREFAILED (3U)
#  endif

#  ifndef DEM_EVENT_STATUS_FDC_THRESHOLD_REACHED
#   define DEM_EVENT_STATUS_FDC_THRESHOLD_REACHED (4U)
#  endif

#  ifndef DEM_EVENT_STATUS_PASSED_CONDITIONS_NOT_FULFILLED
#   define DEM_EVENT_STATUS_PASSED_CONDITIONS_NOT_FULFILLED (5U)
#  endif

#  ifndef DEM_EVENT_STATUS_FAILED_CONDITIONS_NOT_FULFILLED
#   define DEM_EVENT_STATUS_FAILED_CONDITIONS_NOT_FULFILLED (6U)
#  endif

#  ifndef DEM_EVENT_STATUS_PREPASSED_CONDITIONS_NOT_FULFILLED
#   define DEM_EVENT_STATUS_PREPASSED_CONDITIONS_NOT_FULFILLED (7U)
#  endif

#  ifndef DEM_EVENT_STATUS_PREFAILED_CONDITIONS_NOT_FULFILLED
#   define DEM_EVENT_STATUS_PREFAILED_CONDITIONS_NOT_FULFILLED (8U)
#  endif

#  ifndef DEM_INDICATOR_OFF
#   define DEM_INDICATOR_OFF (0U)
#  endif

#  ifndef DEM_INDICATOR_CONTINUOUS
#   define DEM_INDICATOR_CONTINUOUS (1U)
#  endif

#  ifndef DEM_INDICATOR_BLINKING
#   define DEM_INDICATOR_BLINKING (2U)
#  endif

#  ifndef DEM_INDICATOR_BLINK_CONT
#   define DEM_INDICATOR_BLINK_CONT (3U)
#  endif

#  ifndef DEM_INDICATOR_SLOW_FLASH
#   define DEM_INDICATOR_SLOW_FLASH (4U)
#  endif

#  ifndef DEM_INDICATOR_FAST_FLASH
#   define DEM_INDICATOR_FAST_FLASH (5U)
#  endif

#  ifndef DEM_INDICATOR_ON_DEMAND
#   define DEM_INDICATOR_ON_DEMAND (6U)
#  endif

#  ifndef DEM_INDICATOR_SHORT
#   define DEM_INDICATOR_SHORT (7U)
#  endif

#  ifndef DEM_INIT_MONITOR_CLEAR
#   define DEM_INIT_MONITOR_CLEAR (1U)
#  endif

#  ifndef DEM_INIT_MONITOR_RESTART
#   define DEM_INIT_MONITOR_RESTART (2U)
#  endif

#  ifndef DEM_INIT_MONITOR_REENABLED
#   define DEM_INIT_MONITOR_REENABLED (3U)
#  endif

#  ifndef DEM_INIT_MONITOR_STORAGE_REENABLED
#   define DEM_INIT_MONITOR_STORAGE_REENABLED (4U)
#  endif

#  ifndef DEM_IUMPR_GENERAL_DENOMINATOR
#   define DEM_IUMPR_GENERAL_DENOMINATOR (1U)
#  endif

#  ifndef DEM_IUMPR_DEN_COND_COLDSTART
#   define DEM_IUMPR_DEN_COND_COLDSTART (2U)
#  endif

#  ifndef DEM_IUMPR_DEN_COND_EVAP
#   define DEM_IUMPR_DEN_COND_EVAP (3U)
#  endif

#  ifndef DEM_IUMPR_DEN_COND_500MI
#   define DEM_IUMPR_DEN_COND_500MI (4U)
#  endif

#  ifndef DEM_IUMPR_GENERAL_INDIVIDUAL_DENOMINATOR
#   define DEM_IUMPR_GENERAL_INDIVIDUAL_DENOMINATOR (5U)
#  endif

#  ifndef DEM_IUMPR_GENERAL_OBDCOND
#   define DEM_IUMPR_GENERAL_OBDCOND (6U)
#  endif

#  ifndef DEM_IUMPR_DEN_COND_INDEPENDENT
#   define DEM_IUMPR_DEN_COND_INDEPENDENT (7U)
#  endif

#  ifndef DEM_IUMPR_DEN_STATUS_NOT_REACHED
#   define DEM_IUMPR_DEN_STATUS_NOT_REACHED (0U)
#  endif

#  ifndef DEM_IUMPR_DEN_STATUS_REACHED
#   define DEM_IUMPR_DEN_STATUS_REACHED (1U)
#  endif

#  ifndef DEM_IUMPR_DEN_STATUS_INHIBITED
#   define DEM_IUMPR_DEN_STATUS_INHIBITED (2U)
#  endif

#  ifndef DEM_IUMPR_BOOSTPRS
#   define DEM_IUMPR_BOOSTPRS (0U)
#  endif

#  ifndef DEM_IUMPR_CAT1
#   define DEM_IUMPR_CAT1 (1U)
#  endif

#  ifndef DEM_IUMPR_CAT2
#   define DEM_IUMPR_CAT2 (2U)
#  endif

#  ifndef DEM_IUMPR_EGR
#   define DEM_IUMPR_EGR (3U)
#  endif

#  ifndef DEM_IUMPR_EGSENSOR
#   define DEM_IUMPR_EGSENSOR (4U)
#  endif

#  ifndef DEM_IUMPR_EVAP
#   define DEM_IUMPR_EVAP (5U)
#  endif

#  ifndef DEM_IUMPR_NMHCCAT
#   define DEM_IUMPR_NMHCCAT (6U)
#  endif

#  ifndef DEM_IUMPR_NOXADSORB
#   define DEM_IUMPR_NOXADSORB (7U)
#  endif

#  ifndef DEM_IUMPR_NOXCAT
#   define DEM_IUMPR_NOXCAT (8U)
#  endif

#  ifndef DEM_IUMPR_OXS1
#   define DEM_IUMPR_OXS1 (9U)
#  endif

#  ifndef DEM_IUMPR_OXS2
#   define DEM_IUMPR_OXS2 (10U)
#  endif

#  ifndef DEM_IUMPR_PMFILTER
#   define DEM_IUMPR_PMFILTER (11U)
#  endif

#  ifndef DEM_IUMPR_PRIVATE
#   define DEM_IUMPR_PRIVATE (12U)
#  endif

#  ifndef DEM_IUMPR_SAIR
#   define DEM_IUMPR_SAIR (13U)
#  endif

#  ifndef DEM_IUMPR_SECOXS1
#   define DEM_IUMPR_SECOXS1 (14U)
#  endif

#  ifndef DEM_IUMPR_SECOXS2
#   define DEM_IUMPR_SECOXS2 (15U)
#  endif

#  ifndef DEM_IUMPR_FLSYS
#   define DEM_IUMPR_FLSYS (16U)
#  endif

#  ifndef DEM_IUMPR_AFRI1
#   define DEM_IUMPR_AFRI1 (17U)
#  endif

#  ifndef DEM_IUMPR_AFRI2
#   define DEM_IUMPR_AFRI2 (18U)
#  endif

#  ifndef DEM_IUMPR_PF1
#   define DEM_IUMPR_PF1 (19U)
#  endif

#  ifndef DEM_IUMPR_PF2
#   define DEM_IUMPR_PF2 (20U)
#  endif

#  ifndef DEM_IUMPR_ALLGROUPS
#   define DEM_IUMPR_ALLGROUPS (255U)
#  endif

#  ifndef DEM_MONITOR_STATUS_TF
#   define DEM_MONITOR_STATUS_TF (1U)
#  endif

#  ifndef DEM_MONITOR_STATUS_TF_BflMask
#   define DEM_MONITOR_STATUS_TF_BflMask 1U
#  endif

#  ifndef DEM_MONITOR_STATUS_TF_BflPn
#   define DEM_MONITOR_STATUS_TF_BflPn 0U
#  endif

#  ifndef DEM_MONITOR_STATUS_TF_BflLn
#   define DEM_MONITOR_STATUS_TF_BflLn 1U
#  endif

#  ifndef DEM_MONITOR_STATUS_TNCTOC
#   define DEM_MONITOR_STATUS_TNCTOC (2U)
#  endif

#  ifndef DEM_MONITOR_STATUS_TNCTOC_BflMask
#   define DEM_MONITOR_STATUS_TNCTOC_BflMask 2U
#  endif

#  ifndef DEM_MONITOR_STATUS_TNCTOC_BflPn
#   define DEM_MONITOR_STATUS_TNCTOC_BflPn 1U
#  endif

#  ifndef DEM_MONITOR_STATUS_TNCTOC_BflLn
#   define DEM_MONITOR_STATUS_TNCTOC_BflLn 1U
#  endif

#  ifndef DEM_CYCLE_STATE_START
#   define DEM_CYCLE_STATE_START (0U)
#  endif

#  ifndef DEM_CYCLE_STATE_END
#   define DEM_CYCLE_STATE_END (1U)
#  endif

#  ifndef DEM_UDS_STATUS_TF
#   define DEM_UDS_STATUS_TF (1U)
#  endif

#  ifndef DEM_UDS_STATUS_TF_BflMask
#   define DEM_UDS_STATUS_TF_BflMask 1U
#  endif

#  ifndef DEM_UDS_STATUS_TF_BflPn
#   define DEM_UDS_STATUS_TF_BflPn 0U
#  endif

#  ifndef DEM_UDS_STATUS_TF_BflLn
#   define DEM_UDS_STATUS_TF_BflLn 1U
#  endif

#  ifndef DEM_UDS_STATUS_TFTOC
#   define DEM_UDS_STATUS_TFTOC (2U)
#  endif

#  ifndef DEM_UDS_STATUS_TFTOC_BflMask
#   define DEM_UDS_STATUS_TFTOC_BflMask 2U
#  endif

#  ifndef DEM_UDS_STATUS_TFTOC_BflPn
#   define DEM_UDS_STATUS_TFTOC_BflPn 1U
#  endif

#  ifndef DEM_UDS_STATUS_TFTOC_BflLn
#   define DEM_UDS_STATUS_TFTOC_BflLn 1U
#  endif

#  ifndef DEM_UDS_STATUS_PDTC
#   define DEM_UDS_STATUS_PDTC (4U)
#  endif

#  ifndef DEM_UDS_STATUS_PDTC_BflMask
#   define DEM_UDS_STATUS_PDTC_BflMask 4U
#  endif

#  ifndef DEM_UDS_STATUS_PDTC_BflPn
#   define DEM_UDS_STATUS_PDTC_BflPn 2U
#  endif

#  ifndef DEM_UDS_STATUS_PDTC_BflLn
#   define DEM_UDS_STATUS_PDTC_BflLn 1U
#  endif

#  ifndef DEM_UDS_STATUS_CDTC
#   define DEM_UDS_STATUS_CDTC (8U)
#  endif

#  ifndef DEM_UDS_STATUS_CDTC_BflMask
#   define DEM_UDS_STATUS_CDTC_BflMask 8U
#  endif

#  ifndef DEM_UDS_STATUS_CDTC_BflPn
#   define DEM_UDS_STATUS_CDTC_BflPn 3U
#  endif

#  ifndef DEM_UDS_STATUS_CDTC_BflLn
#   define DEM_UDS_STATUS_CDTC_BflLn 1U
#  endif

#  ifndef DEM_UDS_STATUS_TNCSLC
#   define DEM_UDS_STATUS_TNCSLC (16U)
#  endif

#  ifndef DEM_UDS_STATUS_TNCSLC_BflMask
#   define DEM_UDS_STATUS_TNCSLC_BflMask 16U
#  endif

#  ifndef DEM_UDS_STATUS_TNCSLC_BflPn
#   define DEM_UDS_STATUS_TNCSLC_BflPn 4U
#  endif

#  ifndef DEM_UDS_STATUS_TNCSLC_BflLn
#   define DEM_UDS_STATUS_TNCSLC_BflLn 1U
#  endif

#  ifndef DEM_UDS_STATUS_TFSLC
#   define DEM_UDS_STATUS_TFSLC (32U)
#  endif

#  ifndef DEM_UDS_STATUS_TFSLC_BflMask
#   define DEM_UDS_STATUS_TFSLC_BflMask 32U
#  endif

#  ifndef DEM_UDS_STATUS_TFSLC_BflPn
#   define DEM_UDS_STATUS_TFSLC_BflPn 5U
#  endif

#  ifndef DEM_UDS_STATUS_TFSLC_BflLn
#   define DEM_UDS_STATUS_TFSLC_BflLn 1U
#  endif

#  ifndef DEM_UDS_STATUS_TNCTOC
#   define DEM_UDS_STATUS_TNCTOC (64U)
#  endif

#  ifndef DEM_UDS_STATUS_TNCTOC_BflMask
#   define DEM_UDS_STATUS_TNCTOC_BflMask 64U
#  endif

#  ifndef DEM_UDS_STATUS_TNCTOC_BflPn
#   define DEM_UDS_STATUS_TNCTOC_BflPn 6U
#  endif

#  ifndef DEM_UDS_STATUS_TNCTOC_BflLn
#   define DEM_UDS_STATUS_TNCTOC_BflLn 1U
#  endif

#  ifndef DEM_UDS_STATUS_WIR
#   define DEM_UDS_STATUS_WIR (128U)
#  endif

#  ifndef DEM_UDS_STATUS_WIR_BflMask
#   define DEM_UDS_STATUS_WIR_BflMask 128U
#  endif

#  ifndef DEM_UDS_STATUS_WIR_BflPn
#   define DEM_UDS_STATUS_WIR_BflPn 7U
#  endif

#  ifndef DEM_UDS_STATUS_WIR_BflLn
#   define DEM_UDS_STATUS_WIR_BflLn 1U
#  endif

# endif /* RTE_CORE */

# ifdef __cplusplus
} /* extern "C" */
# endif /* __cplusplus */

#endif /* RTE_DEMMASTER_0_TYPE_H */
