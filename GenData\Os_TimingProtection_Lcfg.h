/**********************************************************************************************************************
 *  COPYRIGHT
 *  -------------------------------------------------------------------------------------------------------------------
 *  \verbatim
 *
 *                 This software is copyright protected and proprietary to Vector Informatik GmbH.
 *                 Vector Informatik GmbH grants to you only those rights as set out in the license conditions.
 *                 All other rights remain with Vector Informatik GmbH.
 *  \endverbatim
 *  -------------------------------------------------------------------------------------------------------------------
 *  LICENSE
 *  -------------------------------------------------------------------------------------------------------------------
 *            Module: Os
 *           Program: MSR_Vector_SLP4
 *          Customer: AUTOSAR 4 Evaluation Bundle
 *       Expiry Date: Not restricted
 *  Ordered Derivat.: Aurix 2G/TC377
 *    License Scope : The usage is restricted to CBD2000456_D02
 *
 *  -------------------------------------------------------------------------------------------------------------------
 *  FILE DESCRIPTION
 *  -------------------------------------------------------------------------------------------------------------------
 *              File: Os_TimingProtection_Lcfg.h
 *   Generation Time: 2025-08-05 10:37:19
 *           Project: Demo - Version 1.0
 *          Delivery: CBD2000456_D02
 *      Tool Version: DaVinci Configurator (beta) 5.21.40 SP2
 *
 *
 *********************************************************************************************************************/

/**********************************************************************************************************************
 ! BETA VERSION                                                                                                       !
 !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
 ! This version of DaVinci Configurator Pro and/or the related Basic Software Package is BETA software.               !
 ! BETA Software is basically operable, but not sufficiently tested, verified and/or qualified for use in series      !
 ! production and/or in vehicles operating on public or non-public roads.                                             !
 ! In particular, without limitation, BETA Software may cause unpredictable ECU behavior, may not provide all         !
 ! functions necessary for use in series production and/or may not comply with quality requirements which are         !
 ! necessary according to the state of the art. BETA Software must not be used in series production.                  !
 !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
**********************************************************************************************************************/

#ifndef OS_TIMINGPROTECTION_LCFG_H
# define OS_TIMINGPROTECTION_LCFG_H

/**********************************************************************************************************************
 *  INCLUDES
 *********************************************************************************************************************/

/* AUTOSAR includes */
# include "Std_Types.h"

/* Os module declarations */
# include "Os_TimingProtection_Types.h"

/* Os kernel module dependencies */

/* Os hal dependencies */


/**********************************************************************************************************************
 *  GLOBAL CONSTANT MACROS
 *********************************************************************************************************************/

/**********************************************************************************************************************
 *  GLOBAL FUNCTION MACROS
 *********************************************************************************************************************/

/**********************************************************************************************************************
 *  GLOBAL DATA TYPES AND STRUCTURES
 *********************************************************************************************************************/

/**********************************************************************************************************************
 *  GLOBAL DATA PROTOTYPES
 *********************************************************************************************************************/

/**********************************************************************************************************************
 *  GLOBAL CONSTANT DATA PROTOTYPES
 *********************************************************************************************************************/

# define OS_START_SEC_CORE0_CONST_UNSPECIFIED
# include "Os_MemMap_OsSections.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

/*! Timing protection configuration data: OsCore0 */
extern CONST(Os_TpConfigType, OS_CONST) OsCfg_Tp_OsCore0;

/*! Timing protection configuration data: Os_CoreInitHook_OsCore0 */
extern CONST(Os_TpOwnerThreadConfigType, OS_CONST) OsCfg_Tp_Os_CoreInitHook_OsCore0;

/*! Timing protection configuration data: CanIsr_7 */
extern CONST(Os_TpOwnerThreadConfigType, OS_CONST) OsCfg_Tp_CanIsr_7;

/*! Timing protection configuration data: CounterIsr_SystemTimer */
extern CONST(Os_TpOwnerThreadConfigType, OS_CONST) OsCfg_Tp_CounterIsr_SystemTimer;

/*! Timing protection configuration data: CounterIsr_TpCounter_OsCore0 */
extern CONST(Os_TpThreadConfigType, OS_CONST) OsCfg_Tp_CounterIsr_TpCounter_OsCore0;

/*! Timing protection configuration data: Fr_IrqLine0 */
extern CONST(Os_TpOwnerThreadConfigType, OS_CONST) OsCfg_Tp_Fr_IrqLine0;

/*! Timing protection configuration data: Fr_IrqTimer0 */
extern CONST(Os_TpOwnerThreadConfigType, OS_CONST) OsCfg_Tp_Fr_IrqTimer0;

/*! Timing protection configuration data: Lin_Channel_2_EX_Extended_Error_Interrupt */
extern CONST(Os_TpOwnerThreadConfigType, OS_CONST) OsCfg_Tp_Lin_Channel_2_EX_Extended_Error_Interrupt;

/*! Timing protection configuration data: Lin_Channel_2_RX_Receive_Interrupt */
extern CONST(Os_TpOwnerThreadConfigType, OS_CONST) OsCfg_Tp_Lin_Channel_2_RX_Receive_Interrupt;

/*! Timing protection configuration data: Lin_Channel_2_TX_Transmit_Interrupt */
extern CONST(Os_TpOwnerThreadConfigType, OS_CONST) OsCfg_Tp_Lin_Channel_2_TX_Transmit_Interrupt;

/*! Timing protection configuration data: Default_BSW_Async_Task */
extern CONST(Os_TpOwnerThreadConfigType, OS_CONST) OsCfg_Tp_Default_BSW_Async_Task;

/*! Timing protection configuration data: Default_BSW_Sync_Task */
extern CONST(Os_TpOwnerThreadConfigType, OS_CONST) OsCfg_Tp_Default_BSW_Sync_Task;

/*! Timing protection configuration data: Default_Init_Task */
extern CONST(Os_TpOwnerThreadConfigType, OS_CONST) OsCfg_Tp_Default_Init_Task;

/*! Timing protection configuration data: Default_Init_Task_Trusted */
extern CONST(Os_TpOwnerThreadConfigType, OS_CONST) OsCfg_Tp_Default_Init_Task_Trusted;

/*! Timing protection configuration data: Default_RTE_Mode_switch_Task */
extern CONST(Os_TpOwnerThreadConfigType, OS_CONST) OsCfg_Tp_Default_RTE_Mode_switch_Task;

/*! Timing protection configuration data: IdleTask_OsCore0 */
extern CONST(Os_TpOwnerThreadConfigType, OS_CONST) OsCfg_Tp_IdleTask_OsCore0;

/*! Timing protection configuration data: StartApplication_Appl_Init_Task */
extern CONST(Os_TpOwnerThreadConfigType, OS_CONST) OsCfg_Tp_StartApplication_Appl_Init_Task;

/*! Timing protection configuration data: StartApplication_Appl_Task */
extern CONST(Os_TpOwnerThreadConfigType, OS_CONST) OsCfg_Tp_StartApplication_Appl_Task;

# define OS_STOP_SEC_CORE0_CONST_UNSPECIFIED
# include "Os_MemMap_OsSections.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */


# define OS_START_SEC_CONST_UNSPECIFIED
# include "Os_MemMap_OsSections.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

/*! Timing protection configuration data: EmptyThread */
extern CONST(Os_TpThreadConfigType, OS_CONST) OsCfg_Tp_EmptyThread;

# define OS_STOP_SEC_CONST_UNSPECIFIED
# include "Os_MemMap_OsSections.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */


/**********************************************************************************************************************
 *  GLOBAL FUNCTION PROTOTYPES
 *********************************************************************************************************************/


#endif /* OS_TIMINGPROTECTION_LCFG_H */

/**********************************************************************************************************************
 *  END OF FILE: Os_TimingProtection_Lcfg.h
 *********************************************************************************************************************/
