/**********************************************************************************************************************
 *  COPYRIGHT
 *  -------------------------------------------------------------------------------------------------------------------
 *  \verbatim
 *
 *                 This software is copyright protected and proprietary to Vector Informatik GmbH.
 *                 Vector Informatik GmbH grants to you only those rights as set out in the license conditions.
 *                 All other rights remain with Vector Informatik GmbH.
 *  \endverbatim
 *  -------------------------------------------------------------------------------------------------------------------
 *  LICENSE
 *  -------------------------------------------------------------------------------------------------------------------
 *            Module: FrIf
 *           Program: MSR_Vector_SLP4
 *          Customer: AUTOSAR 4 Evaluation Bundle
 *       Expiry Date: Not restricted
 *  Ordered Derivat.: Aurix 2G/TC377
 *    License Scope : The usage is restricted to CBD2000456_D02
 *
 *  -------------------------------------------------------------------------------------------------------------------
 *  FILE DESCRIPTION
 *  -------------------------------------------------------------------------------------------------------------------
 *              File: FrIf_Cfg.h
 *   Generation Time: 2025-08-05 10:37:18
 *           Project: Demo - Version 1.0
 *          Delivery: CBD2000456_D02
 *      Tool Version: DaVinci Configurator (beta) 5.21.40 SP2
 *
 *
 *********************************************************************************************************************/

/**********************************************************************************************************************
 ! BETA VERSION                                                                                                       !
 !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
 ! This version of DaVinci Configurator Pro and/or the related Basic Software Package is BETA software.               !
 ! BETA Software is basically operable, but not sufficiently tested, verified and/or qualified for use in series      !
 ! production and/or in vehicles operating on public or non-public roads.                                             !
 ! In particular, without limitation, BETA Software may cause unpredictable ECU behavior, may not provide all         !
 ! functions necessary for use in series production and/or may not comply with quality requirements which are         !
 ! necessary according to the state of the art. BETA Software must not be used in series production.                  !
 !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
**********************************************************************************************************************/



/**        \file  FrIf_Cfg.h
 *        \brief  FrIf Cfg header file
 *
 *      \details  Cfg header file of the AUTOSAR FlexRay Interface, according to:
 *                AUTOSAR FlexRay Interface, AUTOSAR Release 4.0
 *
 *********************************************************************************************************************/
 
#if !defined(FRIF_CFG_H)
#define FRIF_CFG_H

/* -----------------------------------------------------------------------------
    &&&~ Includes
 ----------------------------------------------------------------------------- */

#include "Platform_Types.h"



/* -----------------------------------------------------------------------------
    &&&~ Linktime / Precompile CRC
 ----------------------------------------------------------------------------- */

#define FRIF_CRC_CHECK                       		STD_OFF


/* -----------------------------------------------------------------------------
    &&&~ Defines
 ----------------------------------------------------------------------------- */

/*  Version information  */
#define FRIF_CFG_MAJOR_VERSION               4u 
#define FRIF_CFG_MINOR_VERSION               2u
#define FRIF_CFG_PATCH_VERSION               0u

#define FRIF_SYNC_THRESHOLD         (uint16) 50u
#define FRIF_FILL_PATTERN           (uint8)  0x00u
#define FRIF_FILL_PATTERN_DWORD     (uint32) 0x00000000uL

#ifndef FRIF_USE_DUMMY_STATEMENT
#define FRIF_USE_DUMMY_STATEMENT STD_OFF /* /MICROSAR/vSet/vSetGeneral/vSetDummyStatementKind */
#endif
#ifndef FRIF_DUMMY_STATEMENT
#define FRIF_DUMMY_STATEMENT(v)  /* PRQA S 3453 */ /* MD_MSR_FctLikeMacro */  /* /MICROSAR/vSet/vSetGeneral/vSetDummyStatementKind */
#endif
#ifndef FRIF_DUMMY_STATEMENT_CONST
#define FRIF_DUMMY_STATEMENT_CONST(v)  /* PRQA S 3453 */ /* MD_MSR_FctLikeMacro */  /* /MICROSAR/vSet/vSetGeneral/vSetDummyStatementKind */
#endif
#ifndef FRIF_ATOMIC_BIT_ACCESS_IN_BITFIELD
#define FRIF_ATOMIC_BIT_ACCESS_IN_BITFIELD STD_OFF /* /MICROSAR/EcuC/EcucGeneral/AtomicBitAccessInBitfield */
#endif
#ifndef FRIF_ATOMIC_VARIABLE_ACCESS
#define FRIF_ATOMIC_VARIABLE_ACCESS 32u /* /MICROSAR/EcuC/EcucGeneral/AtomicVariableAccess */
#endif
#ifndef FRIF_PROCESSOR_TC377T
#define FRIF_PROCESSOR_TC377T
#endif
#ifndef FRIF_COMP_TASKING
#define FRIF_COMP_TASKING
#endif
#ifndef FRIF_GEN_GENERATOR_MSR
#define FRIF_GEN_GENERATOR_MSR
#endif
#ifndef FRIF_CPUTYPE_BITORDER_LSB2MSB
#define FRIF_CPUTYPE_BITORDER_LSB2MSB /* /MICROSAR/vSet/vSetPlatform/vSetBitOrder */
#endif
#ifndef FRIF_CONFIGURATION_VARIANT_PRECOMPILE
#define FRIF_CONFIGURATION_VARIANT_PRECOMPILE 1
#endif
#ifndef FRIF_CONFIGURATION_VARIANT_LINKTIME
#define FRIF_CONFIGURATION_VARIANT_LINKTIME 2
#endif
#ifndef FRIF_CONFIGURATION_VARIANT_POSTBUILD_LOADABLE
#define FRIF_CONFIGURATION_VARIANT_POSTBUILD_LOADABLE 3
#endif
#ifndef FRIF_CONFIGURATION_VARIANT
#define FRIF_CONFIGURATION_VARIANT FRIF_CONFIGURATION_VARIANT_PRECOMPILE
#endif
#ifndef FRIF_POSTBUILD_VARIANT_SUPPORT
#define FRIF_POSTBUILD_VARIANT_SUPPORT STD_OFF
#endif


#ifndef FRIF_DEV_ERROR_DETECT
# define FRIF_DEV_ERROR_DETECT      STD_ON /* /MICROSAR/FrIf/FrIfGeneral/FrIfSafeBswChecks || /MICROSAR/FrIf/FrIfGeneral/FrIfDevErrorDetect */
#endif
#ifndef FRIF_DEV_ERROR_REPORT
# define FRIF_DEV_ERROR_REPORT      STD_ON /* /MICROSAR/FrIf/FrIfGeneral/FrIfDevErrorDetect */
#endif

/* -----------------------------------------------------------------------------
    &&&~ Symbolic Name Value Defines
 ----------------------------------------------------------------------------- */

/* FrIfClstIdx */



/**
 * \defgroup FrIfHandleIds Handle IDs.
 * \{
 */

/* Handle IDs active in all predefined variants (the application has not to take the active variant into account) */
/*      Symbolic Name                                                 Value   Active in predefined variant(s) */
#define FrIfConf_FrIfCluster_FlexRay_e64a27e1                         0u
/**\} */

/* FrIfCtrlIdx */



/**
 * \defgroup FrIfHandleIds Handle IDs.
 * \{
 */

/* Handle IDs active in all predefined variants (the application has not to take the active variant into account) */
/*      Symbolic Name                                                 Value   Active in predefined variant(s) */
#define FrIfConf_FrIfController_MyECU_6c1a0d52                        0u
/**\} */

/* FrIfLPduIdx */



/**
 * \defgroup FrIfHandleIdsFrIfAllLPdus/ActiveEcuC/FrIf/FrIfConfig/FlexRay_e64a27e1/MyECU_6c1a0d52 Handle IDs of handle space FrIfAllLPdus /ActiveEcuC/FrIf/FrIfConfig/FlexRay_e64a27e1/MyECU_6c1a0d52.
 * \brief Single Tx/Rx LPdu handle space of /ActiveEcuC/FrIf/FrIfConfig/FlexRay_e64a27e1/MyECU_6c1a0d52
 * \{
 */

/* Handle IDs active in all predefined variants (the application has not to take the active variant into account) */
/*      Symbolic Name                                                 Value   Active in predefined variant(s) */
#define FrIfConf_FrIfLPdu_FT_000A0001_e658a733_Rx                     9u
#define FrIfConf_FrIfLPdu_FT_000F0002_f928a5fb_Rx                     8u
#define FrIfConf_FrIfLPdu_FT_005A0040_9467ebe1_Tx                     1u
#define FrIfConf_FrIfLPdu_FT_005A0140_6c38f464_Rx                     6u
#define FrIfConf_FrIfLPdu_FT_005A0240_bfa8d2aa_Rx                     7u
#define FrIfConf_FrIfLPdu_FT_005F0108_8aaa6737_Tx                     0u
#define FrIfConf_FrIfLPdu_FT_00020001_3efb9924_Tx                     4u
#define FrIfConf_FrIfLPdu_FT_00030001_b061dc00_Rx                     10u
#define FrIfConf_FrIfLPdu_FT_00080001_574a298b_Tx                     3u
#define FrIfConf_FrIfLPdu_FT_00100104_78f871a0_Tx                     2u
#define FrIfConf_FrIfLPdu_FT_00670304_3c59848c_Rx                     5u
/**\} */

/* -----------------------------------------------------------------------------
    &&&~ Rx PDU Handles
 ----------------------------------------------------------------------------- */

#define FrIfConf_FrIfRxPdu_FrIfRxPdu_pdu_RxDyn_64_600910d1_Rx 0U 
#define FrIfConf_FrIfRxPdu_FrIfRxPdu_PDU_nm_OtherECU_Fr_30557908_Rx 1U 
#define FrIfConf_FrIfRxPdu_FrIfRxPdu_PDU_nm_RearECU_Fr_2fa98195_Rx 2U 
#define FrIfConf_FrIfRxPdu_FrIfRxPdu_pdu_RxStat_10_51092b83_Rx 3U 
#define FrIfConf_FrIfRxPdu_FrIfRxPdu_pdu_RxStat_30_25bd2d72_Rx 4U 
#define FrIfConf_FrIfRxPdu_FrIfRxPdu_PDU_Fr_StartAppl_BothECU_RX_7a67d8c4_Rx 5U 
#define FrIfConf_FrIfRxPdu_FrIfRxPdu_PDU_Dummy_RearECU_9177c4f3_Rx 6U 


/* -----------------------------------------------------------------------------
    &&&~ Tx PDU Handles
 ----------------------------------------------------------------------------- */



/**
 * \defgroup FrIfHandleIds Handle IDs.
 * \{
 */

/* Handle IDs active in all predefined variants (the application has not to take the active variant into account) */
/*      Symbolic Name                                                 Value   Active in predefined variant(s) */
#define FrIfConf_FrIfTxPdu_PDU_Fr_StartAppl_MyECU_TX_161591f9_Tx      5u
#define FrIfConf_FrIfTxPdu_PDU_Transmit_MyECU_dcbaa590_Tx             6u
#define FrIfConf_FrIfTxPdu_PDU_nm_MyECU_Fr_50a02afb_Tx                2u
#define FrIfConf_FrIfTxPdu_pdu_TxDyn_16_44389cbf_Tx                   1u
#define FrIfConf_FrIfTxPdu_pdu_TxDyn_64_0bd27c77_Tx                   0u
#define FrIfConf_FrIfTxPdu_pdu_TxStat_40_505d7b88_Tx                  3u
#define FrIfConf_FrIfTxPdu_pdu_TxStat_64_2f41aa7f_Tx                  4u
/**\} */

/* -----------------------------------------------------------------------------
    &&&~ Upper Layer PDU Handles
 ----------------------------------------------------------------------------- */
#define FRIF_TX_ulpdu_TxDyn_64_0bd27c77_Tx PduRConf_PduRDestPdu_pdu_TxDyn_64_0bd27c77_Tx 
#define FRIF_TX_ulpdu_TxDyn_16_44389cbf_Tx PduRConf_PduRDestPdu_pdu_TxDyn_16_44389cbf_Tx 
#define FRIF_TX_ulPDU_nm_MyECU_Fr_50a02afb_Tx FrNmConf_FrNmTxPdu_PDU_nm_MyECU_Fr_50a02afb_Tx 
#define FRIF_TX_ulpdu_TxStat_40_505d7b88_Tx PduRConf_PduRDestPdu_pdu_TxStat_40_505d7b88_Tx 
#define FRIF_TX_ulpdu_TxStat_64_2f41aa7f_Tx PduRConf_PduRDestPdu_pdu_TxStat_64_2f41aa7f_Tx 
#define FRIF_TX_ulPDU_Fr_StartAppl_MyECU_TX_161591f9_Tx PduRConf_PduRDestPdu_PDU_Fr_StartAppl_MyECU_TX_161591f9_Tx 
#define FRIF_TX_ulPDU_Transmit_MyECU_dcbaa590_Tx PduRConf_PduRDestPdu_PDU_Transmit_MyECU_dcbaa590_Tx 
#define FRIF_RX_ulpdu_RxDyn_64_600910d1_Rx PduRConf_PduRSrcPdu_PduRSrcPdu_45b853db 
#define FRIF_RX_ulPDU_nm_OtherECU_Fr_30557908_Rx FrNmConf_FrNmRxPdu_PDU_nm_OtherECU_Fr_30557908_Rx 
#define FRIF_RX_ulPDU_nm_RearECU_Fr_2fa98195_Rx FrNmConf_FrNmRxPdu_PDU_nm_RearECU_Fr_2fa98195_Rx 
#define FRIF_RX_ulpdu_RxStat_10_51092b83_Rx PduRConf_PduRSrcPdu_PduRSrcPdu_2bf83137 
#define FRIF_RX_ulpdu_RxStat_30_25bd2d72_Rx PduRConf_PduRSrcPdu_PduRSrcPdu_9474dc2d 
#define FRIF_RX_ulPDU_Fr_StartAppl_BothECU_RX_7a67d8c4_Rx PduRConf_PduRSrcPdu_PduRSrcPdu_b2f650ce 
#define FRIF_RX_ulPDU_Dummy_RearECU_9177c4f3_Rx PduRConf_PduRSrcPdu_PduRSrcPdu_5699e50b 


/* -----------------------------------------------------------------------------
    &&&~ Precompile config
 ----------------------------------------------------------------------------- */

#define FRIF_USE_FRTRCV_API                         STD_OFF
#define FRIF_API_AS_MACRO                           STD_OFF  /* /MICROSAR/FrIf/FrIfGeneral/FrIfWrapperAPIsAsMacro */
#define FRIF_RELATIVE_ALARM_ENABLE                  STD_OFF
#define FRIF_PROD_ERROR_DETECT                      STD_OFF
#define FRIF_CHANNEL_STATUS_ENABLE                  STD_OFF /* /MICROSAR/FrIf/FrIfGeneral/FrIfGetGetChannelStatusSupport */
#define FRIF_SET_EXT_SYNC_DISABLE                   STD_ON
#define FRIF_ASR40FRTRCVAPISUPPORT                  STD_ON
#define FRIF_READCCCONFIGSUPPORT                    STD_OFF /* /MICROSAR/FrIf/FrIfGeneral/FrIfReadCCConfigApi */
#define FRIF_ABSOLUTE_TIMER_API_DISABLE             STD_OFF /* /MICROSAR/FrIf/FrIfGeneral/FrIfAbsTimerIdx */ 
#define FRIF_GETNUMSTARTUPFRAMESSUPPORT             STD_OFF /* /MICROSAR/FrIf/FrIfGeneral/FrIfGetNumOfStartupFramesSupport */
#define FRIF_ALLSLOTSSUPPORT                        STD_OFF /* /MICROSAR/FrIf/FrIfGeneral/FrIfAllSlotsSupport */
#define FRIF_GETWAKEUPRXSTATUSSUPPORT               STD_OFF /* /MICROSAR/FrIf/FrIfGeneral/FrIfGetWakeupRxStatusSupport */
#define FRIF_AMD_RUNTIME_MEASUREMENT                STD_OFF /* /MICROSAR/FrIf/FrIfGeneral/FrIfRuntimeMeasurementSupport */ 
#define FRIF_SUPPRESS_PROTECTED_RANGE_CHECK         STD_OFF /* /MICROSAR/FrIf/FrIfGeneral/FrIfSuppressProtectedRangeCheck */ 
#define FRIF_VERSION_INFO_API                       STD_OFF /* /MICROSAR/FrIf/FrIfGeneral/FrIfVersionInfoApi */
#define FRIF_ENABLE_PREPARE_LPDU                    STD_OFF /* /MICROSAR/FrIf/FrIfGeneral/FrIfEnablePrepareLpdu */
#define FRIF_CTRL_ENABLE_API_OPTIMIZATION           STD_OFF /* /MICROSAR/FrIf/FrIfGeneral/FrIfSingleChannelAPI */
#define FRIF_UNIFORMUPDATEBYTEPOS                   STD_OFF /* /MICROSAR/FrIf/FrIfGeneral/FrIfUniformUpdateBytePos */
#define FRIF_ALL_FRAMES_HAVE_SAME_UPDATEBIT_BYTEPOS STD_OFF /* /MICROSAR/FrIf/FrIfGeneral/FrIfGlobalUniformUpdateBytePos */
#define FRIF_MEASURE_JLETASKTIMES                   STD_OFF
#define FRIF_PDUDIRTYBYTE_USAGE                     STD_OFF /* /MICROSAR/FrIf/FrIfGeneral/FrIfPduDirtyByteUsage */
#define FRIF_JOB_CONCATENATION_ENABLE               STD_OFF /* /MICROSAR/FrIf/FrIfGeneral/FrIfJobConcatenationEnable */
#define FRIF_DIRECT_BUFFER_ACCESS_ENABLE            STD_OFF /* /MICROSAR/Fr/FrGeneral/FrDirectBufferAccessEnable */
#define FRIF_FIFO_SUPPORT_ENABLE                    STD_OFF /* /MICROSAR/Fr/FrGeneral/FrFIFOSupport */
#define FRIF_CLOCK_CORRECTION_ENABLE                STD_OFF /* /MICROSAR/FrIf/FrIfGeneral/FrIfGetClockCorrectionSupport */
#define FRIF_ABORT_COMMUNICATION_DISABLE            STD_OFF /* /MICROSAR/FrIf/FrIfGeneral/FrIfAbortCommunicationDisable */
#define FRIF_SET_WAKEUP_CHANNEL_DISABLE             STD_OFF /* /MICROSAR/FrIf/FrIfGeneral/FrIfSetWakeupChannelDisable */
#define FRIF_REQUEST_COUNTER_HANDLING_DISABLE       STD_OFF /* /MICROSAR/FrIf/FrIfGeneral/FrIfCounterLimitDisable */
#define FRIF_CANCELTRANSMITSUPPORT                  STD_OFF /* /MICROSAR/FrIf/FrIfGeneral/FrIfCancelTransmitSupport */
#define FRIF_DELAYEDTXCONFSUPPORT                   STD_OFF /* /MICROSAR/FrIf/FrIfGeneral/FrIfDelayedTxConfirmationSupport */
#define FRIF_FREEOPCALLBACKSUPPORT                  STD_OFF
#define FRIF_RECONFIGLPDUSUPPORT                    STD_OFF /* /MICROSAR/FrIf/FrIfGeneral/FrIfReconfigLPduSupport */
#define FRIF_DISABLELPDUSUPPORT                     STD_OFF /* /MICROSAR/FrIf/FrIfGeneral/FrIfDisableLPduSupport */
#define FRIF_GETSYNCFRAMELISTSUPPORT                STD_OFF /* /MICROSAR/FrIf/FrIfGeneral/FrIfGetSyncFrameListSupport */
#define FRIF_NMVECTORSUPPORT                        STD_OFF /* /MICROSAR/FrIf/FrIfGeneral/FrIfGetNmVectorSupport */
#define FRIF_DUALCHANNELREDUNDANCYSUPPORT           STD_OFF /* /MICROSAR/FrIf/FrIfGeneral/FrIfDualChannelRedundancySupport */
#define FRIF_SILENT_CHECK                           STD_OFF
#define FRIF_3RD_PARTY_DRIVER_SUPPORT               STD_OFF /* /MICROSAR/FrIf/FrIfGeneral/FrIf3rdPartyDriverSupport */

/* Single Controller API optimization */
#define FRIF_VCTRL_ENABLE_API_OPTIMIZATION   STD_OFF

#define FRIF_VCTRL_ONLY     uint8 FrIf_CtrlIdx
#define FRIF_VCTRL_FIRST    uint8 FrIf_CtrlIdx,
#define FRIF_VCTRL_IDX      FrIf_CtrlIdx

#define FRIF_VCLST_ONLY     uint8 FrIf_ClstIdx
#define FRIF_VCLST_FIRST    uint8 FrIf_ClstIdx,
#define FRIF_VCLST_IDX      FrIf_ClstIdx
#define FRIF_VCLST_OPT      FrIf_ClstIdx


/* -----------------------------------------------------------------------------
    &&&~ Typedefs
 ----------------------------------------------------------------------------- */

typedef uint8 FrIf_FLEIdxType; 
typedef uint8 FrIf_NumberOfPduType;

/* -----------------------------------------------------------------------------
    &&&~ Precompile Defines
 ----------------------------------------------------------------------------- */
 
#define FRIF_AUTOSARVERSION4
#define FrIf_InvalidTxPduHandle                     7u
#define FrIf_NumberOfChannels                       1u
#define FrIf_NumberOfFrTrcvs                        0u
#define FrIf_CommonMaxNumberOfControllers           1u
#define FrIf_CommonMaxNumberOfClusters              1u

/* -----------------------------------------------------------------------------
    &&&~ FrIf EcuC Global Configuration Container Name
 ----------------------------------------------------------------------------- */

#define FrIfConfig                                   FrIf_Config


#endif /* FRIF_CFG_H */

