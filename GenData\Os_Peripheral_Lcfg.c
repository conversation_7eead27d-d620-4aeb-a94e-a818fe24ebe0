/**********************************************************************************************************************
 *  COPYRIGHT
 *  -------------------------------------------------------------------------------------------------------------------
 *  \verbatim
 *
 *                 This software is copyright protected and proprietary to Vector Informatik GmbH.
 *                 Vector Informatik GmbH grants to you only those rights as set out in the license conditions.
 *                 All other rights remain with Vector Informatik GmbH.
 *  \endverbatim
 *  -------------------------------------------------------------------------------------------------------------------
 *  LICENSE
 *  -------------------------------------------------------------------------------------------------------------------
 *            Module: Os
 *           Program: MSR_Vector_SLP4
 *          Customer: AUTOSAR 4 Evaluation Bundle
 *       Expiry Date: Not restricted
 *  Ordered Derivat.: Aurix 2G/TC377
 *    License Scope : The usage is restricted to CBD2000456_D02
 *
 *  -------------------------------------------------------------------------------------------------------------------
 *  FILE DESCRIPTION
 *  -------------------------------------------------------------------------------------------------------------------
 *              File: Os_Peripheral_Lcfg.c
 *   Generation Time: 2025-08-05 10:37:18
 *           Project: Demo - Version 1.0
 *          Delivery: CBD2000456_D02
 *      Tool Version: DaVinci Configurator (beta) 5.21.40 SP2
 *
 *
 *********************************************************************************************************************/

/**********************************************************************************************************************
 ! BETA VERSION                                                                                                       !
 !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
 ! This version of DaVinci Configurator Pro and/or the related Basic Software Package is BETA software.               !
 ! BETA Software is basically operable, but not sufficiently tested, verified and/or qualified for use in series      !
 ! production and/or in vehicles operating on public or non-public roads.                                             !
 ! In particular, without limitation, BETA Software may cause unpredictable ECU behavior, may not provide all         !
 ! functions necessary for use in series production and/or may not comply with quality requirements which are         !
 ! necessary according to the state of the art. BETA Software must not be used in series production.                  !
 !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
**********************************************************************************************************************/

/* PRQA S 0777, 0779, 0828 EOF */ /* MD_MSR_Rule5.1, MD_MSR_Rule5.2, MD_MSR_Dir1.1 */

#define OS_PERIPHERAL_LCFG_SOURCE

/**********************************************************************************************************************
 *  INCLUDES
 *********************************************************************************************************************/

/* AUTOSAR includes */
#include "Std_Types.h"

/* Os module declarations */
#include "Os_Peripheral_Lcfg.h"
#include "Os_Peripheral.h"

/* Os kernel module dependencies */

/* Os hal dependencies */


/**********************************************************************************************************************
 *  LOCAL CONSTANT MACROS
 *********************************************************************************************************************/

/**********************************************************************************************************************
 *  LOCAL FUNCTION MACROS
 *********************************************************************************************************************/

/**********************************************************************************************************************
 *  LOCAL DATA TYPES AND STRUCTURES
 *********************************************************************************************************************/

/**********************************************************************************************************************
 *  LOCAL DATA PROTOTYPES
 *********************************************************************************************************************/

/**********************************************************************************************************************
 *  GLOBAL DATA
 *********************************************************************************************************************/

/**********************************************************************************************************************
 *  GLOBAL CONSTANT DATA
 *********************************************************************************************************************/

#define OS_START_SEC_CONST_UNSPECIFIED
#include "Os_MemMap_OsSections.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */


/*! Peripheral configuration data: CAN_PROTECTED_AREA_CHANNEL */
OS_LOCAL CONST(Os_PeripheralConfigType, OS_CONST) OsCfg_Peripheral_CAN_PROTECTED_AREA_CHANNEL =
{
  /* .AddressStart          = */ (Os_AddressOfConstType) 0xF0218D00uL,  /* PRQA S 0306, 0314, 0326 */ /* MD_Os_Rule11.4_0306_PeripheralConfigType, MD_Os_Dir1.1_0314, MD_Os_Rule11.6_0326 */
  /* .AddressEnd            = */ (Os_AddressOfConstType) 0xF02190FFuL,    /* PRQA S 0306, 0314, 0326 */ /* MD_Os_Rule11.4_0306_PeripheralConfigType, MD_Os_Dir1.1_0314, MD_Os_Rule11.6_0326 */
  /* .AccessingApplications = */ OS_APPID2MASK(OsApplication_NonTrusted_Core0)  /* PRQA S 0410 */ /* MD_MSR_Dir1.1 */
};


/*! Peripheral configuration data: CAN_PROTECTED_AREA_GLOBAL */
OS_LOCAL CONST(Os_PeripheralConfigType, OS_CONST) OsCfg_Peripheral_CAN_PROTECTED_AREA_GLOBAL =
{
  /* .AddressStart          = */ (Os_AddressOfConstType) 0xF0210000uL,  /* PRQA S 0306, 0314, 0326 */ /* MD_Os_Rule11.4_0306_PeripheralConfigType, MD_Os_Dir1.1_0314, MD_Os_Rule11.6_0326 */
  /* .AddressEnd            = */ (Os_AddressOfConstType) 0xF0218FFFuL,    /* PRQA S 0306, 0314, 0326 */ /* MD_Os_Rule11.4_0306_PeripheralConfigType, MD_Os_Dir1.1_0314, MD_Os_Rule11.6_0326 */
  /* .AccessingApplications = */ OS_APPID2MASK(OsApplication_NonTrusted_Core0)  /* PRQA S 0410 */ /* MD_MSR_Dir1.1 */
};


/*! Peripheral configuration data: FR_CLC_PROTECTED_AREA_ENDINIT */
OS_LOCAL CONST(Os_PeripheralConfigType, OS_CONST) OsCfg_Peripheral_FR_CLC_PROTECTED_AREA_ENDINIT =
{
  /* .AddressStart          = */ (Os_AddressOfConstType) 0xF001C000uL,  /* PRQA S 0306, 0314, 0326 */ /* MD_Os_Rule11.4_0306_PeripheralConfigType, MD_Os_Dir1.1_0314, MD_Os_Rule11.6_0326 */
  /* .AddressEnd            = */ (Os_AddressOfConstType) 0xF001CFFFuL,    /* PRQA S 0306, 0314, 0326 */ /* MD_Os_Rule11.4_0306_PeripheralConfigType, MD_Os_Dir1.1_0314, MD_Os_Rule11.6_0326 */
  /* .AccessingApplications = */ OS_APPID2MASK(OsApplication_NonTrusted_Core0)  /* PRQA S 0410 */ /* MD_MSR_Dir1.1 */
};


/*! Peripheral configuration data: FR_IR_PROTECTED_AREA_ENDINIT */
OS_LOCAL CONST(Os_PeripheralConfigType, OS_CONST) OsCfg_Peripheral_FR_IR_PROTECTED_AREA_ENDINIT =
{
  /* .AddressStart          = */ (Os_AddressOfConstType) 0xF0038000uL,  /* PRQA S 0306, 0314, 0326 */ /* MD_Os_Rule11.4_0306_PeripheralConfigType, MD_Os_Dir1.1_0314, MD_Os_Rule11.6_0326 */
  /* .AddressEnd            = */ (Os_AddressOfConstType) 0xF0039FFFuL,    /* PRQA S 0306, 0314, 0326 */ /* MD_Os_Rule11.4_0306_PeripheralConfigType, MD_Os_Dir1.1_0314, MD_Os_Rule11.6_0326 */
  /* .AccessingApplications = */ OS_APPID2MASK(OsApplication_NonTrusted_Core0)  /* PRQA S 0410 */ /* MD_MSR_Dir1.1 */
};


/*! Peripheral configuration data: LIN_PROTECTED_AREA_ENDINIT */
OS_LOCAL CONST(Os_PeripheralConfigType, OS_CONST) OsCfg_Peripheral_LIN_PROTECTED_AREA_ENDINIT =
{
  /* .AddressStart          = */ (Os_AddressOfConstType) 0xF0000800uL,  /* PRQA S 0306, 0314, 0326 */ /* MD_Os_Rule11.4_0306_PeripheralConfigType, MD_Os_Dir1.1_0314, MD_Os_Rule11.6_0326 */
  /* .AddressEnd            = */ (Os_AddressOfConstType) 0xF00008FFuL,    /* PRQA S 0306, 0314, 0326 */ /* MD_Os_Rule11.4_0306_PeripheralConfigType, MD_Os_Dir1.1_0314, MD_Os_Rule11.6_0326 */
  /* .AccessingApplications = */ OS_APPID2MASK(OsApplication_NonTrusted_Core0)  /* PRQA S 0410 */ /* MD_MSR_Dir1.1 */
};


/*! Peripheral configuration data: MCU_CPU_PROTECTED_AREA_ENDINIT */
OS_LOCAL CONST(Os_PeripheralConfigType, OS_CONST) OsCfg_Peripheral_MCU_CPU_PROTECTED_AREA_ENDINIT =
{
  /* .AddressStart          = */ (Os_AddressOfConstType) 0x00000000uL,  /* PRQA S 0306, 0314, 0326 */ /* MD_Os_Rule11.4_0306_PeripheralConfigType, MD_Os_Dir1.1_0314, MD_Os_Rule11.6_0326 */
  /* .AddressEnd            = */ (Os_AddressOfConstType) 0xFFFFFFFFuL,    /* PRQA S 0306, 0314, 0326 */ /* MD_Os_Rule11.4_0306_PeripheralConfigType, MD_Os_Dir1.1_0314, MD_Os_Rule11.6_0326 */
  /* .AccessingApplications = */ OS_APPID2MASK(OsApplication_NonTrusted_Core0)  /* PRQA S 0410 */ /* MD_MSR_Dir1.1 */
};


/*! Peripheral configuration data: MCU_SAFETY_PROTECTED_AREA_ENDINIT */
OS_LOCAL CONST(Os_PeripheralConfigType, OS_CONST) OsCfg_Peripheral_MCU_SAFETY_PROTECTED_AREA_ENDINIT =
{
  /* .AddressStart          = */ (Os_AddressOfConstType) 0x00000000uL,  /* PRQA S 0306, 0314, 0326 */ /* MD_Os_Rule11.4_0306_PeripheralConfigType, MD_Os_Dir1.1_0314, MD_Os_Rule11.6_0326 */
  /* .AddressEnd            = */ (Os_AddressOfConstType) 0xFFFFFFFFuL,    /* PRQA S 0306, 0314, 0326 */ /* MD_Os_Rule11.4_0306_PeripheralConfigType, MD_Os_Dir1.1_0314, MD_Os_Rule11.6_0326 */
  /* .AccessingApplications = */ OS_APPID2MASK(OsApplication_NonTrusted_Core0)  /* PRQA S 0410 */ /* MD_MSR_Dir1.1 */
};


/*! Peripheral configuration data: SCU_WDTCPU0CON0_ENDINIT */
OS_LOCAL CONST(Os_PeripheralConfigType, OS_CONST) OsCfg_Peripheral_SCU_WDTCPU0CON0_ENDINIT =
{
  /* .AddressStart          = */ (Os_AddressOfConstType) 0xF0036000uL,  /* PRQA S 0306, 0314, 0326 */ /* MD_Os_Rule11.4_0306_PeripheralConfigType, MD_Os_Dir1.1_0314, MD_Os_Rule11.6_0326 */
  /* .AddressEnd            = */ (Os_AddressOfConstType) 0xF00363FFuL,    /* PRQA S 0306, 0314, 0326 */ /* MD_Os_Rule11.4_0306_PeripheralConfigType, MD_Os_Dir1.1_0314, MD_Os_Rule11.6_0326 */
  /* .AccessingApplications = */ OS_APPID2MASK(OsApplication_NonTrusted_Core0)  /* PRQA S 0410 */ /* MD_MSR_Dir1.1 */
};

/*! Object reference table for peripherals. */
CONSTP2CONST(Os_PeripheralConfigType, OS_CONST, OS_CONST) OsCfg_PeripheralRefs[OS_PERIPHERALID_COUNT + 1] =             /* PRQA S 1533, 4521 */ /* MD_Os_Rule8.9_1533, MD_Os_Rule1.1_4521 */
{
  &OsCfg_Peripheral_CAN_PROTECTED_AREA_CHANNEL,
  &OsCfg_Peripheral_CAN_PROTECTED_AREA_GLOBAL,
  &OsCfg_Peripheral_FR_CLC_PROTECTED_AREA_ENDINIT,
  &OsCfg_Peripheral_FR_IR_PROTECTED_AREA_ENDINIT,
  &OsCfg_Peripheral_LIN_PROTECTED_AREA_ENDINIT,
  &OsCfg_Peripheral_MCU_CPU_PROTECTED_AREA_ENDINIT,
  &OsCfg_Peripheral_MCU_SAFETY_PROTECTED_AREA_ENDINIT,
  &OsCfg_Peripheral_SCU_WDTCPU0CON0_ENDINIT,
  NULL_PTR
};

#define OS_STOP_SEC_CONST_UNSPECIFIED
#include "Os_MemMap_OsSections.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */


/**********************************************************************************************************************
 *  LOCAL FUNCTION PROTOTYPES
 *********************************************************************************************************************/

/**********************************************************************************************************************
 *  LOCAL FUNCTIONS
 *********************************************************************************************************************/

/**********************************************************************************************************************
 *  GLOBAL FUNCTIONS
 *********************************************************************************************************************/


/**********************************************************************************************************************
 *  END OF FILE: Os_Peripheral_Lcfg.c
 *********************************************************************************************************************/
