
/**********************************************************************************************************************
  COPYRIGHT
-----------------------------------------------------------------------------------------------------------------------
  \par      copyright
  \verbatim
  Copyright (c) 2020 by Vector Informatik GmbH.                                                  All rights reserved.

                This software is copyright protected and proprietary to Vector Informatik GmbH.
                Vector Informatik GmbH grants to you only those rights as set out in the license conditions.
                All other rights remain with Vector Informatik GmbH.
  \endverbatim
-----------------------------------------------------------------------------------------------------------------------
  FILE DESCRIPTION
-----------------------------------------------------------------------------------------------------------------------
  \file  File:  BrsHw.h
      Project:  Vector Basic Runtime System
       Module:  BrsHw for platform Infineon Aurix/AurixPlus

  \brief Description:  This is the hardware specific header file for Vector Basic Runtime System (BRS).

  \attention Please note:
    The demo and example programs only show special aspects of the software. With regard to the fact
    that these programs are meant for demonstration purposes only, Vector Informatik liability shall be
    expressly excluded in cases of ordinary negligence, to the extent admissible by law or statute.
**********************************************************************************************************************/

#ifndef _BRSHW_H_
#define _BRSHW_H_

/**********************************************************************************************************************
  INCLUDES
**********************************************************************************************************************/
/*
 * Description: The BrsCfg header is used to configure different types of
 *              tests and system setups. Therefore it must be included first
 *              in each BRS and test module.
 *              This file is part of the BRS.
 */
#include "vBrsCfg.h"

#if defined (BRS_ENABLE_PORT)
  #include "BrsHw_Ports.h"
#endif

/**********************************************************************************************************************
  MODULE VERSION
**********************************************************************************************************************/
/*
 * Description: This is the BrsHw main and bug fix version. The version numbers are BCD coded.
 *              E.g. a main version of 1.23 is coded with 0x0123, a bug fix version of 9 is coded 0x09.
 */

/* This is the BrsHw template main and bug fix version, the platform BrsHw is reviewed to */
#define BRSHW_SOURCECODE_TEMPLATE_VERSION        0x0100u
#define BRSHW_SOURCECODE_TEMPLATE_BUGFIX_VERSION 0x00u

/* This is the BrsHw main and bug fix version */
#define BRSHW_VERSION        0x0101u
#define BRSHW_BUGFIX_VERSION 0x00u

/**********************************************************************************************************************
  CONFIGURATION CHECK
**********************************************************************************************************************/
/* The following parameters are necessary for this platform, to be generated by BRS-Cfg5-Generator into BrsCfg.h */
#if !defined (BRS_OSC_CLK)
  #error "BRS CHECK: The parameter BRS_OSC_CLK is missing! Please check your BRS Cfg5 configuration."
#endif

#if !defined (BRS_TIMEBASE_CLOCK)
  #error "BRS CHECK: The parameter BRS_TIMEBASE_CLOCK is missing! Please check your BRS Cfg5 configuration."
#endif

#if !defined (BRS_PERIPH_CLOCK)
  #error "BRS CHECK: The parameter BRS_PERIPH_CLOCK is missing! Please check your BRS Cfg5 configuration."
#endif

#if !defined (BRS_CPU_MAX_FREQUENCY)
  #error "BRS CHECK: The parameter BRS_CPU_MAX_FREQUENCY is missing! Please check your BRS Cfg5 configuration."
#endif

#if !(defined (BRS_CPU_CORE_TC161) || defined (BRS_CPU_CORE_TC162))
  #error "BRS CHECK: The parameter BRS_CPU_CORE_x is missing or not supported! Please check your BRS Cfg5 configuration."
#endif

#if !defined (BRS_CPU_CORE_AMOUNT)
  #error "BRS CHECK: The parameter BRS_CPU_CORE_AMOUNT is missing! Please check your BRS Cfg5 configuration."
#endif

/**********************************************************************************************************************
  GLOBAL CONSTANT MACROS
**********************************************************************************************************************/
/*
 * Description: Macro for access to IO addresses
 */
#define BRSHW_IOS(type, address) (*((volatile type *)(address)))

/* STATIC */
#define BRSHW_DERIVATIVE_CORE0_ID  0x0
#define BRSHW_DERIVATIVE_CORE1_ID  0x1
#define BRSHW_DERIVATIVE_CORE2_ID  0x2
#define BRSHW_DERIVATIVE_CORE3_ID  0x3
#define BRSHW_DERIVATIVE_CORE4_ID  0x4
#define BRSHW_DERIVATIVE_CORE5_ID  0x6

/* DERIVATIVE DEPENDENT STATIC */
#define BRSHW_INIT_CORE_ID  BRSHW_DERIVATIVE_CORE0_ID

#define BRS_CORE_ID_REGISTER  0xFE1C
#define BRS_PCXI_OFFSET       0xFE00
#define BRS_PSW_OFFSET        0xFE04
#define BRS_FCX_OFFSET        0xFE38
#define BRS_LCX_OFFSET        0xFE3C
#define BRS_BTV_OFFSET        0xFE24
#define BRS_BIV_OFFSET        0xFE20
#define BRS_PCON0_OFFSET      0x920C
#define BRS_DCON0_OFFSET      0x9040

/**********************************************************************************************************************
  Compiler abstraction
**********************************************************************************************************************/
#if defined (BRS_COMP_TASKING)
  #define nop()                    __nop()

  #define BRS_FORCE_ACTIVE         __attribute__((protect))

  #define BRS_ISYNC()              __isync()
  #define BRS_DSYNC()              __dsync()

  #define BRS_HAL_EXPAND(x)  #x

  #define BRS_MOVE_FROM_CSFR(x)    (uint32)__mfcr((sint32)(x))
  #define BRS_MOVE_TO_CSFR(x, y)   BRS_DSYNC(); __asm("mtcr  #"BRS_HAL_EXPAND(x)", %0" ::"d"(y):); BRS_ISYNC()

  #define BRS_SET_SP(x)            __asm("mov.a  a10, %0" ::"d"(x):"a10")

  #define BRS_STARTUP_LABEL(c)     _Pragma("section code brsResetLabel")  \
                                   _Pragma("optimize g")                  \
                                   void c(void)                           \
                                   {                                      \
                                    __asm(".GLOBAL StartupEntry");                       \
                                    __asm("StartupEntry:");                              \
                                    __asm("movh.a a4, #@his(brsStartupEntry_internal)"); \
                                    __asm("lea a4,[a4]@los(brsStartupEntry_internal)");  \
                                    __asm("ji a4");                       \
                                   }                                      \
                                   _Pragma("optimize restore")            \
                                   _Pragma("section code restore")

#else
  #error "Compiler not yet supported"
#endif /*BRS_COMP_x*/

/* Reads core id value */
#define BRS_READ_COREID()  BRS_MOVE_FROM_CSFR(BRS_CORE_ID_REGISTER)

/**********************************************************************************************************************
  BrsHW configuration
**********************************************************************************************************************/

/*******************************************************************************
  WATCHDOG  GROUP  CONFIG
*******************************************************************************/
#if defined (BRS_WATCHDOG_GROUP_A)
  #define BRS_SFR_WDTCPU0CON0  BRSHW_IOS(uint32, 0xF0036100)  /* original name: SCU_WDTCPU0CON0 */
  #define BRS_SFR_WDTCPU0CON1  BRSHW_IOS(uint32, 0xF0036104)  /* original name: SCU_WDTCPU0CON1 */

  #define BRS_SFR_WDTCPU1CON0  BRSHW_IOS(uint32, 0xF003610C)  /* original name: SCU_WDTCPU1CON0 */
  #define BRS_SFR_WDTCPU1CON1  BRSHW_IOS(uint32, 0xF0036110)  /* original name: SCU_WDTCPU1CON1 */

  #define BRS_SFR_WDTCPU2CON0  BRSHW_IOS(uint32, 0xF0036118)  /* original name: SCU_WDTCPU2CON0 */
  #define BRS_SFR_WDTCPU2CON1  BRSHW_IOS(uint32, 0xF003611C)  /* original name: SCU_WDTCPU2CON1 */

  #define BRS_SFR_WDTSCON0     BRSHW_IOS(uint32, 0xF00360F0)  /* original name: SCU_WDTSCON0 */
  #define BRS_SFR_WDTSCON1     BRSHW_IOS(uint32, 0xF00360F4)  /* original name: SCU_WDTSCON1 */

#elif defined (BRS_WATCHDOG_GROUP_B)
  #define BRS_SFR_WDTCPU0CON0  BRSHW_IOS(uint32, 0xF003624C)  /* CPU0 WDT Control Register 0 - original name: SCU_WDTCPU0CON0 */
  #define BRS_SFR_WDTCPU0CON1  BRSHW_IOS(uint32, 0xF0036250)  /* CPU0 WDT Control Register 1 - original name: SCU_WDTCPU0CON1 */
  #define BRS_SFR_WDTCPU0_SR   BRSHW_IOS(uint32, 0xF0036254)  /* CPU0 WDT Status Register - original name: SCU_WDTCPU0_SR */

  #define BRS_SFR_WDTCPU1CON0  BRSHW_IOS(uint32, 0xF0036258)  /* CPU1 WDT Control Register 0 - original name: SCU_WDTCPU1CON0 */
  #define BRS_SFR_WDTCPU1CON1  BRSHW_IOS(uint32, 0xF003625C)  /* CPU1 WDT Control Register 1 - original name: SCU_WDTCPU1CON1 */
  #define BRS_SFR_WDTCPU1_SR   BRSHW_IOS(uint32, 0xF0036260)  /* CPU1 WDT Status Register - original name: SCU_WDTCPU1_SR */

  #define BRS_SFR_WDTCPU2CON0  BRSHW_IOS(uint32, 0xF0036264)  /* CPU2 WDT Control Register 0 - original name: SCU_WDTCPU2CON0 */
  #define BRS_SFR_WDTCPU2CON1  BRSHW_IOS(uint32, 0xF0036268)  /* CPU2 WDT Control Register 1 - original name: SCU_WDTCPU2CON1 */
  #define BRS_SFR_WDTCPU2_SR   BRSHW_IOS(uint32, 0xF003626C)  /* CPU2 WDT Status Register - original name: SCU_WDTCPU2_SR */

  #define BRS_SFR_WDTCPU3CON0  BRSHW_IOS(uint32, 0xF0036270)  /* CPU3 WDT Control Register 0 - original name: SCU_WDTCPU3CON0 */
  #define BRS_SFR_WDTCPU3CON1  BRSHW_IOS(uint32, 0xF0036274)  /* CPU3 WDT Control Register 1 - original name: SCU_WDTCPU3CON1 */
  #define BRS_SFR_WDTCPU3_SR   BRSHW_IOS(uint32, 0xF0036278)  /* CPU3 WDT Status Register - original name: SCU_WDTCPU3_SR */

  #define BRS_SFR_WDTCPU4CON0  BRSHW_IOS(uint32, 0xF003627C)  /* CPU4 WDT Control Register 0 - original name: SCU_WDTCPU4CON0 */
  #define BRS_SFR_WDTCPU4CON1  BRSHW_IOS(uint32, 0xF0036280)  /* CPU4 WDT Control Register 1 - original name: SCU_WDTCPU4CON1 */
  #define BRS_SFR_WDTCPU4_SR   BRSHW_IOS(uint32, 0xF0036284)  /* CPU4 WDT Status Register - original name: SCU_WDTCPU4_SR */

  #define BRS_SFR_WDTCPU5CON0  BRSHW_IOS(uint32, 0xF0036288)  /* CPU5 WDT Control Register 0 - original name: SCU_WDTCPU5CON0 */
  #define BRS_SFR_WDTCPU5CON1  BRSHW_IOS(uint32, 0xF003628C)  /* CPU5 WDT Control Register 1 - original name: SCU_WDTCPU5CON1 */
  #define BRS_SFR_WDTCPU5_SR   BRSHW_IOS(uint32, 0xF0036290)  /* CPU5 WDT Status Register - original name: SCU_WDTCPU5_SR */

  #define BRS_SFR_WDTSCON0     BRSHW_IOS(uint32, 0xF00362A8)  /* Safety WDT Control Register 0 - original name: SCU_WDTS_CON0 */
  #define BRS_SFR_WDTSCON1     BRSHW_IOS(uint32, 0xF00362AC)  /* Safety WDT Control Register 1 - original name: SCU_WDTS_CON1 */

  #define BRS_SFR_WDTS_SR      BRSHW_IOS(uint32, 0xF00362B0)  /* Safety WDT Status Register - original name: SCU_WDTS_SR */
#endif /*BRS_WATCHDOG_GROUP_x*/

/**********************************************************************************************************************
  Global variables
**********************************************************************************************************************/

/**********************************************************************************************************************
  Global const variables
**********************************************************************************************************************/
/*
 * Description: These constants are used to propagate the Versions over module boundaries.
 *              The version numbers are BCD coded. E.g. a sub version of 23 is coded with 0x23, 
 *              a bug fix version of 9 is coded 0x09.
 */
extern uint8 const kBrsHwMainVersion;
extern uint8 const kBrsHwSubVersion;
extern uint8 const kBrsHwBugfixVersion;

/**********************************************************************************************************************
  Global function prototypes
**********************************************************************************************************************/

#if defined (BRS_ENABLE_WATCHDOG)
/*****************************************************************************/
/**
 * @brief      This function must be used to initialize the Watchdog.
 * @pre        -
 * @param[in]  -
 * @param[out] -
 * @return     -
 * @context    Function is called from BrsMainInit() at power on initialization
 */
/*****************************************************************************/
void BrsHwWatchdogInitPowerOn(void);
#endif /*BRS_ENABLE_WATCHDOG*/

#if defined (BRS_ENABLE_PLLCLOCKS)
/*****************************************************************************/
/**
 * @brief      This function must be used to initialize the PLL.
 * @pre        -
 * @param[in]  -
 * @param[out] -
 * @return     -
 * @context    Function is called from BrsMainInit() at power on initialization
 */
/*****************************************************************************/
void BrsHwPllInitPowerOn(void);
#endif /*BRS_ENABLE_PLLCLOCKS*/

#if defined (BRS_ENABLE_PORT)
# if defined (BRS_ENABLE_FBL_SUPPORT)
  #define BRS_START_SEC_RAM_CODE
  #include "Brs_MemMap.h"
# endif
/*****************************************************************************/
/**
 * @brief      This function sets the output level of a port pin.
 * @pre        Port pin configurations available within BrsHw_Ports.h,
 *             no DrvPort used for port pin initialization and
 *             transferred port pin has to be initialized as output pin with
 *             GPIO functionality.
 * @param[in]  p     - brsHw_Port_PortType, to be set,
 *             Level - level, port pin has to be set to
 *                     (BRSHW_PORT_LOGIC_LOW or BRSHW_PORT_LOGIC_HIGH).
 * @param[out] -
 * @return     -
 * @context    Function is called from BrsHwPortInitPowerOn() and
 *             provided to external modules (e.g. BrsMainTogglePin()).
 */
/*****************************************************************************/
void BrsHwPort_SetLevel(brsHw_Port_PortType p, uint8 Level);

/*****************************************************************************/
/**
 * @brief      This function reads the input level of a port pin.
 * @pre        Port pin configurations available within BrsHw_Ports.h,
 *             no DrvPort used for port pin initialization and
 *             transferred port pin has to be initialized as input pin with
 *             GPIO functionality.
 * @param[in]  p - brsHw_Port_PortType, to be read.
 * @param[out] -
 * @return     Level, read from port pin
 *             (BRSHW_PORT_LOGIC_LOW or BRSHW_PORT_LOGIC_HIGH).
 * @context    Function is provided to external modules.
 */
/*****************************************************************************/
uint8 BrsHwPort_GetLevel(brsHw_Port_PortType p);
# if defined (BRS_ENABLE_FBL_SUPPORT)
  #define BRS_STOP_SEC_RAM_CODE
  #include "Brs_MemMap.h"
# endif

/*****************************************************************************/
/**
 * @brief      This function must be used to initialize the used ports.
 * @pre        -
 * @param[in]  -
 * @param[out] -
 * @return     -
 * @context    Function is called from BrsMainInit() at power on initialization
 */
/*****************************************************************************/
void BrsHwPortInitPowerOn(void);
#endif /*BRS_ENABLE_PORT*/

/*****************************************************************************/
/**
 * @brief      Disable the global system interrupt.
 * @pre        Must be the first function call in main@BrsMain
 * @param[in]  -
 * @param[out] -
 * @return     -
 * @context    Function is called from main@BrsMain at power on initialization
 */
/*****************************************************************************/
void BrsHwDisableInterruptAtPowerOn(void);

/*****************************************************************************/
/**
 * @brief      restart ECU (issue a software reset or jump to startup code)
 * @pre        -
 * @param[in]  -
 * @param[out] -
 * @return     -
 * @context    Function is called from e.g. ECU state handling
 */
/*****************************************************************************/
void BrsHwSoftwareResetECU(void);

typedef enum
{
  BRSHW_RESET_SW,
  BRSHW_RESET_OTHER
}brsHw_ResetReasonType;

/*****************************************************************************/
/**
 * @brief      Get reset reason
 * @pre        -
 * @param[in]  -
 * @param[out] -
 * @return     Reset reason 
 * @context    Function is called from BrsMainStartup to determine if reset
 *             was triggered through software call (BrsHwSoftwareResetECU()).
 */
/*****************************************************************************/
brsHw_ResetReasonType BrsHwGetResetReason(void);

/*****************************************************************************/
/**
 * @brief      This API is used for the BRS time measurement support to get a
 *             default time value for all measurements with this platform to
 *             be able to compare time measurements on different dates based
 *             on this time result.
 * @pre        Should be called with interrupts global disabled
 * @param[in]  -
 * @param[out] -
 * @return     -
 * @context    Function is called from e.g. component testsuits for calibration
 */
/*****************************************************************************/
void BrsHwTime100NOP(void);

#if defined (BRS_ENABLE_SAFECTXSUPPORT)
/*****************************************************************************/
/**
 * @brief      This API is used to enable hardware access in untrusted mode
 * @pre        -
 * @param[in]  -
 * @param[out] -
 * @return     -
 * @context    Function must be called after all depending peripheral modules
 *             are supplied by proper clocks AND before the OS is started.
 */
/*****************************************************************************/
void BrsHw_EnableHwAccess(void);
#endif /*BRS_ENABLE_SAFECTXSUPPORT*/

/*****************************************************************************/
/**
 * @brief      This API is used to read the core ID of the actual running core
 * @pre        -
 * @param[in]  -
 * @param[out] -
 * @return     Core ID of the actual running core
 * @context    Function is e.g. called from main@BrsMain, to only call HW-init
 *             code once, on the boot core.
 *             In MultiCore setups, additional BRSHW_INIT_CORE_ID must be
 *             declared inside BrsHw.h, to configure the proper core ID value
 *             of that boot core.
 */
/*****************************************************************************/
uint32 BrsHw_GetCore(void);

/*****************************************************************************/
/**
 * @brief      This API is used to enable an interrupt source in the core
 *             interrupt controller.
 * @pre        -
 * @param[in]  Source to be enabled.
 * @param[in]  Priority level to be set.
 * @param[out] -
 * @return     -
 * @context    Function is called from HlpTest and other test environments.
 */
/*****************************************************************************/
void BrsHw_EnableInterrupt(uint32, uint8);

/*****************************************************************************/
/**
 * @brief      This API is used to disable an interrupt source in the core
 *             interrupt controller.
 * @pre        -
 * @param[in]  Source to be disabled.
 * @param[out] -
 * @return     -
 * @context    Function is called from HlpTest and other test environments.
 */
/*****************************************************************************/
void BrsHw_DisableInterrupt(uint32);

/*****************************************************************************/
/**
 * @brief      This API is used to trigger the given software interrupt source.
 * @pre        
 * @param[in]  Source to be triggered.
 *             Some derivatives only support few software triggerable sources,
 *             check for their validity.
 * @param[out] -
 * @return     -
 * @context    Function is called from HlpTest and other test environments.
 */
/*****************************************************************************/
void BrsHw_TriggerSoftwareInterrupt(uint32);

#if defined (BRS_ENABLE_FBL_SUPPORT)
#define BRSHW_DISABLE_ECC_AVAILABLE
/*****************************************************************************/
/**
 * @brief      This API is used to disable the flash ECC error reporting,
 *             if this is necessary on the platform to grant access to it.
 * @pre        
 * @param[in]  -
 * @param[out] -
 * @return     -
 * @context    Function is called from FBL boot code, before flash is accessed.
 */
/*****************************************************************************/
void BrsHwDisableEccErrorReporting(void);
#endif /*BRS_ENABLE_FBL_SUPPORT*/

# if defined (BRS_ENABLE_FLEXRAY_SUPPORT)
/*****************************************************************************/
/**
 * @brief      Callback from communication stack. Init E-RAY RAM to avoid ECC errors
 * @pre        
 * @param[in]  -
 * @param[out] -
 * @return     -
 * @context    Function is called Fr_InitMemory(). It is just an empty function
 *             because the RAM init is already done during startup.
 */
/*****************************************************************************/
void Appl_TricoreAurixInit(void);
# endif /* BRS_ENABLE_FLEXRAY_SUPPORT */

#if defined (BRS_ENABLE_OS_MULTICORESUPPORT)
/*****************************************************************************/
/**
 * @brief      This API is used as sync barrier. It is used to unlock cores
 *             that are locked in the Startup code. The implementation is only
 *             needed on platforms, where all cores are started automatically
 *             after a power-on reset. All additional cores are looped, until
 *             the boot core reaches this function.
 * @pre        
 * @param[in]  uint32 coreId
 * @param[out] -
 * @return     -
 * @context    Function is called from BrsMainStartup on the boot core.
 */
/*****************************************************************************/
void BrsHw_UnlockCores(uint32);
#endif /*BRS_ENABLE_OS_MULTICORESUPPORT*/

#endif /*_BRSHW_H_*/
