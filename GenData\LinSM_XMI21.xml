<?xml version="1.0" encoding="UTF-8" standalone="no"?><xmi:XMI xmlns:xmi="http://schema.omg.org/spec/XMI/2.1" xmlns:uml="http://schema.omg.org/spec/UML/2.1" xmi:version="2.1"><xmi:Documentation exporter="Enterprise Architect" exporterVersion="6.5"/><xmi:Extension extender="Enterprise Architect" extenderID="6.5"><elements><element name="ChannelConfig:ConstStruct" scope="public" xmi:idref="EAID_b7f0e084e6e3d69ac889ccbbfd0fc081" xmi:type="uml:Object"><constraints/><attributes><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_5ae3f56741fe9d79ade64789be1e26e2"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute><attribute name="ComMChannelHandleOfChannelConfig" scope="Private" xmi:idref="EAID_13167ed5e28b9e5e82f537104936bb35"><Constraints/><properties changeability="frozen" collection="true" type="NetworkHandleType"/></attribute><attribute name="ConfirmationTimeoutOfChannelConfig" scope="Private" xmi:idref="EAID_534812ab8c8590cf5ae192cafb395430"><Constraints><Constraint name="CNumericalArrayGenCondition" notes="deactivate the LinSM_ConfirmationTimeoutOfChannelConfig if all values are 0" type="Pre-condition"/></Constraints><properties changeability="frozen" collection="true" type="LinSM_ConfirmationTimeoutOfChannelConfigType"/></attribute><attribute name="MasterNodeTypeOfChannelConfig" scope="Private" xmi:idref="EAID_220c95e0d5a1ae598dc31044ac353fbe"><Constraints/><properties changeability="frozen" collection="true" type="LinSM_MasterNodeTypeOfChannelConfigType"/></attribute><attribute name="ScheduleEndNotificationOfChannelConfig" scope="Private" xmi:idref="EAID_9c5292f02609bf4a6aa72d3b945df854"><Constraints><Constraint name="CBoolArrayGenCondition" notes="deactivate the LinSM_ScheduleEndNotificationOfChannelConfig if all values are false" type="Pre-condition"/></Constraints><properties changeability="frozen" collection="true" type="LinSM_ScheduleEndNotificationOfChannelConfigType"/></attribute><attribute name="SilenceAfterWakeupTimeoutOfChannelConfig" scope="Private" xmi:idref="EAID_b68aab6a35ac98a065267d20e7aedaf6"><Constraints><Constraint name="CNumericalArrayGenCondition" notes="deactivate the LinSM_SilenceAfterWakeupTimeoutOfChannelConfig if all values are 0" type="Pre-condition"/></Constraints><properties changeability="frozen" collection="true" type="LinSM_SilenceAfterWakeupTimeoutOfChannelConfigType"/></attribute><attribute name="SleepSupportOfChannelConfig" scope="Private" xmi:idref="EAID_73d1994b562ace204e76565d809fe725"><Constraints><Constraint name="CBoolArrayGenCondition" notes="deactivate the LinSM_SleepSupportOfChannelConfig if all values are false" type="Pre-condition"/></Constraints><properties changeability="frozen" collection="true" type="LinSM_SleepSupportOfChannelConfigType"/></attribute><attribute name="TransceiverHandlingOfChannelConfig" scope="Private" xmi:idref="EAID_f8e0c77edd0b645f497a50d7814c3fc2"><Constraints><Constraint name="CNumericalArrayGenCondition" notes="deactivate the LinSM_TransceiverHandlingOfChannelConfig if all values are 0" type="Pre-condition"/></Constraints><properties changeability="frozen" collection="true" type="LinSM_TransceiverHandlingOfChannelConfigType"/></attribute></attributes></element><element name="ChannelPostBuildConfig:ConstStruct" scope="public" xmi:idref="EAID_58096e4f6e00e4af512027e034afca6d" xmi:type="uml:Object"><constraints/><attributes><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_03e5f1391c7781774a578a26f531e23c"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute><attribute name="ScheduleTableIdRangeOfChannelPostBuildConfig" scope="Private" xmi:idref="EAID_abe67e6295147132692213f3e5626cdc"><Constraints><Constraint name="CUserPreCompileDeactivationCondition" notes="Max Schedule Table ID is only needed for Development Error Detection." type="Pre-condition"/></Constraints><properties changeability="frozen" collection="true" type="LinIf_SchHandleType"/></attribute></attributes></element><element name="ComMToLinSMChannel:ConstArray" scope="public" xmi:idref="EAID_676b97d48b5a73f42e7fd196a63837aa" xmi:type="uml:Object"><attributes><attribute name="ComMToLinSMChannel" scope="Private" xmi:idref="EAID_aecd790c49223c59981d8ac14a2d2ab7"><Constraints><Constraint name="CUserPreCompileDeactivationCondition" notes="Local channel ID is always equal ComM channel ID." type="Pre-condition"/></Constraints><properties changeability="frozen" collection="true" type="LinSM_ComMToLinSMChannelType"/></attribute><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_816973a8c5fde6bcb74d0a2d91395b0b"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute></attributes></element><element name="ModeRequestRepetitionMax:ConstVar" scope="public" xmi:idref="EAID_c68bac9c43086ab8fef2604c61dc74fb" xmi:type="uml:Object"><attributes><attribute name="ModeRequestRepetitionMax" scope="Private" xmi:idref="EAID_e3c7858b1ef163538c79a184edce0e94"><Constraints><Constraint name="CNumericalArrayGenCondition" notes="deactivate the LinSM_ModeRequestRepetitionMax if all values are 0" type="Pre-condition"/></Constraints><properties changeability="frozen" type="LinSM_ModeRequestRepetitionMaxType"/></attribute><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_076ee6cd7187cfdf649c7670b0617636"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute></attributes></element><element name="ComState:VarArray" scope="public" xmi:idref="EAID_1e2086925ba76127f18b92789fbd053e" xmi:type="uml:Object"><attributes><attribute name="ComState" scope="Private" xmi:idref="EAID_ee0c0110c0073b6d68156a6f635fa357"><Constraints/><properties collection="true"/></attribute><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_a35c3836495e88a71b2bc9a3e3fe11ed"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute></attributes></element><element name="ConfirmationTimer:VarArray" scope="public" xmi:idref="EAID_696f0e4845050ed9c3b4e06aa1a1c43e" xmi:type="uml:Object"><attributes><attribute name="ConfirmationTimer" scope="Private" xmi:idref="EAID_4d75d1dab5c1aa291a3a4b72fedc3a27"><Constraints/><properties collection="true"/></attribute><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_e36223d6feabfe0f1991be905bd5c0ad"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute></attributes></element><element name="DesiredConfirmation:VarArray" scope="public" xmi:idref="EAID_d7774ff304b93d52be258f7faa5c87e1" xmi:type="uml:Object"><attributes><attribute name="DesiredConfirmation" scope="Private" xmi:idref="EAID_4101b7a88eb1ff1a1fd6fcc770690481"><Constraints/><properties collection="true"/></attribute><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_778882033916bb513be039e91e5bda46"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute></attributes></element><element name="NegativeConfirmation:VarArray" scope="public" xmi:idref="EAID_9f7a6c60e50281bece7c6b107af54fa6" xmi:type="uml:Object"><attributes><attribute name="NegativeConfirmation" scope="Private" xmi:idref="EAID_fb566937a53835b48da269f3d24fe9cd"><Constraints/><properties collection="true"/></attribute><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_153705799cd7260d7e6aa8f70557888e"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute></attributes></element><element name="RequestedComMode:VarArray" scope="public" xmi:idref="EAID_d22486d072a93c4ddeed4920a659e827" xmi:type="uml:Object"><attributes><attribute name="RequestedComMode" scope="Private" xmi:idref="EAID_a953c46cfd8e9c443dbe9c27c8d677d1"><Constraints/><properties collection="true"/></attribute><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_8959a835c897ba993a5206664aa8e0af"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute></attributes></element><element name="SilenceAfterWakeupTimer:VarArray" scope="public" xmi:idref="EAID_0b51fb513b8e562d606cd4b6a75559cb" xmi:type="uml:Object"><attributes><attribute name="SilenceAfterWakeupTimer" scope="Private" xmi:idref="EAID_cd1d60bf52543661a96d45441b923171"><Constraints/><properties collection="true"/></attribute><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_be1b2f2d4e27d78588675e7ebd5c2afe"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute></attributes></element><element name="WakeUpRetryCounter:VarArray" scope="public" xmi:idref="EAID_3e7cf72bdb797525e94ff0ffce3070cc" xmi:type="uml:Object"><attributes><attribute name="WakeUpRetryCounter" scope="Private" xmi:idref="EAID_0b4e6604169b89a9a1f08e07d80f661b"><Constraints/><properties collection="true"/></attribute><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_1518ae39e79a890c2ce7fe57ec142104"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute></attributes></element><element name="Initialized:Var" scope="public" xmi:idref="EAID_045c79f73ab47665923017576a0b630d" xmi:type="uml:Object"><attributes><attribute name="Initialized" scope="Private" xmi:idref="EAID_8be96278268205bdeaa9aac5b1366ea3"><Constraints><Constraint name="CUserPreCompileDeactivationCondition" notes="Initialized flag is only needed for Development Error Detection." type="Pre-condition"/></Constraints><properties/></attribute><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_1cd452f35061813df58fa6a4b1cf3e69"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute></attributes></element></elements><connectors><connector xmi:idref="EAID_d8f05abd5a57ceea228a5b473eacc492"><source xmi:idref="EAID_1e2086925ba76127f18b92789fbd053e"><model name="ComState" type="Object"/><type aggregation="none"/></source><target xmi:idref="EAID_b7f0e084e6e3d69ac889ccbbfd0fc081"><model name="ChannelConfig" type="Object"/><type aggregation="none"/></target><properties direction="Unspecified" ea_type="Association"/><labels mt="VarIndirectableSizeByConstIndirecableRelation"/></connector><connector xmi:idref="EAID_b13f731dcfe5a4c528ef23b84ce3dbd8"><source xmi:idref="EAID_696f0e4845050ed9c3b4e06aa1a1c43e"><model name="ConfirmationTimer" type="Object"/><type aggregation="none"/></source><target xmi:idref="EAID_b7f0e084e6e3d69ac889ccbbfd0fc081"><model name="ChannelConfig" type="Object"/><type aggregation="none"/></target><properties direction="Unspecified" ea_type="Association"/><labels mt="VarIndirectableSizeByConstIndirecableRelation"/></connector><connector xmi:idref="EAID_e865e5e0c7e9eef4ad908658e3ff9738"><source xmi:idref="EAID_d7774ff304b93d52be258f7faa5c87e1"><model name="DesiredConfirmation" type="Object"/><type aggregation="none"/></source><target xmi:idref="EAID_b7f0e084e6e3d69ac889ccbbfd0fc081"><model name="ChannelConfig" type="Object"/><type aggregation="none"/></target><properties direction="Unspecified" ea_type="Association"/><labels mt="VarIndirectableSizeByConstIndirecableRelation"/></connector><connector xmi:idref="EAID_3c6d2787fc2cd8b46cabfac908b87659"><source xmi:idref="EAID_9f7a6c60e50281bece7c6b107af54fa6"><model name="NegativeConfirmation" type="Object"/><type aggregation="none"/></source><target xmi:idref="EAID_b7f0e084e6e3d69ac889ccbbfd0fc081"><model name="ChannelConfig" type="Object"/><type aggregation="none"/></target><properties direction="Unspecified" ea_type="Association"/><labels mt="VarIndirectableSizeByConstIndirecableRelation"/></connector><connector xmi:idref="EAID_80642a142439d835578b7e009ecdad4d"><source xmi:idref="EAID_d22486d072a93c4ddeed4920a659e827"><model name="RequestedComMode" type="Object"/><type aggregation="none"/></source><target xmi:idref="EAID_b7f0e084e6e3d69ac889ccbbfd0fc081"><model name="ChannelConfig" type="Object"/><type aggregation="none"/></target><properties direction="Unspecified" ea_type="Association"/><labels mt="VarIndirectableSizeByConstIndirecableRelation"/></connector><connector xmi:idref="EAID_9088175e75eed6cbe4eb1e1bf0c619de"><source xmi:idref="EAID_0b51fb513b8e562d606cd4b6a75559cb"><model name="SilenceAfterWakeupTimer" type="Object"/><type aggregation="none"/></source><target xmi:idref="EAID_b7f0e084e6e3d69ac889ccbbfd0fc081"><model name="ChannelConfig" type="Object"/><type aggregation="none"/></target><properties direction="Unspecified" ea_type="Association"/><labels mt="VarIndirectableSizeByConstIndirecableRelation"/></connector><connector xmi:idref="EAID_4c6017b3f07d7c4311229bd3f0725eab"><source xmi:idref="EAID_3e7cf72bdb797525e94ff0ffce3070cc"><model name="WakeUpRetryCounter" type="Object"/><type aggregation="none"/></source><target xmi:idref="EAID_b7f0e084e6e3d69ac889ccbbfd0fc081"><model name="ChannelConfig" type="Object"/><type aggregation="none"/></target><properties direction="Unspecified" ea_type="Association"/><labels mt="VarIndirectableSizeByConstIndirecableRelation"/></connector></connectors><diagrams><diagram xmi:id="EAID_2493607d4471c873dcbefb692a94dffd"><elements><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_b7f0e084e6e3d69ac889ccbbfd0fc081"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_58096e4f6e00e4af512027e034afca6d"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_676b97d48b5a73f42e7fd196a63837aa"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_1e2086925ba76127f18b92789fbd053e"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_d8f05abd5a57ceea228a5b473eacc492"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_696f0e4845050ed9c3b4e06aa1a1c43e"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_b13f731dcfe5a4c528ef23b84ce3dbd8"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_d7774ff304b93d52be258f7faa5c87e1"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_e865e5e0c7e9eef4ad908658e3ff9738"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_9f7a6c60e50281bece7c6b107af54fa6"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_3c6d2787fc2cd8b46cabfac908b87659"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_d22486d072a93c4ddeed4920a659e827"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_80642a142439d835578b7e009ecdad4d"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_0b51fb513b8e562d606cd4b6a75559cb"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_9088175e75eed6cbe4eb1e1bf0c619de"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_3e7cf72bdb797525e94ff0ffce3070cc"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_4c6017b3f07d7c4311229bd3f0725eab"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_045c79f73ab47665923017576a0b630d"/></elements><model owner="EAPKe45878863bb44ed4d75afb7f21f2d2c4" package="EAPKe45878863bb44ed4d75afb7f21f2d2c4"/><properties name="All Data and Relations" type="Object"/><style1 value="ShowPrivate=1;ShowProtected=1;ShowPublic=1;HideRelationships=0;Locked=0;Border=1;HighlightForeign=1;PackageContents=1;SequenceNotes=0;ScalePrintImage=0;PPgs.cx=0;PPgs.cy=0;DocSize.cx=791;DocSize.cy=1134;ShowDetails=0;Orientation=P;Zoom=100;ShowTags=0;OpParams=1;VisibleAttributeDetail=0;ShowOpRetType=1;ShowIcons=1;CollabNums=0;HideProps=0;ShowReqs=0;ShowCons=0;PaperSize=9;HideParents=0;UseAlias=0;HideAtts=0;HideOps=0;HideStereo=0;HideElemStereo=0;ShowTests=0;ShowMaint=0;ConnectorNotation=UML 2.1;ExplicitNavigability=0;AdvancedElementProps=1;AdvancedFeatureProps=1;AdvancedConnectorProps=1;ShowNotes=0;SuppressBrackets=0;SuppConnectorLabels=0;PrintPageHeadFoot=0;ShowAsList=0;"/><style2 value="ExcludeRTF=0;DocAll=0;HideQuals=0;AttPkg=1;ShowTests=0;ShowMaint=0;SuppressFOC=1;MatrixActive=0;SwimlanesActive=0;MatrixLineWidth=1;MatrixLocked=0;TConnectorNotation=UML 2.1;TExplicitNavigability=0;AdvancedElementProps=1;AdvancedFeatureProps=1;AdvancedConnectorProps=1;ProfileData=;MDGDgm=;STBLDgm=;ShowNotes=0;VisibleAttributeDetail=0;ShowOpRetType=1;SuppressBrackets=0;SuppConnectorLabels=0;PrintPageHeadFoot=0;ShowAsList=0;"/><swimlanes value="locked=false;orientation=0;width=0;inbar=false;names=false;color=0;bold=false;fcol=0;;cls=0;"/><matrixitems value="locked=false;matrixactive=false;swimlanesactive=false;width=1;"/><extendedProperties/></diagram><diagram xmi:id="EAID_bc0f506839543174a54e450822408c5c"><elements><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_b7f0e084e6e3d69ac889ccbbfd0fc081"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_58096e4f6e00e4af512027e034afca6d"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_676b97d48b5a73f42e7fd196a63837aa"/></elements><model owner="EAPKe45878863bb44ed4d75afb7f21f2d2c4" package="EAPKe45878863bb44ed4d75afb7f21f2d2c4"/><properties name="CONST with Struct Elements" type="Object"/><style1 value="ShowPrivate=1;ShowProtected=1;ShowPublic=1;HideRelationships=0;Locked=0;Border=1;HighlightForeign=1;PackageContents=1;SequenceNotes=0;ScalePrintImage=0;PPgs.cx=0;PPgs.cy=0;DocSize.cx=791;DocSize.cy=1134;ShowDetails=0;Orientation=P;Zoom=100;ShowTags=0;OpParams=1;VisibleAttributeDetail=0;ShowOpRetType=1;ShowIcons=1;CollabNums=0;HideProps=0;ShowReqs=0;ShowCons=0;PaperSize=9;HideParents=0;UseAlias=0;HideAtts=0;HideOps=0;HideStereo=0;HideElemStereo=0;ShowTests=0;ShowMaint=0;ConnectorNotation=UML 2.1;ExplicitNavigability=0;AdvancedElementProps=1;AdvancedFeatureProps=1;AdvancedConnectorProps=1;ShowNotes=0;SuppressBrackets=0;SuppConnectorLabels=0;PrintPageHeadFoot=0;ShowAsList=0;"/><style2 value="ExcludeRTF=0;DocAll=0;HideQuals=0;AttPkg=1;ShowTests=0;ShowMaint=0;SuppressFOC=1;MatrixActive=0;SwimlanesActive=0;MatrixLineWidth=1;MatrixLocked=0;TConnectorNotation=UML 2.1;TExplicitNavigability=0;AdvancedElementProps=1;AdvancedFeatureProps=1;AdvancedConnectorProps=1;ProfileData=;MDGDgm=;STBLDgm=;ShowNotes=0;VisibleAttributeDetail=0;ShowOpRetType=1;SuppressBrackets=0;SuppConnectorLabels=0;PrintPageHeadFoot=0;ShowAsList=0;"/><swimlanes value="locked=false;orientation=0;width=0;inbar=false;names=false;color=0;bold=false;fcol=0;;cls=0;"/><matrixitems value="locked=false;matrixactive=false;swimlanesactive=false;width=1;"/><extendedProperties/></diagram><diagram xmi:id="EAID_45107e4f222392e9602392db104d0a6b"><elements><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_b7f0e084e6e3d69ac889ccbbfd0fc081"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_58096e4f6e00e4af512027e034afca6d"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_676b97d48b5a73f42e7fd196a63837aa"/></elements><model owner="EAPKe45878863bb44ed4d75afb7f21f2d2c4" package="EAPKe45878863bb44ed4d75afb7f21f2d2c4"/><properties name="CONST without Struct Elements" type="Object"/><style1 value="ShowPrivate=1;ShowProtected=1;ShowPublic=1;HideRelationships=0;Locked=0;Border=1;HighlightForeign=1;PackageContents=1;SequenceNotes=0;ScalePrintImage=0;PPgs.cx=0;PPgs.cy=0;DocSize.cx=791;DocSize.cy=1134;ShowDetails=0;Orientation=P;Zoom=100;ShowTags=0;OpParams=1;VisibleAttributeDetail=0;ShowOpRetType=1;ShowIcons=1;CollabNums=0;HideProps=0;ShowReqs=0;ShowCons=0;PaperSize=9;HideParents=0;UseAlias=0;HideAtts=1;HideOps=1;HideStereo=0;HideElemStereo=0;ShowTests=0;ShowMaint=0;ConnectorNotation=UML 2.1;ExplicitNavigability=0;AdvancedElementProps=1;AdvancedFeatureProps=1;AdvancedConnectorProps=1;ShowNotes=0;SuppressBrackets=0;SuppConnectorLabels=0;PrintPageHeadFoot=0;ShowAsList=0;"/><style2 value="ExcludeRTF=0;DocAll=0;HideQuals=0;AttPkg=1;ShowTests=0;ShowMaint=0;SuppressFOC=1;MatrixActive=0;SwimlanesActive=0;MatrixLineWidth=1;MatrixLocked=0;TConnectorNotation=UML 2.1;TExplicitNavigability=0;AdvancedElementProps=1;AdvancedFeatureProps=1;AdvancedConnectorProps=1;ProfileData=;MDGDgm=;STBLDgm=;ShowNotes=1;VisibleAttributeDetail=0;ShowOpRetType=1;SuppressBrackets=0;SuppConnectorLabels=0;PrintPageHeadFoot=0;ShowAsList=0;"/><swimlanes value="locked=false;orientation=0;width=0;inbar=false;names=false;color=0;bold=false;fcol=0;;cls=0;"/><matrixitems value="locked=false;matrixactive=false;swimlanesactive=false;width=1;"/><extendedProperties/></diagram><diagram xmi:id="EAID_3dc55416a13a28318f5fcd9a01019306"><elements><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_1e2086925ba76127f18b92789fbd053e"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_b7f0e084e6e3d69ac889ccbbfd0fc081"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_696f0e4845050ed9c3b4e06aa1a1c43e"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_b7f0e084e6e3d69ac889ccbbfd0fc081"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_d7774ff304b93d52be258f7faa5c87e1"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_b7f0e084e6e3d69ac889ccbbfd0fc081"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_9f7a6c60e50281bece7c6b107af54fa6"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_b7f0e084e6e3d69ac889ccbbfd0fc081"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_d22486d072a93c4ddeed4920a659e827"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_b7f0e084e6e3d69ac889ccbbfd0fc081"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_0b51fb513b8e562d606cd4b6a75559cb"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_b7f0e084e6e3d69ac889ccbbfd0fc081"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_3e7cf72bdb797525e94ff0ffce3070cc"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_b7f0e084e6e3d69ac889ccbbfd0fc081"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_045c79f73ab47665923017576a0b630d"/></elements><model owner="EAPKe45878863bb44ed4d75afb7f21f2d2c4" package="EAPKe45878863bb44ed4d75afb7f21f2d2c4"/><properties name="VAR and Relations" type="Object"/><style1 value="ShowPrivate=1;ShowProtected=1;ShowPublic=1;HideRelationships=0;Locked=0;Border=1;HighlightForeign=1;PackageContents=1;SequenceNotes=0;ScalePrintImage=0;PPgs.cx=0;PPgs.cy=0;DocSize.cx=791;DocSize.cy=1134;ShowDetails=0;Orientation=P;Zoom=100;ShowTags=0;OpParams=1;VisibleAttributeDetail=0;ShowOpRetType=1;ShowIcons=1;CollabNums=0;HideProps=0;ShowReqs=0;ShowCons=0;PaperSize=9;HideParents=0;UseAlias=0;HideAtts=0;HideOps=0;HideStereo=0;HideElemStereo=0;ShowTests=0;ShowMaint=0;ConnectorNotation=UML 2.1;ExplicitNavigability=0;AdvancedElementProps=1;AdvancedFeatureProps=1;AdvancedConnectorProps=1;ShowNotes=0;SuppressBrackets=0;SuppConnectorLabels=0;PrintPageHeadFoot=0;ShowAsList=0;"/><style2 value="ExcludeRTF=0;DocAll=0;HideQuals=0;AttPkg=1;ShowTests=0;ShowMaint=0;SuppressFOC=1;MatrixActive=0;SwimlanesActive=0;MatrixLineWidth=1;MatrixLocked=0;TConnectorNotation=UML 2.1;TExplicitNavigability=0;AdvancedElementProps=1;AdvancedFeatureProps=1;AdvancedConnectorProps=1;ProfileData=;MDGDgm=;STBLDgm=;ShowNotes=1;VisibleAttributeDetail=0;ShowOpRetType=1;SuppressBrackets=0;SuppConnectorLabels=0;PrintPageHeadFoot=0;ShowAsList=0;"/><swimlanes value="locked=false;orientation=0;width=0;inbar=false;names=false;color=0;bold=false;fcol=0;;cls=0;"/><matrixitems value="locked=false;matrixactive=false;swimlanesactive=false;width=1;"/><extendedProperties/></diagram><diagram xmi:id="EAID_dba61c19092a1f218eec6224b20a791f"><elements><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_1e2086925ba76127f18b92789fbd053e"/></elements><model owner="EAPKe45878863bb44ed4d75afb7f21f2d2c4" package="EAPKe45878863bb44ed4d75afb7f21f2d2c4"/><properties name="Data Accessed by Adress Operator" type="Object"/><style1 value="ShowPrivate=1;ShowProtected=1;ShowPublic=1;HideRelationships=0;Locked=0;Border=1;HighlightForeign=1;PackageContents=1;SequenceNotes=0;ScalePrintImage=0;PPgs.cx=0;PPgs.cy=0;DocSize.cx=791;DocSize.cy=1134;ShowDetails=0;Orientation=P;Zoom=100;ShowTags=0;OpParams=1;VisibleAttributeDetail=0;ShowOpRetType=1;ShowIcons=1;CollabNums=0;HideProps=0;ShowReqs=0;ShowCons=0;PaperSize=9;HideParents=0;UseAlias=0;HideAtts=1;HideOps=1;HideStereo=0;HideElemStereo=0;ShowTests=0;ShowMaint=0;ConnectorNotation=UML 2.1;ExplicitNavigability=0;AdvancedElementProps=1;AdvancedFeatureProps=1;AdvancedConnectorProps=1;ShowNotes=0;SuppressBrackets=0;SuppConnectorLabels=0;PrintPageHeadFoot=0;ShowAsList=0;"/><style2 value="ExcludeRTF=0;DocAll=0;HideQuals=0;AttPkg=1;ShowTests=0;ShowMaint=0;SuppressFOC=1;MatrixActive=0;SwimlanesActive=0;MatrixLineWidth=1;MatrixLocked=0;TConnectorNotation=UML 2.1;TExplicitNavigability=0;AdvancedElementProps=1;AdvancedFeatureProps=1;AdvancedConnectorProps=1;ProfileData=;MDGDgm=;STBLDgm=;ShowNotes=1;VisibleAttributeDetail=0;ShowOpRetType=1;SuppressBrackets=0;SuppConnectorLabels=0;PrintPageHeadFoot=0;ShowAsList=0;"/><swimlanes value="locked=false;orientation=0;width=0;inbar=false;names=false;color=0;bold=false;fcol=0;;cls=0;"/><matrixitems value="locked=false;matrixactive=false;swimlanesactive=false;width=1;"/><extendedProperties/></diagram><diagram xmi:id="EAID_99bd38cf83c76ec8b3279b31c3e4e177"><elements/><model owner="EAPKe45878863bb44ed4d75afb7f21f2d2c4" package="EAPKe45878863bb44ed4d75afb7f21f2d2c4"/><properties name="Data Accessed by Interface Handles" type="Object"/><style1 value="ShowPrivate=1;ShowProtected=1;ShowPublic=1;HideRelationships=0;Locked=0;Border=1;HighlightForeign=1;PackageContents=1;SequenceNotes=0;ScalePrintImage=0;PPgs.cx=0;PPgs.cy=0;DocSize.cx=791;DocSize.cy=1134;ShowDetails=0;Orientation=P;Zoom=100;ShowTags=0;OpParams=1;VisibleAttributeDetail=0;ShowOpRetType=1;ShowIcons=1;CollabNums=0;HideProps=0;ShowReqs=0;ShowCons=0;PaperSize=9;HideParents=0;UseAlias=0;HideAtts=1;HideOps=1;HideStereo=0;HideElemStereo=0;ShowTests=0;ShowMaint=0;ConnectorNotation=UML 2.1;ExplicitNavigability=0;AdvancedElementProps=1;AdvancedFeatureProps=1;AdvancedConnectorProps=1;ShowNotes=0;SuppressBrackets=0;SuppConnectorLabels=0;PrintPageHeadFoot=0;ShowAsList=0;"/><style2 value="ExcludeRTF=0;DocAll=0;HideQuals=0;AttPkg=1;ShowTests=0;ShowMaint=0;SuppressFOC=1;MatrixActive=0;SwimlanesActive=0;MatrixLineWidth=1;MatrixLocked=0;TConnectorNotation=UML 2.1;TExplicitNavigability=0;AdvancedElementProps=1;AdvancedFeatureProps=1;AdvancedConnectorProps=1;ProfileData=;MDGDgm=;STBLDgm=;ShowNotes=1;VisibleAttributeDetail=0;ShowOpRetType=1;SuppressBrackets=0;SuppConnectorLabels=0;PrintPageHeadFoot=0;ShowAsList=0;"/><swimlanes value="locked=false;orientation=0;width=0;inbar=false;names=false;color=0;bold=false;fcol=0;;cls=0;"/><matrixitems value="locked=false;matrixactive=false;swimlanesactive=false;width=1;"/><extendedProperties/></diagram><diagram xmi:id="EAID_542e02833a2c48bb25a308593b32fb3b"><elements><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_b7f0e084e6e3d69ac889ccbbfd0fc081"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_676b97d48b5a73f42e7fd196a63837aa"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_1e2086925ba76127f18b92789fbd053e"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_696f0e4845050ed9c3b4e06aa1a1c43e"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_d7774ff304b93d52be258f7faa5c87e1"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_9f7a6c60e50281bece7c6b107af54fa6"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_d22486d072a93c4ddeed4920a659e827"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_0b51fb513b8e562d606cd4b6a75559cb"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_3e7cf72bdb797525e94ff0ffce3070cc"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_045c79f73ab47665923017576a0b630d"/></elements><model owner="EAPKe45878863bb44ed4d75afb7f21f2d2c4" package="EAPKe45878863bb44ed4d75afb7f21f2d2c4"/><properties name="Max Precompile Configurable Data" type="Object"/><style1 value="ShowPrivate=1;ShowProtected=1;ShowPublic=1;HideRelationships=0;Locked=0;Border=1;HighlightForeign=1;PackageContents=1;SequenceNotes=0;ScalePrintImage=0;PPgs.cx=0;PPgs.cy=0;DocSize.cx=791;DocSize.cy=1134;ShowDetails=0;Orientation=P;Zoom=100;ShowTags=0;OpParams=1;VisibleAttributeDetail=0;ShowOpRetType=1;ShowIcons=1;CollabNums=0;HideProps=0;ShowReqs=0;ShowCons=0;PaperSize=9;HideParents=0;UseAlias=0;HideAtts=1;HideOps=1;HideStereo=0;HideElemStereo=0;ShowTests=0;ShowMaint=0;ConnectorNotation=UML 2.1;ExplicitNavigability=0;AdvancedElementProps=1;AdvancedFeatureProps=1;AdvancedConnectorProps=1;ShowNotes=0;SuppressBrackets=0;SuppConnectorLabels=0;PrintPageHeadFoot=0;ShowAsList=0;"/><style2 value="ExcludeRTF=0;DocAll=0;HideQuals=0;AttPkg=1;ShowTests=0;ShowMaint=0;SuppressFOC=1;MatrixActive=0;SwimlanesActive=0;MatrixLineWidth=1;MatrixLocked=0;TConnectorNotation=UML 2.1;TExplicitNavigability=0;AdvancedElementProps=1;AdvancedFeatureProps=1;AdvancedConnectorProps=1;ProfileData=;MDGDgm=;STBLDgm=;ShowNotes=1;VisibleAttributeDetail=0;ShowOpRetType=1;SuppressBrackets=0;SuppConnectorLabels=0;PrintPageHeadFoot=0;ShowAsList=0;"/><swimlanes value="locked=false;orientation=0;width=0;inbar=false;names=false;color=0;bold=false;fcol=0;;cls=0;"/><matrixitems value="locked=false;matrixactive=false;swimlanesactive=false;width=1;"/><extendedProperties/></diagram><diagram xmi:id="EAID_b7b2cf5561a010c32c32411391e26ca1"><elements/><model owner="EAPKe45878863bb44ed4d75afb7f21f2d2c4" package="EAPKe45878863bb44ed4d75afb7f21f2d2c4"/><properties name="Max Linktime Configurable Data" type="Object"/><style1 value="ShowPrivate=1;ShowProtected=1;ShowPublic=1;HideRelationships=0;Locked=0;Border=1;HighlightForeign=1;PackageContents=1;SequenceNotes=0;ScalePrintImage=0;PPgs.cx=0;PPgs.cy=0;DocSize.cx=791;DocSize.cy=1134;ShowDetails=0;Orientation=P;Zoom=100;ShowTags=0;OpParams=1;VisibleAttributeDetail=0;ShowOpRetType=1;ShowIcons=1;CollabNums=0;HideProps=0;ShowReqs=0;ShowCons=0;PaperSize=9;HideParents=0;UseAlias=0;HideAtts=1;HideOps=1;HideStereo=0;HideElemStereo=0;ShowTests=0;ShowMaint=0;ConnectorNotation=UML 2.1;ExplicitNavigability=0;AdvancedElementProps=1;AdvancedFeatureProps=1;AdvancedConnectorProps=1;ShowNotes=0;SuppressBrackets=0;SuppConnectorLabels=0;PrintPageHeadFoot=0;ShowAsList=0;"/><style2 value="ExcludeRTF=0;DocAll=0;HideQuals=0;AttPkg=1;ShowTests=0;ShowMaint=0;SuppressFOC=1;MatrixActive=0;SwimlanesActive=0;MatrixLineWidth=1;MatrixLocked=0;TConnectorNotation=UML 2.1;TExplicitNavigability=0;AdvancedElementProps=1;AdvancedFeatureProps=1;AdvancedConnectorProps=1;ProfileData=;MDGDgm=;STBLDgm=;ShowNotes=1;VisibleAttributeDetail=0;ShowOpRetType=1;SuppressBrackets=0;SuppConnectorLabels=0;PrintPageHeadFoot=0;ShowAsList=0;"/><swimlanes value="locked=false;orientation=0;width=0;inbar=false;names=false;color=0;bold=false;fcol=0;;cls=0;"/><matrixitems value="locked=false;matrixactive=false;swimlanesactive=false;width=1;"/><extendedProperties/></diagram><diagram xmi:id="EAID_0a5c5812739c5eb698bdbc4d31948e59"><elements><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_58096e4f6e00e4af512027e034afca6d"/><element geometry="Left=0;Top=0;Right=299;Bottom=60;" subject="EAID_c68bac9c43086ab8fef2604c61dc74fb"/></elements><model owner="EAPKe45878863bb44ed4d75afb7f21f2d2c4" package="EAPKe45878863bb44ed4d75afb7f21f2d2c4"/><properties name="Max Postbuild Configurable Data" type="Object"/><style1 value="ShowPrivate=1;ShowProtected=1;ShowPublic=1;HideRelationships=0;Locked=0;Border=1;HighlightForeign=1;PackageContents=1;SequenceNotes=0;ScalePrintImage=0;PPgs.cx=0;PPgs.cy=0;DocSize.cx=791;DocSize.cy=1134;ShowDetails=0;Orientation=P;Zoom=100;ShowTags=0;OpParams=1;VisibleAttributeDetail=0;ShowOpRetType=1;ShowIcons=1;CollabNums=0;HideProps=0;ShowReqs=0;ShowCons=0;PaperSize=9;HideParents=0;UseAlias=0;HideAtts=1;HideOps=1;HideStereo=0;HideElemStereo=0;ShowTests=0;ShowMaint=0;ConnectorNotation=UML 2.1;ExplicitNavigability=0;AdvancedElementProps=1;AdvancedFeatureProps=1;AdvancedConnectorProps=1;ShowNotes=0;SuppressBrackets=0;SuppConnectorLabels=0;PrintPageHeadFoot=0;ShowAsList=0;"/><style2 value="ExcludeRTF=0;DocAll=0;HideQuals=0;AttPkg=1;ShowTests=0;ShowMaint=0;SuppressFOC=1;MatrixActive=0;SwimlanesActive=0;MatrixLineWidth=1;MatrixLocked=0;TConnectorNotation=UML 2.1;TExplicitNavigability=0;AdvancedElementProps=1;AdvancedFeatureProps=1;AdvancedConnectorProps=1;ProfileData=;MDGDgm=;STBLDgm=;ShowNotes=1;VisibleAttributeDetail=0;ShowOpRetType=1;SuppressBrackets=0;SuppConnectorLabels=0;PrintPageHeadFoot=0;ShowAsList=0;"/><swimlanes value="locked=false;orientation=0;width=0;inbar=false;names=false;color=0;bold=false;fcol=0;;cls=0;"/><matrixitems value="locked=false;matrixactive=false;swimlanesactive=false;width=1;"/><extendedProperties/></diagram><diagram xmi:id="EAID_e4635b25c7b008c60f247c5c9f45a221"><elements/><model owner="EAPKe45878863bb44ed4d75afb7f21f2d2c4" package="EAPKe45878863bb44ed4d75afb7f21f2d2c4"/><properties name="Calibration Lite Data" type="Object"/><style1 value="ShowPrivate=1;ShowProtected=1;ShowPublic=1;HideRelationships=0;Locked=0;Border=1;HighlightForeign=1;PackageContents=1;SequenceNotes=0;ScalePrintImage=0;PPgs.cx=0;PPgs.cy=0;DocSize.cx=791;DocSize.cy=1134;ShowDetails=0;Orientation=P;Zoom=100;ShowTags=0;OpParams=1;VisibleAttributeDetail=0;ShowOpRetType=1;ShowIcons=1;CollabNums=0;HideProps=0;ShowReqs=0;ShowCons=0;PaperSize=9;HideParents=0;UseAlias=0;HideAtts=1;HideOps=1;HideStereo=0;HideElemStereo=0;ShowTests=0;ShowMaint=0;ConnectorNotation=UML 2.1;ExplicitNavigability=0;AdvancedElementProps=1;AdvancedFeatureProps=1;AdvancedConnectorProps=1;ShowNotes=0;SuppressBrackets=0;SuppConnectorLabels=0;PrintPageHeadFoot=0;ShowAsList=0;"/><style2 value="ExcludeRTF=0;DocAll=0;HideQuals=0;AttPkg=1;ShowTests=0;ShowMaint=0;SuppressFOC=1;MatrixActive=0;SwimlanesActive=0;MatrixLineWidth=1;MatrixLocked=0;TConnectorNotation=UML 2.1;TExplicitNavigability=0;AdvancedElementProps=1;AdvancedFeatureProps=1;AdvancedConnectorProps=1;ProfileData=;MDGDgm=;STBLDgm=;ShowNotes=1;VisibleAttributeDetail=0;ShowOpRetType=1;SuppressBrackets=0;SuppConnectorLabels=0;PrintPageHeadFoot=0;ShowAsList=0;"/><swimlanes value="locked=false;orientation=0;width=0;inbar=false;names=false;color=0;bold=false;fcol=0;;cls=0;"/><matrixitems value="locked=false;matrixactive=false;swimlanesactive=false;width=1;"/><extendedProperties/></diagram><diagram xmi:id="EAID_08aab7b9cc7a809e85a01c8ed9ee214e"><elements/><model owner="EAPKe45878863bb44ed4d75afb7f21f2d2c4" package="EAPKe45878863bb44ed4d75afb7f21f2d2c4"/><properties name="Sandbox with Details" type="Object"/><style1 value="ShowPrivate=1;ShowProtected=1;ShowPublic=1;HideRelationships=0;Locked=0;Border=1;HighlightForeign=1;PackageContents=1;SequenceNotes=0;ScalePrintImage=0;PPgs.cx=0;PPgs.cy=0;DocSize.cx=791;DocSize.cy=1134;ShowDetails=0;Orientation=P;Zoom=100;ShowTags=0;OpParams=1;VisibleAttributeDetail=0;ShowOpRetType=1;ShowIcons=1;CollabNums=0;HideProps=0;ShowReqs=0;ShowCons=0;PaperSize=9;HideParents=0;UseAlias=0;HideAtts=0;HideOps=0;HideStereo=0;HideElemStereo=0;ShowTests=0;ShowMaint=0;ConnectorNotation=UML 2.1;ExplicitNavigability=0;AdvancedElementProps=1;AdvancedFeatureProps=1;AdvancedConnectorProps=1;ShowNotes=0;SuppressBrackets=0;SuppConnectorLabels=0;PrintPageHeadFoot=0;ShowAsList=0;"/><style2 value="ExcludeRTF=0;DocAll=0;HideQuals=0;AttPkg=1;ShowTests=0;ShowMaint=0;SuppressFOC=1;MatrixActive=0;SwimlanesActive=0;MatrixLineWidth=1;MatrixLocked=0;TConnectorNotation=UML 2.1;TExplicitNavigability=0;AdvancedElementProps=1;AdvancedFeatureProps=1;AdvancedConnectorProps=1;ProfileData=;MDGDgm=;STBLDgm=;ShowNotes=1;VisibleAttributeDetail=0;ShowOpRetType=1;SuppressBrackets=0;SuppConnectorLabels=0;PrintPageHeadFoot=0;ShowAsList=0;"/><swimlanes value="locked=false;orientation=0;width=0;inbar=false;names=false;color=0;bold=false;fcol=0;;cls=0;"/><matrixitems value="locked=false;matrixactive=false;swimlanesactive=false;width=1;"/><extendedProperties/></diagram><diagram xmi:id="EAID_f2d593a3481fecf51bd1daca0c6fd760"><elements/><model owner="EAPKe45878863bb44ed4d75afb7f21f2d2c4" package="EAPKe45878863bb44ed4d75afb7f21f2d2c4"/><properties name="Sandbox without Details" type="Object"/><style1 value="ShowPrivate=1;ShowProtected=1;ShowPublic=1;HideRelationships=0;Locked=0;Border=1;HighlightForeign=1;PackageContents=1;SequenceNotes=0;ScalePrintImage=0;PPgs.cx=0;PPgs.cy=0;DocSize.cx=791;DocSize.cy=1134;ShowDetails=0;Orientation=P;Zoom=100;ShowTags=0;OpParams=1;VisibleAttributeDetail=0;ShowOpRetType=1;ShowIcons=1;CollabNums=0;HideProps=0;ShowReqs=0;ShowCons=0;PaperSize=9;HideParents=0;UseAlias=0;HideAtts=1;HideOps=1;HideStereo=0;HideElemStereo=0;ShowTests=0;ShowMaint=0;ConnectorNotation=UML 2.1;ExplicitNavigability=0;AdvancedElementProps=1;AdvancedFeatureProps=1;AdvancedConnectorProps=1;ShowNotes=0;SuppressBrackets=0;SuppConnectorLabels=0;PrintPageHeadFoot=0;ShowAsList=0;"/><style2 value="ExcludeRTF=0;DocAll=0;HideQuals=0;AttPkg=1;ShowTests=0;ShowMaint=0;SuppressFOC=1;MatrixActive=0;SwimlanesActive=0;MatrixLineWidth=1;MatrixLocked=0;TConnectorNotation=UML 2.1;TExplicitNavigability=0;AdvancedElementProps=1;AdvancedFeatureProps=1;AdvancedConnectorProps=1;ProfileData=;MDGDgm=;STBLDgm=;ShowNotes=0;VisibleAttributeDetail=0;ShowOpRetType=1;SuppressBrackets=0;SuppConnectorLabels=0;PrintPageHeadFoot=0;ShowAsList=0;"/><swimlanes value="locked=false;orientation=0;width=0;inbar=false;names=false;color=0;bold=false;fcol=0;;cls=0;"/><matrixitems value="locked=false;matrixactive=false;swimlanesactive=false;width=1;"/><extendedProperties/></diagram></diagrams></xmi:Extension><uml:Model name="LinSM Abstract Data Model" visibility="public" xmi:id="EAPKe45878863bb44ed4d75afb7f21f2d2c4" xmi:type="uml:Package"><packagedElement name="ChannelConfig:ConstStruct" visibility="public" xmi:id="EAID_b7f0e084e6e3d69ac889ccbbfd0fc081" xmi:type="uml:InstanceSpecification"/><packagedElement name="ChannelPostBuildConfig:ConstStruct" visibility="public" xmi:id="EAID_58096e4f6e00e4af512027e034afca6d" xmi:type="uml:InstanceSpecification"/><packagedElement name="ComMToLinSMChannel:ConstArray" visibility="public" xmi:id="EAID_676b97d48b5a73f42e7fd196a63837aa" xmi:type="uml:InstanceSpecification"/><packagedElement name="ModeRequestRepetitionMax:ConstVar" visibility="public" xmi:id="EAID_c68bac9c43086ab8fef2604c61dc74fb" xmi:type="uml:InstanceSpecification"/><packagedElement name="ComState:VarArray" visibility="public" xmi:id="EAID_1e2086925ba76127f18b92789fbd053e" xmi:type="uml:InstanceSpecification"/><packagedElement name="VarIndirectableSizeByConstIndirecableRelation" visibility="public" xmi:id="EAID_d8f05abd5a57ceea228a5b473eacc492" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dstd8f05abd5a57ceea228a5b473eacc492"/><ownedEnd aggregation="none" association="EAID_d8f05abd5a57ceea228a5b473eacc492" visibility="public" xmi:id="EAID_dstd8f05abd5a57ceea228a5b473eacc492" xmi:type="uml:Property"><type xmi:idref="EAID_b7f0e084e6e3d69ac889ccbbfd0fc081"/></ownedEnd><memberEnd xmi:idref="EAID_srcd8f05abd5a57ceea228a5b473eacc492"/><ownedEnd aggregation="none" association="EAID_d8f05abd5a57ceea228a5b473eacc492" visibility="public" xmi:id="EAID_srcd8f05abd5a57ceea228a5b473eacc492" xmi:type="uml:Property"><type xmi:idref="EAID_1e2086925ba76127f18b92789fbd053e"/></ownedEnd></packagedElement><packagedElement name="ConfirmationTimer:VarArray" visibility="public" xmi:id="EAID_696f0e4845050ed9c3b4e06aa1a1c43e" xmi:type="uml:InstanceSpecification"/><packagedElement name="VarIndirectableSizeByConstIndirecableRelation" visibility="public" xmi:id="EAID_b13f731dcfe5a4c528ef23b84ce3dbd8" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dstb13f731dcfe5a4c528ef23b84ce3dbd8"/><ownedEnd aggregation="none" association="EAID_b13f731dcfe5a4c528ef23b84ce3dbd8" visibility="public" xmi:id="EAID_dstb13f731dcfe5a4c528ef23b84ce3dbd8" xmi:type="uml:Property"><type xmi:idref="EAID_b7f0e084e6e3d69ac889ccbbfd0fc081"/></ownedEnd><memberEnd xmi:idref="EAID_srcb13f731dcfe5a4c528ef23b84ce3dbd8"/><ownedEnd aggregation="none" association="EAID_b13f731dcfe5a4c528ef23b84ce3dbd8" visibility="public" xmi:id="EAID_srcb13f731dcfe5a4c528ef23b84ce3dbd8" xmi:type="uml:Property"><type xmi:idref="EAID_696f0e4845050ed9c3b4e06aa1a1c43e"/></ownedEnd></packagedElement><packagedElement name="DesiredConfirmation:VarArray" visibility="public" xmi:id="EAID_d7774ff304b93d52be258f7faa5c87e1" xmi:type="uml:InstanceSpecification"/><packagedElement name="VarIndirectableSizeByConstIndirecableRelation" visibility="public" xmi:id="EAID_e865e5e0c7e9eef4ad908658e3ff9738" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dste865e5e0c7e9eef4ad908658e3ff9738"/><ownedEnd aggregation="none" association="EAID_e865e5e0c7e9eef4ad908658e3ff9738" visibility="public" xmi:id="EAID_dste865e5e0c7e9eef4ad908658e3ff9738" xmi:type="uml:Property"><type xmi:idref="EAID_b7f0e084e6e3d69ac889ccbbfd0fc081"/></ownedEnd><memberEnd xmi:idref="EAID_srce865e5e0c7e9eef4ad908658e3ff9738"/><ownedEnd aggregation="none" association="EAID_e865e5e0c7e9eef4ad908658e3ff9738" visibility="public" xmi:id="EAID_srce865e5e0c7e9eef4ad908658e3ff9738" xmi:type="uml:Property"><type xmi:idref="EAID_d7774ff304b93d52be258f7faa5c87e1"/></ownedEnd></packagedElement><packagedElement name="NegativeConfirmation:VarArray" visibility="public" xmi:id="EAID_9f7a6c60e50281bece7c6b107af54fa6" xmi:type="uml:InstanceSpecification"/><packagedElement name="VarIndirectableSizeByConstIndirecableRelation" visibility="public" xmi:id="EAID_3c6d2787fc2cd8b46cabfac908b87659" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dst3c6d2787fc2cd8b46cabfac908b87659"/><ownedEnd aggregation="none" association="EAID_3c6d2787fc2cd8b46cabfac908b87659" visibility="public" xmi:id="EAID_dst3c6d2787fc2cd8b46cabfac908b87659" xmi:type="uml:Property"><type xmi:idref="EAID_b7f0e084e6e3d69ac889ccbbfd0fc081"/></ownedEnd><memberEnd xmi:idref="EAID_src3c6d2787fc2cd8b46cabfac908b87659"/><ownedEnd aggregation="none" association="EAID_3c6d2787fc2cd8b46cabfac908b87659" visibility="public" xmi:id="EAID_src3c6d2787fc2cd8b46cabfac908b87659" xmi:type="uml:Property"><type xmi:idref="EAID_9f7a6c60e50281bece7c6b107af54fa6"/></ownedEnd></packagedElement><packagedElement name="RequestedComMode:VarArray" visibility="public" xmi:id="EAID_d22486d072a93c4ddeed4920a659e827" xmi:type="uml:InstanceSpecification"/><packagedElement name="VarIndirectableSizeByConstIndirecableRelation" visibility="public" xmi:id="EAID_80642a142439d835578b7e009ecdad4d" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dst80642a142439d835578b7e009ecdad4d"/><ownedEnd aggregation="none" association="EAID_80642a142439d835578b7e009ecdad4d" visibility="public" xmi:id="EAID_dst80642a142439d835578b7e009ecdad4d" xmi:type="uml:Property"><type xmi:idref="EAID_b7f0e084e6e3d69ac889ccbbfd0fc081"/></ownedEnd><memberEnd xmi:idref="EAID_src80642a142439d835578b7e009ecdad4d"/><ownedEnd aggregation="none" association="EAID_80642a142439d835578b7e009ecdad4d" visibility="public" xmi:id="EAID_src80642a142439d835578b7e009ecdad4d" xmi:type="uml:Property"><type xmi:idref="EAID_d22486d072a93c4ddeed4920a659e827"/></ownedEnd></packagedElement><packagedElement name="SilenceAfterWakeupTimer:VarArray" visibility="public" xmi:id="EAID_0b51fb513b8e562d606cd4b6a75559cb" xmi:type="uml:InstanceSpecification"/><packagedElement name="VarIndirectableSizeByConstIndirecableRelation" visibility="public" xmi:id="EAID_9088175e75eed6cbe4eb1e1bf0c619de" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dst9088175e75eed6cbe4eb1e1bf0c619de"/><ownedEnd aggregation="none" association="EAID_9088175e75eed6cbe4eb1e1bf0c619de" visibility="public" xmi:id="EAID_dst9088175e75eed6cbe4eb1e1bf0c619de" xmi:type="uml:Property"><type xmi:idref="EAID_b7f0e084e6e3d69ac889ccbbfd0fc081"/></ownedEnd><memberEnd xmi:idref="EAID_src9088175e75eed6cbe4eb1e1bf0c619de"/><ownedEnd aggregation="none" association="EAID_9088175e75eed6cbe4eb1e1bf0c619de" visibility="public" xmi:id="EAID_src9088175e75eed6cbe4eb1e1bf0c619de" xmi:type="uml:Property"><type xmi:idref="EAID_0b51fb513b8e562d606cd4b6a75559cb"/></ownedEnd></packagedElement><packagedElement name="WakeUpRetryCounter:VarArray" visibility="public" xmi:id="EAID_3e7cf72bdb797525e94ff0ffce3070cc" xmi:type="uml:InstanceSpecification"/><packagedElement name="VarIndirectableSizeByConstIndirecableRelation" visibility="public" xmi:id="EAID_4c6017b3f07d7c4311229bd3f0725eab" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dst4c6017b3f07d7c4311229bd3f0725eab"/><ownedEnd aggregation="none" association="EAID_4c6017b3f07d7c4311229bd3f0725eab" visibility="public" xmi:id="EAID_dst4c6017b3f07d7c4311229bd3f0725eab" xmi:type="uml:Property"><type xmi:idref="EAID_b7f0e084e6e3d69ac889ccbbfd0fc081"/></ownedEnd><memberEnd xmi:idref="EAID_src4c6017b3f07d7c4311229bd3f0725eab"/><ownedEnd aggregation="none" association="EAID_4c6017b3f07d7c4311229bd3f0725eab" visibility="public" xmi:id="EAID_src4c6017b3f07d7c4311229bd3f0725eab" xmi:type="uml:Property"><type xmi:idref="EAID_3e7cf72bdb797525e94ff0ffce3070cc"/></ownedEnd></packagedElement><packagedElement name="Initialized:Var" visibility="public" xmi:id="EAID_045c79f73ab47665923017576a0b630d" xmi:type="uml:InstanceSpecification"/></uml:Model></xmi:XMI>
