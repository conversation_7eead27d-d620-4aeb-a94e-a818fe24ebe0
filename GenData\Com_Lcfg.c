/**********************************************************************************************************************
 *  COPYRIGHT
 *  -------------------------------------------------------------------------------------------------------------------
 *  \verbatim
 *
 *                 This software is copyright protected and proprietary to Vector Informatik GmbH.
 *                 Vector Informatik GmbH grants to you only those rights as set out in the license conditions.
 *                 All other rights remain with Vector Informatik GmbH.
 *  \endverbatim
 *  -------------------------------------------------------------------------------------------------------------------
 *  LICENSE
 *  -------------------------------------------------------------------------------------------------------------------
 *            Module: Com
 *           Program: MSR_Vector_SLP4
 *          Customer: AUTOSAR 4 Evaluation Bundle
 *       Expiry Date: Not restricted
 *  Ordered Derivat.: Aurix 2G/TC377
 *    License Scope : The usage is restricted to CBD2000456_D02
 *
 *  -------------------------------------------------------------------------------------------------------------------
 *  FILE DESCRIPTION
 *  -------------------------------------------------------------------------------------------------------------------
 *              File: Com_Lcfg.c
 *   Generation Time: 2025-08-05 10:37:18
 *           Project: Demo - Version 1.0
 *          Delivery: CBD2000456_D02
 *      Tool Version: DaVinci Configurator (beta) 5.21.40 SP2
 *
 *
 *********************************************************************************************************************/

/**********************************************************************************************************************
 ! BETA VERSION                                                                                                       !
 !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
 ! This version of DaVinci Configurator Pro and/or the related Basic Software Package is BETA software.               !
 ! BETA Software is basically operable, but not sufficiently tested, verified and/or qualified for use in series      !
 ! production and/or in vehicles operating on public or non-public roads.                                             !
 ! In particular, without limitation, BETA Software may cause unpredictable ECU behavior, may not provide all         !
 ! functions necessary for use in series production and/or may not comply with quality requirements which are         !
 ! necessary according to the state of the art. BETA Software must not be used in series production.                  !
 !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
**********************************************************************************************************************/

/**********************************************************************************************************************
 * WARNING: This code has been generated with reduced-severity errors. 
 * The created output files contain errors that have been ignored. Usage of the created files can lead to unpredictable behavior of the embedded code.
 * Usage of the created files happens at own risk!
 * 
 * [Warning] COM02205 - Inconsistent signal layout. 
 * - [Reduced Severity due to User-Defined Parameter] /ActiveEcuC/EcuC/EcucPduCollection/PDU_nm_MyECU_Fr_ae963333_Tx[0:PduLength](value=6) of /ActiveEcuC/Com/ComConfig/PDU_nm_MyECU_Fr_ae963333_Tx cannot hold contained ComSignals / ComGroupSignals.
 * 
 * Exceeding signals:
 * /ActiveEcuC/Com/ComConfig/Nm_MyECU_UserData_oPDU_nm_MyECU_Fr_82c22536_Tx
 * Erroneous configuration elements:
 * /ActiveEcuC/Com/ComConfig/Nm_MyECU_UserData_oPDU_nm_MyECU_Fr_82c22536_Tx[0:ComBitPosition](value=16) (DefRef: /MICROSAR/Com/ComConfig/ComSignal/ComBitPosition)
 * /ActiveEcuC/Com/ComConfig/Nm_MyECU_UserData_oPDU_nm_MyECU_Fr_82c22536_Tx[0:ComSignalLength](value=6) (DefRef: /MICROSAR/Com/ComConfig/ComSignal/ComSignalLength)
 * /ActiveEcuC/EcuC/EcucPduCollection/PDU_nm_MyECU_Fr_ae963333_Tx[0:PduLength](value=6) (DefRef: /MICROSAR/EcuC/EcucPduCollection/Pdu/PduLength)
 * /ActiveEcuC/Com/ComConfig/PDU_nm_MyECU_Fr_ae963333_Tx[0:ComIPduSignalRef](value=/ActiveEcuC/Com/ComConfig/Nm_MyECU_UserData_oPDU_nm_MyECU_Fr_82c22536_Tx) (DefRef: /MICROSAR/Com/ComConfig/ComIPdu/ComIPduSignalRef)
 * /ActiveEcuC/Com/ComConfig/Nm_MyECU_UserData_oPDU_nm_MyECU_Fr_82c22536_Tx[0:ComSignalEndianness](value=OPAQUE) (DefRef: /MICROSAR/Com/ComConfig/ComSignal/ComSignalEndianness)
 * /ActiveEcuC/Com/ComConfig/Nm_MyECU_UserData_oPDU_nm_MyECU_Fr_82c22536_Tx[0:ComSignalType](value=UINT8_N) (DefRef: /MICROSAR/Com/ComConfig/ComSignal/ComSignalType)
 *********************************************************************************************************************/

/**********************************************************************************************************************
  MISRA / PClint JUSTIFICATIONS
**********************************************************************************************************************/
/* PRQA  S 1881 EOF */ /* MD_MSR_AutosarBoolean */
/* PRQA  S 1882 EOF */ /* MD_MSR_AutosarBoolean */

/**********************************************************************************************************************
  INCLUDES
**********************************************************************************************************************/
#define V_IL_ASRCOMCFG5_LCFG_SOURCE

#include "Com.h"

#include "Com_Lcfg.h"

#include "Rte_Cbk.h"

#include "SchM_Com.h"

/**********************************************************************************************************************
  LOCAL CONSTANT MACROS
**********************************************************************************************************************/

/**********************************************************************************************************************
  LOCAL FUNCTION MACROS
**********************************************************************************************************************/

/**********************************************************************************************************************
  LOCAL DATA TYPES AND STRUCTURES
**********************************************************************************************************************/

/**********************************************************************************************************************
  LOCAL DATA PROTOTYPES
**********************************************************************************************************************/
/**********************************************************************************************************************
  CONFIGURATION CLASS: PRE_COMPILE
  SECTION: LOCAL DATA PROTOTYPES
**********************************************************************************************************************/

/**********************************************************************************************************************
  CONFIGURATION CLASS: LINK
  SECTION: LOCAL DATA PROTOTYPES
**********************************************************************************************************************/


/**********************************************************************************************************************
  LOCAL DATA
**********************************************************************************************************************/
/**********************************************************************************************************************
  CONFIGURATION CLASS: PRE_COMPILE
  SECTION: LOCAL DATA
**********************************************************************************************************************/

/**********************************************************************************************************************
  CONFIGURATION CLASS: LINK
  SECTION: LOCAL DATA
**********************************************************************************************************************/


/**********************************************************************************************************************
  GLOBAL DATA
**********************************************************************************************************************/
/**********************************************************************************************************************
  CONFIGURATION CLASS: PRE_COMPILE
  SECTION: GLOBAL DATA
**********************************************************************************************************************/
/**********************************************************************************************************************
  Com_ActivatableTxComIPdus
**********************************************************************************************************************/
/** 
  \var    Com_ActivatableTxComIPdus
  \brief  Contains all Tx ComIPdu's with assigned ComIPduGroup
  \details
  Element         Description
  TxPduInfoIdx    the index of the 1:1 relation pointing to Com_TxPduInfo
*/ 
#define COM_START_SEC_CONST_UNSPECIFIED
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */
CONST(Com_ActivatableTxComIPdusType, COM_CONST) Com_ActivatableTxComIPdus[13] = {  /* PRQA S 1514, 1533 */  /* MD_CSL_ObjectOnlyAccessedOnce */
    /* Index    TxPduInfoIdx */
  { /*     0 */           0u },
  { /*     1 */           1u },
  { /*     2 */           2u },
  { /*     3 */           3u },
  { /*     4 */           5u },
  { /*     5 */           6u },
  { /*     6 */           7u },
  { /*     7 */           8u },
  { /*     8 */           9u },
  { /*     9 */          11u },
  { /*    10 */          12u },
  { /*    11 */          13u },
  { /*    12 */          14u }
};
#define COM_STOP_SEC_CONST_UNSPECIFIED
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */

/**********************************************************************************************************************
  Com_AlwaysActiveTxComIPdus
**********************************************************************************************************************/
/** 
  \var    Com_AlwaysActiveTxComIPdus
  \brief  Contains all Tx ComIPdu's without any assigned ComIPduGroup
  \details
  Element         Description
  TxPduInfoIdx    the index of the 1:1 relation pointing to Com_TxPduInfo
*/ 
#define COM_START_SEC_CONST_UNSPECIFIED
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */
CONST(Com_AlwaysActiveTxComIPdusType, COM_CONST) Com_AlwaysActiveTxComIPdus[2] = {  /* PRQA S 1514, 1533 */  /* MD_CSL_ObjectOnlyAccessedOnce */
    /* Index    TxPduInfoIdx */
  { /*     0 */           4u },
  { /*     1 */          10u }
};
#define COM_STOP_SEC_CONST_UNSPECIFIED
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */

/**********************************************************************************************************************
  Com_ConstValueArrayBased
**********************************************************************************************************************/
/** 
  \var    Com_ConstValueArrayBased
  \brief  Optimized array of commonly used values like initial or invalid values. (UINT8_N, UINT8_DYN)
*/ 
#define COM_START_SEC_CONST_8BIT
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */
CONST(Com_ConstValueArrayBasedType, COM_CONST) Com_ConstValueArrayBased[7] = {  /* PRQA S 1514, 1533 */  /* MD_CSL_ObjectOnlyAccessedOnce */
  /* Index     ConstValueArrayBased      Referable Keys */
  /*     0 */                 0x00u,  /* [/ActiveEcuC/Com/ComConfig/signal_RxStat_56bit_opdu_RxStat_30_32e21b42_RxInitValue, /ActiveEcuC/Com/ComConfig/signal_RxDyn_40_opdu_RxDyn_64_84de8797_RxInitValue] */
  /*     1 */                 0x00u,  /* [/ActiveEcuC/Com/ComConfig/signal_RxStat_56bit_opdu_RxStat_30_32e21b42_RxInitValue, /ActiveEcuC/Com/ComConfig/signal_RxDyn_40_opdu_RxDyn_64_84de8797_RxInitValue] */
  /*     2 */                 0x00u,  /* [/ActiveEcuC/Com/ComConfig/signal_RxStat_56bit_opdu_RxStat_30_32e21b42_RxInitValue, /ActiveEcuC/Com/ComConfig/signal_RxDyn_40_opdu_RxDyn_64_84de8797_RxInitValue] */
  /*     3 */                 0x00u,  /* [/ActiveEcuC/Com/ComConfig/signal_RxStat_56bit_opdu_RxStat_30_32e21b42_RxInitValue, /ActiveEcuC/Com/ComConfig/signal_RxDyn_40_opdu_RxDyn_64_84de8797_RxInitValue] */
  /*     4 */                 0x00u,  /* [/ActiveEcuC/Com/ComConfig/signal_RxStat_56bit_opdu_RxStat_30_32e21b42_RxInitValue, /ActiveEcuC/Com/ComConfig/signal_RxDyn_40_opdu_RxDyn_64_84de8797_RxInitValue] */
  /*     5 */                 0x00u,  /* [/ActiveEcuC/Com/ComConfig/signal_RxStat_56bit_opdu_RxStat_30_32e21b42_RxInitValue] */
  /*     6 */                 0x00u   /* [/ActiveEcuC/Com/ComConfig/signal_RxStat_56bit_opdu_RxStat_30_32e21b42_RxInitValue] */
};
#define COM_STOP_SEC_CONST_8BIT
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */

/**********************************************************************************************************************
  Com_ConstValueUInt16
**********************************************************************************************************************/
/** 
  \var    Com_ConstValueUInt16
  \brief  Optimized array of commonly used values like initial or invalid values. (UINT16)
*/ 
#define COM_START_SEC_CONST_16BIT
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */
CONST(Com_ConstValueUInt16Type, COM_CONST) Com_ConstValueUInt16[4] = {  /* PRQA S 1514, 1533 */  /* MD_CSL_ObjectOnlyAccessedOnce */
  /* Index     ConstValueUInt16      Referable Keys */
  /*     0 */           0x8000u,  /* [/ActiveEcuC/Com/ComConfig/Signal_Rx16bit_Cyclic_omsg_RxCycle500_20_oCAN_300c1427_RxInitValue] */
  /*     1 */           0x0019u,  /* [/ActiveEcuC/Com/ComConfig/Signal_Rx15bit_OnWrite_omsg_RxEvent_20_oCAN_3a85fa13_RxInitValue] */
  /*     2 */           0x0032u,  /* [/ActiveEcuC/Com/ComConfig/Signal_Rx11bit_OnWrite_omsg_RxEvent_20_oCAN_e14fb9df_RxInitValue] */
  /*     3 */           0x0000u   /* [/ActiveEcuC/Com/ComConfig/Sig_LinTr_Slave3_1_oFrame_LinTr_Slave3_oLIN00_0174e108_RxInitValue, /ActiveEcuC/Com/ComConfig/StartAppl_Fr_BothECU_RX_Ctrl_oPDU_Fr_StartAppl_BothECU_RX_cb3aeb40_RxInitValue, /ActiveEcuC/Com/ComConfig/StartAppl_Fr_BothECU_RX_Data_oPDU_Fr_StartAppl_BothECU_RX_052b6b3f_RxInitValue] */
};
#define COM_STOP_SEC_CONST_16BIT
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */

/**********************************************************************************************************************
  Com_ConstValueUInt32
**********************************************************************************************************************/
/** 
  \var    Com_ConstValueUInt32
  \brief  Optimized array of commonly used values like initial or invalid values. (UINT32)
*/ 
#define COM_START_SEC_CONST_32BIT
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */
CONST(Com_ConstValueUInt32Type, COM_CONST) Com_ConstValueUInt32[3] = {  /* PRQA S 1514, 1533 */  /* MD_CSL_ObjectOnlyAccessedOnce */
  /* Index     ConstValueUInt32      Referable Keys */
  /*     0 */       0x00000002u,  /* [/ActiveEcuC/Com/ComConfig/signal_RxStat_24bit_opdu_RxStat_10_9524d9a2_RxInitValue] */
  /*     1 */       0x00000001u,  /* [/ActiveEcuC/Com/ComConfig/Signal_Rx20bit_Cyclic_omsg_RxCycle500_20_oCAN_983b15aa_RxInitValue, /ActiveEcuC/Com/ComConfig/signal_RxDyn_20_opdu_RxDyn_64_d74fee73_RxInitValue] */
  /*     2 */       0x00000000u   /* [/ActiveEcuC/Com/ComConfig/SG_RgRx_Prof1C_RxCycle_omsg_RxCycle_E2eProf1C_500_10_oCAN_430c5af9_Rx/SigRx_Prof1C_Sig32Bit_omsg_RxCycle_E2eProf1C_500_10_oCAN_f0dd3f4d_RxInitValue] */
};
#define COM_STOP_SEC_CONST_32BIT
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */

/**********************************************************************************************************************
  Com_ConstValueUInt8
**********************************************************************************************************************/
/** 
  \var    Com_ConstValueUInt8
  \brief  Optimized array of commonly used values like initial or invalid values. (BOOLEAN, UINT8)
*/ 
#define COM_START_SEC_CONST_8BIT
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */
CONST(Com_ConstValueUInt8Type, COM_CONST) Com_ConstValueUInt8[2] = {  /* PRQA S 1514, 1533 */  /* MD_CSL_ObjectOnlyAccessedOnce */
  /* Index     ConstValueUInt8      Referable Keys */
  /*     0 */            0x0Fu,  /* [/ActiveEcuC/Com/ComConfig/Signal_Rx8bit_Cyclic_omsg_RxCycle500_20_oCAN_f9e4bcc4_RxInitValue, /ActiveEcuC/Com/ComConfig/signal_RxDyn_4bit_opdu_RxDyn_64_18777321_RxInitValue] */
  /*     1 */            0x00u   /* [/ActiveEcuC/Com/ComConfig/SG_RgRx_Prof1C_RxCycle_omsg_RxCycle_E2eProf1C_500_10_oCAN_430c5af9_Rx/SigRx_Prof1C_CRC_omsg_RxCycle_E2eProf1C_500_10_oCAN_d6a2f34d_RxInitValue, /ActiveEcuC/Com/ComConfig/SG_RgRx_Prof1C_RxCycle_omsg_RxCycle_E2eProf1C_500_10_oCAN_430c5af9_Rx/SigRx_Prof1C_DataId_HiByte_LoNib_omsg_RxCycle_E2eProf1C_500_10_oCAN_bdeb2c98_RxInitValue, /ActiveEcuC/Com/ComConfig/SG_RgRx_Prof1C_RxCycle_omsg_RxCycle_E2eProf1C_500_10_oCAN_430c5af9_Rx/SigRx_Prof1C_SQ_omsg_RxCycle_E2eProf1C_500_10_oCAN_263a4149_RxInitValue, /ActiveEcuC/Com/ComConfig/SG_RgRx_Prof1C_RxCycle_omsg_RxCycle_E2eProf1C_500_10_oCAN_430c5af9_Rx/SigRx_Prof1C_Sig3Bit_omsg_RxCycle_E2eProf1C_500_10_oCAN_95ddcbc9_RxInitValue, /ActiveEcuC/Com/ComConfig/SG_RgRx_Prof1C_RxCycle_omsg_RxCycle_E2eProf1C_500_10_oCAN_430c5af9_Rx/SigRx_Prof1C_Sig4Bit_omsg_RxCycle_E2eProf1C_500_10_oCAN_a713ffd7_RxInitValue, /ActiveEcuC/Com/ComConfig/Sig_ErrBit_Slave3_oFrame_LinTr_Slave3_oLIN00_7385f3a6_RxInitValue, /ActiveEcuC/Com/ComConfig/Sig_LinTr_Slave3_0_oFrame_LinTr_Slave3_oLIN00_00c11c15_RxInitValue, /ActiveEcuC/Com/ComConfig/Sig_StartAppl_LinData_Rx_oFrame_LinTr_RearECU_oLIN00_182ca40c_RxInitValue, /ActiveEcuC/Com/ComConfig/Signal_Dummy_oPDU_Dummy_RearECU_e3ccd573_RxInitValue, /ActiveEcuC/Com/ComConfig/Signal_RearInteriorLight_omsg_RxCycle100_0_oCAN_44115c66_RxInitValue, /ActiveEcuC/Com/ComConfig/Signal_RearLeftDoor_omsg_RxCycle100_0_oCAN_47d3a185_RxInitValue, /ActiveEcuC/Com/ComConfig/Signal_RearRightDoor_omsg_RxCycle100_0_oCAN_cbde8ce7_RxInitValue, /ActiveEcuC/Com/ComConfig/Signal_StartAppl_RxCtrl_MyECU_omsg_StartAppl_Rx_MyECU_oCAN_09591065_RxInitValue, /ActiveEcuC/Com/ComConfig/Signal_StartAppl_RxData_MyECU_omsg_StartAppl_Rx_MyECU_oCAN_315f9453_RxInitValue, /ActiveEcuC/Com/ComConfig/signal_RxStat_8bit_opdu_RxStat_10_469ce3e0_RxInitValue] */
};
#define COM_STOP_SEC_CONST_8BIT
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */

/**********************************************************************************************************************
  Com_PduGrpVector
**********************************************************************************************************************/
/** 
  \var    Com_PduGrpVector
  \brief  Contains an I-PDU-Group vector for each I-PDU, mapping the I-PDU to the corresponding I-PDU-Groups.
*/ 
#define COM_START_SEC_CONST_8BIT
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */
CONST(Com_PduGrpVectorType, COM_CONST) Com_PduGrpVector[6] = {  /* PRQA S 1514, 1533 */  /* MD_CSL_ObjectOnlyAccessedOnce */
  /* Index     PduGrpVector      Referable Keys */
  /*     0 */         0x02u,  /* [/ActiveEcuC/Com/ComConfig/msg_StartAppl_Tx_MyECU_oCAN_ad461e3f_Tx, /ActiveEcuC/Com/ComConfig/msg_TxCycle1000_10_oCAN_d74aed68_Tx, /ActiveEcuC/Com/ComConfig/msg_TxCycle10_0_oCAN_85bf3e37_Tx, /ActiveEcuC/Com/ComConfig/msg_TxCycle_E2eProf1C_500_30_oCAN_eeefc687_Tx, /ActiveEcuC/Com/ComConfig/msg_TxEvent_10_oCAN_b2cd4fc2_Tx] */
  /*     1 */         0x01u,  /* [/ActiveEcuC/Com/ComConfig/msg_RxCycle100_0_oCAN_1e247d16_Rx, /ActiveEcuC/Com/ComConfig/msg_RxCycle500_20_oCAN_a691adb3_Rx, /ActiveEcuC/Com/ComConfig/msg_RxCycle_E2eProf1C_500_10_oCAN_e8110737_Rx, /ActiveEcuC/Com/ComConfig/msg_RxEvent_20_oCAN_a1df81ad_Rx, /ActiveEcuC/Com/ComConfig/msg_StartAppl_Rx_MyECU_oCAN_0da3c1e2_Rx] */
  /*     2 */         0x08u,  /* [/ActiveEcuC/Com/ComConfig/PDU_Fr_StartAppl_MyECU_TX_44b0b044_Tx, /ActiveEcuC/Com/ComConfig/PDU_Transmit_MyECU_05398c7a_Tx, /ActiveEcuC/Com/ComConfig/pdu_TxDyn_16_3c646bcf_Tx, /ActiveEcuC/Com/ComConfig/pdu_TxDyn_64_9d2b9c24_Tx, /ActiveEcuC/Com/ComConfig/pdu_TxStat_40_64c7eeb3_Tx, /ActiveEcuC/Com/ComConfig/pdu_TxStat_64_519c4828_Tx] */
  /*     3 */         0x04u,  /* [/ActiveEcuC/Com/ComConfig/PDU_Dummy_RearECU_1e8d611e_Rx, /ActiveEcuC/Com/ComConfig/PDU_Fr_StartAppl_BothECU_RX_42a9ded9_Rx, /ActiveEcuC/Com/ComConfig/pdu_RxDyn_64_5b4495a3_Rx, /ActiveEcuC/Com/ComConfig/pdu_RxStat_10_6aaa637c_Rx, /ActiveEcuC/Com/ComConfig/pdu_RxStat_30_589c01fe_Rx] */
  /*     4 */         0x10u,  /* [/ActiveEcuC/Com/ComConfig/Frame_LinTr_RearECU_oLIN00_22758e6f_Rx, /ActiveEcuC/Com/ComConfig/Frame_LinTr_Slave3_oLIN00_bac14905_Rx] */
  /*     5 */         0x20u   /* [/ActiveEcuC/Com/ComConfig/Frame_LinTr_MyECU2_oLIN00_3a9f0ce6_Tx, /ActiveEcuC/Com/ComConfig/Frame_LinTr_MyECU_oLIN00_ce6095e4_Tx] */
};
#define COM_STOP_SEC_CONST_8BIT
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */

/**********************************************************************************************************************
  Com_RxAccessInfo
**********************************************************************************************************************/
/** 
  \var    Com_RxAccessInfo
  \brief  Contains all signal layout information necessary for signal access within an I-PDU.
  \details
  Element                                  Description
  InitValueUsed                            TRUE, if the 0:1 relation has minimum 1 relation pointing to Com_ConstValueUInt8,Com_ConstValueUInt16,Com_ConstValueUInt32,Com_ConstValueUInt64,Com_ConstValueSInt8,Com_ConstValueSInt16,Com_ConstValueSInt32,Com_ConstValueSInt64,Com_ConstValueFloat32,Com_ConstValueFloat64
  RxSigBufferArrayBasedBufferUsed          TRUE, if the 0:n relation has 1 relation pointing to Com_RxSigBufferArrayBased
  ShdBufferUsed                            TRUE, if the 0:1 relation has minimum 1 relation pointing to Com_RxSigBufferUInt8,Com_RxSigBufferUInt16,Com_RxSigBufferUInt32,Com_RxSigBufferUInt64,Com_RxSigBufferZeroBit,Com_RxSigBufferSInt8,Com_RxSigBufferSInt16,Com_RxSigBufferSInt32,Com_RxSigBufferSInt64,Com_RxSigBufferFloat32,Com_RxSigBufferFloat64
  ApplType                                 Application data type.
  BitLength                                Bit length of the signal or group signal.
  BitPosition                              Little endian bit position of the signal or group signal within the I-PDU.
  BufferIdx                                the index of the 0:1 relation pointing to Com_RxSigBufferUInt8,Com_RxSigBufferUInt16,Com_RxSigBufferUInt32,Com_RxSigBufferUInt64,Com_RxSigBufferZeroBit,Com_RxSigBufferSInt8,Com_RxSigBufferSInt16,Com_RxSigBufferSInt32,Com_RxSigBufferSInt64,Com_RxSigBufferFloat32,Com_RxSigBufferFloat64
  BusAcc                                   BUS access algorithm for signal or group signal packing / un-packing.
  ByteLength                               Byte length of the signal or group signal.
  ConstValueArrayBasedInitValueEndIdx      the end index of the 0:n relation pointing to Com_ConstValueArrayBased
  ConstValueArrayBasedInitValueStartIdx    the start index of the 0:n relation pointing to Com_ConstValueArrayBased
  InitValueIdx                             the index of the 0:1 relation pointing to Com_ConstValueUInt8,Com_ConstValueUInt16,Com_ConstValueUInt32,Com_ConstValueUInt64,Com_ConstValueSInt8,Com_ConstValueSInt16,Com_ConstValueSInt32,Com_ConstValueSInt64,Com_ConstValueFloat32,Com_ConstValueFloat64
  RxPduInfoIdx                             the index of the 1:1 relation pointing to Com_RxPduInfo
  RxSigBufferArrayBasedBufferEndIdx        the end index of the 0:n relation pointing to Com_RxSigBufferArrayBased
  RxSigBufferArrayBasedBufferStartIdx      the start index of the 0:n relation pointing to Com_RxSigBufferArrayBased
  ShdBufferIdx                             the index of the 0:1 relation pointing to Com_RxSigBufferUInt8,Com_RxSigBufferUInt16,Com_RxSigBufferUInt32,Com_RxSigBufferUInt64,Com_RxSigBufferZeroBit,Com_RxSigBufferSInt8,Com_RxSigBufferSInt16,Com_RxSigBufferSInt32,Com_RxSigBufferSInt64,Com_RxSigBufferFloat32,Com_RxSigBufferFloat64
  StartByteInPduPosition                   Start Byte position of the signal or group signal within the I-PDU.
  TmpBufferIdx                             the index of the 0:1 relation pointing to Com_TmpRxShdBufferUInt8,Com_TmpRxShdBufferUInt16,Com_TmpRxShdBufferUInt32,Com_TmpRxShdBufferUInt64,Com_TmpRxShdBufferSInt8,Com_TmpRxShdBufferSInt16,Com_TmpRxShdBufferSInt32,Com_TmpRxShdBufferSInt64,Com_TmpRxShdBufferFloat32,Com_TmpRxShdBufferFloat64
*/ 
#define COM_START_SEC_CONST_UNSPECIFIED
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */
CONST(Com_RxAccessInfoType, COM_CONST) Com_RxAccessInfo[29] = {  /* PRQA S 1514, 1533 */  /* MD_CSL_ObjectOnlyAccessedOnce */
    /* Index    InitValueUsed  RxSigBufferArrayBasedBufferUsed  ShdBufferUsed  ApplType                            BitLength  BitPosition  BufferIdx                       BusAcc                                ByteLength  ConstValueArrayBasedInitValueEndIdx                       ConstValueArrayBasedInitValueStartIdx                       InitValueIdx                       RxPduInfoIdx  RxSigBufferArrayBasedBufferEndIdx                       RxSigBufferArrayBasedBufferStartIdx                       ShdBufferIdx                       StartByteInPduPosition  TmpBufferIdx                             Referable Keys */
  { /*     0 */          TRUE,                           FALSE,          TRUE,   COM_UINT8_APPLTYPEOFRXACCESSINFO,        8u,          0u,                             0u,        COM_BYTE_BUSACCOFRXACCESSINFO,         1u, COM_NO_CONSTVALUEARRAYBASEDINITVALUEENDIDXOFRXACCESSINFO, COM_NO_CONSTVALUEARRAYBASEDINITVALUESTARTIDXOFRXACCESSINFO,                                1u,           6u, COM_NO_RXSIGBUFFERARRAYBASEDBUFFERENDIDXOFRXACCESSINFO, COM_NO_RXSIGBUFFERARRAYBASEDBUFFERSTARTIDXOFRXACCESSINFO,                                1u,                     0u,                                0u },  /* [/ActiveEcuC/Com/ComConfig/SG_RgRx_Prof1C_RxCycle_omsg_RxCycle_E2eProf1C_500_10_oCAN_430c5af9_Rx/SigRx_Prof1C_CRC_omsg_RxCycle_E2eProf1C_500_10_oCAN_d6a2f34d_Rx, /ActiveEcuC/Com/ComConfig/msg_RxCycle_E2eProf1C_500_10_oCAN_e8110737_Rx, /ActiveEcuC/Com/ComConfig/SG_RgRx_Prof1C_RxCycle_omsg_RxCycle_E2eProf1C_500_10_oCAN_430c5af9_Rx] */
  { /*     1 */          TRUE,                           FALSE,          TRUE,   COM_UINT8_APPLTYPEOFRXACCESSINFO,        4u,         12u,                             2u,        COM_NBIT_BUSACCOFRXACCESSINFO,         0u, COM_NO_CONSTVALUEARRAYBASEDINITVALUEENDIDXOFRXACCESSINFO, COM_NO_CONSTVALUEARRAYBASEDINITVALUESTARTIDXOFRXACCESSINFO,                                1u,           6u, COM_NO_RXSIGBUFFERARRAYBASEDBUFFERENDIDXOFRXACCESSINFO, COM_NO_RXSIGBUFFERARRAYBASEDBUFFERSTARTIDXOFRXACCESSINFO,                                3u,                     1u,                                1u },  /* [/ActiveEcuC/Com/ComConfig/SG_RgRx_Prof1C_RxCycle_omsg_RxCycle_E2eProf1C_500_10_oCAN_430c5af9_Rx/SigRx_Prof1C_DataId_HiByte_LoNib_omsg_RxCycle_E2eProf1C_500_10_oCAN_bdeb2c98_Rx, /ActiveEcuC/Com/ComConfig/msg_RxCycle_E2eProf1C_500_10_oCAN_e8110737_Rx, /ActiveEcuC/Com/ComConfig/SG_RgRx_Prof1C_RxCycle_omsg_RxCycle_E2eProf1C_500_10_oCAN_430c5af9_Rx] */
  { /*     2 */          TRUE,                           FALSE,          TRUE,   COM_UINT8_APPLTYPEOFRXACCESSINFO,        4u,          8u,                             4u,        COM_NBIT_BUSACCOFRXACCESSINFO,         0u, COM_NO_CONSTVALUEARRAYBASEDINITVALUEENDIDXOFRXACCESSINFO, COM_NO_CONSTVALUEARRAYBASEDINITVALUESTARTIDXOFRXACCESSINFO,                                1u,           6u, COM_NO_RXSIGBUFFERARRAYBASEDBUFFERENDIDXOFRXACCESSINFO, COM_NO_RXSIGBUFFERARRAYBASEDBUFFERSTARTIDXOFRXACCESSINFO,                                5u,                     1u,                                3u },  /* [/ActiveEcuC/Com/ComConfig/SG_RgRx_Prof1C_RxCycle_omsg_RxCycle_E2eProf1C_500_10_oCAN_430c5af9_Rx/SigRx_Prof1C_SQ_omsg_RxCycle_E2eProf1C_500_10_oCAN_263a4149_Rx, /ActiveEcuC/Com/ComConfig/msg_RxCycle_E2eProf1C_500_10_oCAN_e8110737_Rx, /ActiveEcuC/Com/ComConfig/SG_RgRx_Prof1C_RxCycle_omsg_RxCycle_E2eProf1C_500_10_oCAN_430c5af9_Rx] */
  { /*     3 */          TRUE,                           FALSE,          TRUE,   COM_UINT8_APPLTYPEOFRXACCESSINFO,        3u,         21u,                             6u,        COM_NBIT_BUSACCOFRXACCESSINFO,         0u, COM_NO_CONSTVALUEARRAYBASEDINITVALUEENDIDXOFRXACCESSINFO, COM_NO_CONSTVALUEARRAYBASEDINITVALUESTARTIDXOFRXACCESSINFO,                                1u,           6u, COM_NO_RXSIGBUFFERARRAYBASEDBUFFERENDIDXOFRXACCESSINFO, COM_NO_RXSIGBUFFERARRAYBASEDBUFFERSTARTIDXOFRXACCESSINFO,                                7u,                     2u,                                2u },  /* [/ActiveEcuC/Com/ComConfig/SG_RgRx_Prof1C_RxCycle_omsg_RxCycle_E2eProf1C_500_10_oCAN_430c5af9_Rx/SigRx_Prof1C_Sig3Bit_omsg_RxCycle_E2eProf1C_500_10_oCAN_95ddcbc9_Rx, /ActiveEcuC/Com/ComConfig/msg_RxCycle_E2eProf1C_500_10_oCAN_e8110737_Rx, /ActiveEcuC/Com/ComConfig/SG_RgRx_Prof1C_RxCycle_omsg_RxCycle_E2eProf1C_500_10_oCAN_430c5af9_Rx] */
  { /*     4 */          TRUE,                           FALSE,          TRUE,   COM_UINT8_APPLTYPEOFRXACCESSINFO,        4u,         16u,                             8u,        COM_NBIT_BUSACCOFRXACCESSINFO,         0u, COM_NO_CONSTVALUEARRAYBASEDINITVALUEENDIDXOFRXACCESSINFO, COM_NO_CONSTVALUEARRAYBASEDINITVALUESTARTIDXOFRXACCESSINFO,                                1u,           6u, COM_NO_RXSIGBUFFERARRAYBASEDBUFFERENDIDXOFRXACCESSINFO, COM_NO_RXSIGBUFFERARRAYBASEDBUFFERSTARTIDXOFRXACCESSINFO,                                9u,                     2u,                                4u },  /* [/ActiveEcuC/Com/ComConfig/SG_RgRx_Prof1C_RxCycle_omsg_RxCycle_E2eProf1C_500_10_oCAN_430c5af9_Rx/SigRx_Prof1C_Sig4Bit_omsg_RxCycle_E2eProf1C_500_10_oCAN_a713ffd7_Rx, /ActiveEcuC/Com/ComConfig/msg_RxCycle_E2eProf1C_500_10_oCAN_e8110737_Rx, /ActiveEcuC/Com/ComConfig/SG_RgRx_Prof1C_RxCycle_omsg_RxCycle_E2eProf1C_500_10_oCAN_430c5af9_Rx] */
  { /*     5 */          TRUE,                           FALSE,          TRUE,  COM_UINT32_APPLTYPEOFRXACCESSINFO,       32u,         24u,                             0u,       COM_NBYTE_BUSACCOFRXACCESSINFO,         4u, COM_NO_CONSTVALUEARRAYBASEDINITVALUEENDIDXOFRXACCESSINFO, COM_NO_CONSTVALUEARRAYBASEDINITVALUESTARTIDXOFRXACCESSINFO,                                2u,           6u, COM_NO_RXSIGBUFFERARRAYBASEDBUFFERENDIDXOFRXACCESSINFO, COM_NO_RXSIGBUFFERARRAYBASEDBUFFERSTARTIDXOFRXACCESSINFO,                                1u,                     3u,                                0u },  /* [/ActiveEcuC/Com/ComConfig/SG_RgRx_Prof1C_RxCycle_omsg_RxCycle_E2eProf1C_500_10_oCAN_430c5af9_Rx/SigRx_Prof1C_Sig32Bit_omsg_RxCycle_E2eProf1C_500_10_oCAN_f0dd3f4d_Rx, /ActiveEcuC/Com/ComConfig/msg_RxCycle_E2eProf1C_500_10_oCAN_e8110737_Rx, /ActiveEcuC/Com/ComConfig/SG_RgRx_Prof1C_RxCycle_omsg_RxCycle_E2eProf1C_500_10_oCAN_430c5af9_Rx] */
  { /*     6 */          TRUE,                           FALSE,         FALSE,   COM_UINT8_APPLTYPEOFRXACCESSINFO,        1u,         16u,                            10u,        COM_NBIT_BUSACCOFRXACCESSINFO,         0u, COM_NO_CONSTVALUEARRAYBASEDINITVALUEENDIDXOFRXACCESSINFO, COM_NO_CONSTVALUEARRAYBASEDINITVALUESTARTIDXOFRXACCESSINFO,                                1u,           1u, COM_NO_RXSIGBUFFERARRAYBASEDBUFFERENDIDXOFRXACCESSINFO, COM_NO_RXSIGBUFFERARRAYBASEDBUFFERSTARTIDXOFRXACCESSINFO, COM_NO_SHDBUFFERIDXOFRXACCESSINFO,                     2u, COM_NO_TMPBUFFERIDXOFRXACCESSINFO },  /* [/ActiveEcuC/Com/ComConfig/Sig_ErrBit_Slave3_oFrame_LinTr_Slave3_oLIN00_7385f3a6_Rx, /ActiveEcuC/Com/ComConfig/Frame_LinTr_Slave3_oLIN00_bac14905_Rx] */
  { /*     7 */          TRUE,                           FALSE,         FALSE,   COM_UINT8_APPLTYPEOFRXACCESSINFO,        4u,          0u,                            11u,        COM_NBIT_BUSACCOFRXACCESSINFO,         0u, COM_NO_CONSTVALUEARRAYBASEDINITVALUEENDIDXOFRXACCESSINFO, COM_NO_CONSTVALUEARRAYBASEDINITVALUESTARTIDXOFRXACCESSINFO,                                1u,           1u, COM_NO_RXSIGBUFFERARRAYBASEDBUFFERENDIDXOFRXACCESSINFO, COM_NO_RXSIGBUFFERARRAYBASEDBUFFERSTARTIDXOFRXACCESSINFO, COM_NO_SHDBUFFERIDXOFRXACCESSINFO,                     0u, COM_NO_TMPBUFFERIDXOFRXACCESSINFO },  /* [/ActiveEcuC/Com/ComConfig/Sig_LinTr_Slave3_0_oFrame_LinTr_Slave3_oLIN00_00c11c15_Rx, /ActiveEcuC/Com/ComConfig/Frame_LinTr_Slave3_oLIN00_bac14905_Rx] */
  { /*     8 */          TRUE,                           FALSE,         FALSE,  COM_UINT16_APPLTYPEOFRXACCESSINFO,       12u,          4u,                             0u,   COM_NBITNBYTE_BUSACCOFRXACCESSINFO,         1u, COM_NO_CONSTVALUEARRAYBASEDINITVALUEENDIDXOFRXACCESSINFO, COM_NO_CONSTVALUEARRAYBASEDINITVALUESTARTIDXOFRXACCESSINFO,                                3u,           1u, COM_NO_RXSIGBUFFERARRAYBASEDBUFFERENDIDXOFRXACCESSINFO, COM_NO_RXSIGBUFFERARRAYBASEDBUFFERSTARTIDXOFRXACCESSINFO, COM_NO_SHDBUFFERIDXOFRXACCESSINFO,                     0u, COM_NO_TMPBUFFERIDXOFRXACCESSINFO },  /* [/ActiveEcuC/Com/ComConfig/Sig_LinTr_Slave3_1_oFrame_LinTr_Slave3_oLIN00_0174e108_Rx, /ActiveEcuC/Com/ComConfig/Frame_LinTr_Slave3_oLIN00_bac14905_Rx] */
  { /*     9 */          TRUE,                           FALSE,         FALSE,   COM_UINT8_APPLTYPEOFRXACCESSINFO,        8u,          0u,                            12u,        COM_BYTE_BUSACCOFRXACCESSINFO,         1u, COM_NO_CONSTVALUEARRAYBASEDINITVALUEENDIDXOFRXACCESSINFO, COM_NO_CONSTVALUEARRAYBASEDINITVALUESTARTIDXOFRXACCESSINFO,                                1u,           0u, COM_NO_RXSIGBUFFERARRAYBASEDBUFFERENDIDXOFRXACCESSINFO, COM_NO_RXSIGBUFFERARRAYBASEDBUFFERSTARTIDXOFRXACCESSINFO, COM_NO_SHDBUFFERIDXOFRXACCESSINFO,                     0u, COM_NO_TMPBUFFERIDXOFRXACCESSINFO },  /* [/ActiveEcuC/Com/ComConfig/Sig_StartAppl_LinData_Rx_oFrame_LinTr_RearECU_oLIN00_182ca40c_Rx, /ActiveEcuC/Com/ComConfig/Frame_LinTr_RearECU_oLIN00_22758e6f_Rx] */
  { /*    10 */          TRUE,                           FALSE,         FALSE,   COM_UINT8_APPLTYPEOFRXACCESSINFO,        8u,          0u,                            13u,        COM_BYTE_BUSACCOFRXACCESSINFO,         1u, COM_NO_CONSTVALUEARRAYBASEDINITVALUEENDIDXOFRXACCESSINFO, COM_NO_CONSTVALUEARRAYBASEDINITVALUESTARTIDXOFRXACCESSINFO,                                1u,           2u, COM_NO_RXSIGBUFFERARRAYBASEDBUFFERENDIDXOFRXACCESSINFO, COM_NO_RXSIGBUFFERARRAYBASEDBUFFERSTARTIDXOFRXACCESSINFO, COM_NO_SHDBUFFERIDXOFRXACCESSINFO,                     0u, COM_NO_TMPBUFFERIDXOFRXACCESSINFO },  /* [/ActiveEcuC/Com/ComConfig/Signal_Dummy_oPDU_Dummy_RearECU_e3ccd573_Rx, /ActiveEcuC/Com/ComConfig/PDU_Dummy_RearECU_1e8d611e_Rx] */
  { /*    11 */          TRUE,                           FALSE,         FALSE,   COM_UINT8_APPLTYPEOFRXACCESSINFO,        1u,          0u,                            14u,        COM_NBIT_BUSACCOFRXACCESSINFO,         0u, COM_NO_CONSTVALUEARRAYBASEDINITVALUEENDIDXOFRXACCESSINFO, COM_NO_CONSTVALUEARRAYBASEDINITVALUESTARTIDXOFRXACCESSINFO,                                1u,           4u, COM_NO_RXSIGBUFFERARRAYBASEDBUFFERENDIDXOFRXACCESSINFO, COM_NO_RXSIGBUFFERARRAYBASEDBUFFERSTARTIDXOFRXACCESSINFO, COM_NO_SHDBUFFERIDXOFRXACCESSINFO,                     0u, COM_NO_TMPBUFFERIDXOFRXACCESSINFO },  /* [/ActiveEcuC/Com/ComConfig/Signal_RearInteriorLight_omsg_RxCycle100_0_oCAN_44115c66_Rx, /ActiveEcuC/Com/ComConfig/msg_RxCycle100_0_oCAN_1e247d16_Rx] */
  { /*    12 */          TRUE,                           FALSE,         FALSE,   COM_UINT8_APPLTYPEOFRXACCESSINFO,        1u,          1u,                            15u,        COM_NBIT_BUSACCOFRXACCESSINFO,         0u, COM_NO_CONSTVALUEARRAYBASEDINITVALUEENDIDXOFRXACCESSINFO, COM_NO_CONSTVALUEARRAYBASEDINITVALUESTARTIDXOFRXACCESSINFO,                                1u,           4u, COM_NO_RXSIGBUFFERARRAYBASEDBUFFERENDIDXOFRXACCESSINFO, COM_NO_RXSIGBUFFERARRAYBASEDBUFFERSTARTIDXOFRXACCESSINFO, COM_NO_SHDBUFFERIDXOFRXACCESSINFO,                     0u, COM_NO_TMPBUFFERIDXOFRXACCESSINFO },  /* [/ActiveEcuC/Com/ComConfig/Signal_RearLeftDoor_omsg_RxCycle100_0_oCAN_47d3a185_Rx, /ActiveEcuC/Com/ComConfig/msg_RxCycle100_0_oCAN_1e247d16_Rx] */
  { /*    13 */          TRUE,                           FALSE,         FALSE,   COM_UINT8_APPLTYPEOFRXACCESSINFO,        1u,          2u,                            16u,        COM_NBIT_BUSACCOFRXACCESSINFO,         0u, COM_NO_CONSTVALUEARRAYBASEDINITVALUEENDIDXOFRXACCESSINFO, COM_NO_CONSTVALUEARRAYBASEDINITVALUESTARTIDXOFRXACCESSINFO,                                1u,           4u, COM_NO_RXSIGBUFFERARRAYBASEDBUFFERENDIDXOFRXACCESSINFO, COM_NO_RXSIGBUFFERARRAYBASEDBUFFERSTARTIDXOFRXACCESSINFO, COM_NO_SHDBUFFERIDXOFRXACCESSINFO,                     0u, COM_NO_TMPBUFFERIDXOFRXACCESSINFO },  /* [/ActiveEcuC/Com/ComConfig/Signal_RearRightDoor_omsg_RxCycle100_0_oCAN_cbde8ce7_Rx, /ActiveEcuC/Com/ComConfig/msg_RxCycle100_0_oCAN_1e247d16_Rx] */
  { /*    14 */          TRUE,                           FALSE,         FALSE,   COM_UINT8_APPLTYPEOFRXACCESSINFO,        8u,          0u,                            17u,        COM_BYTE_BUSACCOFRXACCESSINFO,         1u, COM_NO_CONSTVALUEARRAYBASEDINITVALUEENDIDXOFRXACCESSINFO, COM_NO_CONSTVALUEARRAYBASEDINITVALUESTARTIDXOFRXACCESSINFO,                                0u,           5u, COM_NO_RXSIGBUFFERARRAYBASEDBUFFERENDIDXOFRXACCESSINFO, COM_NO_RXSIGBUFFERARRAYBASEDBUFFERSTARTIDXOFRXACCESSINFO, COM_NO_SHDBUFFERIDXOFRXACCESSINFO,                     0u, COM_NO_TMPBUFFERIDXOFRXACCESSINFO },  /* [/ActiveEcuC/Com/ComConfig/Signal_Rx8bit_Cyclic_omsg_RxCycle500_20_oCAN_f9e4bcc4_Rx, /ActiveEcuC/Com/ComConfig/msg_RxCycle500_20_oCAN_a691adb3_Rx] */
  { /*    15 */          TRUE,                           FALSE,         FALSE,  COM_UINT16_APPLTYPEOFRXACCESSINFO,       11u,          0u,                             1u,   COM_NBITNBYTE_BUSACCOFRXACCESSINFO,         1u, COM_NO_CONSTVALUEARRAYBASEDINITVALUEENDIDXOFRXACCESSINFO, COM_NO_CONSTVALUEARRAYBASEDINITVALUESTARTIDXOFRXACCESSINFO,                                2u,           7u, COM_NO_RXSIGBUFFERARRAYBASEDBUFFERENDIDXOFRXACCESSINFO, COM_NO_RXSIGBUFFERARRAYBASEDBUFFERSTARTIDXOFRXACCESSINFO, COM_NO_SHDBUFFERIDXOFRXACCESSINFO,                     0u, COM_NO_TMPBUFFERIDXOFRXACCESSINFO },  /* [/ActiveEcuC/Com/ComConfig/Signal_Rx11bit_OnWrite_omsg_RxEvent_20_oCAN_e14fb9df_Rx, /ActiveEcuC/Com/ComConfig/msg_RxEvent_20_oCAN_a1df81ad_Rx] */
  { /*    16 */          TRUE,                           FALSE,         FALSE,  COM_UINT16_APPLTYPEOFRXACCESSINFO,       15u,         12u,                             2u,   COM_NBITNBYTE_BUSACCOFRXACCESSINFO,         1u, COM_NO_CONSTVALUEARRAYBASEDINITVALUEENDIDXOFRXACCESSINFO, COM_NO_CONSTVALUEARRAYBASEDINITVALUESTARTIDXOFRXACCESSINFO,                                1u,           7u, COM_NO_RXSIGBUFFERARRAYBASEDBUFFERENDIDXOFRXACCESSINFO, COM_NO_RXSIGBUFFERARRAYBASEDBUFFERSTARTIDXOFRXACCESSINFO, COM_NO_SHDBUFFERIDXOFRXACCESSINFO,                     1u, COM_NO_TMPBUFFERIDXOFRXACCESSINFO },  /* [/ActiveEcuC/Com/ComConfig/Signal_Rx15bit_OnWrite_omsg_RxEvent_20_oCAN_3a85fa13_Rx, /ActiveEcuC/Com/ComConfig/msg_RxEvent_20_oCAN_a1df81ad_Rx] */
  { /*    17 */          TRUE,                           FALSE,         FALSE,  COM_UINT16_APPLTYPEOFRXACCESSINFO,       16u,         32u,                             3u,       COM_NBYTE_BUSACCOFRXACCESSINFO,         2u, COM_NO_CONSTVALUEARRAYBASEDINITVALUEENDIDXOFRXACCESSINFO, COM_NO_CONSTVALUEARRAYBASEDINITVALUESTARTIDXOFRXACCESSINFO,                                0u,           5u, COM_NO_RXSIGBUFFERARRAYBASEDBUFFERENDIDXOFRXACCESSINFO, COM_NO_RXSIGBUFFERARRAYBASEDBUFFERSTARTIDXOFRXACCESSINFO, COM_NO_SHDBUFFERIDXOFRXACCESSINFO,                     4u, COM_NO_TMPBUFFERIDXOFRXACCESSINFO },  /* [/ActiveEcuC/Com/ComConfig/Signal_Rx16bit_Cyclic_omsg_RxCycle500_20_oCAN_300c1427_Rx, /ActiveEcuC/Com/ComConfig/msg_RxCycle500_20_oCAN_a691adb3_Rx] */
  { /*    18 */          TRUE,                           FALSE,         FALSE,  COM_UINT32_APPLTYPEOFRXACCESSINFO,       20u,          8u,                             2u,   COM_NBITNBYTE_BUSACCOFRXACCESSINFO,         2u, COM_NO_CONSTVALUEARRAYBASEDINITVALUEENDIDXOFRXACCESSINFO, COM_NO_CONSTVALUEARRAYBASEDINITVALUESTARTIDXOFRXACCESSINFO,                                1u,           5u, COM_NO_RXSIGBUFFERARRAYBASEDBUFFERENDIDXOFRXACCESSINFO, COM_NO_RXSIGBUFFERARRAYBASEDBUFFERSTARTIDXOFRXACCESSINFO, COM_NO_SHDBUFFERIDXOFRXACCESSINFO,                     1u, COM_NO_TMPBUFFERIDXOFRXACCESSINFO },  /* [/ActiveEcuC/Com/ComConfig/Signal_Rx20bit_Cyclic_omsg_RxCycle500_20_oCAN_983b15aa_Rx, /ActiveEcuC/Com/ComConfig/msg_RxCycle500_20_oCAN_a691adb3_Rx] */
  { /*    19 */          TRUE,                           FALSE,         FALSE,   COM_UINT8_APPLTYPEOFRXACCESSINFO,        8u,          0u,                            18u,        COM_BYTE_BUSACCOFRXACCESSINFO,         1u, COM_NO_CONSTVALUEARRAYBASEDINITVALUEENDIDXOFRXACCESSINFO, COM_NO_CONSTVALUEARRAYBASEDINITVALUESTARTIDXOFRXACCESSINFO,                                1u,           8u, COM_NO_RXSIGBUFFERARRAYBASEDBUFFERENDIDXOFRXACCESSINFO, COM_NO_RXSIGBUFFERARRAYBASEDBUFFERSTARTIDXOFRXACCESSINFO, COM_NO_SHDBUFFERIDXOFRXACCESSINFO,                     0u, COM_NO_TMPBUFFERIDXOFRXACCESSINFO },  /* [/ActiveEcuC/Com/ComConfig/Signal_StartAppl_RxCtrl_MyECU_omsg_StartAppl_Rx_MyECU_oCAN_09591065_Rx, /ActiveEcuC/Com/ComConfig/msg_StartAppl_Rx_MyECU_oCAN_0da3c1e2_Rx] */
  { /*    20 */          TRUE,                           FALSE,         FALSE,   COM_UINT8_APPLTYPEOFRXACCESSINFO,        8u,          8u,                            19u,        COM_BYTE_BUSACCOFRXACCESSINFO,         1u, COM_NO_CONSTVALUEARRAYBASEDINITVALUEENDIDXOFRXACCESSINFO, COM_NO_CONSTVALUEARRAYBASEDINITVALUESTARTIDXOFRXACCESSINFO,                                1u,           8u, COM_NO_RXSIGBUFFERARRAYBASEDBUFFERENDIDXOFRXACCESSINFO, COM_NO_RXSIGBUFFERARRAYBASEDBUFFERSTARTIDXOFRXACCESSINFO, COM_NO_SHDBUFFERIDXOFRXACCESSINFO,                     1u, COM_NO_TMPBUFFERIDXOFRXACCESSINFO },  /* [/ActiveEcuC/Com/ComConfig/Signal_StartAppl_RxData_MyECU_omsg_StartAppl_Rx_MyECU_oCAN_315f9453_Rx, /ActiveEcuC/Com/ComConfig/msg_StartAppl_Rx_MyECU_oCAN_0da3c1e2_Rx] */
  { /*    21 */          TRUE,                           FALSE,         FALSE,  COM_UINT16_APPLTYPEOFRXACCESSINFO,       16u,          0u,                             4u,       COM_NBYTE_BUSACCOFRXACCESSINFO,         2u, COM_NO_CONSTVALUEARRAYBASEDINITVALUEENDIDXOFRXACCESSINFO, COM_NO_CONSTVALUEARRAYBASEDINITVALUESTARTIDXOFRXACCESSINFO,                                3u,           3u, COM_NO_RXSIGBUFFERARRAYBASEDBUFFERENDIDXOFRXACCESSINFO, COM_NO_RXSIGBUFFERARRAYBASEDBUFFERSTARTIDXOFRXACCESSINFO, COM_NO_SHDBUFFERIDXOFRXACCESSINFO,                     0u, COM_NO_TMPBUFFERIDXOFRXACCESSINFO },  /* [/ActiveEcuC/Com/ComConfig/StartAppl_Fr_BothECU_RX_Ctrl_oPDU_Fr_StartAppl_BothECU_RX_cb3aeb40_Rx, /ActiveEcuC/Com/ComConfig/PDU_Fr_StartAppl_BothECU_RX_42a9ded9_Rx] */
  { /*    22 */          TRUE,                           FALSE,         FALSE,  COM_UINT16_APPLTYPEOFRXACCESSINFO,       16u,         16u,                             5u,       COM_NBYTE_BUSACCOFRXACCESSINFO,         2u, COM_NO_CONSTVALUEARRAYBASEDINITVALUEENDIDXOFRXACCESSINFO, COM_NO_CONSTVALUEARRAYBASEDINITVALUESTARTIDXOFRXACCESSINFO,                                3u,           3u, COM_NO_RXSIGBUFFERARRAYBASEDBUFFERENDIDXOFRXACCESSINFO, COM_NO_RXSIGBUFFERARRAYBASEDBUFFERSTARTIDXOFRXACCESSINFO, COM_NO_SHDBUFFERIDXOFRXACCESSINFO,                     2u, COM_NO_TMPBUFFERIDXOFRXACCESSINFO },  /* [/ActiveEcuC/Com/ComConfig/StartAppl_Fr_BothECU_RX_Data_oPDU_Fr_StartAppl_BothECU_RX_052b6b3f_Rx, /ActiveEcuC/Com/ComConfig/PDU_Fr_StartAppl_BothECU_RX_42a9ded9_Rx] */
  { /*    23 */          TRUE,                           FALSE,         FALSE,   COM_UINT8_APPLTYPEOFRXACCESSINFO,        4u,          0u,                            20u,        COM_NBIT_BUSACCOFRXACCESSINFO,         0u, COM_NO_CONSTVALUEARRAYBASEDINITVALUEENDIDXOFRXACCESSINFO, COM_NO_CONSTVALUEARRAYBASEDINITVALUESTARTIDXOFRXACCESSINFO,                                0u,           9u, COM_NO_RXSIGBUFFERARRAYBASEDBUFFERENDIDXOFRXACCESSINFO, COM_NO_RXSIGBUFFERARRAYBASEDBUFFERSTARTIDXOFRXACCESSINFO, COM_NO_SHDBUFFERIDXOFRXACCESSINFO,                     0u, COM_NO_TMPBUFFERIDXOFRXACCESSINFO },  /* [/ActiveEcuC/Com/ComConfig/signal_RxDyn_4bit_opdu_RxDyn_64_18777321_Rx, /ActiveEcuC/Com/ComConfig/pdu_RxDyn_64_5b4495a3_Rx] */
  { /*    24 */          TRUE,                           FALSE,         FALSE,  COM_UINT32_APPLTYPEOFRXACCESSINFO,       20u,          4u,                             3u,   COM_NBITNBYTE_BUSACCOFRXACCESSINFO,         2u, COM_NO_CONSTVALUEARRAYBASEDINITVALUEENDIDXOFRXACCESSINFO, COM_NO_CONSTVALUEARRAYBASEDINITVALUESTARTIDXOFRXACCESSINFO,                                1u,           9u, COM_NO_RXSIGBUFFERARRAYBASEDBUFFERENDIDXOFRXACCESSINFO, COM_NO_RXSIGBUFFERARRAYBASEDBUFFERSTARTIDXOFRXACCESSINFO, COM_NO_SHDBUFFERIDXOFRXACCESSINFO,                     0u, COM_NO_TMPBUFFERIDXOFRXACCESSINFO },  /* [/ActiveEcuC/Com/ComConfig/signal_RxDyn_20_opdu_RxDyn_64_d74fee73_Rx, /ActiveEcuC/Com/ComConfig/pdu_RxDyn_64_5b4495a3_Rx] */
  { /*    25 */         FALSE,                            TRUE,         FALSE, COM_UINT8_N_APPLTYPEOFRXACCESSINFO,       40u,         24u, COM_NO_BUFFERIDXOFRXACCESSINFO, COM_ARRAY_BASED_BUSACCOFRXACCESSINFO,         5u,                                                       5u,                                                         0u, COM_NO_INITVALUEIDXOFRXACCESSINFO,           9u,                                                     5u,                                                       0u, COM_NO_SHDBUFFERIDXOFRXACCESSINFO,                     3u, COM_NO_TMPBUFFERIDXOFRXACCESSINFO },  /* [/ActiveEcuC/Com/ComConfig/signal_RxDyn_40_opdu_RxDyn_64_84de8797_Rx, /ActiveEcuC/Com/ComConfig/pdu_RxDyn_64_5b4495a3_Rx] */
  { /*    26 */          TRUE,                           FALSE,         FALSE,   COM_UINT8_APPLTYPEOFRXACCESSINFO,        8u,         24u,                            21u,        COM_BYTE_BUSACCOFRXACCESSINFO,         1u, COM_NO_CONSTVALUEARRAYBASEDINITVALUEENDIDXOFRXACCESSINFO, COM_NO_CONSTVALUEARRAYBASEDINITVALUESTARTIDXOFRXACCESSINFO,                                1u,          10u, COM_NO_RXSIGBUFFERARRAYBASEDBUFFERENDIDXOFRXACCESSINFO, COM_NO_RXSIGBUFFERARRAYBASEDBUFFERSTARTIDXOFRXACCESSINFO, COM_NO_SHDBUFFERIDXOFRXACCESSINFO,                     3u, COM_NO_TMPBUFFERIDXOFRXACCESSINFO },  /* [/ActiveEcuC/Com/ComConfig/signal_RxStat_8bit_opdu_RxStat_10_469ce3e0_Rx, /ActiveEcuC/Com/ComConfig/pdu_RxStat_10_6aaa637c_Rx] */
  { /*    27 */          TRUE,                           FALSE,         FALSE,  COM_UINT32_APPLTYPEOFRXACCESSINFO,       24u,          0u,                             4u,       COM_NBYTE_BUSACCOFRXACCESSINFO,         3u, COM_NO_CONSTVALUEARRAYBASEDINITVALUEENDIDXOFRXACCESSINFO, COM_NO_CONSTVALUEARRAYBASEDINITVALUESTARTIDXOFRXACCESSINFO,                                0u,          10u, COM_NO_RXSIGBUFFERARRAYBASEDBUFFERENDIDXOFRXACCESSINFO, COM_NO_RXSIGBUFFERARRAYBASEDBUFFERSTARTIDXOFRXACCESSINFO, COM_NO_SHDBUFFERIDXOFRXACCESSINFO,                     0u, COM_NO_TMPBUFFERIDXOFRXACCESSINFO },  /* [/ActiveEcuC/Com/ComConfig/signal_RxStat_24bit_opdu_RxStat_10_9524d9a2_Rx, /ActiveEcuC/Com/ComConfig/pdu_RxStat_10_6aaa637c_Rx] */
  { /*    28 */         FALSE,                            TRUE,         FALSE, COM_UINT8_N_APPLTYPEOFRXACCESSINFO,       56u,          0u, COM_NO_BUFFERIDXOFRXACCESSINFO, COM_ARRAY_BASED_BUSACCOFRXACCESSINFO,         7u,                                                       7u,                                                         0u, COM_NO_INITVALUEIDXOFRXACCESSINFO,          11u,                                                    12u,                                                       5u, COM_NO_SHDBUFFERIDXOFRXACCESSINFO,                     0u, COM_NO_TMPBUFFERIDXOFRXACCESSINFO }   /* [/ActiveEcuC/Com/ComConfig/signal_RxStat_56bit_opdu_RxStat_30_32e21b42_Rx, /ActiveEcuC/Com/ComConfig/pdu_RxStat_30_589c01fe_Rx] */
};
#define COM_STOP_SEC_CONST_UNSPECIFIED
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */

/**********************************************************************************************************************
  Com_RxAccessInfoInd
**********************************************************************************************************************/
/** 
  \var    Com_RxAccessInfoInd
  \brief  the indexes of the 1:1 sorted relation pointing to Com_RxAccessInfo
*/ 
#define COM_START_SEC_CONST_8BIT
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */
CONST(Com_RxAccessInfoIndType, COM_CONST) Com_RxAccessInfoInd[29] = {  /* PRQA S 1514, 1533 */  /* MD_CSL_ObjectOnlyAccessedOnce */
  /* Index     RxAccessInfoInd      Referable Keys */
  /*     0 */               9u,  /* [/ActiveEcuC/Com/ComConfig/Frame_LinTr_RearECU_oLIN00_22758e6f_Rx] */
  /*     1 */               6u,  /* [/ActiveEcuC/Com/ComConfig/Frame_LinTr_Slave3_oLIN00_bac14905_Rx] */
  /*     2 */               7u,  /* [/ActiveEcuC/Com/ComConfig/Frame_LinTr_Slave3_oLIN00_bac14905_Rx] */
  /*     3 */               8u,  /* [/ActiveEcuC/Com/ComConfig/Frame_LinTr_Slave3_oLIN00_bac14905_Rx] */
  /*     4 */              10u,  /* [/ActiveEcuC/Com/ComConfig/PDU_Dummy_RearECU_1e8d611e_Rx] */
  /*     5 */              21u,  /* [/ActiveEcuC/Com/ComConfig/PDU_Fr_StartAppl_BothECU_RX_42a9ded9_Rx] */
  /*     6 */              22u,  /* [/ActiveEcuC/Com/ComConfig/PDU_Fr_StartAppl_BothECU_RX_42a9ded9_Rx] */
  /*     7 */              11u,  /* [/ActiveEcuC/Com/ComConfig/msg_RxCycle100_0_oCAN_1e247d16_Rx] */
  /*     8 */              12u,  /* [/ActiveEcuC/Com/ComConfig/msg_RxCycle100_0_oCAN_1e247d16_Rx] */
  /*     9 */              13u,  /* [/ActiveEcuC/Com/ComConfig/msg_RxCycle100_0_oCAN_1e247d16_Rx] */
  /*    10 */              14u,  /* [/ActiveEcuC/Com/ComConfig/msg_RxCycle500_20_oCAN_a691adb3_Rx] */
  /*    11 */              17u,  /* [/ActiveEcuC/Com/ComConfig/msg_RxCycle500_20_oCAN_a691adb3_Rx] */
  /*    12 */              18u,  /* [/ActiveEcuC/Com/ComConfig/msg_RxCycle500_20_oCAN_a691adb3_Rx] */
  /*    13 */               0u,  /* [/ActiveEcuC/Com/ComConfig/msg_RxCycle_E2eProf1C_500_10_oCAN_e8110737_Rx] */
  /*    14 */               1u,  /* [/ActiveEcuC/Com/ComConfig/msg_RxCycle_E2eProf1C_500_10_oCAN_e8110737_Rx] */
  /*    15 */               2u,  /* [/ActiveEcuC/Com/ComConfig/msg_RxCycle_E2eProf1C_500_10_oCAN_e8110737_Rx] */
  /*    16 */               3u,  /* [/ActiveEcuC/Com/ComConfig/msg_RxCycle_E2eProf1C_500_10_oCAN_e8110737_Rx] */
  /*    17 */               4u,  /* [/ActiveEcuC/Com/ComConfig/msg_RxCycle_E2eProf1C_500_10_oCAN_e8110737_Rx] */
  /*    18 */               5u,  /* [/ActiveEcuC/Com/ComConfig/msg_RxCycle_E2eProf1C_500_10_oCAN_e8110737_Rx] */
  /*    19 */              15u,  /* [/ActiveEcuC/Com/ComConfig/msg_RxEvent_20_oCAN_a1df81ad_Rx] */
  /*    20 */              16u,  /* [/ActiveEcuC/Com/ComConfig/msg_RxEvent_20_oCAN_a1df81ad_Rx] */
  /*    21 */              19u,  /* [/ActiveEcuC/Com/ComConfig/msg_StartAppl_Rx_MyECU_oCAN_0da3c1e2_Rx] */
  /*    22 */              20u,  /* [/ActiveEcuC/Com/ComConfig/msg_StartAppl_Rx_MyECU_oCAN_0da3c1e2_Rx] */
  /*    23 */              23u,  /* [/ActiveEcuC/Com/ComConfig/pdu_RxDyn_64_5b4495a3_Rx] */
  /*    24 */              24u,  /* [/ActiveEcuC/Com/ComConfig/pdu_RxDyn_64_5b4495a3_Rx] */
  /*    25 */              25u,  /* [/ActiveEcuC/Com/ComConfig/pdu_RxDyn_64_5b4495a3_Rx] */
  /*    26 */              26u,  /* [/ActiveEcuC/Com/ComConfig/pdu_RxStat_10_6aaa637c_Rx] */
  /*    27 */              27u,  /* [/ActiveEcuC/Com/ComConfig/pdu_RxStat_10_6aaa637c_Rx] */
  /*    28 */              28u   /* [/ActiveEcuC/Com/ComConfig/pdu_RxStat_30_589c01fe_Rx] */
};
#define COM_STOP_SEC_CONST_8BIT
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */

/**********************************************************************************************************************
  Com_RxCbkFuncPtr
**********************************************************************************************************************/
/** 
  \var    Com_RxCbkFuncPtr
  \brief  Function pointer table containing configured notification and invalid notifications function pointer for signals and signal groups.
*/ 
#define COM_START_SEC_CONST_UNSPECIFIED
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */
CONST(ComRxCbkType, COM_CONST) Com_RxCbkFuncPtr[2] = {  /* PRQA S 1514, 1533 */  /* MD_CSL_ObjectOnlyAccessedOnce */
  /* Index     RxCbkFuncPtr                                                                          Referable Keys */
  /*     0 */ Rte_COMCbk_Signal_Rx20bit_Cyclic_omsg_RxCycle500_20_oCAN_983b15aa_Rx             ,  /* [/ActiveEcuC/Com/ComConfig/Signal_Rx20bit_Cyclic_omsg_RxCycle500_20_oCAN_983b15aa_RxAck] */
  /*     1 */ Rte_COMCbk_SG_RgRx_Prof1C_RxCycle_omsg_RxCycle_E2eProf1C_500_10_oCAN_430c5af9_Rx    /* [/ActiveEcuC/Com/ComConfig/SG_RgRx_Prof1C_RxCycle_omsg_RxCycle_E2eProf1C_500_10_oCAN_430c5af9_RxAck] */
};
#define COM_STOP_SEC_CONST_UNSPECIFIED
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */

/**********************************************************************************************************************
  Com_RxPduInfo
**********************************************************************************************************************/
/** 
  \var    Com_RxPduInfo
  \brief  Contains all relevant common information for Rx I-PDUs.
  \details
  Element                    Description
  PduGrpVectorUsed           TRUE, if the 0:n relation has 1 relation pointing to Com_PduGrpVector
  RxSigInfoUsed              TRUE, if the 0:n relation has 1 relation pointing to Com_RxSigInfo
  PduGrpVectorStartIdx       the start index of the 0:n relation pointing to Com_PduGrpVector
  RxAccessInfoIndEndIdx      the end index of the 0:n relation pointing to Com_RxAccessInfoInd
  RxAccessInfoIndStartIdx    the start index of the 0:n relation pointing to Com_RxAccessInfoInd
  RxDefPduBufferEndIdx       the end index of the 0:n relation pointing to Com_RxDefPduBuffer
  RxDefPduBufferStartIdx     the start index of the 0:n relation pointing to Com_RxDefPduBuffer
  RxSigGrpInfoIndEndIdx      the end index of the 0:n relation pointing to Com_RxSigGrpInfoInd
  RxSigGrpInfoIndStartIdx    the start index of the 0:n relation pointing to Com_RxSigGrpInfoInd
  RxSigInfoEndIdx            the end index of the 0:n relation pointing to Com_RxSigInfo
  RxSigInfoStartIdx          the start index of the 0:n relation pointing to Com_RxSigInfo
  Type                       Defines whether rx Pdu is a NORMAL or TP IPdu.
*/ 
#define COM_START_SEC_CONST_UNSPECIFIED
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */
CONST(Com_RxPduInfoType, COM_CONST) Com_RxPduInfo[12] = {  /* PRQA S 1514, 1533 */  /* MD_CSL_ObjectOnlyAccessedOnce */
    /* Index    PduGrpVectorUsed  RxSigInfoUsed  PduGrpVectorStartIdx  RxAccessInfoIndEndIdx  RxAccessInfoIndStartIdx  RxDefPduBufferEndIdx  RxDefPduBufferStartIdx  RxSigGrpInfoIndEndIdx                    RxSigGrpInfoIndStartIdx                    RxSigInfoEndIdx                    RxSigInfoStartIdx                    Type                              Referable Keys */
  { /*     0 */             TRUE,          TRUE,                   4u,                    1u,                      0u,                   1u,                     0u, COM_NO_RXSIGGRPINFOINDENDIDXOFRXPDUINFO, COM_NO_RXSIGGRPINFOINDSTARTIDXOFRXPDUINFO,                                1u,                                  0u, COM_NORMAL_TYPEOFRXPDUINFO },  /* [/ActiveEcuC/Com/ComConfig/Frame_LinTr_RearECU_oLIN00_22758e6f_Rx, /ActiveEcuC/Com/ComConfig/MyECU_oLIN00_Rx_a6702d3c] */
  { /*     1 */             TRUE,          TRUE,                   4u,                    4u,                      1u,                   4u,                     1u, COM_NO_RXSIGGRPINFOINDENDIDXOFRXPDUINFO, COM_NO_RXSIGGRPINFOINDSTARTIDXOFRXPDUINFO,                                4u,                                  1u, COM_NORMAL_TYPEOFRXPDUINFO },  /* [/ActiveEcuC/Com/ComConfig/Frame_LinTr_Slave3_oLIN00_bac14905_Rx, /ActiveEcuC/Com/ComConfig/MyECU_oLIN00_Rx_a6702d3c] */
  { /*     2 */             TRUE,          TRUE,                   3u,                    5u,                      4u,                  20u,                     4u, COM_NO_RXSIGGRPINFOINDENDIDXOFRXPDUINFO, COM_NO_RXSIGGRPINFOINDSTARTIDXOFRXPDUINFO,                                5u,                                  4u, COM_NORMAL_TYPEOFRXPDUINFO },  /* [/ActiveEcuC/Com/ComConfig/PDU_Dummy_RearECU_1e8d611e_Rx, /ActiveEcuC/Com/ComConfig/MyECU_oFlexRay_Rx_bc06950a] */
  { /*     3 */             TRUE,          TRUE,                   3u,                    7u,                      5u,                  36u,                    20u, COM_NO_RXSIGGRPINFOINDENDIDXOFRXPDUINFO, COM_NO_RXSIGGRPINFOINDSTARTIDXOFRXPDUINFO,                                7u,                                  5u, COM_NORMAL_TYPEOFRXPDUINFO },  /* [/ActiveEcuC/Com/ComConfig/PDU_Fr_StartAppl_BothECU_RX_42a9ded9_Rx, /ActiveEcuC/Com/ComConfig/MyECU_oFlexRay_Rx_bc06950a] */
  { /*     4 */             TRUE,          TRUE,                   1u,                   10u,                      7u,                  37u,                    36u, COM_NO_RXSIGGRPINFOINDENDIDXOFRXPDUINFO, COM_NO_RXSIGGRPINFOINDSTARTIDXOFRXPDUINFO,                               10u,                                  7u, COM_NORMAL_TYPEOFRXPDUINFO },  /* [/ActiveEcuC/Com/ComConfig/msg_RxCycle100_0_oCAN_1e247d16_Rx, /ActiveEcuC/Com/ComConfig/MyECU_oCAN_Rx_63a3e3ce] */
  { /*     5 */             TRUE,          TRUE,                   1u,                   13u,                     10u,                  43u,                    37u, COM_NO_RXSIGGRPINFOINDENDIDXOFRXPDUINFO, COM_NO_RXSIGGRPINFOINDSTARTIDXOFRXPDUINFO,                               13u,                                 10u, COM_NORMAL_TYPEOFRXPDUINFO },  /* [/ActiveEcuC/Com/ComConfig/msg_RxCycle500_20_oCAN_a691adb3_Rx, /ActiveEcuC/Com/ComConfig/MyECU_oCAN_Rx_63a3e3ce] */
  { /*     6 */             TRUE,         FALSE,                   1u,                   19u,                     13u,                  51u,                    43u,                                      1u,                                        0u, COM_NO_RXSIGINFOENDIDXOFRXPDUINFO, COM_NO_RXSIGINFOSTARTIDXOFRXPDUINFO, COM_NORMAL_TYPEOFRXPDUINFO },  /* [/ActiveEcuC/Com/ComConfig/msg_RxCycle_E2eProf1C_500_10_oCAN_e8110737_Rx, /ActiveEcuC/Com/ComConfig/MyECU_oCAN_Rx_63a3e3ce] */
  { /*     7 */             TRUE,          TRUE,                   1u,                   21u,                     19u,                  55u,                    51u, COM_NO_RXSIGGRPINFOINDENDIDXOFRXPDUINFO, COM_NO_RXSIGGRPINFOINDSTARTIDXOFRXPDUINFO,                               15u,                                 13u, COM_NORMAL_TYPEOFRXPDUINFO },  /* [/ActiveEcuC/Com/ComConfig/msg_RxEvent_20_oCAN_a1df81ad_Rx, /ActiveEcuC/Com/ComConfig/MyECU_oCAN_Rx_63a3e3ce] */
  { /*     8 */             TRUE,          TRUE,                   1u,                   23u,                     21u,                 119u,                    55u, COM_NO_RXSIGGRPINFOINDENDIDXOFRXPDUINFO, COM_NO_RXSIGGRPINFOINDSTARTIDXOFRXPDUINFO,                               17u,                                 15u, COM_NORMAL_TYPEOFRXPDUINFO },  /* [/ActiveEcuC/Com/ComConfig/msg_StartAppl_Rx_MyECU_oCAN_0da3c1e2_Rx, /ActiveEcuC/Com/ComConfig/MyECU_oCAN_Rx_63a3e3ce] */
  { /*     9 */             TRUE,          TRUE,                   3u,                   26u,                     23u,                 127u,                   119u, COM_NO_RXSIGGRPINFOINDENDIDXOFRXPDUINFO, COM_NO_RXSIGGRPINFOINDSTARTIDXOFRXPDUINFO,                               20u,                                 17u, COM_NORMAL_TYPEOFRXPDUINFO },  /* [/ActiveEcuC/Com/ComConfig/pdu_RxDyn_64_5b4495a3_Rx, /ActiveEcuC/Com/ComConfig/MyECU_oFlexRay_Rx_bc06950a] */
  { /*    10 */             TRUE,          TRUE,                   3u,                   28u,                     26u,                 131u,                   127u, COM_NO_RXSIGGRPINFOINDENDIDXOFRXPDUINFO, COM_NO_RXSIGGRPINFOINDSTARTIDXOFRXPDUINFO,                               22u,                                 20u, COM_NORMAL_TYPEOFRXPDUINFO },  /* [/ActiveEcuC/Com/ComConfig/pdu_RxStat_10_6aaa637c_Rx, /ActiveEcuC/Com/ComConfig/MyECU_oFlexRay_Rx_bc06950a] */
  { /*    11 */             TRUE,          TRUE,                   3u,                   29u,                     28u,                 138u,                   131u, COM_NO_RXSIGGRPINFOINDENDIDXOFRXPDUINFO, COM_NO_RXSIGGRPINFOINDSTARTIDXOFRXPDUINFO,                               23u,                                 22u, COM_NORMAL_TYPEOFRXPDUINFO }   /* [/ActiveEcuC/Com/ComConfig/pdu_RxStat_30_589c01fe_Rx, /ActiveEcuC/Com/ComConfig/MyECU_oFlexRay_Rx_bc06950a] */
};
#define COM_STOP_SEC_CONST_UNSPECIFIED
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */

/**********************************************************************************************************************
  Com_RxSigGrpInfo
**********************************************************************************************************************/
/** 
  \var    Com_RxSigGrpInfo
  \brief  Contains all relevant information for Rx signal groups.
  \details
  Element                Description
  RxCbkFuncPtrAckUsed    TRUE, if the 0:1 relation has minimum 1 relation pointing to Com_RxCbkFuncPtr
*/ 
#define COM_START_SEC_CONST_UNSPECIFIED
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */
CONST(Com_RxSigGrpInfoType, COM_CONST) Com_RxSigGrpInfo[1] = {  /* PRQA S 1514, 1533 */  /* MD_CSL_ObjectOnlyAccessedOnce */
    /* Index    RxCbkFuncPtrAckUsed        Referable Keys */
  { /*     0 */                TRUE }   /* [/ActiveEcuC/Com/ComConfig/msg_RxCycle_E2eProf1C_500_10_oCAN_e8110737_Rx, /ActiveEcuC/Com/ComConfig/SG_RgRx_Prof1C_RxCycle_omsg_RxCycle_E2eProf1C_500_10_oCAN_430c5af9_Rx] */
};
#define COM_STOP_SEC_CONST_UNSPECIFIED
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */

/**********************************************************************************************************************
  Com_RxSigInfo
**********************************************************************************************************************/
/** 
  \var    Com_RxSigInfo
  \brief  Contains all relevant information for Rx signals.
  \details
  Element               Description
  RxAccessInfoIdx       the index of the 1:1 relation pointing to Com_RxAccessInfo
  RxCbkFuncPtrAckIdx    the index of the 0:1 relation pointing to Com_RxCbkFuncPtr
  SignalProcessing  
  ValidDlc              Minimum length of PDU required to completely receive the signal or signal group.
*/ 
#define COM_START_SEC_CONST_UNSPECIFIED
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */
CONST(Com_RxSigInfoType, COM_CONST) Com_RxSigInfo[23] = {  /* PRQA S 1514, 1533 */  /* MD_CSL_ObjectOnlyAccessedOnce */
    /* Index    RxAccessInfoIdx  RxCbkFuncPtrAckIdx                    SignalProcessing                          ValidDlc        Referable Keys */
  { /*     0 */              9u, COM_NO_RXCBKFUNCPTRACKIDXOFRXSIGINFO, COM_DEFERRED_SIGNALPROCESSINGOFRXSIGINFO,       1u },  /* [/ActiveEcuC/Com/ComConfig/Frame_LinTr_RearECU_oLIN00_22758e6f_Rx] */
  { /*     1 */              6u, COM_NO_RXCBKFUNCPTRACKIDXOFRXSIGINFO, COM_DEFERRED_SIGNALPROCESSINGOFRXSIGINFO,       3u },  /* [/ActiveEcuC/Com/ComConfig/Frame_LinTr_Slave3_oLIN00_bac14905_Rx] */
  { /*     2 */              7u, COM_NO_RXCBKFUNCPTRACKIDXOFRXSIGINFO, COM_DEFERRED_SIGNALPROCESSINGOFRXSIGINFO,       1u },  /* [/ActiveEcuC/Com/ComConfig/Frame_LinTr_Slave3_oLIN00_bac14905_Rx] */
  { /*     3 */              8u, COM_NO_RXCBKFUNCPTRACKIDXOFRXSIGINFO, COM_DEFERRED_SIGNALPROCESSINGOFRXSIGINFO,       2u },  /* [/ActiveEcuC/Com/ComConfig/Frame_LinTr_Slave3_oLIN00_bac14905_Rx] */
  { /*     4 */             10u, COM_NO_RXCBKFUNCPTRACKIDXOFRXSIGINFO, COM_DEFERRED_SIGNALPROCESSINGOFRXSIGINFO,       1u },  /* [/ActiveEcuC/Com/ComConfig/PDU_Dummy_RearECU_1e8d611e_Rx] */
  { /*     5 */             21u, COM_NO_RXCBKFUNCPTRACKIDXOFRXSIGINFO, COM_DEFERRED_SIGNALPROCESSINGOFRXSIGINFO,       2u },  /* [/ActiveEcuC/Com/ComConfig/PDU_Fr_StartAppl_BothECU_RX_42a9ded9_Rx] */
  { /*     6 */             22u, COM_NO_RXCBKFUNCPTRACKIDXOFRXSIGINFO, COM_DEFERRED_SIGNALPROCESSINGOFRXSIGINFO,       4u },  /* [/ActiveEcuC/Com/ComConfig/PDU_Fr_StartAppl_BothECU_RX_42a9ded9_Rx] */
  { /*     7 */             11u, COM_NO_RXCBKFUNCPTRACKIDXOFRXSIGINFO, COM_DEFERRED_SIGNALPROCESSINGOFRXSIGINFO,       1u },  /* [/ActiveEcuC/Com/ComConfig/msg_RxCycle100_0_oCAN_1e247d16_Rx] */
  { /*     8 */             12u, COM_NO_RXCBKFUNCPTRACKIDXOFRXSIGINFO, COM_DEFERRED_SIGNALPROCESSINGOFRXSIGINFO,       1u },  /* [/ActiveEcuC/Com/ComConfig/msg_RxCycle100_0_oCAN_1e247d16_Rx] */
  { /*     9 */             13u, COM_NO_RXCBKFUNCPTRACKIDXOFRXSIGINFO, COM_DEFERRED_SIGNALPROCESSINGOFRXSIGINFO,       1u },  /* [/ActiveEcuC/Com/ComConfig/msg_RxCycle100_0_oCAN_1e247d16_Rx] */
  { /*    10 */             17u, COM_NO_RXCBKFUNCPTRACKIDXOFRXSIGINFO, COM_DEFERRED_SIGNALPROCESSINGOFRXSIGINFO,       6u },  /* [/ActiveEcuC/Com/ComConfig/msg_RxCycle500_20_oCAN_a691adb3_Rx] */
  { /*    11 */             18u,                                   0u, COM_DEFERRED_SIGNALPROCESSINGOFRXSIGINFO,       4u },  /* [/ActiveEcuC/Com/ComConfig/msg_RxCycle500_20_oCAN_a691adb3_Rx] */
  { /*    12 */             14u, COM_NO_RXCBKFUNCPTRACKIDXOFRXSIGINFO, COM_DEFERRED_SIGNALPROCESSINGOFRXSIGINFO,       1u },  /* [/ActiveEcuC/Com/ComConfig/msg_RxCycle500_20_oCAN_a691adb3_Rx] */
  { /*    13 */             15u, COM_NO_RXCBKFUNCPTRACKIDXOFRXSIGINFO, COM_DEFERRED_SIGNALPROCESSINGOFRXSIGINFO,       2u },  /* [/ActiveEcuC/Com/ComConfig/msg_RxEvent_20_oCAN_a1df81ad_Rx] */
  { /*    14 */             16u, COM_NO_RXCBKFUNCPTRACKIDXOFRXSIGINFO, COM_DEFERRED_SIGNALPROCESSINGOFRXSIGINFO,       4u },  /* [/ActiveEcuC/Com/ComConfig/msg_RxEvent_20_oCAN_a1df81ad_Rx] */
  { /*    15 */             19u, COM_NO_RXCBKFUNCPTRACKIDXOFRXSIGINFO, COM_DEFERRED_SIGNALPROCESSINGOFRXSIGINFO,       1u },  /* [/ActiveEcuC/Com/ComConfig/msg_StartAppl_Rx_MyECU_oCAN_0da3c1e2_Rx] */
  { /*    16 */             20u, COM_NO_RXCBKFUNCPTRACKIDXOFRXSIGINFO, COM_DEFERRED_SIGNALPROCESSINGOFRXSIGINFO,       2u },  /* [/ActiveEcuC/Com/ComConfig/msg_StartAppl_Rx_MyECU_oCAN_0da3c1e2_Rx] */
  { /*    17 */             24u, COM_NO_RXCBKFUNCPTRACKIDXOFRXSIGINFO, COM_DEFERRED_SIGNALPROCESSINGOFRXSIGINFO,       3u },  /* [/ActiveEcuC/Com/ComConfig/pdu_RxDyn_64_5b4495a3_Rx] */
  { /*    18 */             25u, COM_NO_RXCBKFUNCPTRACKIDXOFRXSIGINFO, COM_DEFERRED_SIGNALPROCESSINGOFRXSIGINFO,       8u },  /* [/ActiveEcuC/Com/ComConfig/pdu_RxDyn_64_5b4495a3_Rx] */
  { /*    19 */             23u, COM_NO_RXCBKFUNCPTRACKIDXOFRXSIGINFO, COM_DEFERRED_SIGNALPROCESSINGOFRXSIGINFO,       1u },  /* [/ActiveEcuC/Com/ComConfig/pdu_RxDyn_64_5b4495a3_Rx] */
  { /*    20 */             27u, COM_NO_RXCBKFUNCPTRACKIDXOFRXSIGINFO, COM_DEFERRED_SIGNALPROCESSINGOFRXSIGINFO,       3u },  /* [/ActiveEcuC/Com/ComConfig/pdu_RxStat_10_6aaa637c_Rx] */
  { /*    21 */             26u, COM_NO_RXCBKFUNCPTRACKIDXOFRXSIGINFO, COM_DEFERRED_SIGNALPROCESSINGOFRXSIGINFO,       4u },  /* [/ActiveEcuC/Com/ComConfig/pdu_RxStat_10_6aaa637c_Rx] */
  { /*    22 */             28u, COM_NO_RXCBKFUNCPTRACKIDXOFRXSIGINFO, COM_DEFERRED_SIGNALPROCESSINGOFRXSIGINFO,       7u }   /* [/ActiveEcuC/Com/ComConfig/pdu_RxStat_30_589c01fe_Rx] */
};
#define COM_STOP_SEC_CONST_UNSPECIFIED
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */

/**********************************************************************************************************************
  Com_TxCyclicPdu
**********************************************************************************************************************/
/** 
  \var    Com_TxCyclicPdu
  \details
  Element         Description
  TxPduInfoIdx    the index of the 1:1 relation pointing to Com_TxPduInfo
*/ 
#define COM_START_SEC_CONST_UNSPECIFIED
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */
CONST(Com_TxCyclicPduType, COM_CONST) Com_TxCyclicPdu[5] = {  /* PRQA S 1514, 1533 */  /* MD_CSL_ObjectOnlyAccessedOnce */
    /* Index    TxPduInfoIdx        Referable Keys */
  { /*     0 */           7u },  /* [/ActiveEcuC/Com/ComConfig/msg_TxCycle1000_10_oCAN_d74aed68_Tx] */
  { /*     1 */           5u },  /* [/ActiveEcuC/Com/ComConfig/msg_StartAppl_Tx_MyECU_oCAN_ad461e3f_Tx] */
  { /*     2 */           3u },  /* [/ActiveEcuC/Com/ComConfig/PDU_Transmit_MyECU_05398c7a_Tx] */
  { /*     3 */           6u },  /* [/ActiveEcuC/Com/ComConfig/msg_TxCycle10_0_oCAN_85bf3e37_Tx] */
  { /*     4 */           8u }   /* [/ActiveEcuC/Com/ComConfig/msg_TxCycle_E2eProf1C_500_30_oCAN_eeefc687_Tx] */
};
#define COM_STOP_SEC_CONST_UNSPECIFIED
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */

/**********************************************************************************************************************
  Com_TxModeFalse
**********************************************************************************************************************/
/** 
  \var    Com_TxModeFalse
  \brief  Contains all relevant information for transmission mode false.
  \details
  Element       Description
  Direct        TRUE if transmission mode contains a direct part.
  TimeOffset    Initial time offset factor for cyclic transmission.
  TimePeriod    Cycle time factor.
*/ 
#define COM_START_SEC_CONST_UNSPECIFIED
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */
CONST(Com_TxModeFalseType, COM_CONST) Com_TxModeFalse[6] = {  /* PRQA S 1514, 1533 */  /* MD_CSL_ObjectOnlyAccessedOnce */
    /* Index    Direct  TimeOffset  TimePeriod        Referable Keys */
  { /*     0 */   TRUE,         1u,         0u },  /* [/ActiveEcuC/Com/ComConfig/msg_TxEvent_10_oCAN_b2cd4fc2_Tx] */
  { /*     1 */  FALSE,         4u,        50u },  /* [/ActiveEcuC/Com/ComConfig/msg_TxCycle_E2eProf1C_500_30_oCAN_eeefc687_Tx] */
  { /*     2 */  FALSE,         1u,         1u },  /* [/ActiveEcuC/Com/ComConfig/msg_TxCycle10_0_oCAN_85bf3e37_Tx] */
  { /*     3 */  FALSE,         2u,       100u },  /* [/ActiveEcuC/Com/ComConfig/msg_TxCycle1000_10_oCAN_d74aed68_Tx] */
  { /*     4 */  FALSE,         1u,         5u },  /* [/ActiveEcuC/Com/ComConfig/msg_StartAppl_Tx_MyECU_oCAN_ad461e3f_Tx] */
  { /*     5 */  FALSE,         1u,         0u }   /* [/ActiveEcuC/Com/ComConfig/Frame_LinTr_MyECU2_oLIN00_3a9f0ce6_Tx, /ActiveEcuC/Com/ComConfig/Frame_LinTr_MyECU_oLIN00_ce6095e4_Tx, /ActiveEcuC/Com/ComConfig/PDU_Fr_StartAppl_MyECU_TX_44b0b044_Tx, /ActiveEcuC/Com/ComConfig/PDU_Transmit_MyECU_05398c7a_Tx, /ActiveEcuC/Com/ComConfig/PDU_nm_MyECU_Fr_ae963333_Tx, /ActiveEcuC/Com/ComConfig/msg_nm_MyECU_oCAN_c97b60cc_Tx, /ActiveEcuC/Com/ComConfig/pdu_TxDyn_16_3c646bcf_Tx, /ActiveEcuC/Com/ComConfig/pdu_TxDyn_64_9d2b9c24_Tx, /ActiveEcuC/Com/ComConfig/pdu_TxStat_40_64c7eeb3_Tx, /ActiveEcuC/Com/ComConfig/pdu_TxStat_64_519c4828_Tx] */
};
#define COM_STOP_SEC_CONST_UNSPECIFIED
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */

/**********************************************************************************************************************
  Com_TxModeInfo
**********************************************************************************************************************/
/** 
  \var    Com_TxModeInfo
  \brief  Contains all relevant information for transmission mode handling.
  \details
  Element           Description
  InitMode          Initial transmission mode selector of the Tx I-PDU.
  TxModeFalseIdx    the index of the 1:1 relation pointing to Com_TxModeFalse
  TxModeTrueIdx     the index of the 1:1 relation pointing to Com_TxModeTrue
*/ 
#define COM_START_SEC_CONST_UNSPECIFIED
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */
CONST(Com_TxModeInfoType, COM_CONST) Com_TxModeInfo[15] = {  /* PRQA S 1514, 1533 */  /* MD_CSL_ObjectOnlyAccessedOnce */
    /* Index    InitMode  TxModeFalseIdx  TxModeTrueIdx        Referable Keys */
  { /*     0 */     TRUE,             5u,            6u },  /* [/ActiveEcuC/Com/ComConfig/Frame_LinTr_MyECU2_oLIN00_3a9f0ce6_Tx] */
  { /*     1 */     TRUE,             5u,            6u },  /* [/ActiveEcuC/Com/ComConfig/Frame_LinTr_MyECU_oLIN00_ce6095e4_Tx] */
  { /*     2 */     TRUE,             5u,            6u },  /* [/ActiveEcuC/Com/ComConfig/PDU_Fr_StartAppl_MyECU_TX_44b0b044_Tx] */
  { /*     3 */     TRUE,             5u,            5u },  /* [/ActiveEcuC/Com/ComConfig/PDU_Transmit_MyECU_05398c7a_Tx] */
  { /*     4 */     TRUE,             5u,            6u },  /* [/ActiveEcuC/Com/ComConfig/PDU_nm_MyECU_Fr_ae963333_Tx] */
  { /*     5 */     TRUE,             4u,            4u },  /* [/ActiveEcuC/Com/ComConfig/msg_StartAppl_Tx_MyECU_oCAN_ad461e3f_Tx] */
  { /*     6 */     TRUE,             2u,            2u },  /* [/ActiveEcuC/Com/ComConfig/msg_TxCycle10_0_oCAN_85bf3e37_Tx] */
  { /*     7 */     TRUE,             3u,            3u },  /* [/ActiveEcuC/Com/ComConfig/msg_TxCycle1000_10_oCAN_d74aed68_Tx] */
  { /*     8 */     TRUE,             1u,            1u },  /* [/ActiveEcuC/Com/ComConfig/msg_TxCycle_E2eProf1C_500_30_oCAN_eeefc687_Tx] */
  { /*     9 */     TRUE,             0u,            0u },  /* [/ActiveEcuC/Com/ComConfig/msg_TxEvent_10_oCAN_b2cd4fc2_Tx] */
  { /*    10 */     TRUE,             5u,            6u },  /* [/ActiveEcuC/Com/ComConfig/msg_nm_MyECU_oCAN_c97b60cc_Tx] */
  { /*    11 */     TRUE,             5u,            6u },  /* [/ActiveEcuC/Com/ComConfig/pdu_TxDyn_16_3c646bcf_Tx] */
  { /*    12 */     TRUE,             5u,            6u },  /* [/ActiveEcuC/Com/ComConfig/pdu_TxDyn_64_9d2b9c24_Tx] */
  { /*    13 */     TRUE,             5u,            6u },  /* [/ActiveEcuC/Com/ComConfig/pdu_TxStat_40_64c7eeb3_Tx] */
  { /*    14 */     TRUE,             5u,            6u }   /* [/ActiveEcuC/Com/ComConfig/pdu_TxStat_64_519c4828_Tx] */
};
#define COM_STOP_SEC_CONST_UNSPECIFIED
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */

/**********************************************************************************************************************
  Com_TxModeTrue
**********************************************************************************************************************/
/** 
  \var    Com_TxModeTrue
  \brief  Contains all relevant information for transmission mode true.
  \details
  Element       Description
  Direct        TRUE if transmission mode contains a direct part.
  TimeOffset    Initial time offset factor for cyclic transmission.
  TimePeriod    Cycle time factor.
*/ 
#define COM_START_SEC_CONST_UNSPECIFIED
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */
CONST(Com_TxModeTrueType, COM_CONST) Com_TxModeTrue[7] = {  /* PRQA S 1514, 1533 */  /* MD_CSL_ObjectOnlyAccessedOnce */
    /* Index    Direct  TimeOffset  TimePeriod        Referable Keys */
  { /*     0 */   TRUE,         1u,         0u },  /* [/ActiveEcuC/Com/ComConfig/msg_TxEvent_10_oCAN_b2cd4fc2_Tx] */
  { /*     1 */  FALSE,         4u,        50u },  /* [/ActiveEcuC/Com/ComConfig/msg_TxCycle_E2eProf1C_500_30_oCAN_eeefc687_Tx] */
  { /*     2 */  FALSE,         1u,         1u },  /* [/ActiveEcuC/Com/ComConfig/msg_TxCycle10_0_oCAN_85bf3e37_Tx] */
  { /*     3 */  FALSE,         2u,       100u },  /* [/ActiveEcuC/Com/ComConfig/msg_TxCycle1000_10_oCAN_d74aed68_Tx] */
  { /*     4 */  FALSE,         1u,         5u },  /* [/ActiveEcuC/Com/ComConfig/msg_StartAppl_Tx_MyECU_oCAN_ad461e3f_Tx] */
  { /*     5 */  FALSE,         1u,        10u },  /* [/ActiveEcuC/Com/ComConfig/PDU_Transmit_MyECU_05398c7a_Tx] */
  { /*     6 */  FALSE,         1u,         0u }   /* [/ActiveEcuC/Com/ComConfig/Frame_LinTr_MyECU2_oLIN00_3a9f0ce6_Tx, /ActiveEcuC/Com/ComConfig/Frame_LinTr_MyECU_oLIN00_ce6095e4_Tx, /ActiveEcuC/Com/ComConfig/PDU_Fr_StartAppl_MyECU_TX_44b0b044_Tx, /ActiveEcuC/Com/ComConfig/PDU_nm_MyECU_Fr_ae963333_Tx, /ActiveEcuC/Com/ComConfig/msg_nm_MyECU_oCAN_c97b60cc_Tx, /ActiveEcuC/Com/ComConfig/pdu_TxDyn_16_3c646bcf_Tx, /ActiveEcuC/Com/ComConfig/pdu_TxDyn_64_9d2b9c24_Tx, /ActiveEcuC/Com/ComConfig/pdu_TxStat_40_64c7eeb3_Tx, /ActiveEcuC/Com/ComConfig/pdu_TxStat_64_519c4828_Tx] */
};
#define COM_STOP_SEC_CONST_UNSPECIFIED
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */

/**********************************************************************************************************************
  Com_TxPduInfo
**********************************************************************************************************************/
/** 
  \var    Com_TxPduInfo
  \brief  Contains all relevant information for Tx I-PDUs.
  \details
  Element                    Description
  TxPduInitValueUsed         TRUE, if the 0:n relation has 1 relation pointing to Com_TxPduInitValue
  ExternalId                 External ID used to call PduR_ComTransmit().
  MetaDataLength             Length of MetaData.
  PduGrpVectorEndIdx         the end index of the 0:n relation pointing to Com_PduGrpVector
  PduGrpVectorStartIdx       the start index of the 0:n relation pointing to Com_PduGrpVector
  TxBufferEndIdx             the end index of the 0:n relation pointing to Com_TxBuffer
  TxBufferStartIdx           the start index of the 0:n relation pointing to Com_TxBuffer
  TxPduInitValueEndIdx       the end index of the 0:n relation pointing to Com_TxPduInitValue
  TxPduInitValueStartIdx     the start index of the 0:n relation pointing to Com_TxPduInitValue
  TxSigGrpInfoIndEndIdx      the end index of the 0:n relation pointing to Com_TxSigGrpInfoInd
  TxSigGrpInfoIndStartIdx    the start index of the 0:n relation pointing to Com_TxSigGrpInfoInd
*/ 
#define COM_START_SEC_CONST_UNSPECIFIED
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */
CONST(Com_TxPduInfoType, COM_CONST) Com_TxPduInfo[15] = {  /* PRQA S 1514, 1533 */  /* MD_CSL_ObjectOnlyAccessedOnce */
    /* Index    TxPduInitValueUsed  ExternalId                               MetaDataLength  PduGrpVectorEndIdx                    PduGrpVectorStartIdx                    TxBufferEndIdx  TxBufferStartIdx  TxPduInitValueEndIdx  TxPduInitValueStartIdx  TxSigGrpInfoIndEndIdx                    TxSigGrpInfoIndStartIdx                          Referable Keys */
  { /*     0 */               TRUE, PduRConf_PduRSrcPdu_PduRSrcPdu_12782a39,             0u,                                   6u,                                     5u,             3u,               0u,                   3u,                     0u, COM_NO_TXSIGGRPINFOINDENDIDXOFTXPDUINFO, COM_NO_TXSIGGRPINFOINDSTARTIDXOFTXPDUINFO },  /* [/ActiveEcuC/Com/ComConfig/Frame_LinTr_MyECU2_oLIN00_3a9f0ce6_Tx, /ActiveEcuC/Com/ComConfig/MyECU_oLIN00_Tx_f02a8aba] */
  { /*     1 */               TRUE, PduRConf_PduRSrcPdu_PduRSrcPdu_783591af,             0u,                                   6u,                                     5u,             4u,               3u,                   4u,                     3u, COM_NO_TXSIGGRPINFOINDENDIDXOFTXPDUINFO, COM_NO_TXSIGGRPINFOINDSTARTIDXOFTXPDUINFO },  /* [/ActiveEcuC/Com/ComConfig/Frame_LinTr_MyECU_oLIN00_ce6095e4_Tx, /ActiveEcuC/Com/ComConfig/MyECU_oLIN00_Tx_f02a8aba] */
  { /*     2 */               TRUE, PduRConf_PduRSrcPdu_PduRSrcPdu_d1717db2,             0u,                                   3u,                                     2u,            20u,               4u,                  20u,                     4u, COM_NO_TXSIGGRPINFOINDENDIDXOFTXPDUINFO, COM_NO_TXSIGGRPINFOINDSTARTIDXOFTXPDUINFO },  /* [/ActiveEcuC/Com/ComConfig/PDU_Fr_StartAppl_MyECU_TX_44b0b044_Tx, /ActiveEcuC/Com/ComConfig/MyECU_oFlexRay_Tx_ea5c328c] */
  { /*     3 */               TRUE, PduRConf_PduRSrcPdu_PduRSrcPdu_8c1e0786,             0u,                                   3u,                                     2u,            28u,              20u,                  28u,                    20u, COM_NO_TXSIGGRPINFOINDENDIDXOFTXPDUINFO, COM_NO_TXSIGGRPINFOINDSTARTIDXOFTXPDUINFO },  /* [/ActiveEcuC/Com/ComConfig/PDU_Transmit_MyECU_05398c7a_Tx, /ActiveEcuC/Com/ComConfig/MyECU_oFlexRay_Tx_ea5c328c] */
  { /*     4 */               TRUE, PduRConf_PduRSrcPdu_PduRSrcPdu_c844becc,             0u, COM_NO_PDUGRPVECTORENDIDXOFTXPDUINFO, COM_NO_PDUGRPVECTORSTARTIDXOFTXPDUINFO,            34u,              28u,                  34u,                    28u, COM_NO_TXSIGGRPINFOINDENDIDXOFTXPDUINFO, COM_NO_TXSIGGRPINFOINDSTARTIDXOFTXPDUINFO },  /* [/ActiveEcuC/Com/ComConfig/PDU_nm_MyECU_Fr_ae963333_Tx] */
  { /*     5 */               TRUE, PduRConf_PduRSrcPdu_PduRSrcPdu_cd5f4416,             0u,                                   1u,                                     0u,           100u,              36u,                  98u,                    34u, COM_NO_TXSIGGRPINFOINDENDIDXOFTXPDUINFO, COM_NO_TXSIGGRPINFOINDSTARTIDXOFTXPDUINFO },  /* [/ActiveEcuC/Com/ComConfig/msg_StartAppl_Tx_MyECU_oCAN_ad461e3f_Tx, /ActiveEcuC/Com/ComConfig/MyECU_oCAN_Tx_35f94448] */
  { /*     6 */               TRUE, PduRConf_PduRSrcPdu_PduRSrcPdu_2e41d513,             0u,                                   1u,                                     0u,           107u,             100u,                 105u,                    98u, COM_NO_TXSIGGRPINFOINDENDIDXOFTXPDUINFO, COM_NO_TXSIGGRPINFOINDSTARTIDXOFTXPDUINFO },  /* [/ActiveEcuC/Com/ComConfig/msg_TxCycle10_0_oCAN_85bf3e37_Tx, /ActiveEcuC/Com/ComConfig/MyECU_oCAN_Tx_35f94448] */
  { /*     7 */               TRUE, PduRConf_PduRSrcPdu_PduRSrcPdu_704563f1,             0u,                                   1u,                                     0u,           109u,             107u,                 107u,                   105u, COM_NO_TXSIGGRPINFOINDENDIDXOFTXPDUINFO, COM_NO_TXSIGGRPINFOINDSTARTIDXOFTXPDUINFO },  /* [/ActiveEcuC/Com/ComConfig/msg_TxCycle1000_10_oCAN_d74aed68_Tx, /ActiveEcuC/Com/ComConfig/MyECU_oCAN_Tx_35f94448] */
  { /*     8 */               TRUE, PduRConf_PduRSrcPdu_PduRSrcPdu_c3d2922e,             0u,                                   1u,                                     0u,           117u,             109u,                 115u,                   107u,                                      1u,                                        0u },  /* [/ActiveEcuC/Com/ComConfig/msg_TxCycle_E2eProf1C_500_30_oCAN_eeefc687_Tx, /ActiveEcuC/Com/ComConfig/MyECU_oCAN_Tx_35f94448] */
  { /*     9 */               TRUE, PduRConf_PduRSrcPdu_PduRSrcPdu_36788591,             0u,                                   1u,                                     0u,           124u,             117u,                 122u,                   115u, COM_NO_TXSIGGRPINFOINDENDIDXOFTXPDUINFO, COM_NO_TXSIGGRPINFOINDSTARTIDXOFTXPDUINFO },  /* [/ActiveEcuC/Com/ComConfig/msg_TxEvent_10_oCAN_b2cd4fc2_Tx, /ActiveEcuC/Com/ComConfig/MyECU_oCAN_Tx_35f94448] */
  { /*    10 */               TRUE, PduRConf_PduRSrcPdu_PduRSrcPdu_616fda32,             0u, COM_NO_PDUGRPVECTORENDIDXOFTXPDUINFO, COM_NO_PDUGRPVECTORSTARTIDXOFTXPDUINFO,           130u,             124u,                 128u,                   122u, COM_NO_TXSIGGRPINFOINDENDIDXOFTXPDUINFO, COM_NO_TXSIGGRPINFOINDSTARTIDXOFTXPDUINFO },  /* [/ActiveEcuC/Com/ComConfig/msg_nm_MyECU_oCAN_c97b60cc_Tx] */
  { /*    11 */               TRUE, PduRConf_PduRSrcPdu_PduRSrcPdu_3920f728,             0u,                                   3u,                                     2u,           132u,             130u,                 130u,                   128u, COM_NO_TXSIGGRPINFOINDENDIDXOFTXPDUINFO, COM_NO_TXSIGGRPINFOINDSTARTIDXOFTXPDUINFO },  /* [/ActiveEcuC/Com/ComConfig/pdu_TxDyn_16_3c646bcf_Tx, /ActiveEcuC/Com/ComConfig/MyECU_oFlexRay_Tx_ea5c328c] */
  { /*    12 */               TRUE, PduRConf_PduRSrcPdu_PduRSrcPdu_ab04820d,             0u,                                   3u,                                     2u,           140u,             132u,                 138u,                   130u, COM_NO_TXSIGGRPINFOINDENDIDXOFTXPDUINFO, COM_NO_TXSIGGRPINFOINDSTARTIDXOFTXPDUINFO },  /* [/ActiveEcuC/Com/ComConfig/pdu_TxDyn_64_9d2b9c24_Tx, /ActiveEcuC/Com/ComConfig/MyECU_oFlexRay_Tx_ea5c328c] */
  { /*    13 */               TRUE, PduRConf_PduRSrcPdu_PduRSrcPdu_a7760f35,             0u,                                   3u,                                     2u,           145u,             140u,                 143u,                   138u, COM_NO_TXSIGGRPINFOINDENDIDXOFTXPDUINFO, COM_NO_TXSIGGRPINFOINDSTARTIDXOFTXPDUINFO },  /* [/ActiveEcuC/Com/ComConfig/pdu_TxStat_40_64c7eeb3_Tx, /ActiveEcuC/Com/ComConfig/MyECU_oFlexRay_Tx_ea5c328c] */
  { /*    14 */               TRUE, PduRConf_PduRSrcPdu_PduRSrcPdu_15da8b7f,             0u,                                   3u,                                     2u,           153u,             145u,                 151u,                   143u, COM_NO_TXSIGGRPINFOINDENDIDXOFTXPDUINFO, COM_NO_TXSIGGRPINFOINDSTARTIDXOFTXPDUINFO }   /* [/ActiveEcuC/Com/ComConfig/pdu_TxStat_64_519c4828_Tx, /ActiveEcuC/Com/ComConfig/MyECU_oFlexRay_Tx_ea5c328c] */
};
#define COM_STOP_SEC_CONST_UNSPECIFIED
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */

/**********************************************************************************************************************
  Com_TxPduInitValue
**********************************************************************************************************************/
/** 
  \var    Com_TxPduInitValue
  \brief  Initial values used for Tx I-PDU buffer initialization.
*/ 
#define COM_START_SEC_CONST_8BIT
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */
CONST(Com_TxPduInitValueType, COM_CONST) Com_TxPduInitValue[151] = {  /* PRQA S 1514, 1533 */  /* MD_CSL_ObjectOnlyAccessedOnce */
  /* Index     TxPduInitValue      Referable Keys */
  /*     0 */           0x00u,  /* [/ActiveEcuC/Com/ComConfig/Frame_LinTr_MyECU2_oLIN00_3a9f0ce6_Tx] */
  /*     1 */           0x00u,  /* [/ActiveEcuC/Com/ComConfig/Frame_LinTr_MyECU2_oLIN00_3a9f0ce6_Tx] */
  /*     2 */           0xF0u,  /* [/ActiveEcuC/Com/ComConfig/Frame_LinTr_MyECU2_oLIN00_3a9f0ce6_Tx] */
  /*     3 */           0x00u,  /* [/ActiveEcuC/Com/ComConfig/Frame_LinTr_MyECU_oLIN00_ce6095e4_Tx] */
  /*     4 */           0x00u,  /* [/ActiveEcuC/Com/ComConfig/PDU_Fr_StartAppl_MyECU_TX_44b0b044_Tx] */
  /*     5 */           0x00u,  /* [/ActiveEcuC/Com/ComConfig/PDU_Fr_StartAppl_MyECU_TX_44b0b044_Tx] */
  /*     6 */           0x00u,  /* [/ActiveEcuC/Com/ComConfig/PDU_Fr_StartAppl_MyECU_TX_44b0b044_Tx] */
  /*     7 */           0x00u,  /* [/ActiveEcuC/Com/ComConfig/PDU_Fr_StartAppl_MyECU_TX_44b0b044_Tx] */
  /*     8 */           0x00u,  /* [/ActiveEcuC/Com/ComConfig/PDU_Fr_StartAppl_MyECU_TX_44b0b044_Tx] */
  /*     9 */           0x00u,  /* [/ActiveEcuC/Com/ComConfig/PDU_Fr_StartAppl_MyECU_TX_44b0b044_Tx] */
  /*    10 */           0x00u,  /* [/ActiveEcuC/Com/ComConfig/PDU_Fr_StartAppl_MyECU_TX_44b0b044_Tx] */
  /*    11 */           0x00u,  /* [/ActiveEcuC/Com/ComConfig/PDU_Fr_StartAppl_MyECU_TX_44b0b044_Tx] */
  /*    12 */           0x00u,  /* [/ActiveEcuC/Com/ComConfig/PDU_Fr_StartAppl_MyECU_TX_44b0b044_Tx] */
  /*    13 */           0x00u,  /* [/ActiveEcuC/Com/ComConfig/PDU_Fr_StartAppl_MyECU_TX_44b0b044_Tx] */
  /*    14 */           0x00u,  /* [/ActiveEcuC/Com/ComConfig/PDU_Fr_StartAppl_MyECU_TX_44b0b044_Tx] */
  /*    15 */           0x00u,  /* [/ActiveEcuC/Com/ComConfig/PDU_Fr_StartAppl_MyECU_TX_44b0b044_Tx] */
  /*    16 */           0x00u,  /* [/ActiveEcuC/Com/ComConfig/PDU_Fr_StartAppl_MyECU_TX_44b0b044_Tx] */
  /*    17 */           0x00u,  /* [/ActiveEcuC/Com/ComConfig/PDU_Fr_StartAppl_MyECU_TX_44b0b044_Tx] */
  /*    18 */           0x00u,  /* [/ActiveEcuC/Com/ComConfig/PDU_Fr_StartAppl_MyECU_TX_44b0b044_Tx] */
  /*    19 */           0x00u,  /* [/ActiveEcuC/Com/ComConfig/PDU_Fr_StartAppl_MyECU_TX_44b0b044_Tx] */
  /*    20 */           0x00u,  /* [/ActiveEcuC/Com/ComConfig/PDU_Transmit_MyECU_05398c7a_Tx] */
  /*    21 */           0x00u,  /* [/ActiveEcuC/Com/ComConfig/PDU_Transmit_MyECU_05398c7a_Tx] */
  /*    22 */           0x00u,  /* [/ActiveEcuC/Com/ComConfig/PDU_Transmit_MyECU_05398c7a_Tx] */
  /*    23 */           0x00u,  /* [/ActiveEcuC/Com/ComConfig/PDU_Transmit_MyECU_05398c7a_Tx] */
  /*    24 */           0x00u,  /* [/ActiveEcuC/Com/ComConfig/PDU_Transmit_MyECU_05398c7a_Tx] */
  /*    25 */           0x00u,  /* [/ActiveEcuC/Com/ComConfig/PDU_Transmit_MyECU_05398c7a_Tx] */
  /*    26 */           0x00u,  /* [/ActiveEcuC/Com/ComConfig/PDU_Transmit_MyECU_05398c7a_Tx] */
  /*    27 */           0x00u,  /* [/ActiveEcuC/Com/ComConfig/PDU_Transmit_MyECU_05398c7a_Tx] */
  /*    28 */           0x00u,  /* [/ActiveEcuC/Com/ComConfig/PDU_nm_MyECU_Fr_ae963333_Tx] */
  /*    29 */           0x00u,  /* [/ActiveEcuC/Com/ComConfig/PDU_nm_MyECU_Fr_ae963333_Tx] */
  /*    30 */           0x00u,  /* [/ActiveEcuC/Com/ComConfig/PDU_nm_MyECU_Fr_ae963333_Tx] */
  /*    31 */           0x00u,  /* [/ActiveEcuC/Com/ComConfig/PDU_nm_MyECU_Fr_ae963333_Tx] */
  /*    32 */           0x00u,  /* [/ActiveEcuC/Com/ComConfig/PDU_nm_MyECU_Fr_ae963333_Tx] */
  /*    33 */           0x00u,  /* [/ActiveEcuC/Com/ComConfig/PDU_nm_MyECU_Fr_ae963333_Tx] */
  /*    34 */           0x00u,  /* [/ActiveEcuC/Com/ComConfig/msg_StartAppl_Tx_MyECU_oCAN_ad461e3f_Tx] */
  /*    35 */           0x00u,  /* [/ActiveEcuC/Com/ComConfig/msg_StartAppl_Tx_MyECU_oCAN_ad461e3f_Tx] */
  /*    36 */           0x00u,  /* [/ActiveEcuC/Com/ComConfig/msg_StartAppl_Tx_MyECU_oCAN_ad461e3f_Tx] */
  /*    37 */           0x00u,  /* [/ActiveEcuC/Com/ComConfig/msg_StartAppl_Tx_MyECU_oCAN_ad461e3f_Tx] */
  /*    38 */           0x00u,  /* [/ActiveEcuC/Com/ComConfig/msg_StartAppl_Tx_MyECU_oCAN_ad461e3f_Tx] */
  /*    39 */           0x00u,  /* [/ActiveEcuC/Com/ComConfig/msg_StartAppl_Tx_MyECU_oCAN_ad461e3f_Tx] */
  /*    40 */           0x00u,  /* [/ActiveEcuC/Com/ComConfig/msg_StartAppl_Tx_MyECU_oCAN_ad461e3f_Tx] */
  /*    41 */           0x00u,  /* [/ActiveEcuC/Com/ComConfig/msg_StartAppl_Tx_MyECU_oCAN_ad461e3f_Tx] */
  /*    42 */           0x00u,  /* [/ActiveEcuC/Com/ComConfig/msg_StartAppl_Tx_MyECU_oCAN_ad461e3f_Tx] */
  /*    43 */           0x00u,  /* [/ActiveEcuC/Com/ComConfig/msg_StartAppl_Tx_MyECU_oCAN_ad461e3f_Tx] */
  /*    44 */           0x00u,  /* [/ActiveEcuC/Com/ComConfig/msg_StartAppl_Tx_MyECU_oCAN_ad461e3f_Tx] */
  /*    45 */           0x00u,  /* [/ActiveEcuC/Com/ComConfig/msg_StartAppl_Tx_MyECU_oCAN_ad461e3f_Tx] */
  /*    46 */           0x00u,  /* [/ActiveEcuC/Com/ComConfig/msg_StartAppl_Tx_MyECU_oCAN_ad461e3f_Tx] */
  /*    47 */           0x00u,  /* [/ActiveEcuC/Com/ComConfig/msg_StartAppl_Tx_MyECU_oCAN_ad461e3f_Tx] */
  /*    48 */           0x00u,  /* [/ActiveEcuC/Com/ComConfig/msg_StartAppl_Tx_MyECU_oCAN_ad461e3f_Tx] */
  /*    49 */           0x00u,  /* [/ActiveEcuC/Com/ComConfig/msg_StartAppl_Tx_MyECU_oCAN_ad461e3f_Tx] */
  /* Index     TxPduInitValue      Referable Keys */
  /*    50 */           0x00u,  /* [/ActiveEcuC/Com/ComConfig/msg_StartAppl_Tx_MyECU_oCAN_ad461e3f_Tx] */
  /*    51 */           0x00u,  /* [/ActiveEcuC/Com/ComConfig/msg_StartAppl_Tx_MyECU_oCAN_ad461e3f_Tx] */
  /*    52 */           0x00u,  /* [/ActiveEcuC/Com/ComConfig/msg_StartAppl_Tx_MyECU_oCAN_ad461e3f_Tx] */
  /*    53 */           0x00u,  /* [/ActiveEcuC/Com/ComConfig/msg_StartAppl_Tx_MyECU_oCAN_ad461e3f_Tx] */
  /*    54 */           0x00u,  /* [/ActiveEcuC/Com/ComConfig/msg_StartAppl_Tx_MyECU_oCAN_ad461e3f_Tx] */
  /*    55 */           0x00u,  /* [/ActiveEcuC/Com/ComConfig/msg_StartAppl_Tx_MyECU_oCAN_ad461e3f_Tx] */
  /*    56 */           0x00u,  /* [/ActiveEcuC/Com/ComConfig/msg_StartAppl_Tx_MyECU_oCAN_ad461e3f_Tx] */
  /*    57 */           0x00u,  /* [/ActiveEcuC/Com/ComConfig/msg_StartAppl_Tx_MyECU_oCAN_ad461e3f_Tx] */
  /*    58 */           0x00u,  /* [/ActiveEcuC/Com/ComConfig/msg_StartAppl_Tx_MyECU_oCAN_ad461e3f_Tx] */
  /*    59 */           0x00u,  /* [/ActiveEcuC/Com/ComConfig/msg_StartAppl_Tx_MyECU_oCAN_ad461e3f_Tx] */
  /*    60 */           0x00u,  /* [/ActiveEcuC/Com/ComConfig/msg_StartAppl_Tx_MyECU_oCAN_ad461e3f_Tx] */
  /*    61 */           0x00u,  /* [/ActiveEcuC/Com/ComConfig/msg_StartAppl_Tx_MyECU_oCAN_ad461e3f_Tx] */
  /*    62 */           0x00u,  /* [/ActiveEcuC/Com/ComConfig/msg_StartAppl_Tx_MyECU_oCAN_ad461e3f_Tx] */
  /*    63 */           0x00u,  /* [/ActiveEcuC/Com/ComConfig/msg_StartAppl_Tx_MyECU_oCAN_ad461e3f_Tx] */
  /*    64 */           0x00u,  /* [/ActiveEcuC/Com/ComConfig/msg_StartAppl_Tx_MyECU_oCAN_ad461e3f_Tx] */
  /*    65 */           0x00u,  /* [/ActiveEcuC/Com/ComConfig/msg_StartAppl_Tx_MyECU_oCAN_ad461e3f_Tx] */
  /*    66 */           0x00u,  /* [/ActiveEcuC/Com/ComConfig/msg_StartAppl_Tx_MyECU_oCAN_ad461e3f_Tx] */
  /*    67 */           0x00u,  /* [/ActiveEcuC/Com/ComConfig/msg_StartAppl_Tx_MyECU_oCAN_ad461e3f_Tx] */
  /*    68 */           0x00u,  /* [/ActiveEcuC/Com/ComConfig/msg_StartAppl_Tx_MyECU_oCAN_ad461e3f_Tx] */
  /*    69 */           0x00u,  /* [/ActiveEcuC/Com/ComConfig/msg_StartAppl_Tx_MyECU_oCAN_ad461e3f_Tx] */
  /*    70 */           0x00u,  /* [/ActiveEcuC/Com/ComConfig/msg_StartAppl_Tx_MyECU_oCAN_ad461e3f_Tx] */
  /*    71 */           0x00u,  /* [/ActiveEcuC/Com/ComConfig/msg_StartAppl_Tx_MyECU_oCAN_ad461e3f_Tx] */
  /*    72 */           0x00u,  /* [/ActiveEcuC/Com/ComConfig/msg_StartAppl_Tx_MyECU_oCAN_ad461e3f_Tx] */
  /*    73 */           0x00u,  /* [/ActiveEcuC/Com/ComConfig/msg_StartAppl_Tx_MyECU_oCAN_ad461e3f_Tx] */
  /*    74 */           0x00u,  /* [/ActiveEcuC/Com/ComConfig/msg_StartAppl_Tx_MyECU_oCAN_ad461e3f_Tx] */
  /*    75 */           0x00u,  /* [/ActiveEcuC/Com/ComConfig/msg_StartAppl_Tx_MyECU_oCAN_ad461e3f_Tx] */
  /*    76 */           0x00u,  /* [/ActiveEcuC/Com/ComConfig/msg_StartAppl_Tx_MyECU_oCAN_ad461e3f_Tx] */
  /*    77 */           0x00u,  /* [/ActiveEcuC/Com/ComConfig/msg_StartAppl_Tx_MyECU_oCAN_ad461e3f_Tx] */
  /*    78 */           0x00u,  /* [/ActiveEcuC/Com/ComConfig/msg_StartAppl_Tx_MyECU_oCAN_ad461e3f_Tx] */
  /*    79 */           0x00u,  /* [/ActiveEcuC/Com/ComConfig/msg_StartAppl_Tx_MyECU_oCAN_ad461e3f_Tx] */
  /*    80 */           0x00u,  /* [/ActiveEcuC/Com/ComConfig/msg_StartAppl_Tx_MyECU_oCAN_ad461e3f_Tx] */
  /*    81 */           0x00u,  /* [/ActiveEcuC/Com/ComConfig/msg_StartAppl_Tx_MyECU_oCAN_ad461e3f_Tx] */
  /*    82 */           0x00u,  /* [/ActiveEcuC/Com/ComConfig/msg_StartAppl_Tx_MyECU_oCAN_ad461e3f_Tx] */
  /*    83 */           0x00u,  /* [/ActiveEcuC/Com/ComConfig/msg_StartAppl_Tx_MyECU_oCAN_ad461e3f_Tx] */
  /*    84 */           0x00u,  /* [/ActiveEcuC/Com/ComConfig/msg_StartAppl_Tx_MyECU_oCAN_ad461e3f_Tx] */
  /*    85 */           0x00u,  /* [/ActiveEcuC/Com/ComConfig/msg_StartAppl_Tx_MyECU_oCAN_ad461e3f_Tx] */
  /*    86 */           0x00u,  /* [/ActiveEcuC/Com/ComConfig/msg_StartAppl_Tx_MyECU_oCAN_ad461e3f_Tx] */
  /*    87 */           0x00u,  /* [/ActiveEcuC/Com/ComConfig/msg_StartAppl_Tx_MyECU_oCAN_ad461e3f_Tx] */
  /*    88 */           0x00u,  /* [/ActiveEcuC/Com/ComConfig/msg_StartAppl_Tx_MyECU_oCAN_ad461e3f_Tx] */
  /*    89 */           0x00u,  /* [/ActiveEcuC/Com/ComConfig/msg_StartAppl_Tx_MyECU_oCAN_ad461e3f_Tx] */
  /*    90 */           0x00u,  /* [/ActiveEcuC/Com/ComConfig/msg_StartAppl_Tx_MyECU_oCAN_ad461e3f_Tx] */
  /*    91 */           0x00u,  /* [/ActiveEcuC/Com/ComConfig/msg_StartAppl_Tx_MyECU_oCAN_ad461e3f_Tx] */
  /*    92 */           0x00u,  /* [/ActiveEcuC/Com/ComConfig/msg_StartAppl_Tx_MyECU_oCAN_ad461e3f_Tx] */
  /*    93 */           0x00u,  /* [/ActiveEcuC/Com/ComConfig/msg_StartAppl_Tx_MyECU_oCAN_ad461e3f_Tx] */
  /*    94 */           0x00u,  /* [/ActiveEcuC/Com/ComConfig/msg_StartAppl_Tx_MyECU_oCAN_ad461e3f_Tx] */
  /*    95 */           0x00u,  /* [/ActiveEcuC/Com/ComConfig/msg_StartAppl_Tx_MyECU_oCAN_ad461e3f_Tx] */
  /*    96 */           0x00u,  /* [/ActiveEcuC/Com/ComConfig/msg_StartAppl_Tx_MyECU_oCAN_ad461e3f_Tx] */
  /*    97 */           0x00u,  /* [/ActiveEcuC/Com/ComConfig/msg_StartAppl_Tx_MyECU_oCAN_ad461e3f_Tx] */
  /*    98 */           0x00u,  /* [/ActiveEcuC/Com/ComConfig/msg_TxCycle10_0_oCAN_85bf3e37_Tx] */
  /*    99 */           0x00u,  /* [/ActiveEcuC/Com/ComConfig/msg_TxCycle10_0_oCAN_85bf3e37_Tx] */
  /* Index     TxPduInitValue      Referable Keys */
  /*   100 */           0x00u,  /* [/ActiveEcuC/Com/ComConfig/msg_TxCycle10_0_oCAN_85bf3e37_Tx] */
  /*   101 */           0x00u,  /* [/ActiveEcuC/Com/ComConfig/msg_TxCycle10_0_oCAN_85bf3e37_Tx] */
  /*   102 */           0x00u,  /* [/ActiveEcuC/Com/ComConfig/msg_TxCycle10_0_oCAN_85bf3e37_Tx] */
  /*   103 */           0x00u,  /* [/ActiveEcuC/Com/ComConfig/msg_TxCycle10_0_oCAN_85bf3e37_Tx] */
  /*   104 */           0x00u,  /* [/ActiveEcuC/Com/ComConfig/msg_TxCycle10_0_oCAN_85bf3e37_Tx] */
  /*   105 */           0x00u,  /* [/ActiveEcuC/Com/ComConfig/msg_TxCycle1000_10_oCAN_d74aed68_Tx] */
  /*   106 */           0x00u,  /* [/ActiveEcuC/Com/ComConfig/msg_TxCycle1000_10_oCAN_d74aed68_Tx] */
  /*   107 */           0x00u,  /* [/ActiveEcuC/Com/ComConfig/msg_TxCycle_E2eProf1C_500_30_oCAN_eeefc687_Tx] */
  /*   108 */           0x00u,  /* [/ActiveEcuC/Com/ComConfig/msg_TxCycle_E2eProf1C_500_30_oCAN_eeefc687_Tx] */
  /*   109 */           0x00u,  /* [/ActiveEcuC/Com/ComConfig/msg_TxCycle_E2eProf1C_500_30_oCAN_eeefc687_Tx] */
  /*   110 */           0x00u,  /* [/ActiveEcuC/Com/ComConfig/msg_TxCycle_E2eProf1C_500_30_oCAN_eeefc687_Tx] */
  /*   111 */           0x00u,  /* [/ActiveEcuC/Com/ComConfig/msg_TxCycle_E2eProf1C_500_30_oCAN_eeefc687_Tx] */
  /*   112 */           0x00u,  /* [/ActiveEcuC/Com/ComConfig/msg_TxCycle_E2eProf1C_500_30_oCAN_eeefc687_Tx] */
  /*   113 */           0x00u,  /* [/ActiveEcuC/Com/ComConfig/msg_TxCycle_E2eProf1C_500_30_oCAN_eeefc687_Tx] */
  /*   114 */           0x00u,  /* [/ActiveEcuC/Com/ComConfig/msg_TxCycle_E2eProf1C_500_30_oCAN_eeefc687_Tx] */
  /*   115 */           0x1Fu,  /* [/ActiveEcuC/Com/ComConfig/msg_TxEvent_10_oCAN_b2cd4fc2_Tx] */
  /*   116 */           0x00u,  /* [/ActiveEcuC/Com/ComConfig/msg_TxEvent_10_oCAN_b2cd4fc2_Tx] */
  /*   117 */           0x00u,  /* [/ActiveEcuC/Com/ComConfig/msg_TxEvent_10_oCAN_b2cd4fc2_Tx] */
  /*   118 */           0x00u,  /* [/ActiveEcuC/Com/ComConfig/msg_TxEvent_10_oCAN_b2cd4fc2_Tx] */
  /*   119 */           0x3Fu,  /* [/ActiveEcuC/Com/ComConfig/msg_TxEvent_10_oCAN_b2cd4fc2_Tx] */
  /*   120 */           0x00u,  /* [/ActiveEcuC/Com/ComConfig/msg_TxEvent_10_oCAN_b2cd4fc2_Tx] */
  /*   121 */           0x00u,  /* [/ActiveEcuC/Com/ComConfig/msg_TxEvent_10_oCAN_b2cd4fc2_Tx] */
  /*   122 */           0x00u,  /* [/ActiveEcuC/Com/ComConfig/msg_nm_MyECU_oCAN_c97b60cc_Tx] */
  /*   123 */           0x00u,  /* [/ActiveEcuC/Com/ComConfig/msg_nm_MyECU_oCAN_c97b60cc_Tx] */
  /*   124 */           0x00u,  /* [/ActiveEcuC/Com/ComConfig/msg_nm_MyECU_oCAN_c97b60cc_Tx] */
  /*   125 */           0x00u,  /* [/ActiveEcuC/Com/ComConfig/msg_nm_MyECU_oCAN_c97b60cc_Tx] */
  /*   126 */           0x00u,  /* [/ActiveEcuC/Com/ComConfig/msg_nm_MyECU_oCAN_c97b60cc_Tx] */
  /*   127 */           0x00u,  /* [/ActiveEcuC/Com/ComConfig/msg_nm_MyECU_oCAN_c97b60cc_Tx] */
  /*   128 */           0xFFu,  /* [/ActiveEcuC/Com/ComConfig/pdu_TxDyn_16_3c646bcf_Tx] */
  /*   129 */           0xFFu,  /* [/ActiveEcuC/Com/ComConfig/pdu_TxDyn_16_3c646bcf_Tx] */
  /*   130 */           0x00u,  /* [/ActiveEcuC/Com/ComConfig/pdu_TxDyn_64_9d2b9c24_Tx] */
  /*   131 */           0x00u,  /* [/ActiveEcuC/Com/ComConfig/pdu_TxDyn_64_9d2b9c24_Tx] */
  /*   132 */           0x00u,  /* [/ActiveEcuC/Com/ComConfig/pdu_TxDyn_64_9d2b9c24_Tx] */
  /*   133 */           0x00u,  /* [/ActiveEcuC/Com/ComConfig/pdu_TxDyn_64_9d2b9c24_Tx] */
  /*   134 */           0x00u,  /* [/ActiveEcuC/Com/ComConfig/pdu_TxDyn_64_9d2b9c24_Tx] */
  /*   135 */           0x00u,  /* [/ActiveEcuC/Com/ComConfig/pdu_TxDyn_64_9d2b9c24_Tx] */
  /*   136 */           0x00u,  /* [/ActiveEcuC/Com/ComConfig/pdu_TxDyn_64_9d2b9c24_Tx] */
  /*   137 */           0x00u,  /* [/ActiveEcuC/Com/ComConfig/pdu_TxDyn_64_9d2b9c24_Tx] */
  /*   138 */           0x00u,  /* [/ActiveEcuC/Com/ComConfig/pdu_TxStat_40_64c7eeb3_Tx] */
  /*   139 */           0x01u,  /* [/ActiveEcuC/Com/ComConfig/pdu_TxStat_40_64c7eeb3_Tx] */
  /*   140 */           0x00u,  /* [/ActiveEcuC/Com/ComConfig/pdu_TxStat_40_64c7eeb3_Tx] */
  /*   141 */           0x00u,  /* [/ActiveEcuC/Com/ComConfig/pdu_TxStat_40_64c7eeb3_Tx] */
  /*   142 */           0x00u,  /* [/ActiveEcuC/Com/ComConfig/pdu_TxStat_40_64c7eeb3_Tx] */
  /*   143 */           0x00u,  /* [/ActiveEcuC/Com/ComConfig/pdu_TxStat_64_519c4828_Tx] */
  /*   144 */           0x00u,  /* [/ActiveEcuC/Com/ComConfig/pdu_TxStat_64_519c4828_Tx] */
  /*   145 */           0x00u,  /* [/ActiveEcuC/Com/ComConfig/pdu_TxStat_64_519c4828_Tx] */
  /*   146 */           0x00u,  /* [/ActiveEcuC/Com/ComConfig/pdu_TxStat_64_519c4828_Tx] */
  /*   147 */           0x00u,  /* [/ActiveEcuC/Com/ComConfig/pdu_TxStat_64_519c4828_Tx] */
  /*   148 */           0x00u,  /* [/ActiveEcuC/Com/ComConfig/pdu_TxStat_64_519c4828_Tx] */
  /*   149 */           0x00u,  /* [/ActiveEcuC/Com/ComConfig/pdu_TxStat_64_519c4828_Tx] */
  /* Index     TxPduInitValue      Referable Keys */
  /*   150 */           0x00u   /* [/ActiveEcuC/Com/ComConfig/pdu_TxStat_64_519c4828_Tx] */
};
#define COM_STOP_SEC_CONST_8BIT
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */

/**********************************************************************************************************************
  Com_TxSigGrpInfo
**********************************************************************************************************************/
/** 
  \var    Com_TxSigGrpInfo
  \brief  Contains all relevant information for Tx Signal Groups.
  \details
  Element             Description
  TxSigGrpMaskUsed    TRUE, if the 0:n relation has 1 relation pointing to Com_TxSigGrpMask
*/ 
#define COM_START_SEC_CONST_UNSPECIFIED
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */
CONST(Com_TxSigGrpInfoType, COM_CONST) Com_TxSigGrpInfo[1] = {  /* PRQA S 1514, 1533 */  /* MD_CSL_ObjectOnlyAccessedOnce */
    /* Index    TxSigGrpMaskUsed        Referable Keys */
  { /*     0 */             TRUE }   /* [/ActiveEcuC/Com/ComConfig/msg_TxCycle_E2eProf1C_500_30_oCAN_eeefc687_Tx, /ActiveEcuC/Com/ComConfig/SG_SgTx_Prof1C_TxCycle_omsg_TxCycle_E2eProf1C_500_30_oCAN_9bfb2a76_Tx/SigTx_Prof1C_SQ_omsg_TxCycle_E2eProf1C_500_30_oCAN_8ca3bde6_Tx, /ActiveEcuC/Com/ComConfig/SG_SgTx_Prof1C_TxCycle_omsg_TxCycle_E2eProf1C_500_30_oCAN_9bfb2a76_Tx/SigTx_Prof1C_Sig8Bit_omsg_TxCycle_E2eProf1C_500_30_oCAN_f6b84f66_Tx, /ActiveEcuC/Com/ComConfig/SG_SgTx_Prof1C_TxCycle_omsg_TxCycle_E2eProf1C_500_30_oCAN_9bfb2a76_Tx/SigTx_Prof1C_CRC_omsg_TxCycle_E2eProf1C_500_30_oCAN_5df85835_Tx, /ActiveEcuC/Com/ComConfig/SG_SgTx_Prof1C_TxCycle_omsg_TxCycle_E2eProf1C_500_30_oCAN_9bfb2a76_Tx/SigTx_Prof1C_Sig2Bit_omsg_TxCycle_E2eProf1C_500_30_oCAN_72710fbf_Tx, /ActiveEcuC/Com/ComConfig/SG_SgTx_Prof1C_TxCycle_omsg_TxCycle_E2eProf1C_500_30_oCAN_9bfb2a76_Tx/SigTx_Prof1C_DataId_HiByte_LoNib_omsg_TxCycle_E2eProf1C_500_30_oCAN_2f490cd4_Tx, /ActiveEcuC/Com/ComConfig/SG_SgTx_Prof1C_TxCycle_omsg_TxCycle_E2eProf1C_500_30_oCAN_9bfb2a76_Tx/SigTx_Prof1C_Sig16Bit_omsg_TxCycle_E2eProf1C_500_30_oCAN_4f2c5310_Tx, /ActiveEcuC/Com/ComConfig/SG_SgTx_Prof1C_TxCycle_omsg_TxCycle_E2eProf1C_500_30_oCAN_9bfb2a76_Tx/SigTx_Prof1C_Sig4Bit_omsg_TxCycle_E2eProf1C_500_30_oCAN_0e363008_Tx] */
};
#define COM_STOP_SEC_CONST_UNSPECIFIED
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */

/**********************************************************************************************************************
  Com_TxSigGrpMask
**********************************************************************************************************************/
/** 
  \var    Com_TxSigGrpMask
  \brief  Signal group mask needed to copy interlaced signal groups to the Tx PDU buffer.
*/ 
#define COM_START_SEC_CONST_8BIT
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */
CONST(Com_TxSigGrpMaskType, COM_CONST) Com_TxSigGrpMask[7] = {  /* PRQA S 1514, 1533 */  /* MD_CSL_ObjectOnlyAccessedOnce */
  /* Index     TxSigGrpMask      Referable Keys */
  /*     0 */         0xFFu,  /* [/ActiveEcuC/Com/ComConfig/SG_SgTx_Prof1C_TxCycle_omsg_TxCycle_E2eProf1C_500_30_oCAN_9bfb2a76_Tx] */
  /*     1 */         0xFFu,  /* [/ActiveEcuC/Com/ComConfig/SG_SgTx_Prof1C_TxCycle_omsg_TxCycle_E2eProf1C_500_30_oCAN_9bfb2a76_Tx] */
  /*     2 */         0x0Fu,  /* [/ActiveEcuC/Com/ComConfig/SG_SgTx_Prof1C_TxCycle_omsg_TxCycle_E2eProf1C_500_30_oCAN_9bfb2a76_Tx] */
  /*     3 */         0xC0u,  /* [/ActiveEcuC/Com/ComConfig/SG_SgTx_Prof1C_TxCycle_omsg_TxCycle_E2eProf1C_500_30_oCAN_9bfb2a76_Tx] */
  /*     4 */         0xFFu,  /* [/ActiveEcuC/Com/ComConfig/SG_SgTx_Prof1C_TxCycle_omsg_TxCycle_E2eProf1C_500_30_oCAN_9bfb2a76_Tx] */
  /*     5 */         0xFFu,  /* [/ActiveEcuC/Com/ComConfig/SG_SgTx_Prof1C_TxCycle_omsg_TxCycle_E2eProf1C_500_30_oCAN_9bfb2a76_Tx] */
  /*     6 */         0xFFu   /* [/ActiveEcuC/Com/ComConfig/SG_SgTx_Prof1C_TxCycle_omsg_TxCycle_E2eProf1C_500_30_oCAN_9bfb2a76_Tx] */
};
#define COM_STOP_SEC_CONST_8BIT
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */

/**********************************************************************************************************************
  Com_TxSigInfo
**********************************************************************************************************************/
/** 
  \var    Com_TxSigInfo
  \brief  Contains all relevant information for Tx signals and group signals.
  \details
  Element                   Description
  ApplType                  Application data type.
  BitLength                 Bit length of the signal or group signal.
  BitPosition               Little endian bit position of the signal or group signal within the I-PDU.
  BusAcc                    BUS access algorithm for signal or group signal packing / un-packing.
  ByteLength                Byte length of the signal or group signal.
  StartByteInPduPosition    Start Byte position of the signal or group signal within the I-PDU.
  TxBufferEndIdx            the end index of the 0:n relation pointing to Com_TxBuffer
  TxBufferStartIdx          the start index of the 0:n relation pointing to Com_TxBuffer
  TxPduInfoIdx              the index of the 1:1 relation pointing to Com_TxPduInfo
  TxSigGrpInfoIdx           the index of the 0:1 relation pointing to Com_TxSigGrpInfo
*/ 
#define COM_START_SEC_CONST_UNSPECIFIED
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */
CONST(Com_TxSigInfoType, COM_CONST) Com_TxSigInfo[31] = {  /* PRQA S 1514, 1533 */  /* MD_CSL_ObjectOnlyAccessedOnce */
    /* Index    ApplType                         BitLength  BitPosition  BusAcc                             ByteLength  StartByteInPduPosition  TxBufferEndIdx  TxBufferStartIdx  TxPduInfoIdx  TxSigGrpInfoIdx                          Referable Keys */
  { /*     0 */  COM_UINT16_APPLTYPEOFTXSIGINFO,       16u,          0u,       COM_NBYTE_BUSACCOFTXSIGINFO,         2u,                     0u,           126u,             124u,          10u, COM_NO_TXSIGGRPINFOIDXOFTXSIGINFO },  /* [/ActiveEcuC/Com/ComConfig/NmUd_MyECU_16bit_omsg_nm_MyECU_oCAN_3d555341_Tx, /ActiveEcuC/Com/ComConfig/msg_nm_MyECU_oCAN_c97b60cc_Tx] */
  { /*     1 */  COM_UINT32_APPLTYPEOFTXSIGINFO,       32u,         16u,       COM_NBYTE_BUSACCOFTXSIGINFO,         4u,                     2u,           130u,             126u,          10u, COM_NO_TXSIGGRPINFOIDXOFTXSIGINFO },  /* [/ActiveEcuC/Com/ComConfig/NmUd_MyECU_32bit_omsg_nm_MyECU_oCAN_e4323edd_Tx, /ActiveEcuC/Com/ComConfig/msg_nm_MyECU_oCAN_c97b60cc_Tx] */
  { /*     2 */ COM_UINT8_N_APPLTYPEOFTXSIGINFO,       48u,         16u, COM_ARRAY_BASED_BUSACCOFTXSIGINFO,         6u,                     2u,            36u,              30u,           4u, COM_NO_TXSIGGRPINFOIDXOFTXSIGINFO },  /* [/ActiveEcuC/Com/ComConfig/Nm_MyECU_UserData_oPDU_nm_MyECU_Fr_82c22536_Tx, /ActiveEcuC/Com/ComConfig/PDU_nm_MyECU_Fr_ae963333_Tx] */
  { /*     3 */   COM_UINT8_APPLTYPEOFTXSIGINFO,        8u,          0u,        COM_BYTE_BUSACCOFTXSIGINFO,         1u,                     0u,           154u,             153u,           8u,                                0u },  /* [/ActiveEcuC/Com/ComConfig/SG_SgTx_Prof1C_TxCycle_omsg_TxCycle_E2eProf1C_500_30_oCAN_9bfb2a76_Tx/SigTx_Prof1C_CRC_omsg_TxCycle_E2eProf1C_500_30_oCAN_5df85835_Tx, /ActiveEcuC/Com/ComConfig/msg_TxCycle_E2eProf1C_500_30_oCAN_eeefc687_Tx] */
  { /*     4 */   COM_UINT8_APPLTYPEOFTXSIGINFO,        4u,         12u,        COM_NBIT_BUSACCOFTXSIGINFO,         0u,                     1u,           155u,             154u,           8u,                                0u },  /* [/ActiveEcuC/Com/ComConfig/SG_SgTx_Prof1C_TxCycle_omsg_TxCycle_E2eProf1C_500_30_oCAN_9bfb2a76_Tx/SigTx_Prof1C_DataId_HiByte_LoNib_omsg_TxCycle_E2eProf1C_500_30_oCAN_2f490cd4_Tx, /ActiveEcuC/Com/ComConfig/msg_TxCycle_E2eProf1C_500_30_oCAN_eeefc687_Tx] */
  { /*     5 */   COM_UINT8_APPLTYPEOFTXSIGINFO,        4u,          8u,        COM_NBIT_BUSACCOFTXSIGINFO,         0u,                     1u,           155u,             154u,           8u,                                0u },  /* [/ActiveEcuC/Com/ComConfig/SG_SgTx_Prof1C_TxCycle_omsg_TxCycle_E2eProf1C_500_30_oCAN_9bfb2a76_Tx/SigTx_Prof1C_SQ_omsg_TxCycle_E2eProf1C_500_30_oCAN_8ca3bde6_Tx, /ActiveEcuC/Com/ComConfig/msg_TxCycle_E2eProf1C_500_30_oCAN_eeefc687_Tx] */
  { /*     6 */   COM_UINT8_APPLTYPEOFTXSIGINFO,        2u,         30u,        COM_NBIT_BUSACCOFTXSIGINFO,         0u,                     3u,           157u,             156u,           8u,                                0u },  /* [/ActiveEcuC/Com/ComConfig/SG_SgTx_Prof1C_TxCycle_omsg_TxCycle_E2eProf1C_500_30_oCAN_9bfb2a76_Tx/SigTx_Prof1C_Sig2Bit_omsg_TxCycle_E2eProf1C_500_30_oCAN_72710fbf_Tx, /ActiveEcuC/Com/ComConfig/msg_TxCycle_E2eProf1C_500_30_oCAN_eeefc687_Tx] */
  { /*     7 */   COM_UINT8_APPLTYPEOFTXSIGINFO,        4u,         16u,        COM_NBIT_BUSACCOFTXSIGINFO,         0u,                     2u,           156u,             155u,           8u,                                0u },  /* [/ActiveEcuC/Com/ComConfig/SG_SgTx_Prof1C_TxCycle_omsg_TxCycle_E2eProf1C_500_30_oCAN_9bfb2a76_Tx/SigTx_Prof1C_Sig4Bit_omsg_TxCycle_E2eProf1C_500_30_oCAN_0e363008_Tx, /ActiveEcuC/Com/ComConfig/msg_TxCycle_E2eProf1C_500_30_oCAN_eeefc687_Tx] */
  { /*     8 */   COM_UINT8_APPLTYPEOFTXSIGINFO,        8u,         48u,        COM_BYTE_BUSACCOFTXSIGINFO,         1u,                     6u,           160u,             159u,           8u,                                0u },  /* [/ActiveEcuC/Com/ComConfig/SG_SgTx_Prof1C_TxCycle_omsg_TxCycle_E2eProf1C_500_30_oCAN_9bfb2a76_Tx/SigTx_Prof1C_Sig8Bit_omsg_TxCycle_E2eProf1C_500_30_oCAN_f6b84f66_Tx, /ActiveEcuC/Com/ComConfig/msg_TxCycle_E2eProf1C_500_30_oCAN_eeefc687_Tx] */
  { /*     9 */  COM_UINT16_APPLTYPEOFTXSIGINFO,       16u,         32u,       COM_NBYTE_BUSACCOFTXSIGINFO,         2u,                     4u,           159u,             157u,           8u,                                0u },  /* [/ActiveEcuC/Com/ComConfig/SG_SgTx_Prof1C_TxCycle_omsg_TxCycle_E2eProf1C_500_30_oCAN_9bfb2a76_Tx/SigTx_Prof1C_Sig16Bit_omsg_TxCycle_E2eProf1C_500_30_oCAN_4f2c5310_Tx, /ActiveEcuC/Com/ComConfig/msg_TxCycle_E2eProf1C_500_30_oCAN_eeefc687_Tx] */
  { /*    10 */   COM_UINT8_APPLTYPEOFTXSIGINFO,        6u,          0u,        COM_NBIT_BUSACCOFTXSIGINFO,         0u,                     0u,             1u,               0u,           0u, COM_NO_TXSIGGRPINFOIDXOFTXSIGINFO },  /* [/ActiveEcuC/Com/ComConfig/Sig_LinTr_MyECU2_0_oFrame_LinTr_MyECU2_oLIN00_9bce56ad_Tx, /ActiveEcuC/Com/ComConfig/Frame_LinTr_MyECU2_oLIN00_3a9f0ce6_Tx] */
  { /*    11 */  COM_UINT16_APPLTYPEOFTXSIGINFO,       14u,          6u,   COM_NBITNBYTE_BUSACCOFTXSIGINFO,         1u,                     0u,             3u,               0u,           0u, COM_NO_TXSIGGRPINFOIDXOFTXSIGINFO },  /* [/ActiveEcuC/Com/ComConfig/Sig_LinTr_MyECU2_1_oFrame_LinTr_MyECU2_oLIN00_9a7babb0_Tx, /ActiveEcuC/Com/ComConfig/Frame_LinTr_MyECU2_oLIN00_3a9f0ce6_Tx] */
  { /*    12 */   COM_UINT8_APPLTYPEOFTXSIGINFO,        8u,          0u,        COM_BYTE_BUSACCOFTXSIGINFO,         1u,                     0u,             4u,               3u,           1u, COM_NO_TXSIGGRPINFOIDXOFTXSIGINFO },  /* [/ActiveEcuC/Com/ComConfig/Sig_StartAppl_LinData_Tx_oFrame_LinTr_MyECU_oLIN00_d383dc6b_Tx, /ActiveEcuC/Com/ComConfig/Frame_LinTr_MyECU_oLIN00_ce6095e4_Tx] */
  { /*    13 */  COM_UINT32_APPLTYPEOFTXSIGINFO,       32u,          0u,       COM_NBYTE_BUSACCOFTXSIGINFO,         4u,                     0u,            24u,              20u,           3u, COM_NO_TXSIGGRPINFOIDXOFTXSIGINFO },  /* [/ActiveEcuC/Com/ComConfig/Signal_SomeTxSignal_oPDU_Transmit_MyECU_763437bb_Tx, /ActiveEcuC/Com/ComConfig/PDU_Transmit_MyECU_05398c7a_Tx] */
  { /*    14 */   COM_UINT8_APPLTYPEOFTXSIGINFO,        8u,          0u,        COM_BYTE_BUSACCOFTXSIGINFO,         1u,                     0u,            37u,              36u,           5u, COM_NO_TXSIGGRPINFOIDXOFTXSIGINFO },  /* [/ActiveEcuC/Com/ComConfig/Signal_StartAppl_TxCtrl_MyECU_omsg_StartAppl_Tx_MyECU_oCAN_b45ea6bd_Tx, /ActiveEcuC/Com/ComConfig/msg_StartAppl_Tx_MyECU_oCAN_ad461e3f_Tx] */
  { /*    15 */   COM_UINT8_APPLTYPEOFTXSIGINFO,        8u,          8u,        COM_BYTE_BUSACCOFTXSIGINFO,         1u,                     1u,            38u,              37u,           5u, COM_NO_TXSIGGRPINFOIDXOFTXSIGINFO },  /* [/ActiveEcuC/Com/ComConfig/Signal_StartAppl_TxData_MyECU_omsg_StartAppl_Tx_MyECU_oCAN_8c58228b_Tx, /ActiveEcuC/Com/ComConfig/msg_StartAppl_Tx_MyECU_oCAN_ad461e3f_Tx] */
  { /*    16 */   COM_UINT8_APPLTYPEOFTXSIGINFO,        4u,         10u,        COM_NBIT_BUSACCOFTXSIGINFO,         0u,                     1u,           109u,             108u,           7u, COM_NO_TXSIGGRPINFOIDXOFTXSIGINFO },  /* [/ActiveEcuC/Com/ComConfig/Signal_Tx4bit_Cyclic_omsg_TxCycle1000_10_oCAN_ca549591_Tx, /ActiveEcuC/Com/ComConfig/msg_TxCycle1000_10_oCAN_d74aed68_Tx] */
  { /*    17 */  COM_UINT16_APPLTYPEOFTXSIGINFO,       10u,          0u,   COM_NBITNBYTE_BUSACCOFTXSIGINFO,         1u,                     0u,           109u,             107u,           7u, COM_NO_TXSIGGRPINFOIDXOFTXSIGINFO },  /* [/ActiveEcuC/Com/ComConfig/Signal_Tx10bit_Cyclic_omsg_TxCycle1000_10_oCAN_9539efc7_Tx, /ActiveEcuC/Com/ComConfig/msg_TxCycle1000_10_oCAN_d74aed68_Tx] */
  { /*    18 */  COM_UINT32_APPLTYPEOFTXSIGINFO,       24u,         32u,       COM_NBYTE_BUSACCOFTXSIGINFO,         3u,                     4u,           107u,             104u,           6u, COM_NO_TXSIGGRPINFOIDXOFTXSIGINFO },  /* [/ActiveEcuC/Com/ComConfig/Signal_Tx24bit_Cyclic_omsg_TxCycle10_0_oCAN_393dd5fe_Tx, /ActiveEcuC/Com/ComConfig/msg_TxCycle10_0_oCAN_85bf3e37_Tx] */
  { /*    19 */  COM_UINT32_APPLTYPEOFTXSIGINFO,       24u,         32u,       COM_NBYTE_BUSACCOFTXSIGINFO,         3u,                     4u,           124u,             121u,           9u, COM_NO_TXSIGGRPINFOIDXOFTXSIGINFO },  /* [/ActiveEcuC/Com/ComConfig/Signal_Tx24bit_OnWrite_omsg_TxEvent_10_oCAN_bb23a029_Tx, /ActiveEcuC/Com/ComConfig/msg_TxEvent_10_oCAN_b2cd4fc2_Tx] */
  { /*    20 */  COM_UINT32_APPLTYPEOFTXSIGINFO,       32u,          0u,       COM_NBYTE_BUSACCOFTXSIGINFO,         4u,                     0u,           104u,             100u,           6u, COM_NO_TXSIGGRPINFOIDXOFTXSIGINFO },  /* [/ActiveEcuC/Com/ComConfig/Signal_Tx32bit_Cyclic_omsg_TxCycle10_0_oCAN_ab90e6e2_Tx, /ActiveEcuC/Com/ComConfig/msg_TxCycle10_0_oCAN_85bf3e37_Tx] */
  { /*    21 */  COM_UINT32_APPLTYPEOFTXSIGINFO,       32u,          0u,       COM_NBYTE_BUSACCOFTXSIGINFO,         4u,                     0u,           121u,             117u,           9u, COM_NO_TXSIGGRPINFOIDXOFTXSIGINFO },  /* [/ActiveEcuC/Com/ComConfig/Signal_Tx32bit_OnWrite_omsg_TxEvent_10_oCAN_298e9335_Tx, /ActiveEcuC/Com/ComConfig/msg_TxEvent_10_oCAN_b2cd4fc2_Tx] */
  { /*    22 */  COM_UINT16_APPLTYPEOFTXSIGINFO,       16u,          0u,       COM_NBYTE_BUSACCOFTXSIGINFO,         2u,                     0u,             6u,               4u,           2u, COM_NO_TXSIGGRPINFOIDXOFTXSIGINFO },  /* [/ActiveEcuC/Com/ComConfig/StartAppl_Fr_MyECU_TX_Ctrl_oPDU_Fr_StartAppl_MyECU_TX_644d68c7_Tx, /ActiveEcuC/Com/ComConfig/PDU_Fr_StartAppl_MyECU_TX_44b0b044_Tx] */
  { /*    23 */  COM_UINT16_APPLTYPEOFTXSIGINFO,       16u,         16u,       COM_NBYTE_BUSACCOFTXSIGINFO,         2u,                     2u,             8u,               6u,           2u, COM_NO_TXSIGGRPINFOIDXOFTXSIGINFO },  /* [/ActiveEcuC/Com/ComConfig/StartAppl_Fr_MyECU_TX_Data_oPDU_Fr_StartAppl_MyECU_TX_6a5990c2_Tx, /ActiveEcuC/Com/ComConfig/PDU_Fr_StartAppl_MyECU_TX_44b0b044_Tx] */
  { /*    24 */ COM_UINT8_N_APPLTYPEOFTXSIGINFO,       32u,         32u, COM_ARRAY_BASED_BUSACCOFTXSIGINFO,         4u,                     4u,           140u,             136u,          12u, COM_NO_TXSIGGRPINFOIDXOFTXSIGINFO },  /* [/ActiveEcuC/Com/ComConfig/pdu_TxDyn_32bit_2_opdu_TxDyn_64_65bb7ebd_Tx, /ActiveEcuC/Com/ComConfig/pdu_TxDyn_64_9d2b9c24_Tx] */
  { /*    25 */  COM_UINT16_APPLTYPEOFTXSIGINFO,       16u,          0u,       COM_NBYTE_BUSACCOFTXSIGINFO,         2u,                     0u,           132u,             130u,          11u, COM_NO_TXSIGGRPINFOIDXOFTXSIGINFO },  /* [/ActiveEcuC/Com/ComConfig/signal_TxDyn_16bit_opdu_TxDyn_16_bfe7058b_Tx, /ActiveEcuC/Com/ComConfig/pdu_TxDyn_16_3c646bcf_Tx] */
  { /*    26 */  COM_UINT32_APPLTYPEOFTXSIGINFO,       32u,          0u,       COM_NBYTE_BUSACCOFTXSIGINFO,         4u,                     0u,           136u,             132u,          12u, COM_NO_TXSIGGRPINFOIDXOFTXSIGINFO },  /* [/ActiveEcuC/Com/ComConfig/signal_TxDyn_32bit_opdu_TxDyn_64_61b42397_Tx, /ActiveEcuC/Com/ComConfig/pdu_TxDyn_64_9d2b9c24_Tx] */
  { /*    27 */   COM_UINT8_APPLTYPEOFTXSIGINFO,        8u,          0u,        COM_BYTE_BUSACCOFTXSIGINFO,         1u,                     0u,           141u,             140u,          13u, COM_NO_TXSIGGRPINFOIDXOFTXSIGINFO },  /* [/ActiveEcuC/Com/ComConfig/signal_TxStat_8bit_opdu_TxStat_40_772fb39e_Tx, /ActiveEcuC/Com/ComConfig/pdu_TxStat_40_64c7eeb3_Tx] */
  { /*    28 */  COM_UINT32_APPLTYPEOFTXSIGINFO,       24u,          0u,       COM_NBYTE_BUSACCOFTXSIGINFO,         3u,                     0u,           148u,             145u,          14u, COM_NO_TXSIGGRPINFOIDXOFTXSIGINFO },  /* [/ActiveEcuC/Com/ComConfig/signal_TxStat_24bit_opdu_TxStat_64_124baf31_Tx, /ActiveEcuC/Com/ComConfig/pdu_TxStat_64_519c4828_Tx] */
  { /*    29 */  COM_UINT32_APPLTYPEOFTXSIGINFO,       32u,          8u,       COM_NBYTE_BUSACCOFTXSIGINFO,         4u,                     1u,           145u,             141u,          13u, COM_NO_TXSIGGRPINFOIDXOFTXSIGINFO },  /* [/ActiveEcuC/Com/ComConfig/signal_TxStat_32bit_opdu_TxStat_40_dfab39ff_Tx, /ActiveEcuC/Com/ComConfig/pdu_TxStat_40_64c7eeb3_Tx] */
  { /*    30 */ COM_UINT8_N_APPLTYPEOFTXSIGINFO,       40u,         24u, COM_ARRAY_BASED_BUSACCOFTXSIGINFO,         5u,                     3u,           153u,             148u,          14u, COM_NO_TXSIGGRPINFOIDXOFTXSIGINFO }   /* [/ActiveEcuC/Com/ComConfig/signal_TxStat_40bit_opdu_TxStat_64_7f003f06_Tx, /ActiveEcuC/Com/ComConfig/pdu_TxStat_64_519c4828_Tx] */
};
#define COM_STOP_SEC_CONST_UNSPECIFIED
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */

/**********************************************************************************************************************
  Com_CurrentTxMode
**********************************************************************************************************************/
/** 
  \var    Com_CurrentTxMode
  \brief  Current transmission mode state of all Tx I-PDUs.
*/ 
#define COM_START_SEC_VAR_NOINIT_8BIT
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */
VAR(Com_CurrentTxModeType, COM_VAR_NOINIT) Com_CurrentTxMode[15];  /* PRQA S 1514, 1533 */  /* MD_CSL_ObjectOnlyAccessedOnce */
  /* Index        Referable Keys */
  /*     0 */  /* [/ActiveEcuC/Com/ComConfig/Frame_LinTr_MyECU2_oLIN00_3a9f0ce6_Tx, /ActiveEcuC/Com/ComConfig/MyECU_oLIN00_Tx_f02a8aba] */
  /*     1 */  /* [/ActiveEcuC/Com/ComConfig/Frame_LinTr_MyECU_oLIN00_ce6095e4_Tx, /ActiveEcuC/Com/ComConfig/MyECU_oLIN00_Tx_f02a8aba] */
  /*     2 */  /* [/ActiveEcuC/Com/ComConfig/PDU_Fr_StartAppl_MyECU_TX_44b0b044_Tx, /ActiveEcuC/Com/ComConfig/MyECU_oFlexRay_Tx_ea5c328c] */
  /*     3 */  /* [/ActiveEcuC/Com/ComConfig/PDU_Transmit_MyECU_05398c7a_Tx, /ActiveEcuC/Com/ComConfig/MyECU_oFlexRay_Tx_ea5c328c] */
  /*     4 */  /* [/ActiveEcuC/Com/ComConfig/PDU_nm_MyECU_Fr_ae963333_Tx] */
  /*     5 */  /* [/ActiveEcuC/Com/ComConfig/msg_StartAppl_Tx_MyECU_oCAN_ad461e3f_Tx, /ActiveEcuC/Com/ComConfig/MyECU_oCAN_Tx_35f94448] */
  /*     6 */  /* [/ActiveEcuC/Com/ComConfig/msg_TxCycle10_0_oCAN_85bf3e37_Tx, /ActiveEcuC/Com/ComConfig/MyECU_oCAN_Tx_35f94448] */
  /*     7 */  /* [/ActiveEcuC/Com/ComConfig/msg_TxCycle1000_10_oCAN_d74aed68_Tx, /ActiveEcuC/Com/ComConfig/MyECU_oCAN_Tx_35f94448] */
  /*     8 */  /* [/ActiveEcuC/Com/ComConfig/msg_TxCycle_E2eProf1C_500_30_oCAN_eeefc687_Tx, /ActiveEcuC/Com/ComConfig/MyECU_oCAN_Tx_35f94448] */
  /*     9 */  /* [/ActiveEcuC/Com/ComConfig/msg_TxEvent_10_oCAN_b2cd4fc2_Tx, /ActiveEcuC/Com/ComConfig/MyECU_oCAN_Tx_35f94448] */
  /*    10 */  /* [/ActiveEcuC/Com/ComConfig/msg_nm_MyECU_oCAN_c97b60cc_Tx] */
  /*    11 */  /* [/ActiveEcuC/Com/ComConfig/pdu_TxDyn_16_3c646bcf_Tx, /ActiveEcuC/Com/ComConfig/MyECU_oFlexRay_Tx_ea5c328c] */
  /*    12 */  /* [/ActiveEcuC/Com/ComConfig/pdu_TxDyn_64_9d2b9c24_Tx, /ActiveEcuC/Com/ComConfig/MyECU_oFlexRay_Tx_ea5c328c] */
  /*    13 */  /* [/ActiveEcuC/Com/ComConfig/pdu_TxStat_40_64c7eeb3_Tx, /ActiveEcuC/Com/ComConfig/MyECU_oFlexRay_Tx_ea5c328c] */
  /*    14 */  /* [/ActiveEcuC/Com/ComConfig/pdu_TxStat_64_519c4828_Tx, /ActiveEcuC/Com/ComConfig/MyECU_oFlexRay_Tx_ea5c328c] */

#define COM_STOP_SEC_VAR_NOINIT_8BIT
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */

/**********************************************************************************************************************
  Com_CycleTimeCnt
**********************************************************************************************************************/
/** 
  \var    Com_CycleTimeCnt
  \brief  Current counter value of cyclic transmission.
*/ 
#define COM_START_SEC_VAR_NOINIT_8BIT
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */
VAR(Com_CycleTimeCntType, COM_VAR_NOINIT) Com_CycleTimeCnt[15];  /* PRQA S 1514, 1533 */  /* MD_CSL_ObjectOnlyAccessedOnce */
  /* Index        Referable Keys */
  /*     0 */  /* [/ActiveEcuC/Com/ComConfig/Frame_LinTr_MyECU2_oLIN00_3a9f0ce6_Tx, /ActiveEcuC/Com/ComConfig/MyECU_oLIN00_Tx_f02a8aba] */
  /*     1 */  /* [/ActiveEcuC/Com/ComConfig/Frame_LinTr_MyECU_oLIN00_ce6095e4_Tx, /ActiveEcuC/Com/ComConfig/MyECU_oLIN00_Tx_f02a8aba] */
  /*     2 */  /* [/ActiveEcuC/Com/ComConfig/PDU_Fr_StartAppl_MyECU_TX_44b0b044_Tx, /ActiveEcuC/Com/ComConfig/MyECU_oFlexRay_Tx_ea5c328c] */
  /*     3 */  /* [/ActiveEcuC/Com/ComConfig/PDU_Transmit_MyECU_05398c7a_Tx, /ActiveEcuC/Com/ComConfig/MyECU_oFlexRay_Tx_ea5c328c] */
  /*     4 */  /* [/ActiveEcuC/Com/ComConfig/PDU_nm_MyECU_Fr_ae963333_Tx] */
  /*     5 */  /* [/ActiveEcuC/Com/ComConfig/msg_StartAppl_Tx_MyECU_oCAN_ad461e3f_Tx, /ActiveEcuC/Com/ComConfig/MyECU_oCAN_Tx_35f94448] */
  /*     6 */  /* [/ActiveEcuC/Com/ComConfig/msg_TxCycle10_0_oCAN_85bf3e37_Tx, /ActiveEcuC/Com/ComConfig/MyECU_oCAN_Tx_35f94448] */
  /*     7 */  /* [/ActiveEcuC/Com/ComConfig/msg_TxCycle1000_10_oCAN_d74aed68_Tx, /ActiveEcuC/Com/ComConfig/MyECU_oCAN_Tx_35f94448] */
  /*     8 */  /* [/ActiveEcuC/Com/ComConfig/msg_TxCycle_E2eProf1C_500_30_oCAN_eeefc687_Tx, /ActiveEcuC/Com/ComConfig/MyECU_oCAN_Tx_35f94448] */
  /*     9 */  /* [/ActiveEcuC/Com/ComConfig/msg_TxEvent_10_oCAN_b2cd4fc2_Tx, /ActiveEcuC/Com/ComConfig/MyECU_oCAN_Tx_35f94448] */
  /*    10 */  /* [/ActiveEcuC/Com/ComConfig/msg_nm_MyECU_oCAN_c97b60cc_Tx] */
  /*    11 */  /* [/ActiveEcuC/Com/ComConfig/pdu_TxDyn_16_3c646bcf_Tx, /ActiveEcuC/Com/ComConfig/MyECU_oFlexRay_Tx_ea5c328c] */
  /*    12 */  /* [/ActiveEcuC/Com/ComConfig/pdu_TxDyn_64_9d2b9c24_Tx, /ActiveEcuC/Com/ComConfig/MyECU_oFlexRay_Tx_ea5c328c] */
  /*    13 */  /* [/ActiveEcuC/Com/ComConfig/pdu_TxStat_40_64c7eeb3_Tx, /ActiveEcuC/Com/ComConfig/MyECU_oFlexRay_Tx_ea5c328c] */
  /*    14 */  /* [/ActiveEcuC/Com/ComConfig/pdu_TxStat_64_519c4828_Tx, /ActiveEcuC/Com/ComConfig/MyECU_oFlexRay_Tx_ea5c328c] */

#define COM_STOP_SEC_VAR_NOINIT_8BIT
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */

/**********************************************************************************************************************
  Com_CyclicSendRequest
**********************************************************************************************************************/
/** 
  \var    Com_CyclicSendRequest
  \brief  Cyclic send request flag used to indicate cyclic transmission mode for all Tx I-PDU.
*/ 
#define COM_START_SEC_VAR_NOINIT_8BIT
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */
VAR(Com_CyclicSendRequestType, COM_VAR_NOINIT) Com_CyclicSendRequest[15];  /* PRQA S 1514, 1533 */  /* MD_CSL_ObjectOnlyAccessedOnce */
  /* Index        Referable Keys */
  /*     0 */  /* [/ActiveEcuC/Com/ComConfig/Frame_LinTr_MyECU2_oLIN00_3a9f0ce6_Tx, /ActiveEcuC/Com/ComConfig/MyECU_oLIN00_Tx_f02a8aba] */
  /*     1 */  /* [/ActiveEcuC/Com/ComConfig/Frame_LinTr_MyECU_oLIN00_ce6095e4_Tx, /ActiveEcuC/Com/ComConfig/MyECU_oLIN00_Tx_f02a8aba] */
  /*     2 */  /* [/ActiveEcuC/Com/ComConfig/PDU_Fr_StartAppl_MyECU_TX_44b0b044_Tx, /ActiveEcuC/Com/ComConfig/MyECU_oFlexRay_Tx_ea5c328c] */
  /*     3 */  /* [/ActiveEcuC/Com/ComConfig/PDU_Transmit_MyECU_05398c7a_Tx, /ActiveEcuC/Com/ComConfig/MyECU_oFlexRay_Tx_ea5c328c] */
  /*     4 */  /* [/ActiveEcuC/Com/ComConfig/PDU_nm_MyECU_Fr_ae963333_Tx] */
  /*     5 */  /* [/ActiveEcuC/Com/ComConfig/msg_StartAppl_Tx_MyECU_oCAN_ad461e3f_Tx, /ActiveEcuC/Com/ComConfig/MyECU_oCAN_Tx_35f94448] */
  /*     6 */  /* [/ActiveEcuC/Com/ComConfig/msg_TxCycle10_0_oCAN_85bf3e37_Tx, /ActiveEcuC/Com/ComConfig/MyECU_oCAN_Tx_35f94448] */
  /*     7 */  /* [/ActiveEcuC/Com/ComConfig/msg_TxCycle1000_10_oCAN_d74aed68_Tx, /ActiveEcuC/Com/ComConfig/MyECU_oCAN_Tx_35f94448] */
  /*     8 */  /* [/ActiveEcuC/Com/ComConfig/msg_TxCycle_E2eProf1C_500_30_oCAN_eeefc687_Tx, /ActiveEcuC/Com/ComConfig/MyECU_oCAN_Tx_35f94448] */
  /*     9 */  /* [/ActiveEcuC/Com/ComConfig/msg_TxEvent_10_oCAN_b2cd4fc2_Tx, /ActiveEcuC/Com/ComConfig/MyECU_oCAN_Tx_35f94448] */
  /*    10 */  /* [/ActiveEcuC/Com/ComConfig/msg_nm_MyECU_oCAN_c97b60cc_Tx] */
  /*    11 */  /* [/ActiveEcuC/Com/ComConfig/pdu_TxDyn_16_3c646bcf_Tx, /ActiveEcuC/Com/ComConfig/MyECU_oFlexRay_Tx_ea5c328c] */
  /*    12 */  /* [/ActiveEcuC/Com/ComConfig/pdu_TxDyn_64_9d2b9c24_Tx, /ActiveEcuC/Com/ComConfig/MyECU_oFlexRay_Tx_ea5c328c] */
  /*    13 */  /* [/ActiveEcuC/Com/ComConfig/pdu_TxStat_40_64c7eeb3_Tx, /ActiveEcuC/Com/ComConfig/MyECU_oFlexRay_Tx_ea5c328c] */
  /*    14 */  /* [/ActiveEcuC/Com/ComConfig/pdu_TxStat_64_519c4828_Tx, /ActiveEcuC/Com/ComConfig/MyECU_oFlexRay_Tx_ea5c328c] */

#define COM_STOP_SEC_VAR_NOINIT_8BIT
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */

/**********************************************************************************************************************
  Com_HandleRxPduDeferred
**********************************************************************************************************************/
/** 
  \var    Com_HandleRxPduDeferred
  \brief  Array indicating received Rx I-PDUs to be processed deferred within the next call of Com_MainfunctionRx().
*/ 
#define COM_START_SEC_VAR_NOINIT_16BIT
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */
VAR(Com_HandleRxPduDeferredUType, COM_VAR_NOINIT) Com_HandleRxPduDeferred;  /* PRQA S 0759, 1514, 1533 */  /* MD_CSL_Union, MD_CSL_ObjectOnlyAccessedOnce, MD_CSL_ObjectOnlyAccessedOnce */
  /* Index        Referable Keys */
  /*     0 */  /* [/ActiveEcuC/Com/ComConfig/Frame_LinTr_RearECU_oLIN00_22758e6f_Rx] */
  /*     1 */  /* [/ActiveEcuC/Com/ComConfig/Frame_LinTr_Slave3_oLIN00_bac14905_Rx] */
  /*     2 */  /* [/ActiveEcuC/Com/ComConfig/PDU_Dummy_RearECU_1e8d611e_Rx] */
  /*     3 */  /* [/ActiveEcuC/Com/ComConfig/PDU_Fr_StartAppl_BothECU_RX_42a9ded9_Rx] */
  /*     4 */  /* [/ActiveEcuC/Com/ComConfig/msg_RxCycle100_0_oCAN_1e247d16_Rx] */
  /*     5 */  /* [/ActiveEcuC/Com/ComConfig/msg_RxCycle500_20_oCAN_a691adb3_Rx] */
  /*     6 */  /* [/ActiveEcuC/Com/ComConfig/msg_RxCycle_E2eProf1C_500_10_oCAN_e8110737_Rx] */
  /*     7 */  /* [/ActiveEcuC/Com/ComConfig/msg_RxEvent_20_oCAN_a1df81ad_Rx] */
  /*     8 */  /* [/ActiveEcuC/Com/ComConfig/msg_StartAppl_Rx_MyECU_oCAN_0da3c1e2_Rx] */
  /*     9 */  /* [/ActiveEcuC/Com/ComConfig/pdu_RxDyn_64_5b4495a3_Rx] */
  /*    10 */  /* [/ActiveEcuC/Com/ComConfig/pdu_RxStat_10_6aaa637c_Rx] */
  /*    11 */  /* [/ActiveEcuC/Com/ComConfig/pdu_RxStat_30_589c01fe_Rx] */

#define COM_STOP_SEC_VAR_NOINIT_16BIT
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */

/**********************************************************************************************************************
  Com_Initialized
**********************************************************************************************************************/
/** 
  \var    Com_Initialized
  \brief  Initialization state of Com. TRUE, if Com_Init() has been called, else FALSE.
*/ 
#define COM_START_SEC_VAR_ZERO_INIT_8BIT
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */
VAR(Com_InitializedType, COM_VAR_ZERO_INIT) Com_Initialized = FALSE;  /* PRQA S 1514, 1533 */  /* MD_CSL_ObjectOnlyAccessedOnce */
#define COM_STOP_SEC_VAR_ZERO_INIT_8BIT
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */

/**********************************************************************************************************************
  Com_RxDefPduBuffer
**********************************************************************************************************************/
/** 
  \var    Com_RxDefPduBuffer
  \brief  Rx I-PDU buffer for deferred signal processing.
*/ 
#define COM_START_SEC_VAR_NOINIT_8BIT
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */
VAR(Com_RxDefPduBufferUType, COM_VAR_NOINIT) Com_RxDefPduBuffer;  /* PRQA S 0759, 1514, 1533 */  /* MD_CSL_Union, MD_CSL_ObjectOnlyAccessedOnce, MD_CSL_ObjectOnlyAccessedOnce */
  /* Index        Referable Keys */
  /*     0 */  /* [/ActiveEcuC/Com/ComConfig/Frame_LinTr_RearECU_oLIN00_22758e6f_Rx] */
  /*     1 */  /* [/ActiveEcuC/Com/ComConfig/Frame_LinTr_Slave3_oLIN00_bac14905_Rx] */
  /*   ... */  /* [/ActiveEcuC/Com/ComConfig/Frame_LinTr_Slave3_oLIN00_bac14905_Rx] */
  /*     3 */  /* [/ActiveEcuC/Com/ComConfig/Frame_LinTr_Slave3_oLIN00_bac14905_Rx] */
  /*     4 */  /* [/ActiveEcuC/Com/ComConfig/PDU_Dummy_RearECU_1e8d611e_Rx] */
  /*   ... */  /* [/ActiveEcuC/Com/ComConfig/PDU_Dummy_RearECU_1e8d611e_Rx] */
  /*    19 */  /* [/ActiveEcuC/Com/ComConfig/PDU_Dummy_RearECU_1e8d611e_Rx] */
  /*    20 */  /* [/ActiveEcuC/Com/ComConfig/PDU_Fr_StartAppl_BothECU_RX_42a9ded9_Rx] */
  /*   ... */  /* [/ActiveEcuC/Com/ComConfig/PDU_Fr_StartAppl_BothECU_RX_42a9ded9_Rx] */
  /*    35 */  /* [/ActiveEcuC/Com/ComConfig/PDU_Fr_StartAppl_BothECU_RX_42a9ded9_Rx] */
  /*    36 */  /* [/ActiveEcuC/Com/ComConfig/msg_RxCycle100_0_oCAN_1e247d16_Rx] */
  /*    37 */  /* [/ActiveEcuC/Com/ComConfig/msg_RxCycle500_20_oCAN_a691adb3_Rx] */
  /*   ... */  /* [/ActiveEcuC/Com/ComConfig/msg_RxCycle500_20_oCAN_a691adb3_Rx] */
  /*    42 */  /* [/ActiveEcuC/Com/ComConfig/msg_RxCycle500_20_oCAN_a691adb3_Rx] */
  /*    43 */  /* [/ActiveEcuC/Com/ComConfig/msg_RxCycle_E2eProf1C_500_10_oCAN_e8110737_Rx] */
  /*   ... */  /* [/ActiveEcuC/Com/ComConfig/msg_RxCycle_E2eProf1C_500_10_oCAN_e8110737_Rx] */
  /*    50 */  /* [/ActiveEcuC/Com/ComConfig/msg_RxCycle_E2eProf1C_500_10_oCAN_e8110737_Rx] */
  /*    51 */  /* [/ActiveEcuC/Com/ComConfig/msg_RxEvent_20_oCAN_a1df81ad_Rx] */
  /*   ... */  /* [/ActiveEcuC/Com/ComConfig/msg_RxEvent_20_oCAN_a1df81ad_Rx] */
  /*    54 */  /* [/ActiveEcuC/Com/ComConfig/msg_RxEvent_20_oCAN_a1df81ad_Rx] */
  /*    55 */  /* [/ActiveEcuC/Com/ComConfig/msg_StartAppl_Rx_MyECU_oCAN_0da3c1e2_Rx] */
  /*   ... */  /* [/ActiveEcuC/Com/ComConfig/msg_StartAppl_Rx_MyECU_oCAN_0da3c1e2_Rx] */
  /*   118 */  /* [/ActiveEcuC/Com/ComConfig/msg_StartAppl_Rx_MyECU_oCAN_0da3c1e2_Rx] */
  /*   119 */  /* [/ActiveEcuC/Com/ComConfig/pdu_RxDyn_64_5b4495a3_Rx] */
  /*   ... */  /* [/ActiveEcuC/Com/ComConfig/pdu_RxDyn_64_5b4495a3_Rx] */
  /*   126 */  /* [/ActiveEcuC/Com/ComConfig/pdu_RxDyn_64_5b4495a3_Rx] */
  /*   127 */  /* [/ActiveEcuC/Com/ComConfig/pdu_RxStat_10_6aaa637c_Rx] */
  /*   ... */  /* [/ActiveEcuC/Com/ComConfig/pdu_RxStat_10_6aaa637c_Rx] */
  /*   130 */  /* [/ActiveEcuC/Com/ComConfig/pdu_RxStat_10_6aaa637c_Rx] */
  /*   131 */  /* [/ActiveEcuC/Com/ComConfig/pdu_RxStat_30_589c01fe_Rx] */
  /*   ... */  /* [/ActiveEcuC/Com/ComConfig/pdu_RxStat_30_589c01fe_Rx] */
  /*   137 */  /* [/ActiveEcuC/Com/ComConfig/pdu_RxStat_30_589c01fe_Rx] */

#define COM_STOP_SEC_VAR_NOINIT_8BIT
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */

/**********************************************************************************************************************
  Com_RxDeferredFctPtrCache
**********************************************************************************************************************/
/** 
  \var    Com_RxDeferredFctPtrCache
  \brief  Cache for deferred Rx (invalid) notification.
*/ 
#define COM_START_SEC_VAR_NOINIT_32BIT
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */
VAR(Com_RxDeferredFctPtrCacheType, COM_VAR_NOINIT) Com_RxDeferredFctPtrCache[1];  /* PRQA S 1514, 1533 */  /* MD_CSL_ObjectOnlyAccessedOnce */
#define COM_STOP_SEC_VAR_NOINIT_32BIT
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */

/**********************************************************************************************************************
  Com_RxDeferredProcessingISRLockCounter
**********************************************************************************************************************/
#define COM_START_SEC_VAR_NOINIT_16BIT
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */
VAR(Com_RxDeferredProcessingISRLockCounterType, COM_VAR_NOINIT) Com_RxDeferredProcessingISRLockCounter;  /* PRQA S 1514, 1533 */  /* MD_CSL_ObjectOnlyAccessedOnce */
#define COM_STOP_SEC_VAR_NOINIT_16BIT
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */

/**********************************************************************************************************************
  Com_RxIPduGroupISRLockCounter
**********************************************************************************************************************/
#define COM_START_SEC_VAR_NOINIT_16BIT
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */
VAR(Com_RxIPduGroupISRLockCounterType, COM_VAR_NOINIT) Com_RxIPduGroupISRLockCounter;  /* PRQA S 1514, 1533 */  /* MD_CSL_ObjectOnlyAccessedOnce */
#define COM_STOP_SEC_VAR_NOINIT_16BIT
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */

/**********************************************************************************************************************
  Com_RxPduGrpActive
**********************************************************************************************************************/
/** 
  \var    Com_RxPduGrpActive
  \brief  Rx I-PDU based state (started/stopped) of the corresponding I-PDU-Group.
*/ 
#define COM_START_SEC_VAR_NOINIT_8BIT
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */
VAR(Com_RxPduGrpActiveType, COM_VAR_NOINIT) Com_RxPduGrpActive[12];  /* PRQA S 1514, 1533 */  /* MD_CSL_ObjectOnlyAccessedOnce */
  /* Index        Referable Keys */
  /*     0 */  /* [/ActiveEcuC/Com/ComConfig/Frame_LinTr_RearECU_oLIN00_22758e6f_Rx, /ActiveEcuC/Com/ComConfig/MyECU_oLIN00_Rx_a6702d3c] */
  /*     1 */  /* [/ActiveEcuC/Com/ComConfig/Frame_LinTr_Slave3_oLIN00_bac14905_Rx, /ActiveEcuC/Com/ComConfig/MyECU_oLIN00_Rx_a6702d3c] */
  /*     2 */  /* [/ActiveEcuC/Com/ComConfig/PDU_Dummy_RearECU_1e8d611e_Rx, /ActiveEcuC/Com/ComConfig/MyECU_oFlexRay_Rx_bc06950a] */
  /*     3 */  /* [/ActiveEcuC/Com/ComConfig/PDU_Fr_StartAppl_BothECU_RX_42a9ded9_Rx, /ActiveEcuC/Com/ComConfig/MyECU_oFlexRay_Rx_bc06950a] */
  /*     4 */  /* [/ActiveEcuC/Com/ComConfig/msg_RxCycle100_0_oCAN_1e247d16_Rx, /ActiveEcuC/Com/ComConfig/MyECU_oCAN_Rx_63a3e3ce] */
  /*     5 */  /* [/ActiveEcuC/Com/ComConfig/msg_RxCycle500_20_oCAN_a691adb3_Rx, /ActiveEcuC/Com/ComConfig/MyECU_oCAN_Rx_63a3e3ce] */
  /*     6 */  /* [/ActiveEcuC/Com/ComConfig/msg_RxCycle_E2eProf1C_500_10_oCAN_e8110737_Rx, /ActiveEcuC/Com/ComConfig/MyECU_oCAN_Rx_63a3e3ce] */
  /*     7 */  /* [/ActiveEcuC/Com/ComConfig/msg_RxEvent_20_oCAN_a1df81ad_Rx, /ActiveEcuC/Com/ComConfig/MyECU_oCAN_Rx_63a3e3ce] */
  /*     8 */  /* [/ActiveEcuC/Com/ComConfig/msg_StartAppl_Rx_MyECU_oCAN_0da3c1e2_Rx, /ActiveEcuC/Com/ComConfig/MyECU_oCAN_Rx_63a3e3ce] */
  /*     9 */  /* [/ActiveEcuC/Com/ComConfig/pdu_RxDyn_64_5b4495a3_Rx, /ActiveEcuC/Com/ComConfig/MyECU_oFlexRay_Rx_bc06950a] */
  /*    10 */  /* [/ActiveEcuC/Com/ComConfig/pdu_RxStat_10_6aaa637c_Rx, /ActiveEcuC/Com/ComConfig/MyECU_oFlexRay_Rx_bc06950a] */
  /*    11 */  /* [/ActiveEcuC/Com/ComConfig/pdu_RxStat_30_589c01fe_Rx, /ActiveEcuC/Com/ComConfig/MyECU_oFlexRay_Rx_bc06950a] */

#define COM_STOP_SEC_VAR_NOINIT_8BIT
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */

/**********************************************************************************************************************
  Com_RxSigBufferArrayBased
**********************************************************************************************************************/
/** 
  \var    Com_RxSigBufferArrayBased
  \brief  Rx Signal and Group Signal Buffer. (UINT8_N, UINT8_DYN)
*/ 
#define COM_START_SEC_VAR_NOINIT_8BIT
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */
VAR(Com_RxSigBufferArrayBasedType, COM_VAR_NOINIT) Com_RxSigBufferArrayBased[12];  /* PRQA S 1514, 1533 */  /* MD_CSL_ObjectOnlyAccessedOnce */
  /* Index        Referable Keys */
  /*     0 */  /* [/ActiveEcuC/Com/ComConfig/signal_RxDyn_40_opdu_RxDyn_64_84de8797_Rx, /ActiveEcuC/Com/ComConfig/signal_RxDyn_40_opdu_RxDyn_64_84de8797_Rx_RxSignalBufferRouting] */
  /*   ... */  /* [/ActiveEcuC/Com/ComConfig/signal_RxDyn_40_opdu_RxDyn_64_84de8797_Rx, /ActiveEcuC/Com/ComConfig/signal_RxDyn_40_opdu_RxDyn_64_84de8797_Rx_RxSignalBufferRouting] */
  /*     4 */  /* [/ActiveEcuC/Com/ComConfig/signal_RxDyn_40_opdu_RxDyn_64_84de8797_Rx, /ActiveEcuC/Com/ComConfig/signal_RxDyn_40_opdu_RxDyn_64_84de8797_Rx_RxSignalBufferRouting] */
  /*     5 */  /* [/ActiveEcuC/Com/ComConfig/signal_RxStat_56bit_opdu_RxStat_30_32e21b42_Rx, /ActiveEcuC/Com/ComConfig/signal_RxStat_56bit_opdu_RxStat_30_32e21b42_Rx_RxSignalBufferRouting] */
  /*   ... */  /* [/ActiveEcuC/Com/ComConfig/signal_RxStat_56bit_opdu_RxStat_30_32e21b42_Rx, /ActiveEcuC/Com/ComConfig/signal_RxStat_56bit_opdu_RxStat_30_32e21b42_Rx_RxSignalBufferRouting] */
  /*    11 */  /* [/ActiveEcuC/Com/ComConfig/signal_RxStat_56bit_opdu_RxStat_30_32e21b42_Rx, /ActiveEcuC/Com/ComConfig/signal_RxStat_56bit_opdu_RxStat_30_32e21b42_Rx_RxSignalBufferRouting] */

#define COM_STOP_SEC_VAR_NOINIT_8BIT
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */

/**********************************************************************************************************************
  Com_RxSigBufferUInt16
**********************************************************************************************************************/
/** 
  \var    Com_RxSigBufferUInt16
  \brief  Rx Signal and Group Signal Buffer. (UINT16)
*/ 
#define COM_START_SEC_VAR_NOINIT_16BIT
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */
VAR(Com_RxSigBufferUInt16Type, COM_VAR_NOINIT) Com_RxSigBufferUInt16[6];  /* PRQA S 1514, 1533 */  /* MD_CSL_ObjectOnlyAccessedOnce */
  /* Index        Referable Keys */
  /*     0 */  /* [/ActiveEcuC/Com/ComConfig/Sig_LinTr_Slave3_1_oFrame_LinTr_Slave3_oLIN00_0174e108_Rx, /ActiveEcuC/Com/ComConfig/Sig_LinTr_Slave3_1_oFrame_LinTr_Slave3_oLIN00_0174e108_Rx_RxSignalBufferRouting] */
  /*     1 */  /* [/ActiveEcuC/Com/ComConfig/Signal_Rx11bit_OnWrite_omsg_RxEvent_20_oCAN_e14fb9df_Rx, /ActiveEcuC/Com/ComConfig/Signal_Rx11bit_OnWrite_omsg_RxEvent_20_oCAN_e14fb9df_Rx_RxSignalBufferRouting] */
  /*     2 */  /* [/ActiveEcuC/Com/ComConfig/Signal_Rx15bit_OnWrite_omsg_RxEvent_20_oCAN_3a85fa13_Rx, /ActiveEcuC/Com/ComConfig/Signal_Rx15bit_OnWrite_omsg_RxEvent_20_oCAN_3a85fa13_Rx_RxSignalBufferRouting] */
  /*     3 */  /* [/ActiveEcuC/Com/ComConfig/Signal_Rx16bit_Cyclic_omsg_RxCycle500_20_oCAN_300c1427_Rx, /ActiveEcuC/Com/ComConfig/Signal_Rx16bit_Cyclic_omsg_RxCycle500_20_oCAN_300c1427_Rx_RxSignalBufferRouting] */
  /*     4 */  /* [/ActiveEcuC/Com/ComConfig/StartAppl_Fr_BothECU_RX_Ctrl_oPDU_Fr_StartAppl_BothECU_RX_cb3aeb40_Rx, /ActiveEcuC/Com/ComConfig/StartAppl_Fr_BothECU_RX_Ctrl_oPDU_Fr_StartAppl_BothECU_RX_cb3aeb40_Rx_RxSignalBufferRouting] */
  /*     5 */  /* [/ActiveEcuC/Com/ComConfig/StartAppl_Fr_BothECU_RX_Data_oPDU_Fr_StartAppl_BothECU_RX_052b6b3f_Rx, /ActiveEcuC/Com/ComConfig/StartAppl_Fr_BothECU_RX_Data_oPDU_Fr_StartAppl_BothECU_RX_052b6b3f_Rx_RxSignalBufferRouting] */

#define COM_STOP_SEC_VAR_NOINIT_16BIT
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */

/**********************************************************************************************************************
  Com_RxSigBufferUInt32
**********************************************************************************************************************/
/** 
  \var    Com_RxSigBufferUInt32
  \brief  Rx Signal and Group Signal Buffer. (UINT32)
*/ 
#define COM_START_SEC_VAR_NOINIT_32BIT
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */
VAR(Com_RxSigBufferUInt32Type, COM_VAR_NOINIT) Com_RxSigBufferUInt32[5];  /* PRQA S 1514, 1533 */  /* MD_CSL_ObjectOnlyAccessedOnce */
  /* Index        Referable Keys */
  /*     0 */  /* [/ActiveEcuC/Com/ComConfig/SG_RgRx_Prof1C_RxCycle_omsg_RxCycle_E2eProf1C_500_10_oCAN_430c5af9_Rx/SigRx_Prof1C_Sig32Bit_omsg_RxCycle_E2eProf1C_500_10_oCAN_f0dd3f4d_Rx] */
  /*     1 */  /* [/ActiveEcuC/Com/ComConfig/SG_RgRx_Prof1C_RxCycle_omsg_RxCycle_E2eProf1C_500_10_oCAN_430c5af9_Rx/SigRx_Prof1C_Sig32Bit_omsg_RxCycle_E2eProf1C_500_10_oCAN_f0dd3f4d_Rx_RxGroupSignalShadowBuffer, /ActiveEcuC/Com/ComConfig/SG_RgRx_Prof1C_RxCycle_omsg_RxCycle_E2eProf1C_500_10_oCAN_430c5af9_Rx/SigRx_Prof1C_Sig32Bit_omsg_RxCycle_E2eProf1C_500_10_oCAN_f0dd3f4d_Rx_RxSignalBufferRouting] */
  /*     2 */  /* [/ActiveEcuC/Com/ComConfig/Signal_Rx20bit_Cyclic_omsg_RxCycle500_20_oCAN_983b15aa_Rx, /ActiveEcuC/Com/ComConfig/Signal_Rx20bit_Cyclic_omsg_RxCycle500_20_oCAN_983b15aa_Rx_RxSignalBufferRouting] */
  /*     3 */  /* [/ActiveEcuC/Com/ComConfig/signal_RxDyn_20_opdu_RxDyn_64_d74fee73_Rx, /ActiveEcuC/Com/ComConfig/signal_RxDyn_20_opdu_RxDyn_64_d74fee73_Rx_RxSignalBufferRouting] */
  /*     4 */  /* [/ActiveEcuC/Com/ComConfig/signal_RxStat_24bit_opdu_RxStat_10_9524d9a2_Rx, /ActiveEcuC/Com/ComConfig/signal_RxStat_24bit_opdu_RxStat_10_9524d9a2_Rx_RxSignalBufferRouting] */

#define COM_STOP_SEC_VAR_NOINIT_32BIT
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */

/**********************************************************************************************************************
  Com_RxSigBufferUInt8
**********************************************************************************************************************/
/** 
  \var    Com_RxSigBufferUInt8
  \brief  Rx Signal and Group Signal Buffer. (BOOLEAN, UINT8)
*/ 
#define COM_START_SEC_VAR_NOINIT_8BIT
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */
VAR(Com_RxSigBufferUInt8Type, COM_VAR_NOINIT) Com_RxSigBufferUInt8[22];  /* PRQA S 1514, 1533 */  /* MD_CSL_ObjectOnlyAccessedOnce */
  /* Index        Referable Keys */
  /*     0 */  /* [/ActiveEcuC/Com/ComConfig/SG_RgRx_Prof1C_RxCycle_omsg_RxCycle_E2eProf1C_500_10_oCAN_430c5af9_Rx/SigRx_Prof1C_CRC_omsg_RxCycle_E2eProf1C_500_10_oCAN_d6a2f34d_Rx] */
  /*     1 */  /* [/ActiveEcuC/Com/ComConfig/SG_RgRx_Prof1C_RxCycle_omsg_RxCycle_E2eProf1C_500_10_oCAN_430c5af9_Rx/SigRx_Prof1C_CRC_omsg_RxCycle_E2eProf1C_500_10_oCAN_d6a2f34d_Rx_RxGroupSignalShadowBuffer, /ActiveEcuC/Com/ComConfig/SG_RgRx_Prof1C_RxCycle_omsg_RxCycle_E2eProf1C_500_10_oCAN_430c5af9_Rx/SigRx_Prof1C_CRC_omsg_RxCycle_E2eProf1C_500_10_oCAN_d6a2f34d_Rx_RxSignalBufferRouting] */
  /*     2 */  /* [/ActiveEcuC/Com/ComConfig/SG_RgRx_Prof1C_RxCycle_omsg_RxCycle_E2eProf1C_500_10_oCAN_430c5af9_Rx/SigRx_Prof1C_DataId_HiByte_LoNib_omsg_RxCycle_E2eProf1C_500_10_oCAN_bdeb2c98_Rx] */
  /*     3 */  /* [/ActiveEcuC/Com/ComConfig/SG_RgRx_Prof1C_RxCycle_omsg_RxCycle_E2eProf1C_500_10_oCAN_430c5af9_Rx/SigRx_Prof1C_DataId_HiByte_LoNib_omsg_RxCycle_E2eProf1C_500_10_oCAN_bdeb2c98_Rx_RxGroupSignalShadowBuffer, /ActiveEcuC/Com/ComConfig/SG_RgRx_Prof1C_RxCycle_omsg_RxCycle_E2eProf1C_500_10_oCAN_430c5af9_Rx/SigRx_Prof1C_DataId_HiByte_LoNib_omsg_RxCycle_E2eProf1C_500_10_oCAN_bdeb2c98_Rx_RxSignalBufferRouting] */
  /*     4 */  /* [/ActiveEcuC/Com/ComConfig/SG_RgRx_Prof1C_RxCycle_omsg_RxCycle_E2eProf1C_500_10_oCAN_430c5af9_Rx/SigRx_Prof1C_SQ_omsg_RxCycle_E2eProf1C_500_10_oCAN_263a4149_Rx] */
  /*     5 */  /* [/ActiveEcuC/Com/ComConfig/SG_RgRx_Prof1C_RxCycle_omsg_RxCycle_E2eProf1C_500_10_oCAN_430c5af9_Rx/SigRx_Prof1C_SQ_omsg_RxCycle_E2eProf1C_500_10_oCAN_263a4149_Rx_RxGroupSignalShadowBuffer, /ActiveEcuC/Com/ComConfig/SG_RgRx_Prof1C_RxCycle_omsg_RxCycle_E2eProf1C_500_10_oCAN_430c5af9_Rx/SigRx_Prof1C_SQ_omsg_RxCycle_E2eProf1C_500_10_oCAN_263a4149_Rx_RxSignalBufferRouting] */
  /*     6 */  /* [/ActiveEcuC/Com/ComConfig/SG_RgRx_Prof1C_RxCycle_omsg_RxCycle_E2eProf1C_500_10_oCAN_430c5af9_Rx/SigRx_Prof1C_Sig3Bit_omsg_RxCycle_E2eProf1C_500_10_oCAN_95ddcbc9_Rx] */
  /*     7 */  /* [/ActiveEcuC/Com/ComConfig/SG_RgRx_Prof1C_RxCycle_omsg_RxCycle_E2eProf1C_500_10_oCAN_430c5af9_Rx/SigRx_Prof1C_Sig3Bit_omsg_RxCycle_E2eProf1C_500_10_oCAN_95ddcbc9_Rx_RxGroupSignalShadowBuffer, /ActiveEcuC/Com/ComConfig/SG_RgRx_Prof1C_RxCycle_omsg_RxCycle_E2eProf1C_500_10_oCAN_430c5af9_Rx/SigRx_Prof1C_Sig3Bit_omsg_RxCycle_E2eProf1C_500_10_oCAN_95ddcbc9_Rx_RxSignalBufferRouting] */
  /*     8 */  /* [/ActiveEcuC/Com/ComConfig/SG_RgRx_Prof1C_RxCycle_omsg_RxCycle_E2eProf1C_500_10_oCAN_430c5af9_Rx/SigRx_Prof1C_Sig4Bit_omsg_RxCycle_E2eProf1C_500_10_oCAN_a713ffd7_Rx] */
  /*     9 */  /* [/ActiveEcuC/Com/ComConfig/SG_RgRx_Prof1C_RxCycle_omsg_RxCycle_E2eProf1C_500_10_oCAN_430c5af9_Rx/SigRx_Prof1C_Sig4Bit_omsg_RxCycle_E2eProf1C_500_10_oCAN_a713ffd7_Rx_RxGroupSignalShadowBuffer, /ActiveEcuC/Com/ComConfig/SG_RgRx_Prof1C_RxCycle_omsg_RxCycle_E2eProf1C_500_10_oCAN_430c5af9_Rx/SigRx_Prof1C_Sig4Bit_omsg_RxCycle_E2eProf1C_500_10_oCAN_a713ffd7_Rx_RxSignalBufferRouting] */
  /*    10 */  /* [/ActiveEcuC/Com/ComConfig/Sig_ErrBit_Slave3_oFrame_LinTr_Slave3_oLIN00_7385f3a6_Rx, /ActiveEcuC/Com/ComConfig/Sig_ErrBit_Slave3_oFrame_LinTr_Slave3_oLIN00_7385f3a6_Rx_RxSignalBufferRouting] */
  /*    11 */  /* [/ActiveEcuC/Com/ComConfig/Sig_LinTr_Slave3_0_oFrame_LinTr_Slave3_oLIN00_00c11c15_Rx, /ActiveEcuC/Com/ComConfig/Sig_LinTr_Slave3_0_oFrame_LinTr_Slave3_oLIN00_00c11c15_Rx_RxSignalBufferRouting] */
  /*    12 */  /* [/ActiveEcuC/Com/ComConfig/Sig_StartAppl_LinData_Rx_oFrame_LinTr_RearECU_oLIN00_182ca40c_Rx, /ActiveEcuC/Com/ComConfig/Sig_StartAppl_LinData_Rx_oFrame_LinTr_RearECU_oLIN00_182ca40c_Rx_RxSignalBufferRouting] */
  /*    13 */  /* [/ActiveEcuC/Com/ComConfig/Signal_Dummy_oPDU_Dummy_RearECU_e3ccd573_Rx, /ActiveEcuC/Com/ComConfig/Signal_Dummy_oPDU_Dummy_RearECU_e3ccd573_Rx_RxSignalBufferRouting] */
  /*    14 */  /* [/ActiveEcuC/Com/ComConfig/Signal_RearInteriorLight_omsg_RxCycle100_0_oCAN_44115c66_Rx, /ActiveEcuC/Com/ComConfig/Signal_RearInteriorLight_omsg_RxCycle100_0_oCAN_44115c66_Rx_RxSignalBufferRouting] */
  /*    15 */  /* [/ActiveEcuC/Com/ComConfig/Signal_RearLeftDoor_omsg_RxCycle100_0_oCAN_47d3a185_Rx, /ActiveEcuC/Com/ComConfig/Signal_RearLeftDoor_omsg_RxCycle100_0_oCAN_47d3a185_Rx_RxSignalBufferRouting] */
  /*    16 */  /* [/ActiveEcuC/Com/ComConfig/Signal_RearRightDoor_omsg_RxCycle100_0_oCAN_cbde8ce7_Rx, /ActiveEcuC/Com/ComConfig/Signal_RearRightDoor_omsg_RxCycle100_0_oCAN_cbde8ce7_Rx_RxSignalBufferRouting] */
  /*    17 */  /* [/ActiveEcuC/Com/ComConfig/Signal_Rx8bit_Cyclic_omsg_RxCycle500_20_oCAN_f9e4bcc4_Rx, /ActiveEcuC/Com/ComConfig/Signal_Rx8bit_Cyclic_omsg_RxCycle500_20_oCAN_f9e4bcc4_Rx_RxSignalBufferRouting] */
  /*    18 */  /* [/ActiveEcuC/Com/ComConfig/Signal_StartAppl_RxCtrl_MyECU_omsg_StartAppl_Rx_MyECU_oCAN_09591065_Rx, /ActiveEcuC/Com/ComConfig/Signal_StartAppl_RxCtrl_MyECU_omsg_StartAppl_Rx_MyECU_oCAN_09591065_Rx_RxSignalBufferRouting] */
  /*    19 */  /* [/ActiveEcuC/Com/ComConfig/Signal_StartAppl_RxData_MyECU_omsg_StartAppl_Rx_MyECU_oCAN_315f9453_Rx, /ActiveEcuC/Com/ComConfig/Signal_StartAppl_RxData_MyECU_omsg_StartAppl_Rx_MyECU_oCAN_315f9453_Rx_RxSignalBufferRouting] */
  /*    20 */  /* [/ActiveEcuC/Com/ComConfig/signal_RxDyn_4bit_opdu_RxDyn_64_18777321_Rx, /ActiveEcuC/Com/ComConfig/signal_RxDyn_4bit_opdu_RxDyn_64_18777321_Rx_RxSignalBufferRouting] */
  /*    21 */  /* [/ActiveEcuC/Com/ComConfig/signal_RxStat_8bit_opdu_RxStat_10_469ce3e0_Rx, /ActiveEcuC/Com/ComConfig/signal_RxStat_8bit_opdu_RxStat_10_469ce3e0_Rx_RxSignalBufferRouting] */

#define COM_STOP_SEC_VAR_NOINIT_8BIT
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */

/**********************************************************************************************************************
  Com_SigGrpEventFlag
**********************************************************************************************************************/
/** 
  \var    Com_SigGrpEventFlag
  \brief  Flag is set if a group signal write access caused a triggered event.
*/ 
#define COM_START_SEC_VAR_NOINIT_8BIT
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */
VAR(Com_SigGrpEventFlagType, COM_VAR_NOINIT) Com_SigGrpEventFlag[1];  /* PRQA S 1514, 1533 */  /* MD_CSL_ObjectOnlyAccessedOnce */
#define COM_STOP_SEC_VAR_NOINIT_8BIT
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */

/**********************************************************************************************************************
  Com_TmpRxBuffer
**********************************************************************************************************************/
/** 
  \var    Com_TmpRxBuffer
  \brief  Temporary buffer for Rx UINT8_N and UINT8_DYN signals.
*/ 
#define COM_START_SEC_VAR_NOINIT_8BIT
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */
VAR(Com_TmpRxBufferType, COM_VAR_NOINIT) Com_TmpRxBuffer[7];  /* PRQA S 1514, 1533 */  /* MD_CSL_ObjectOnlyAccessedOnce */
#define COM_STOP_SEC_VAR_NOINIT_8BIT
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */

/**********************************************************************************************************************
  Com_TmpRxShdBufferUInt32
**********************************************************************************************************************/
/** 
  \var    Com_TmpRxShdBufferUInt32
  \brief  Temporary Rx Group Signal Shadow Buffer. (UINT32)
*/ 
#define COM_START_SEC_VAR_NOINIT_32BIT
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */
VAR(Com_TmpRxShdBufferUInt32UType, COM_VAR_NOINIT) Com_TmpRxShdBufferUInt32;  /* PRQA S 0759, 1514, 1533 */  /* MD_CSL_Union, MD_CSL_ObjectOnlyAccessedOnce, MD_CSL_ObjectOnlyAccessedOnce */
  /* Index        Referable Keys */
  /*     0 */  /* [/ActiveEcuC/Com/ComConfig/SG_RgRx_Prof1C_RxCycle_omsg_RxCycle_E2eProf1C_500_10_oCAN_430c5af9_Rx/SigRx_Prof1C_Sig32Bit_omsg_RxCycle_E2eProf1C_500_10_oCAN_f0dd3f4d_Rx] */

#define COM_STOP_SEC_VAR_NOINIT_32BIT
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */

/**********************************************************************************************************************
  Com_TmpRxShdBufferUInt8
**********************************************************************************************************************/
/** 
  \var    Com_TmpRxShdBufferUInt8
  \brief  Temporary Rx Group Signal Shadow Buffer. (BOOLEAN, UINT8)
*/ 
#define COM_START_SEC_VAR_NOINIT_8BIT
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */
VAR(Com_TmpRxShdBufferUInt8UType, COM_VAR_NOINIT) Com_TmpRxShdBufferUInt8;  /* PRQA S 0759, 1514, 1533 */  /* MD_CSL_Union, MD_CSL_ObjectOnlyAccessedOnce, MD_CSL_ObjectOnlyAccessedOnce */
  /* Index        Referable Keys */
  /*     0 */  /* [/ActiveEcuC/Com/ComConfig/SG_RgRx_Prof1C_RxCycle_omsg_RxCycle_E2eProf1C_500_10_oCAN_430c5af9_Rx/SigRx_Prof1C_CRC_omsg_RxCycle_E2eProf1C_500_10_oCAN_d6a2f34d_Rx] */
  /*     1 */  /* [/ActiveEcuC/Com/ComConfig/SG_RgRx_Prof1C_RxCycle_omsg_RxCycle_E2eProf1C_500_10_oCAN_430c5af9_Rx/SigRx_Prof1C_DataId_HiByte_LoNib_omsg_RxCycle_E2eProf1C_500_10_oCAN_bdeb2c98_Rx] */
  /*     2 */  /* [/ActiveEcuC/Com/ComConfig/SG_RgRx_Prof1C_RxCycle_omsg_RxCycle_E2eProf1C_500_10_oCAN_430c5af9_Rx/SigRx_Prof1C_Sig3Bit_omsg_RxCycle_E2eProf1C_500_10_oCAN_95ddcbc9_Rx] */
  /*     3 */  /* [/ActiveEcuC/Com/ComConfig/SG_RgRx_Prof1C_RxCycle_omsg_RxCycle_E2eProf1C_500_10_oCAN_430c5af9_Rx/SigRx_Prof1C_SQ_omsg_RxCycle_E2eProf1C_500_10_oCAN_263a4149_Rx] */
  /*     4 */  /* [/ActiveEcuC/Com/ComConfig/SG_RgRx_Prof1C_RxCycle_omsg_RxCycle_E2eProf1C_500_10_oCAN_430c5af9_Rx/SigRx_Prof1C_Sig4Bit_omsg_RxCycle_E2eProf1C_500_10_oCAN_a713ffd7_Rx] */

#define COM_STOP_SEC_VAR_NOINIT_8BIT
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */

/**********************************************************************************************************************
  Com_TransmitRequest
**********************************************************************************************************************/
/** 
  \var    Com_TransmitRequest
  \brief  Transmit request flag used for decoupled Tx I-PDU tranmission.
*/ 
#define COM_START_SEC_VAR_NOINIT_8BIT
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */
VAR(Com_TransmitRequestType, COM_VAR_NOINIT) Com_TransmitRequest[15];  /* PRQA S 1514, 1533 */  /* MD_CSL_ObjectOnlyAccessedOnce */
  /* Index        Referable Keys */
  /*     0 */  /* [/ActiveEcuC/Com/ComConfig/Frame_LinTr_MyECU2_oLIN00_3a9f0ce6_Tx, /ActiveEcuC/Com/ComConfig/MyECU_oLIN00_Tx_f02a8aba] */
  /*     1 */  /* [/ActiveEcuC/Com/ComConfig/Frame_LinTr_MyECU_oLIN00_ce6095e4_Tx, /ActiveEcuC/Com/ComConfig/MyECU_oLIN00_Tx_f02a8aba] */
  /*     2 */  /* [/ActiveEcuC/Com/ComConfig/PDU_Fr_StartAppl_MyECU_TX_44b0b044_Tx, /ActiveEcuC/Com/ComConfig/MyECU_oFlexRay_Tx_ea5c328c] */
  /*     3 */  /* [/ActiveEcuC/Com/ComConfig/PDU_Transmit_MyECU_05398c7a_Tx, /ActiveEcuC/Com/ComConfig/MyECU_oFlexRay_Tx_ea5c328c] */
  /*     4 */  /* [/ActiveEcuC/Com/ComConfig/PDU_nm_MyECU_Fr_ae963333_Tx] */
  /*     5 */  /* [/ActiveEcuC/Com/ComConfig/msg_StartAppl_Tx_MyECU_oCAN_ad461e3f_Tx, /ActiveEcuC/Com/ComConfig/MyECU_oCAN_Tx_35f94448] */
  /*     6 */  /* [/ActiveEcuC/Com/ComConfig/msg_TxCycle10_0_oCAN_85bf3e37_Tx, /ActiveEcuC/Com/ComConfig/MyECU_oCAN_Tx_35f94448] */
  /*     7 */  /* [/ActiveEcuC/Com/ComConfig/msg_TxCycle1000_10_oCAN_d74aed68_Tx, /ActiveEcuC/Com/ComConfig/MyECU_oCAN_Tx_35f94448] */
  /*     8 */  /* [/ActiveEcuC/Com/ComConfig/msg_TxCycle_E2eProf1C_500_30_oCAN_eeefc687_Tx, /ActiveEcuC/Com/ComConfig/MyECU_oCAN_Tx_35f94448] */
  /*     9 */  /* [/ActiveEcuC/Com/ComConfig/msg_TxEvent_10_oCAN_b2cd4fc2_Tx, /ActiveEcuC/Com/ComConfig/MyECU_oCAN_Tx_35f94448] */
  /*    10 */  /* [/ActiveEcuC/Com/ComConfig/msg_nm_MyECU_oCAN_c97b60cc_Tx] */
  /*    11 */  /* [/ActiveEcuC/Com/ComConfig/pdu_TxDyn_16_3c646bcf_Tx, /ActiveEcuC/Com/ComConfig/MyECU_oFlexRay_Tx_ea5c328c] */
  /*    12 */  /* [/ActiveEcuC/Com/ComConfig/pdu_TxDyn_64_9d2b9c24_Tx, /ActiveEcuC/Com/ComConfig/MyECU_oFlexRay_Tx_ea5c328c] */
  /*    13 */  /* [/ActiveEcuC/Com/ComConfig/pdu_TxStat_40_64c7eeb3_Tx, /ActiveEcuC/Com/ComConfig/MyECU_oFlexRay_Tx_ea5c328c] */
  /*    14 */  /* [/ActiveEcuC/Com/ComConfig/pdu_TxStat_64_519c4828_Tx, /ActiveEcuC/Com/ComConfig/MyECU_oFlexRay_Tx_ea5c328c] */

#define COM_STOP_SEC_VAR_NOINIT_8BIT
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */

/**********************************************************************************************************************
  Com_TxBuffer
**********************************************************************************************************************/
/** 
  \var    Com_TxBuffer
  \brief  Shared uint8 buffer for Tx I-PDUs and ComSignalGroup shadow buffer.
*/ 
#define COM_START_SEC_VAR_NOINIT_8BIT
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */
VAR(Com_TxBufferType, COM_VAR_NOINIT) Com_TxBuffer[160];  /* PRQA S 1514, 1533 */  /* MD_CSL_ObjectOnlyAccessedOnce */
  /* Index        Referable Keys */
  /*     0 */  /* [/ActiveEcuC/Com/ComConfig/Frame_LinTr_MyECU2_oLIN00_3a9f0ce6_Tx, /ActiveEcuC/Com/ComConfig/Sig_LinTr_MyECU2_0_oFrame_LinTr_MyECU2_oLIN00_9bce56ad_Tx, /ActiveEcuC/Com/ComConfig/Sig_LinTr_MyECU2_1_oFrame_LinTr_MyECU2_oLIN00_9a7babb0_Tx] */
  /*     1 */  /* [/ActiveEcuC/Com/ComConfig/Frame_LinTr_MyECU2_oLIN00_3a9f0ce6_Tx, /ActiveEcuC/Com/ComConfig/Sig_LinTr_MyECU2_1_oFrame_LinTr_MyECU2_oLIN00_9a7babb0_Tx] */
  /*     2 */  /* [/ActiveEcuC/Com/ComConfig/Frame_LinTr_MyECU2_oLIN00_3a9f0ce6_Tx, /ActiveEcuC/Com/ComConfig/Sig_LinTr_MyECU2_1_oFrame_LinTr_MyECU2_oLIN00_9a7babb0_Tx] */
  /*     3 */  /* [/ActiveEcuC/Com/ComConfig/Frame_LinTr_MyECU_oLIN00_ce6095e4_Tx, /ActiveEcuC/Com/ComConfig/Sig_StartAppl_LinData_Tx_oFrame_LinTr_MyECU_oLIN00_d383dc6b_Tx] */
  /*     4 */  /* [/ActiveEcuC/Com/ComConfig/PDU_Fr_StartAppl_MyECU_TX_44b0b044_Tx, /ActiveEcuC/Com/ComConfig/StartAppl_Fr_MyECU_TX_Ctrl_oPDU_Fr_StartAppl_MyECU_TX_644d68c7_Tx] */
  /*     5 */  /* [/ActiveEcuC/Com/ComConfig/PDU_Fr_StartAppl_MyECU_TX_44b0b044_Tx, /ActiveEcuC/Com/ComConfig/StartAppl_Fr_MyECU_TX_Ctrl_oPDU_Fr_StartAppl_MyECU_TX_644d68c7_Tx] */
  /*     6 */  /* [/ActiveEcuC/Com/ComConfig/PDU_Fr_StartAppl_MyECU_TX_44b0b044_Tx, /ActiveEcuC/Com/ComConfig/StartAppl_Fr_MyECU_TX_Data_oPDU_Fr_StartAppl_MyECU_TX_6a5990c2_Tx] */
  /*     7 */  /* [/ActiveEcuC/Com/ComConfig/PDU_Fr_StartAppl_MyECU_TX_44b0b044_Tx, /ActiveEcuC/Com/ComConfig/StartAppl_Fr_MyECU_TX_Data_oPDU_Fr_StartAppl_MyECU_TX_6a5990c2_Tx] */
  /*     8 */  /* [/ActiveEcuC/Com/ComConfig/PDU_Fr_StartAppl_MyECU_TX_44b0b044_Tx] */
  /*   ... */  /* [/ActiveEcuC/Com/ComConfig/PDU_Fr_StartAppl_MyECU_TX_44b0b044_Tx] */
  /*    19 */  /* [/ActiveEcuC/Com/ComConfig/PDU_Fr_StartAppl_MyECU_TX_44b0b044_Tx] */
  /*    20 */  /* [/ActiveEcuC/Com/ComConfig/PDU_Transmit_MyECU_05398c7a_Tx, /ActiveEcuC/Com/ComConfig/Signal_SomeTxSignal_oPDU_Transmit_MyECU_763437bb_Tx] */
  /*   ... */  /* [/ActiveEcuC/Com/ComConfig/PDU_Transmit_MyECU_05398c7a_Tx, /ActiveEcuC/Com/ComConfig/Signal_SomeTxSignal_oPDU_Transmit_MyECU_763437bb_Tx] */
  /*    23 */  /* [/ActiveEcuC/Com/ComConfig/PDU_Transmit_MyECU_05398c7a_Tx, /ActiveEcuC/Com/ComConfig/Signal_SomeTxSignal_oPDU_Transmit_MyECU_763437bb_Tx] */
  /*    24 */  /* [/ActiveEcuC/Com/ComConfig/PDU_Transmit_MyECU_05398c7a_Tx] */
  /*   ... */  /* [/ActiveEcuC/Com/ComConfig/PDU_Transmit_MyECU_05398c7a_Tx] */
  /*    27 */  /* [/ActiveEcuC/Com/ComConfig/PDU_Transmit_MyECU_05398c7a_Tx] */
  /*    28 */  /* [/ActiveEcuC/Com/ComConfig/PDU_nm_MyECU_Fr_ae963333_Tx] */
  /*    29 */  /* [/ActiveEcuC/Com/ComConfig/PDU_nm_MyECU_Fr_ae963333_Tx] */
  /*    30 */  /* [/ActiveEcuC/Com/ComConfig/PDU_nm_MyECU_Fr_ae963333_Tx, /ActiveEcuC/Com/ComConfig/Nm_MyECU_UserData_oPDU_nm_MyECU_Fr_82c22536_Tx] */
  /*   ... */  /* [/ActiveEcuC/Com/ComConfig/PDU_nm_MyECU_Fr_ae963333_Tx, /ActiveEcuC/Com/ComConfig/Nm_MyECU_UserData_oPDU_nm_MyECU_Fr_82c22536_Tx] */
  /*    33 */  /* [/ActiveEcuC/Com/ComConfig/PDU_nm_MyECU_Fr_ae963333_Tx, /ActiveEcuC/Com/ComConfig/Nm_MyECU_UserData_oPDU_nm_MyECU_Fr_82c22536_Tx] */
  /*    34 */  /* [/ActiveEcuC/Com/ComConfig/Nm_MyECU_UserData_oPDU_nm_MyECU_Fr_82c22536_Tx] */
  /*    35 */  /* [/ActiveEcuC/Com/ComConfig/Nm_MyECU_UserData_oPDU_nm_MyECU_Fr_82c22536_Tx] */
  /*    36 */  /* [/ActiveEcuC/Com/ComConfig/msg_StartAppl_Tx_MyECU_oCAN_ad461e3f_Tx, /ActiveEcuC/Com/ComConfig/Signal_StartAppl_TxCtrl_MyECU_omsg_StartAppl_Tx_MyECU_oCAN_b45ea6bd_Tx] */
  /*    37 */  /* [/ActiveEcuC/Com/ComConfig/msg_StartAppl_Tx_MyECU_oCAN_ad461e3f_Tx, /ActiveEcuC/Com/ComConfig/Signal_StartAppl_TxData_MyECU_omsg_StartAppl_Tx_MyECU_oCAN_8c58228b_Tx] */
  /*    38 */  /* [/ActiveEcuC/Com/ComConfig/msg_StartAppl_Tx_MyECU_oCAN_ad461e3f_Tx] */
  /*   ... */  /* [/ActiveEcuC/Com/ComConfig/msg_StartAppl_Tx_MyECU_oCAN_ad461e3f_Tx] */
  /*    99 */  /* [/ActiveEcuC/Com/ComConfig/msg_StartAppl_Tx_MyECU_oCAN_ad461e3f_Tx] */
  /*   100 */  /* [/ActiveEcuC/Com/ComConfig/msg_TxCycle10_0_oCAN_85bf3e37_Tx, /ActiveEcuC/Com/ComConfig/Signal_Tx32bit_Cyclic_omsg_TxCycle10_0_oCAN_ab90e6e2_Tx] */
  /*   ... */  /* [/ActiveEcuC/Com/ComConfig/msg_TxCycle10_0_oCAN_85bf3e37_Tx, /ActiveEcuC/Com/ComConfig/Signal_Tx32bit_Cyclic_omsg_TxCycle10_0_oCAN_ab90e6e2_Tx] */
  /*   103 */  /* [/ActiveEcuC/Com/ComConfig/msg_TxCycle10_0_oCAN_85bf3e37_Tx, /ActiveEcuC/Com/ComConfig/Signal_Tx32bit_Cyclic_omsg_TxCycle10_0_oCAN_ab90e6e2_Tx] */
  /*   104 */  /* [/ActiveEcuC/Com/ComConfig/msg_TxCycle10_0_oCAN_85bf3e37_Tx, /ActiveEcuC/Com/ComConfig/Signal_Tx24bit_Cyclic_omsg_TxCycle10_0_oCAN_393dd5fe_Tx] */
  /*   ... */  /* [/ActiveEcuC/Com/ComConfig/msg_TxCycle10_0_oCAN_85bf3e37_Tx, /ActiveEcuC/Com/ComConfig/Signal_Tx24bit_Cyclic_omsg_TxCycle10_0_oCAN_393dd5fe_Tx] */
  /*   106 */  /* [/ActiveEcuC/Com/ComConfig/msg_TxCycle10_0_oCAN_85bf3e37_Tx, /ActiveEcuC/Com/ComConfig/Signal_Tx24bit_Cyclic_omsg_TxCycle10_0_oCAN_393dd5fe_Tx] */
  /*   107 */  /* [/ActiveEcuC/Com/ComConfig/msg_TxCycle1000_10_oCAN_d74aed68_Tx, /ActiveEcuC/Com/ComConfig/Signal_Tx10bit_Cyclic_omsg_TxCycle1000_10_oCAN_9539efc7_Tx] */
  /*   108 */  /* [/ActiveEcuC/Com/ComConfig/msg_TxCycle1000_10_oCAN_d74aed68_Tx, /ActiveEcuC/Com/ComConfig/Signal_Tx4bit_Cyclic_omsg_TxCycle1000_10_oCAN_ca549591_Tx, /ActiveEcuC/Com/ComConfig/Signal_Tx10bit_Cyclic_omsg_TxCycle1000_10_oCAN_9539efc7_Tx] */
  /*   109 */  /* [/ActiveEcuC/Com/ComConfig/msg_TxCycle_E2eProf1C_500_30_oCAN_eeefc687_Tx, /ActiveEcuC/Com/ComConfig/SG_SgTx_Prof1C_TxCycle_omsg_TxCycle_E2eProf1C_500_30_oCAN_9bfb2a76_TxSigGrpInTxIPDU] */
  /*   ... */  /* [/ActiveEcuC/Com/ComConfig/msg_TxCycle_E2eProf1C_500_30_oCAN_eeefc687_Tx, /ActiveEcuC/Com/ComConfig/SG_SgTx_Prof1C_TxCycle_omsg_TxCycle_E2eProf1C_500_30_oCAN_9bfb2a76_TxSigGrpInTxIPDU] */
  /*   115 */  /* [/ActiveEcuC/Com/ComConfig/msg_TxCycle_E2eProf1C_500_30_oCAN_eeefc687_Tx, /ActiveEcuC/Com/ComConfig/SG_SgTx_Prof1C_TxCycle_omsg_TxCycle_E2eProf1C_500_30_oCAN_9bfb2a76_TxSigGrpInTxIPDU] */
  /*   116 */  /* [/ActiveEcuC/Com/ComConfig/msg_TxCycle_E2eProf1C_500_30_oCAN_eeefc687_Tx] */
  /*   117 */  /* [/ActiveEcuC/Com/ComConfig/msg_TxEvent_10_oCAN_b2cd4fc2_Tx, /ActiveEcuC/Com/ComConfig/Signal_Tx32bit_OnWrite_omsg_TxEvent_10_oCAN_298e9335_Tx] */
  /*   ... */  /* [/ActiveEcuC/Com/ComConfig/msg_TxEvent_10_oCAN_b2cd4fc2_Tx, /ActiveEcuC/Com/ComConfig/Signal_Tx32bit_OnWrite_omsg_TxEvent_10_oCAN_298e9335_Tx] */
  /*   120 */  /* [/ActiveEcuC/Com/ComConfig/msg_TxEvent_10_oCAN_b2cd4fc2_Tx, /ActiveEcuC/Com/ComConfig/Signal_Tx32bit_OnWrite_omsg_TxEvent_10_oCAN_298e9335_Tx] */
  /*   121 */  /* [/ActiveEcuC/Com/ComConfig/msg_TxEvent_10_oCAN_b2cd4fc2_Tx, /ActiveEcuC/Com/ComConfig/Signal_Tx24bit_OnWrite_omsg_TxEvent_10_oCAN_bb23a029_Tx] */
  /*   ... */  /* [/ActiveEcuC/Com/ComConfig/msg_TxEvent_10_oCAN_b2cd4fc2_Tx, /ActiveEcuC/Com/ComConfig/Signal_Tx24bit_OnWrite_omsg_TxEvent_10_oCAN_bb23a029_Tx] */
  /*   123 */  /* [/ActiveEcuC/Com/ComConfig/msg_TxEvent_10_oCAN_b2cd4fc2_Tx, /ActiveEcuC/Com/ComConfig/Signal_Tx24bit_OnWrite_omsg_TxEvent_10_oCAN_bb23a029_Tx] */
  /*   124 */  /* [/ActiveEcuC/Com/ComConfig/msg_nm_MyECU_oCAN_c97b60cc_Tx, /ActiveEcuC/Com/ComConfig/NmUd_MyECU_16bit_omsg_nm_MyECU_oCAN_3d555341_Tx] */
  /*   125 */  /* [/ActiveEcuC/Com/ComConfig/msg_nm_MyECU_oCAN_c97b60cc_Tx, /ActiveEcuC/Com/ComConfig/NmUd_MyECU_16bit_omsg_nm_MyECU_oCAN_3d555341_Tx] */
  /*   126 */  /* [/ActiveEcuC/Com/ComConfig/msg_nm_MyECU_oCAN_c97b60cc_Tx, /ActiveEcuC/Com/ComConfig/NmUd_MyECU_32bit_omsg_nm_MyECU_oCAN_e4323edd_Tx] */
  /*   ... */  /* [/ActiveEcuC/Com/ComConfig/msg_nm_MyECU_oCAN_c97b60cc_Tx, /ActiveEcuC/Com/ComConfig/NmUd_MyECU_32bit_omsg_nm_MyECU_oCAN_e4323edd_Tx] */
  /*   129 */  /* [/ActiveEcuC/Com/ComConfig/msg_nm_MyECU_oCAN_c97b60cc_Tx, /ActiveEcuC/Com/ComConfig/NmUd_MyECU_32bit_omsg_nm_MyECU_oCAN_e4323edd_Tx] */
  /* Index        Referable Keys */
  /*   130 */  /* [/ActiveEcuC/Com/ComConfig/pdu_TxDyn_16_3c646bcf_Tx, /ActiveEcuC/Com/ComConfig/signal_TxDyn_16bit_opdu_TxDyn_16_bfe7058b_Tx] */
  /*   131 */  /* [/ActiveEcuC/Com/ComConfig/pdu_TxDyn_16_3c646bcf_Tx, /ActiveEcuC/Com/ComConfig/signal_TxDyn_16bit_opdu_TxDyn_16_bfe7058b_Tx] */
  /*   132 */  /* [/ActiveEcuC/Com/ComConfig/pdu_TxDyn_64_9d2b9c24_Tx, /ActiveEcuC/Com/ComConfig/signal_TxDyn_32bit_opdu_TxDyn_64_61b42397_Tx] */
  /*   ... */  /* [/ActiveEcuC/Com/ComConfig/pdu_TxDyn_64_9d2b9c24_Tx, /ActiveEcuC/Com/ComConfig/signal_TxDyn_32bit_opdu_TxDyn_64_61b42397_Tx] */
  /*   135 */  /* [/ActiveEcuC/Com/ComConfig/pdu_TxDyn_64_9d2b9c24_Tx, /ActiveEcuC/Com/ComConfig/signal_TxDyn_32bit_opdu_TxDyn_64_61b42397_Tx] */
  /*   136 */  /* [/ActiveEcuC/Com/ComConfig/pdu_TxDyn_64_9d2b9c24_Tx, /ActiveEcuC/Com/ComConfig/pdu_TxDyn_32bit_2_opdu_TxDyn_64_65bb7ebd_Tx] */
  /*   ... */  /* [/ActiveEcuC/Com/ComConfig/pdu_TxDyn_64_9d2b9c24_Tx, /ActiveEcuC/Com/ComConfig/pdu_TxDyn_32bit_2_opdu_TxDyn_64_65bb7ebd_Tx] */
  /*   139 */  /* [/ActiveEcuC/Com/ComConfig/pdu_TxDyn_64_9d2b9c24_Tx, /ActiveEcuC/Com/ComConfig/pdu_TxDyn_32bit_2_opdu_TxDyn_64_65bb7ebd_Tx] */
  /*   140 */  /* [/ActiveEcuC/Com/ComConfig/pdu_TxStat_40_64c7eeb3_Tx, /ActiveEcuC/Com/ComConfig/signal_TxStat_8bit_opdu_TxStat_40_772fb39e_Tx] */
  /*   141 */  /* [/ActiveEcuC/Com/ComConfig/pdu_TxStat_40_64c7eeb3_Tx, /ActiveEcuC/Com/ComConfig/signal_TxStat_32bit_opdu_TxStat_40_dfab39ff_Tx] */
  /*   ... */  /* [/ActiveEcuC/Com/ComConfig/pdu_TxStat_40_64c7eeb3_Tx, /ActiveEcuC/Com/ComConfig/signal_TxStat_32bit_opdu_TxStat_40_dfab39ff_Tx] */
  /*   144 */  /* [/ActiveEcuC/Com/ComConfig/pdu_TxStat_40_64c7eeb3_Tx, /ActiveEcuC/Com/ComConfig/signal_TxStat_32bit_opdu_TxStat_40_dfab39ff_Tx] */
  /*   145 */  /* [/ActiveEcuC/Com/ComConfig/pdu_TxStat_64_519c4828_Tx, /ActiveEcuC/Com/ComConfig/signal_TxStat_24bit_opdu_TxStat_64_124baf31_Tx] */
  /*   ... */  /* [/ActiveEcuC/Com/ComConfig/pdu_TxStat_64_519c4828_Tx, /ActiveEcuC/Com/ComConfig/signal_TxStat_24bit_opdu_TxStat_64_124baf31_Tx] */
  /*   147 */  /* [/ActiveEcuC/Com/ComConfig/pdu_TxStat_64_519c4828_Tx, /ActiveEcuC/Com/ComConfig/signal_TxStat_24bit_opdu_TxStat_64_124baf31_Tx] */
  /*   148 */  /* [/ActiveEcuC/Com/ComConfig/pdu_TxStat_64_519c4828_Tx, /ActiveEcuC/Com/ComConfig/signal_TxStat_40bit_opdu_TxStat_64_7f003f06_Tx] */
  /*   ... */  /* [/ActiveEcuC/Com/ComConfig/pdu_TxStat_64_519c4828_Tx, /ActiveEcuC/Com/ComConfig/signal_TxStat_40bit_opdu_TxStat_64_7f003f06_Tx] */
  /*   152 */  /* [/ActiveEcuC/Com/ComConfig/pdu_TxStat_64_519c4828_Tx, /ActiveEcuC/Com/ComConfig/signal_TxStat_40bit_opdu_TxStat_64_7f003f06_Tx] */
  /*   153 */  /* [/ActiveEcuC/Com/ComConfig/SG_SgTx_Prof1C_TxCycle_omsg_TxCycle_E2eProf1C_500_30_oCAN_9bfb2a76_Tx, /ActiveEcuC/Com/ComConfig/SG_SgTx_Prof1C_TxCycle_omsg_TxCycle_E2eProf1C_500_30_oCAN_9bfb2a76_Tx/SigTx_Prof1C_CRC_omsg_TxCycle_E2eProf1C_500_30_oCAN_5df85835_Tx] */
  /*   154 */  /* [/ActiveEcuC/Com/ComConfig/SG_SgTx_Prof1C_TxCycle_omsg_TxCycle_E2eProf1C_500_30_oCAN_9bfb2a76_Tx, /ActiveEcuC/Com/ComConfig/SG_SgTx_Prof1C_TxCycle_omsg_TxCycle_E2eProf1C_500_30_oCAN_9bfb2a76_Tx/SigTx_Prof1C_SQ_omsg_TxCycle_E2eProf1C_500_30_oCAN_8ca3bde6_Tx, /ActiveEcuC/Com/ComConfig/SG_SgTx_Prof1C_TxCycle_omsg_TxCycle_E2eProf1C_500_30_oCAN_9bfb2a76_Tx/SigTx_Prof1C_DataId_HiByte_LoNib_omsg_TxCycle_E2eProf1C_500_30_oCAN_2f490cd4_Tx] */
  /*   155 */  /* [/ActiveEcuC/Com/ComConfig/SG_SgTx_Prof1C_TxCycle_omsg_TxCycle_E2eProf1C_500_30_oCAN_9bfb2a76_Tx, /ActiveEcuC/Com/ComConfig/SG_SgTx_Prof1C_TxCycle_omsg_TxCycle_E2eProf1C_500_30_oCAN_9bfb2a76_Tx/SigTx_Prof1C_Sig4Bit_omsg_TxCycle_E2eProf1C_500_30_oCAN_0e363008_Tx] */
  /*   156 */  /* [/ActiveEcuC/Com/ComConfig/SG_SgTx_Prof1C_TxCycle_omsg_TxCycle_E2eProf1C_500_30_oCAN_9bfb2a76_Tx, /ActiveEcuC/Com/ComConfig/SG_SgTx_Prof1C_TxCycle_omsg_TxCycle_E2eProf1C_500_30_oCAN_9bfb2a76_Tx/SigTx_Prof1C_Sig2Bit_omsg_TxCycle_E2eProf1C_500_30_oCAN_72710fbf_Tx] */
  /*   157 */  /* [/ActiveEcuC/Com/ComConfig/SG_SgTx_Prof1C_TxCycle_omsg_TxCycle_E2eProf1C_500_30_oCAN_9bfb2a76_Tx, /ActiveEcuC/Com/ComConfig/SG_SgTx_Prof1C_TxCycle_omsg_TxCycle_E2eProf1C_500_30_oCAN_9bfb2a76_Tx/SigTx_Prof1C_Sig16Bit_omsg_TxCycle_E2eProf1C_500_30_oCAN_4f2c5310_Tx] */
  /*   158 */  /* [/ActiveEcuC/Com/ComConfig/SG_SgTx_Prof1C_TxCycle_omsg_TxCycle_E2eProf1C_500_30_oCAN_9bfb2a76_Tx, /ActiveEcuC/Com/ComConfig/SG_SgTx_Prof1C_TxCycle_omsg_TxCycle_E2eProf1C_500_30_oCAN_9bfb2a76_Tx/SigTx_Prof1C_Sig16Bit_omsg_TxCycle_E2eProf1C_500_30_oCAN_4f2c5310_Tx] */
  /*   159 */  /* [/ActiveEcuC/Com/ComConfig/SG_SgTx_Prof1C_TxCycle_omsg_TxCycle_E2eProf1C_500_30_oCAN_9bfb2a76_Tx, /ActiveEcuC/Com/ComConfig/SG_SgTx_Prof1C_TxCycle_omsg_TxCycle_E2eProf1C_500_30_oCAN_9bfb2a76_Tx/SigTx_Prof1C_Sig8Bit_omsg_TxCycle_E2eProf1C_500_30_oCAN_f6b84f66_Tx] */

#define COM_STOP_SEC_VAR_NOINIT_8BIT
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */

/**********************************************************************************************************************
  Com_TxCyclicProcessingISRLockCounter
**********************************************************************************************************************/
#define COM_START_SEC_VAR_NOINIT_16BIT
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */
VAR(Com_TxCyclicProcessingISRLockCounterType, COM_VAR_NOINIT) Com_TxCyclicProcessingISRLockCounter;  /* PRQA S 1514, 1533 */  /* MD_CSL_ObjectOnlyAccessedOnce */
#define COM_STOP_SEC_VAR_NOINIT_16BIT
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */

/**********************************************************************************************************************
  Com_TxIPduGroupISRLockCounter
**********************************************************************************************************************/
#define COM_START_SEC_VAR_NOINIT_16BIT
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */
VAR(Com_TxIPduGroupISRLockCounterType, COM_VAR_NOINIT) Com_TxIPduGroupISRLockCounter;  /* PRQA S 1514, 1533 */  /* MD_CSL_ObjectOnlyAccessedOnce */
#define COM_STOP_SEC_VAR_NOINIT_16BIT
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */

/**********************************************************************************************************************
  Com_TxPduGrpActive
**********************************************************************************************************************/
/** 
  \var    Com_TxPduGrpActive
  \brief  Tx I-PDU based state (started/stopped) of the corresponding I-PDU-Group.
*/ 
#define COM_START_SEC_VAR_NOINIT_8BIT
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */
VAR(Com_TxPduGrpActiveType, COM_VAR_NOINIT) Com_TxPduGrpActive[15];  /* PRQA S 1514, 1533 */  /* MD_CSL_ObjectOnlyAccessedOnce */
  /* Index        Referable Keys */
  /*     0 */  /* [/ActiveEcuC/Com/ComConfig/Frame_LinTr_MyECU2_oLIN00_3a9f0ce6_Tx, /ActiveEcuC/Com/ComConfig/MyECU_oLIN00_Tx_f02a8aba] */
  /*     1 */  /* [/ActiveEcuC/Com/ComConfig/Frame_LinTr_MyECU_oLIN00_ce6095e4_Tx, /ActiveEcuC/Com/ComConfig/MyECU_oLIN00_Tx_f02a8aba] */
  /*     2 */  /* [/ActiveEcuC/Com/ComConfig/PDU_Fr_StartAppl_MyECU_TX_44b0b044_Tx, /ActiveEcuC/Com/ComConfig/MyECU_oFlexRay_Tx_ea5c328c] */
  /*     3 */  /* [/ActiveEcuC/Com/ComConfig/PDU_Transmit_MyECU_05398c7a_Tx, /ActiveEcuC/Com/ComConfig/MyECU_oFlexRay_Tx_ea5c328c] */
  /*     4 */  /* [/ActiveEcuC/Com/ComConfig/PDU_nm_MyECU_Fr_ae963333_Tx] */
  /*     5 */  /* [/ActiveEcuC/Com/ComConfig/msg_StartAppl_Tx_MyECU_oCAN_ad461e3f_Tx, /ActiveEcuC/Com/ComConfig/MyECU_oCAN_Tx_35f94448] */
  /*     6 */  /* [/ActiveEcuC/Com/ComConfig/msg_TxCycle10_0_oCAN_85bf3e37_Tx, /ActiveEcuC/Com/ComConfig/MyECU_oCAN_Tx_35f94448] */
  /*     7 */  /* [/ActiveEcuC/Com/ComConfig/msg_TxCycle1000_10_oCAN_d74aed68_Tx, /ActiveEcuC/Com/ComConfig/MyECU_oCAN_Tx_35f94448] */
  /*     8 */  /* [/ActiveEcuC/Com/ComConfig/msg_TxCycle_E2eProf1C_500_30_oCAN_eeefc687_Tx, /ActiveEcuC/Com/ComConfig/MyECU_oCAN_Tx_35f94448] */
  /*     9 */  /* [/ActiveEcuC/Com/ComConfig/msg_TxEvent_10_oCAN_b2cd4fc2_Tx, /ActiveEcuC/Com/ComConfig/MyECU_oCAN_Tx_35f94448] */
  /*    10 */  /* [/ActiveEcuC/Com/ComConfig/msg_nm_MyECU_oCAN_c97b60cc_Tx] */
  /*    11 */  /* [/ActiveEcuC/Com/ComConfig/pdu_TxDyn_16_3c646bcf_Tx, /ActiveEcuC/Com/ComConfig/MyECU_oFlexRay_Tx_ea5c328c] */
  /*    12 */  /* [/ActiveEcuC/Com/ComConfig/pdu_TxDyn_64_9d2b9c24_Tx, /ActiveEcuC/Com/ComConfig/MyECU_oFlexRay_Tx_ea5c328c] */
  /*    13 */  /* [/ActiveEcuC/Com/ComConfig/pdu_TxStat_40_64c7eeb3_Tx, /ActiveEcuC/Com/ComConfig/MyECU_oFlexRay_Tx_ea5c328c] */
  /*    14 */  /* [/ActiveEcuC/Com/ComConfig/pdu_TxStat_64_519c4828_Tx, /ActiveEcuC/Com/ComConfig/MyECU_oFlexRay_Tx_ea5c328c] */

#define COM_STOP_SEC_VAR_NOINIT_8BIT
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */

/**********************************************************************************************************************
  Com_TxProcessingISRLockCounter
**********************************************************************************************************************/
#define COM_START_SEC_VAR_NOINIT_16BIT
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */
VAR(Com_TxProcessingISRLockCounterType, COM_VAR_NOINIT) Com_TxProcessingISRLockCounter;  /* PRQA S 1514, 1533 */  /* MD_CSL_ObjectOnlyAccessedOnce */
#define COM_STOP_SEC_VAR_NOINIT_16BIT
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */

/**********************************************************************************************************************
  Com_TxSduLength
**********************************************************************************************************************/
/** 
  \var    Com_TxSduLength
  \brief  This var Array contains the Com Ipdu Length.
*/ 
#define COM_START_SEC_VAR_NOINIT_16BIT
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */
VAR(Com_TxSduLengthType, COM_VAR_NOINIT) Com_TxSduLength[15];  /* PRQA S 1514, 1533 */  /* MD_CSL_ObjectOnlyAccessedOnce */
  /* Index        Referable Keys */
  /*     0 */  /* [/ActiveEcuC/Com/ComConfig/Frame_LinTr_MyECU2_oLIN00_3a9f0ce6_Tx, /ActiveEcuC/Com/ComConfig/MyECU_oLIN00_Tx_f02a8aba] */
  /*     1 */  /* [/ActiveEcuC/Com/ComConfig/Frame_LinTr_MyECU_oLIN00_ce6095e4_Tx, /ActiveEcuC/Com/ComConfig/MyECU_oLIN00_Tx_f02a8aba] */
  /*     2 */  /* [/ActiveEcuC/Com/ComConfig/PDU_Fr_StartAppl_MyECU_TX_44b0b044_Tx, /ActiveEcuC/Com/ComConfig/MyECU_oFlexRay_Tx_ea5c328c] */
  /*     3 */  /* [/ActiveEcuC/Com/ComConfig/PDU_Transmit_MyECU_05398c7a_Tx, /ActiveEcuC/Com/ComConfig/MyECU_oFlexRay_Tx_ea5c328c] */
  /*     4 */  /* [/ActiveEcuC/Com/ComConfig/PDU_nm_MyECU_Fr_ae963333_Tx] */
  /*     5 */  /* [/ActiveEcuC/Com/ComConfig/msg_StartAppl_Tx_MyECU_oCAN_ad461e3f_Tx, /ActiveEcuC/Com/ComConfig/MyECU_oCAN_Tx_35f94448] */
  /*     6 */  /* [/ActiveEcuC/Com/ComConfig/msg_TxCycle10_0_oCAN_85bf3e37_Tx, /ActiveEcuC/Com/ComConfig/MyECU_oCAN_Tx_35f94448] */
  /*     7 */  /* [/ActiveEcuC/Com/ComConfig/msg_TxCycle1000_10_oCAN_d74aed68_Tx, /ActiveEcuC/Com/ComConfig/MyECU_oCAN_Tx_35f94448] */
  /*     8 */  /* [/ActiveEcuC/Com/ComConfig/msg_TxCycle_E2eProf1C_500_30_oCAN_eeefc687_Tx, /ActiveEcuC/Com/ComConfig/MyECU_oCAN_Tx_35f94448] */
  /*     9 */  /* [/ActiveEcuC/Com/ComConfig/msg_TxEvent_10_oCAN_b2cd4fc2_Tx, /ActiveEcuC/Com/ComConfig/MyECU_oCAN_Tx_35f94448] */
  /*    10 */  /* [/ActiveEcuC/Com/ComConfig/msg_nm_MyECU_oCAN_c97b60cc_Tx] */
  /*    11 */  /* [/ActiveEcuC/Com/ComConfig/pdu_TxDyn_16_3c646bcf_Tx, /ActiveEcuC/Com/ComConfig/MyECU_oFlexRay_Tx_ea5c328c] */
  /*    12 */  /* [/ActiveEcuC/Com/ComConfig/pdu_TxDyn_64_9d2b9c24_Tx, /ActiveEcuC/Com/ComConfig/MyECU_oFlexRay_Tx_ea5c328c] */
  /*    13 */  /* [/ActiveEcuC/Com/ComConfig/pdu_TxStat_40_64c7eeb3_Tx, /ActiveEcuC/Com/ComConfig/MyECU_oFlexRay_Tx_ea5c328c] */
  /*    14 */  /* [/ActiveEcuC/Com/ComConfig/pdu_TxStat_64_519c4828_Tx, /ActiveEcuC/Com/ComConfig/MyECU_oFlexRay_Tx_ea5c328c] */

#define COM_STOP_SEC_VAR_NOINIT_16BIT
/*lint -save -esym(961, 19.1) */
#include "MemMap.h"  /* PRQA S 5087 */  /* MD_MSR_MemMap */
/*lint -restore */


/**********************************************************************************************************************
  CONFIGURATION CLASS: LINK
  SECTION: GLOBAL DATA
**********************************************************************************************************************/


/**********************************************************************************************************************
  LOCAL FUNCTION PROTOTYPES
**********************************************************************************************************************/



/**********************************************************************************************************************
  LOCAL FUNCTIONS
**********************************************************************************************************************/

/**********************************************************************************************************************
  GLOBAL FUNCTIONS
**********************************************************************************************************************/
/**********************************************************************************************************************
  CONFIGURATION CLASS: PRE_COMPILE
  SECTION: GLOBAL FUNCTIONS
**********************************************************************************************************************/

/**********************************************************************************************************************
  CONFIGURATION CLASS: LINK
  SECTION: GLOBAL FUNCTIONS
**********************************************************************************************************************/






/**********************************************************************************************************************
  END OF FILE: Com_Lcfg.c
**********************************************************************************************************************/

