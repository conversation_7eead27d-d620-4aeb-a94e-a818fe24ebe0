/**********************************************************************************************************************
 *  COPYRIGHT
 *  -------------------------------------------------------------------------------------------------------------------
 *  \verbatim
 *
 *                 This software is copyright protected and proprietary to Vector Informatik GmbH.
 *                 Vector Informatik GmbH grants to you only those rights as set out in the license conditions.
 *                 All other rights remain with Vector Informatik GmbH.
 *  \endverbatim
 *  -------------------------------------------------------------------------------------------------------------------
 *  LICENSE
 *  -------------------------------------------------------------------------------------------------------------------
 *            Module: Fr
 *           Program: MSR_Vector_SLP4
 *          Customer: AUTOSAR 4 Evaluation Bundle
 *       Expiry Date: Not restricted
 *  Ordered Derivat.: Aurix 2G/TC377
 *    License Scope : The usage is restricted to CBD2000456_D02
 *
 *  -------------------------------------------------------------------------------------------------------------------
 *  FILE DESCRIPTION
 *  -------------------------------------------------------------------------------------------------------------------
 *              File: Fr_LCfg.c
 *   Generation Time: 2025-08-05 10:37:17
 *           Project: Demo - Version 1.0
 *          Delivery: CBD2000456_D02
 *      Tool Version: DaVinci Configurator (beta) 5.21.40 SP2
 *
 *
 *********************************************************************************************************************/

/**********************************************************************************************************************
 ! BETA VERSION                                                                                                       !
 !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
 ! This version of DaVinci Configurator Pro and/or the related Basic Software Package is BETA software.               !
 ! BETA Software is basically operable, but not sufficiently tested, verified and/or qualified for use in series      !
 ! production and/or in vehicles operating on public or non-public roads.                                             !
 ! In particular, without limitation, BETA Software may cause unpredictable ECU behavior, may not provide all         !
 ! functions necessary for use in series production and/or may not comply with quality requirements which are         !
 ! necessary according to the state of the art. BETA Software must not be used in series production.                  !
 !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
**********************************************************************************************************************/



#define FR_LCFG_SOURCE

/* PRQA S 0777 EOF */ /* MD_MSR_5.1_777 */

#include "Fr.h"


#define FR_START_SEC_CONST_UNSPECIFIED
/* PRQA S 5087 1 */ /* MD_MSR_19.1 */
#include "MemMap.h"

CONST(Fr_VConfigType, FR_CONST) Fr_Config = 0;



CONST(Fr_VRxVirtBuf2PhysBufMapType, FR_CONST) Fr_VRxVirtBuf2PhysBufMap[] = /* PRQA S 3408 */ /* MD_Fr_3408 */
{
  {
    0x09u /*  FT_00670304_8aa49a29_Rx  */ , 
    0x03u /*  Cycle Mask  */ , 
    0x03u /*  Cycle Base  */ 
  }, 
  {
    0x08u /*  FT_005A0140_8f53e10d_Rx  */ , 
    0x3Fu /*  Cycle Mask  */ , 
    0x01u /*  Cycle Base  */ 
  }, 
  {
    0x08u /*  FT_005A0240_5bb2b0d0_Rx  */ , 
    0x3Fu /*  Cycle Mask  */ , 
    0x02u /*  Cycle Base  */ 
  }, 
  {
    0x05u /*  FT_000F0002_986124ab_Rx  */ , 
    0x01u /*  Cycle Mask  */ , 
    0x00u /*  Cycle Base  */ 
  }, 
  {
    0x04u /*  FT_000A0001_0e7f58b5_Rx  */ , 
    0x00u /*  Cycle Mask  */ , 
    0x00u /*  Cycle Base  */ 
  }, 
  {
    0x03u /*  FT_00030001_e9c801d5_Rx  */ , 
    0x00u /*  Cycle Mask  */ , 
    0x00u /*  Cycle Base  */ 
  }
};


CONST(Fr_VTxVirtBuf2PhysBufMapType, FR_CONST) Fr_VTxVirtBuf2PhysBufMap[] = /* PRQA S 3408 */ /* MD_Fr_3408 */
{
  {
    716u /*  CRC  */ , 
    0x07u /*  FT_005F0108_af5cc8ad_Tx Idx2DrvMemBufConf(cfgBufIdx)  */ , 
    0x00u /*  Bit0=dynamicPayload, Bit1=IsReconfigBuffer, Bit2=ReconfigLPdu  */ 
  }, 
  {
    1240u /*  CRC  */ , 
    0x06u /*  FT_005A0040_75dc2c79_Tx Idx2DrvMemBufConf(cfgBufIdx)  */ , 
    0x00u /*  Bit0=dynamicPayload, Bit1=IsReconfigBuffer, Bit2=ReconfigLPdu  */ 
  }, 
  {
    1613u /*  CRC  */ , 
    0x02u /*  FT_00100104_bb944e7e_Tx Idx2DrvMemBufConf(cfgBufIdx)  */ , 
    0x00u /*  Bit0=dynamicPayload, Bit1=IsReconfigBuffer, Bit2=ReconfigLPdu  */ 
  }, 
  {
    1956u /*  CRC  */ , 
    0x01u /*  FT_00080001_1f1414c5_Tx Idx2DrvMemBufConf(cfgBufIdx)  */ , 
    0x00u /*  Bit0=dynamicPayload, Bit1=IsReconfigBuffer, Bit2=ReconfigLPdu  */ 
  }, 
  {
    9u /*  CRC  */ , 
    0x00u /*  FT_00020001_65829600_Tx Idx2DrvMemBufConf(cfgBufIdx)  */ , 
    0x00u /*  Bit0=dynamicPayload, Bit1=IsReconfigBuffer, Bit2=ReconfigLPdu  */ 
  }
};



 

#define FR_STOP_SEC_CONST_UNSPECIFIED
/* PRQA S 5087 1 */ /* MD_MSR_19.1 */
#include "MemMap.h"

 


#define FR_START_SEC_CONST_16BIT
/* PRQA S 5087 1 */ /* MD_MSR_19.1 */
#include "MemMap.h"



#define FR_STOP_SEC_CONST_16BIT
/* PRQA S 5087 1 */ /* MD_MSR_19.1 */
#include "MemMap.h"


 

    


#define FR_START_SEC_CONST_UNSPECIFIED
/* PRQA S 5087 1 */ /* MD_MSR_19.1 */
#include "MemMap.h"



CONST(uint16, FR_CONST) Fr_VCcRegAddrs[] =
{
  0x0020u /*  EIR  */ , 
  0x0024u /*  SIR  */ , 
  0x0028u /*  EILS  */ , 
  0x002Cu /*  SILS  */ , 
  0x0030u /*  EIES  */ , 
  0x0034u /*  EIER  */ , 
  0x0038u /*  SIES  */ , 
  0x003Cu /*  SIER  */ , 
  0x0040u /*  ILE  */ , 
  0x0044u /*  T0C  */ , 
  0x0048u /*  T1C  */ , 
  0x004Cu /*  STPW1  */ , 
  0x0080u /*  SUCC1  */ , 
  0x0084u /*  SUCC2  */ , 
  0x0088u /*  SUCC3  */ , 
  0x008Cu /*  NEMC  */ , 
  0x0090u /*  PRTC1  */ , 
  0x0094u /*  PRTC2  */ , 
  0x0098u /*  MHDC  */ , 
  0x00A0u /*  GTUC1  */ , 
  0x00A4u /*  GTUC2  */ , 
  0x00A8u /*  GTUC3  */ , 
  0x00ACu /*  GTUC4  */ , 
  0x00B0u /*  GTUC5  */ , 
  0x00B4u /*  GTUC6  */ , 
  0x00B8u /*  GTUC7  */ , 
  0x00BCu /*  GTUC8  */ , 
  0x00C0u /*  GTUC9  */ , 
  0x00C4u /*  GTUC10  */ , 
  0x00C8u /*  GTUC11  */ , 
  0x0300u /*  MRC  */ , 
  0x0304u /*  FRF  */ , 
  0x0308u /*  FRFM  */ , 
  0x0310u /*  MHDS  */ , 
  0x0004u /*  CUST1  */ , 
  0x000Cu /*  CUST3  */ 
};



CONST(uint32, FR_CONST) Fr_VCcRegVals[] = /* PRQA S 3408 */ /* MD_Fr_3408 */
{
  0x07070FFFuL /*  EIR  */ , 
  0x0303FFFFuL /*  SIR  */ , 
  0x00000000uL /*  EILS  */ , 
  0x00000000uL /*  SILS  */ , 
  0x00000000uL /*  EIES  */ , 
  0x07070FFFuL /*  EIER  */ , 
  0x00000004uL /*  SIES  */ , 
  0x0303FFFBuL /*  SIER  */ , 
  0x00000001uL /*  ILE  */ , 
  0x00000000uL /*  T0C  */ , 
  0x00000000uL /*  T1C  */ , 
  0x00000000uL /*  STPW1  */ , 
  0x0480FB00uL /*  SUCC1  */ , 
  0x01061F32uL /*  SUCC2  */ , 
  0x00000011uL /*  SUCC3  */ , 
  0x00000000uL /*  NEMC  */ , 
  0xFD2D063FuL /*  PRTC1  */ , 
  0x3CB4373BuL /*  PRTC2  */ , 
  0x00010008uL /*  MHDC  */ , 
  0x00030D40uL /*  GTUC1  */ , 
  0x00091388uL /*  GTUC2  */ , 
  0x05050707uL /*  GTUC3  */ , 
  0x135B131EuL /*  GTUC4  */ , 
  0x48020101uL /*  GTUC5  */ , 
  0x025900F2uL /*  GTUC6  */ , 
  0x00590022uL /*  GTUC7  */ , 
  0x010B0007uL /*  GTUC8  */ , 
  0x00010303uL /*  GTUC9  */ , 
  0x02590096uL /*  GTUC10  */ , 
  0x00000000uL /*  GTUC11  */ , 
  0x01098006uL /*  MRC  */ , 
  0x01800003uL /*  FRF  */ , 
  0x00000000uL /*  FRFM  */ , 
  0x0000007FuL /*  MHDS  */ , 
  0x00000C00uL /*  CUST1  */ , 
  0x00000000uL /*  CUST3  */ 
};


/* -----------------------------------------------------------------------------
    &&&~ FlexrayBuffer Config
 ----------------------------------------------------------------------------- */

 
CONST(Fr_VCcBufType, FR_CONST) Fr_VCcBufs[] = /* PRQA S 3408 */ /* MD_Fr_3408 */
{
  {
    0x15010002uL /*  WRHS1  */ , 
    0x00080304uL /*  WRHS2  */ , 
    0x07FCu /*  WRHS3  */ , 
    0x00u /*  IBCR  */ , 
    0x00u /*  Buffer INFO - currently only relevant for TMS platforms  */ 
  } /*  SlotID 2, IsTx: 1, FT_00020001_65829600_Tx  */ , 
  {
    0x15010008uL /*  WRHS1  */ , 
    0x000803E0uL /*  WRHS2  */ , 
    0x07F8u /*  WRHS3  */ , 
    0x01u /*  IBCR  */ , 
    0x00u /*  Buffer INFO - currently only relevant for TMS platforms  */ 
  } /*  SlotID 8, IsTx: 1, FT_00080001_1f1414c5_Tx  */ , 
  {
    0x15050010uL /*  WRHS1  */ , 
    0x00080412uL /*  WRHS2  */ , 
    0x07F4u /*  WRHS3  */ , 
    0x02u /*  IBCR  */ , 
    0x00u /*  Buffer INFO - currently only relevant for TMS platforms  */ 
  } /*  SlotID 16, IsTx: 1, FT_00100104_bb944e7e_Tx  */ , 
  {
    0x11010003uL /*  WRHS1  */ , 
    0x000805D2uL /*  WRHS2  */ , 
    0x07F0u /*  WRHS3  */ , 
    0x03u /*  IBCR  */ , 
    0x00u /*  Buffer INFO - currently only relevant for TMS platforms  */ 
  } /*  SlotID 3, IsTx: 0, FT_00030001_e9c801d5_Rx  */ , 
  {
    0x1101000AuL /*  WRHS1  */ , 
    0x0008000AuL /*  WRHS2  */ , 
    0x07ECu /*  WRHS3  */ , 
    0x04u /*  IBCR  */ , 
    0x00u /*  Buffer INFO - currently only relevant for TMS platforms  */ 
  } /*  SlotID 10, IsTx: 0, FT_000A0001_0e7f58b5_Rx  */ , 
  {
    0x1102000FuL /*  WRHS1  */ , 
    0x0008062BuL /*  WRHS2  */ , 
    0x07E8u /*  WRHS3  */ , 
    0x05u /*  IBCR  */ , 
    0x00u /*  Buffer INFO - currently only relevant for TMS platforms  */ 
  } /*  SlotID 15, IsTx: 0, FT_000F0002_986124ab_Rx  */ , 
  {
    0x1540005AuL /*  WRHS1  */ , 
    0x0004021AuL /*  WRHS2  */ , 
    0x07E6u /*  WRHS3  */ , 
    0x06u /*  IBCR  */ , 
    0x00u /*  Buffer INFO - currently only relevant for TMS platforms  */ 
  } /*  SlotID 90, IsTx: 1, FT_005A0040_75dc2c79_Tx  */ , 
  {
    0x1509005FuL /*  WRHS1  */ , 
    0x000507BEuL /*  WRHS2  */ , 
    0x07E3u /*  WRHS3  */ , 
    0x07u /*  IBCR  */ , 
    0x00u /*  Buffer INFO - currently only relevant for TMS platforms  */ 
  } /*  SlotID 95, IsTx: 1, FT_005F0108_af5cc8ad_Tx  */ , 
  {
    0x1100005AuL /*  WRHS1  */ , 
    0x0004021AuL /*  WRHS2  */ , 
    0x07E1u /*  WRHS3  */ , 
    0x08u /*  IBCR  */ , 
    0x04u /*  Buffer INFO - currently only relevant for TMS platforms  */ 
  } /*  SlotID 90, IsTx: 0, FT_005A0140_8f53e10d_Rx  */ , 
  {
    0x11070067uL /*  WRHS1  */ , 
    0x00040477uL /*  WRHS2  */ , 
    0x07DFu /*  WRHS3  */ , 
    0x09u /*  IBCR  */ , 
    0x00u /*  Buffer INFO - currently only relevant for TMS platforms  */ 
  } /*  SlotID 103, IsTx: 0, FT_00670304_8aa49a29_Rx  */ 
};

 

 

#define FR_STOP_SEC_CONST_UNSPECIFIED
/* PRQA S 5087 1 */ /* MD_MSR_19.1 */
#include "MemMap.h"

 






#define FR_START_SEC_CONST_32BIT
/* PRQA S 5087 1 */ /* MD_MSR_19.1 */
#include "MemMap.h"

CONST(uint32, FR_CONST) Fr_VIrqCfgVals[] = 
{
  0x02000423uL /*  INT0SRC  */ , 
  0x02000000uL /*  INT1SRC  */ , 
  0x02000402uL /*  TINT0SRC  */ , 
  0x02000003uL /*  TINT1SRC  */ , 
  0x00000000uL /*  NDAT0SRC  */ , 
  0x00000000uL /*  NDAT1SRC  */ , 
  0x00000000uL /*  MBSC0SRC  */ , 
  0x00000000uL /*  MBSC1SRC  */ , 
  0x00000000uL /*  OBUSYSRC  */ , 
  0x00000000uL /*  IBUSYSRC  */ 
};



 
#define FR_STOP_SEC_CONST_32BIT
/* PRQA S 5087 1 */ /* MD_MSR_19.1 */
#include "MemMap.h"

#define FR_START_SEC_CONST_16BIT
/* PRQA S 5087 1 */ /* MD_MSR_19.1 */
#include "MemMap.h"

CONST(uint16, FR_CONST) Fr_VIrqCfgAddrs[] =
{
  0x0BE0u /*  INT0SRC  */ , 
  0x0BE4u /*  INT1SRC  */ , 
  0x0BE8u /*  TINT0SRC  */ , 
  0x0BECu /*  TINT1SRC  */ , 
  0x0BF0u /*  NDAT0SRC  */ , 
  0x0BF4u /*  NDAT1SRC  */ , 
  0x0BF8u /*  MBSC0SRC  */ , 
  0x0BFCu /*  MBSC1SRC  */ , 
  0x0C00u /*  OBUSYSRC  */ , 
  0x0C04u /*  IBUSYSRC  */ 
};

#define FR_STOP_SEC_CONST_16BIT
/* PRQA S 5087 1 */ /* MD_MSR_19.1 */
#include "MemMap.h"







