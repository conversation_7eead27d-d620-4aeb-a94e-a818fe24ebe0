/**********************************************************************************************************************
 *  COPYRIGHT
 *  -------------------------------------------------------------------------------------------------------------------
 *  \verbatim
 *
 *                This software is copyright protected and proprietary to Vector Informatik GmbH.
 *                Vector Informatik GmbH grants to you only those rights as set out in the license conditions.
 *                All other rights remain with Vector Informatik GmbH.
 *  \endverbatim
 *  -------------------------------------------------------------------------------------------------------------------
 *  FILE DESCRIPTION
 *  -------------------------------------------------------------------------------------------------------------------
 *             File:  E2EXf_LCfg.c
 *           Config:  Demo.dpa
 *      ECU-Project:  Demo
 *
 *        Generator:  MICROSAR E2EXf Generator Version 1.9.0
 *                    RTE Core Version 1.22.1
 *          License:  CBD2000456
 *
 *      Description:  MICROSAR E2EXf implementation file for link-time variant
 *********************************************************************************************************************/

/* PRQA S 0777, 0779, 0857 EOF */ /* MD_MSR_Rule5.1, MD_MSR_Rule5.2, MD_MSR_Dir1.1 */

/**********************************************************************************************************************
 *  INCLUDES
 *********************************************************************************************************************/

#include "E2EXf_LCfg.h"

#if !defined (E2EXF_LOCAL)
# define E2EXF_LOCAL static
#endif

/**********************************************************************************************************************
 *  LOCAL DATA PROTOTYPES
 *********************************************************************************************************************/

/**********************************************************************************************************************
 *  GLOBAL FUNCTIONS
 *********************************************************************************************************************/

#define E2EXF_START_SEC_CODE
#include "MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

/***********************************************************************************************************************
 *  E2EXf_Init_Calls()
 **********************************************************************************************************************/
FUNC(void, E2EXF_CODE) E2EXf_Init_Calls(void)
{
} /* PRQA S 6050 */ /* MD_MSR_STCAL */

#define E2EXF_STOP_SEC_CODE
#include "MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

