/**********************************************************************************************************************
 *  COPYRIGHT
 *  -------------------------------------------------------------------------------------------------------------------
 *  \verbatim
 *
 *                 This software is copyright protected and proprietary to Vector Informatik GmbH.
 *                 Vector Informatik GmbH grants to you only those rights as set out in the license conditions.
 *                 All other rights remain with Vector Informatik GmbH.
 *  \endverbatim
 *  -------------------------------------------------------------------------------------------------------------------
 *  LICENSE
 *  -------------------------------------------------------------------------------------------------------------------
 *            Module: Os
 *           Program: MSR_Vector_SLP4
 *          Customer: AUTOSAR 4 Evaluation Bundle
 *       Expiry Date: Not restricted
 *  Ordered Derivat.: Aurix 2G/TC377
 *    License Scope : The usage is restricted to CBD2000456_D02
 *
 *  -------------------------------------------------------------------------------------------------------------------
 *  FILE DESCRIPTION
 *  -------------------------------------------------------------------------------------------------------------------
 *              File: Os_Link_Core0_Stacks.lsl
 *   Generation Time: 2025-08-05 10:37:18
 *           Project: Demo - Version 1.0
 *          Delivery: CBD2000456_D02
 *      Tool Version: DaVinci Configurator (beta) 5.21.40 SP2
 *
 *
 *********************************************************************************************************************/

/**********************************************************************************************************************
 ! BETA VERSION                                                                                                       !
 !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
 ! This version of DaVinci Configurator Pro and/or the related Basic Software Package is BETA software.               !
 ! BETA Software is basically operable, but not sufficiently tested, verified and/or qualified for use in series      !
 ! production and/or in vehicles operating on public or non-public roads.                                             !
 ! In particular, without limitation, BETA Software may cause unpredictable ECU behavior, may not provide all         !
 ! functions necessary for use in series production and/or may not comply with quality requirements which are         !
 ! necessary according to the state of the art. BETA Software must not be used in series production.                  !
 !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
**********************************************************************************************************************/

/**********************************************************************************************************************
 *  STACK SECTIONS
 *********************************************************************************************************************/

#if defined ( OS_LINK_KERNEL_STACKS )
group OS_CORE0_STACKS_GROUP(ordered, contiguous, fill)
{

/* Stack: Default_BSW_Async_Task (1024 Byte) */
group OS_STACK_DEFAULT_BSW_ASYNC_TASK_VAR_NOINIT_GROUP(ordered, contiguous, fill, align=8)
{
  select "[.]bss.OS_STACK_DEFAULT_BSW_ASYNC_TASK_VAR_NOINIT";
}
"_OS_STACK_DEFAULT_BSW_ASYNC_TASK_VAR_NOINIT_START" = "_lc_gb_OS_STACK_DEFAULT_BSW_ASYNC_TASK_VAR_NOINIT_GROUP";
"_OS_STACK_DEFAULT_BSW_ASYNC_TASK_VAR_NOINIT_END" = "_lc_ge_OS_STACK_DEFAULT_BSW_ASYNC_TASK_VAR_NOINIT_GROUP" - 1;
"_OS_STACK_DEFAULT_BSW_ASYNC_TASK_VAR_NOINIT_LIMIT" = "_lc_ge_OS_STACK_DEFAULT_BSW_ASYNC_TASK_VAR_NOINIT_GROUP";

/* Stack: OsCore0_Error (1024 Byte) */
group OS_STACK_OSCORE0_ERROR_VAR_NOINIT_GROUP(ordered, contiguous, fill, align=8)
{
  select "[.]bss.OS_STACK_OSCORE0_ERROR_VAR_NOINIT";
}
"_OS_STACK_OSCORE0_ERROR_VAR_NOINIT_START" = "_lc_gb_OS_STACK_OSCORE0_ERROR_VAR_NOINIT_GROUP";
"_OS_STACK_OSCORE0_ERROR_VAR_NOINIT_END" = "_lc_ge_OS_STACK_OSCORE0_ERROR_VAR_NOINIT_GROUP" - 1;
"_OS_STACK_OSCORE0_ERROR_VAR_NOINIT_LIMIT" = "_lc_ge_OS_STACK_OSCORE0_ERROR_VAR_NOINIT_GROUP";

/* Stack: OsCore0_Init (1024 Byte) */
group OS_STACK_OSCORE0_INIT_VAR_NOINIT_GROUP(ordered, contiguous, fill, align=8)
{
  select "[.]bss.OS_STACK_OSCORE0_INIT_VAR_NOINIT";
}
"_OS_STACK_OSCORE0_INIT_VAR_NOINIT_START" = "_lc_gb_OS_STACK_OSCORE0_INIT_VAR_NOINIT_GROUP";
"_OS_STACK_OSCORE0_INIT_VAR_NOINIT_END" = "_lc_ge_OS_STACK_OSCORE0_INIT_VAR_NOINIT_GROUP" - 1;
"_OS_STACK_OSCORE0_INIT_VAR_NOINIT_LIMIT" = "_lc_ge_OS_STACK_OSCORE0_INIT_VAR_NOINIT_GROUP";

/* Stack: OsCore0_Isr_Core (1024 Byte) */
group OS_STACK_OSCORE0_ISR_CORE_VAR_NOINIT_GROUP(ordered, contiguous, fill, align=8)
{
  select "[.]bss.OS_STACK_OSCORE0_ISR_CORE_VAR_NOINIT";
}
"_OS_STACK_OSCORE0_ISR_CORE_VAR_NOINIT_START" = "_lc_gb_OS_STACK_OSCORE0_ISR_CORE_VAR_NOINIT_GROUP";
"_OS_STACK_OSCORE0_ISR_CORE_VAR_NOINIT_END" = "_lc_ge_OS_STACK_OSCORE0_ISR_CORE_VAR_NOINIT_GROUP" - 1;
"_OS_STACK_OSCORE0_ISR_CORE_VAR_NOINIT_LIMIT" = "_lc_ge_OS_STACK_OSCORE0_ISR_CORE_VAR_NOINIT_GROUP";

/* Stack: OsCore0_Isr_Level22 (1024 Byte) */
group OS_STACK_OSCORE0_ISR_LEVEL22_VAR_NOINIT_GROUP(ordered, contiguous, fill, align=8)
{
  select "[.]bss.OS_STACK_OSCORE0_ISR_LEVEL22_VAR_NOINIT";
}
"_OS_STACK_OSCORE0_ISR_LEVEL22_VAR_NOINIT_START" = "_lc_gb_OS_STACK_OSCORE0_ISR_LEVEL22_VAR_NOINIT_GROUP";
"_OS_STACK_OSCORE0_ISR_LEVEL22_VAR_NOINIT_END" = "_lc_ge_OS_STACK_OSCORE0_ISR_LEVEL22_VAR_NOINIT_GROUP" - 1;
"_OS_STACK_OSCORE0_ISR_LEVEL22_VAR_NOINIT_LIMIT" = "_lc_ge_OS_STACK_OSCORE0_ISR_LEVEL22_VAR_NOINIT_GROUP";

/* Stack: OsCore0_Kernel (1024 Byte) */
group OS_STACK_OSCORE0_KERNEL_VAR_NOINIT_GROUP(ordered, contiguous, fill, align=8)
{
  select "[.]bss.OS_STACK_OSCORE0_KERNEL_VAR_NOINIT";
}
"_OS_STACK_OSCORE0_KERNEL_VAR_NOINIT_START" = "_lc_gb_OS_STACK_OSCORE0_KERNEL_VAR_NOINIT_GROUP";
"_OS_STACK_OSCORE0_KERNEL_VAR_NOINIT_END" = "_lc_ge_OS_STACK_OSCORE0_KERNEL_VAR_NOINIT_GROUP" - 1;
"_OS_STACK_OSCORE0_KERNEL_VAR_NOINIT_LIMIT" = "_lc_ge_OS_STACK_OSCORE0_KERNEL_VAR_NOINIT_GROUP";

/* Stack: OsCore0_Task_Prio10 (1024 Byte) */
group OS_STACK_OSCORE0_TASK_PRIO10_VAR_NOINIT_GROUP(ordered, contiguous, fill, align=8)
{
  select "[.]bss.OS_STACK_OSCORE0_TASK_PRIO10_VAR_NOINIT";
}
"_OS_STACK_OSCORE0_TASK_PRIO10_VAR_NOINIT_START" = "_lc_gb_OS_STACK_OSCORE0_TASK_PRIO10_VAR_NOINIT_GROUP";
"_OS_STACK_OSCORE0_TASK_PRIO10_VAR_NOINIT_END" = "_lc_ge_OS_STACK_OSCORE0_TASK_PRIO10_VAR_NOINIT_GROUP" - 1;
"_OS_STACK_OSCORE0_TASK_PRIO10_VAR_NOINIT_LIMIT" = "_lc_ge_OS_STACK_OSCORE0_TASK_PRIO10_VAR_NOINIT_GROUP";

/* Stack: OsCore0_Task_Prio40 (1024 Byte) */
group OS_STACK_OSCORE0_TASK_PRIO40_VAR_NOINIT_GROUP(ordered, contiguous, fill, align=8)
{
  select "[.]bss.OS_STACK_OSCORE0_TASK_PRIO40_VAR_NOINIT";
}
"_OS_STACK_OSCORE0_TASK_PRIO40_VAR_NOINIT_START" = "_lc_gb_OS_STACK_OSCORE0_TASK_PRIO40_VAR_NOINIT_GROUP";
"_OS_STACK_OSCORE0_TASK_PRIO40_VAR_NOINIT_END" = "_lc_ge_OS_STACK_OSCORE0_TASK_PRIO40_VAR_NOINIT_GROUP" - 1;
"_OS_STACK_OSCORE0_TASK_PRIO40_VAR_NOINIT_LIMIT" = "_lc_ge_OS_STACK_OSCORE0_TASK_PRIO40_VAR_NOINIT_GROUP";

/* Stack: OsCore0_Task_Prio4294967295 (1024 Byte) */
group OS_STACK_OSCORE0_TASK_PRIO4294967295_VAR_NOINIT_GROUP(ordered, contiguous, fill, align=8)
{
  select "[.]bss.OS_STACK_OSCORE0_TASK_PRIO4294967295_VAR_NOINIT";
}
"_OS_STACK_OSCORE0_TASK_PRIO4294967295_VAR_NOINIT_START" = "_lc_gb_OS_STACK_OSCORE0_TASK_PRIO4294967295_VAR_NOINIT_GROUP";
"_OS_STACK_OSCORE0_TASK_PRIO4294967295_VAR_NOINIT_END" = "_lc_ge_OS_STACK_OSCORE0_TASK_PRIO4294967295_VAR_NOINIT_GROUP" - 1;
"_OS_STACK_OSCORE0_TASK_PRIO4294967295_VAR_NOINIT_LIMIT" = "_lc_ge_OS_STACK_OSCORE0_TASK_PRIO4294967295_VAR_NOINIT_GROUP";

/* Stack: OsCore0_Task_Prio45 (1024 Byte) */
group OS_STACK_OSCORE0_TASK_PRIO45_VAR_NOINIT_GROUP(ordered, contiguous, fill, align=8)
{
  select "[.]bss.OS_STACK_OSCORE0_TASK_PRIO45_VAR_NOINIT";
}
"_OS_STACK_OSCORE0_TASK_PRIO45_VAR_NOINIT_START" = "_lc_gb_OS_STACK_OSCORE0_TASK_PRIO45_VAR_NOINIT_GROUP";
"_OS_STACK_OSCORE0_TASK_PRIO45_VAR_NOINIT_END" = "_lc_ge_OS_STACK_OSCORE0_TASK_PRIO45_VAR_NOINIT_GROUP" - 1;
"_OS_STACK_OSCORE0_TASK_PRIO45_VAR_NOINIT_LIMIT" = "_lc_ge_OS_STACK_OSCORE0_TASK_PRIO45_VAR_NOINIT_GROUP";

/* Stack: OsCore0_Task_Prio49 (1024 Byte) */
group OS_STACK_OSCORE0_TASK_PRIO49_VAR_NOINIT_GROUP(ordered, contiguous, fill, align=8)
{
  select "[.]bss.OS_STACK_OSCORE0_TASK_PRIO49_VAR_NOINIT";
}
"_OS_STACK_OSCORE0_TASK_PRIO49_VAR_NOINIT_START" = "_lc_gb_OS_STACK_OSCORE0_TASK_PRIO49_VAR_NOINIT_GROUP";
"_OS_STACK_OSCORE0_TASK_PRIO49_VAR_NOINIT_END" = "_lc_ge_OS_STACK_OSCORE0_TASK_PRIO49_VAR_NOINIT_GROUP" - 1;
"_OS_STACK_OSCORE0_TASK_PRIO49_VAR_NOINIT_LIMIT" = "_lc_ge_OS_STACK_OSCORE0_TASK_PRIO49_VAR_NOINIT_GROUP";

/* Stack: OsCore0_Task_Prio50 (1024 Byte) */
group OS_STACK_OSCORE0_TASK_PRIO50_VAR_NOINIT_GROUP(ordered, contiguous, fill, align=8)
{
  select "[.]bss.OS_STACK_OSCORE0_TASK_PRIO50_VAR_NOINIT";
}
"_OS_STACK_OSCORE0_TASK_PRIO50_VAR_NOINIT_START" = "_lc_gb_OS_STACK_OSCORE0_TASK_PRIO50_VAR_NOINIT_GROUP";
"_OS_STACK_OSCORE0_TASK_PRIO50_VAR_NOINIT_END" = "_lc_ge_OS_STACK_OSCORE0_TASK_PRIO50_VAR_NOINIT_GROUP" - 1;
"_OS_STACK_OSCORE0_TASK_PRIO50_VAR_NOINIT_LIMIT" = "_lc_ge_OS_STACK_OSCORE0_TASK_PRIO50_VAR_NOINIT_GROUP";

/* Stack: StartApplication_Appl_Task (1024 Byte) */
group OS_STACK_STARTAPPLICATION_APPL_TASK_VAR_NOINIT_GROUP(ordered, contiguous, fill, align=8)
{
  select "[.]bss.OS_STACK_STARTAPPLICATION_APPL_TASK_VAR_NOINIT";
}
"_OS_STACK_STARTAPPLICATION_APPL_TASK_VAR_NOINIT_START" = "_lc_gb_OS_STACK_STARTAPPLICATION_APPL_TASK_VAR_NOINIT_GROUP";
"_OS_STACK_STARTAPPLICATION_APPL_TASK_VAR_NOINIT_END" = "_lc_ge_OS_STACK_STARTAPPLICATION_APPL_TASK_VAR_NOINIT_GROUP" - 1;
"_OS_STACK_STARTAPPLICATION_APPL_TASK_VAR_NOINIT_LIMIT" = "_lc_ge_OS_STACK_STARTAPPLICATION_APPL_TASK_VAR_NOINIT_GROUP";

} /* OS_CORE0_STACKS_GROUP */
"_OS_CORE0_STACKS_START" = "_lc_gb_OS_CORE0_STACKS_GROUP";
"_OS_CORE0_STACKS_END" = "_lc_ge_OS_CORE0_STACKS_GROUP" - 1;
"_OS_CORE0_STACKS_LIMIT" = "_lc_ge_OS_CORE0_STACKS_GROUP";
"_OS_CORE0_STACKS_END_ALIAS" = (("_lc_ge_OS_CORE0_STACKS_GROUP")&~0xF0000000)|0xD0000000;
"_OS_CORE0_STACKS_LIMIT_ALIAS" = (("_lc_ge_OS_CORE0_STACKS_GROUP")&~0xF0000000)|0xD0000000;

#endif

#ifdef OS_LINK_KERNEL_STACKS
# undef OS_LINK_KERNEL_STACKS
#endif


