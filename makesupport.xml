<?xml version='1.0' encoding='UTF-8'?>
<makesupport>
<controller name='AURIX' derivative='TC37x' />
 <devtoolinfo>
    <tool type='Compiler' manufacturer='TASKING'>
      <entry type='Version'>TASKING VX-toolset for TriCore: C compiler v6.2r2 Build 18101764 Copyright 2002-2018 TASKING BV SN-00099759</entry>
      <entry type='VectorDefaultOptions'>--core=tc1.6.2 --iso=99 --keep-temporary-files --integer-enumeration -Wa--emit-locals=+equ,+symbols -Wa--section-info=+list,-console -Wa--optimize=+generics,+instr-size -Wa--debug-info=+asm,+hll,+local,+smart -Wc--debug-info=default -Wc--align=4 -Wc--default-a0-size=0 -Wc--default-a1-size=0 -Wc--default-near-size=0 -Wc--optimize=aceFgIklMnopRsUvwy,+predict -Wc--tradeoff=2 -Wc--language=-gcc,+volatile,-strings,-comments</entry>
      <entry type='VectorBuildEnvironmentOptions'>-DBRS_PLATFORM_AURIX -DBRS_COMP_TASKING --create=object -o</entry>


    </tool>
    <tool type='Assembler' manufacturer='TASKING'>
      <entry type='Version'>TASKING VX-toolset for TriCore: assembler v6.2r2 Build 18101764 Copyright 2002-2018 TASKING BV</entry>
      <entry type='VectorDefaultOptions'></entry>
      <entry type='VectorBuildEnvironmentOptions'></entry>
    </tool>
    <tool type='Linker' manufacturer='TASKING'>
      <entry type='Version'>TASKING VX-toolset for TriCore: object linker v6.2r2 Build 18041957 Copyright 2002-2018 TASKING BV SN-00099759</entry>
      <entry type='VectorDefaultOptions'>--core=tc1.6.2 -Wl--output=TestSuit.hex:IHEX -Wl--optimize=1 -Wl--map-file=TestSuit.map -Wl--map-file-format=2</entry>
      <entry type='VectorBuildEnvironmentOptions'>-o TestSuit.elf -Wl--user-provided-initialization-code --no-default-libraries --lsl-file=Source/vLinkGen_Template.lsl</entry>
    </tool>
 </devtoolinfo>
</makesupport>
