/**********************************************************************************************************************
 *  COPYRIGHT
 *  -------------------------------------------------------------------------------------------------------------------
 *  \verbatim
 *
 *                 This software is copyright protected and proprietary to Vector Informatik GmbH.
 *                 Vector Informatik GmbH grants to you only those rights as set out in the license conditions.
 *                 All other rights remain with Vector Informatik GmbH.
 *  \endverbatim
 *  -------------------------------------------------------------------------------------------------------------------
 *  LICENSE
 *  -------------------------------------------------------------------------------------------------------------------
 *            Module: PduR
 *           Program: MSR_Vector_SLP4
 *          Customer: AUTOSAR 4 Evaluation Bundle
 *       Expiry Date: Not restricted
 *  Ordered Derivat.: Aurix 2G/TC377
 *    License Scope : The usage is restricted to CBD2000456_D02
 *
 *  -------------------------------------------------------------------------------------------------------------------
 *  FILE DESCRIPTION
 *  -------------------------------------------------------------------------------------------------------------------
 *              File: PduR_Cfg.h
 *   Generation Time: 2025-08-05 10:37:17
 *           Project: Demo - Version 1.0
 *          Delivery: CBD2000456_D02
 *      Tool Version: DaVinci Configurator (beta) 5.21.40 SP2
 *
 *
 *********************************************************************************************************************/

/**********************************************************************************************************************
 ! BETA VERSION                                                                                                       !
 !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
 ! This version of DaVinci Configurator Pro and/or the related Basic Software Package is BETA software.               !
 ! BETA Software is basically operable, but not sufficiently tested, verified and/or qualified for use in series      !
 ! production and/or in vehicles operating on public or non-public roads.                                             !
 ! In particular, without limitation, BETA Software may cause unpredictable ECU behavior, may not provide all         !
 ! functions necessary for use in series production and/or may not comply with quality requirements which are         !
 ! necessary according to the state of the art. BETA Software must not be used in series production.                  !
 !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
**********************************************************************************************************************/

#if !defined (PDUR_CFG_H)
# define PDUR_CFG_H

/**********************************************************************************************************************
 * MISRA JUSTIFICATION
 *********************************************************************************************************************/

/**********************************************************************************************************************
 * INCLUDES
 *********************************************************************************************************************/
#include "PduR_Types.h"

/**********************************************************************************************************************
 * GLOBAL CONSTANT MACROS
 *********************************************************************************************************************/
#ifndef PDUR_USE_DUMMY_STATEMENT
#define PDUR_USE_DUMMY_STATEMENT STD_OFF /* /MICROSAR/vSet/vSetGeneral/vSetDummyStatementKind */
#endif
#ifndef PDUR_DUMMY_STATEMENT
#define PDUR_DUMMY_STATEMENT(v)  /* PRQA S 3453 */ /* MD_MSR_FctLikeMacro */  /* /MICROSAR/vSet/vSetGeneral/vSetDummyStatementKind */
#endif
#ifndef PDUR_DUMMY_STATEMENT_CONST
#define PDUR_DUMMY_STATEMENT_CONST(v)  /* PRQA S 3453 */ /* MD_MSR_FctLikeMacro */  /* /MICROSAR/vSet/vSetGeneral/vSetDummyStatementKind */
#endif
#ifndef PDUR_ATOMIC_BIT_ACCESS_IN_BITFIELD
#define PDUR_ATOMIC_BIT_ACCESS_IN_BITFIELD STD_OFF /* /MICROSAR/EcuC/EcucGeneral/AtomicBitAccessInBitfield */
#endif
#ifndef PDUR_ATOMIC_VARIABLE_ACCESS
#define PDUR_ATOMIC_VARIABLE_ACCESS 32u /* /MICROSAR/EcuC/EcucGeneral/AtomicVariableAccess */
#endif
#ifndef PDUR_PROCESSOR_TC377T
#define PDUR_PROCESSOR_TC377T
#endif
#ifndef PDUR_COMP_TASKING
#define PDUR_COMP_TASKING
#endif
#ifndef PDUR_GEN_GENERATOR_MSR
#define PDUR_GEN_GENERATOR_MSR
#endif
#ifndef PDUR_CPUTYPE_BITORDER_LSB2MSB
#define PDUR_CPUTYPE_BITORDER_LSB2MSB /* /MICROSAR/vSet/vSetPlatform/vSetBitOrder */
#endif
#ifndef PDUR_CONFIGURATION_VARIANT_PRECOMPILE
#define PDUR_CONFIGURATION_VARIANT_PRECOMPILE 1
#endif
#ifndef PDUR_CONFIGURATION_VARIANT_LINKTIME
#define PDUR_CONFIGURATION_VARIANT_LINKTIME 2
#endif
#ifndef PDUR_CONFIGURATION_VARIANT_POSTBUILD_LOADABLE
#define PDUR_CONFIGURATION_VARIANT_POSTBUILD_LOADABLE 3
#endif
#ifndef PDUR_CONFIGURATION_VARIANT
#define PDUR_CONFIGURATION_VARIANT PDUR_CONFIGURATION_VARIANT_PRECOMPILE
#endif
#ifndef PDUR_POSTBUILD_VARIANT_SUPPORT
#define PDUR_POSTBUILD_VARIANT_SUPPORT STD_OFF
#endif



#define PDUR_DEV_ERROR_DETECT STD_ON  /**< /ActiveEcuC/PduR/PduRGeneral[0:PduRSafeBswChecks] || /ActiveEcuC/PduR/PduRGeneral[0:PduRDevErrorDetect] */
#define PDUR_DEV_ERROR_REPORT STD_ON  /**< /ActiveEcuC/PduR/PduRGeneral[0:PduRDevErrorDetect] */

#define PDUR_EXTENDED_ERROR_CHECKS STD_OFF  /**< /ActiveEcuC/PduR/PduRGeneral[0:PduRExtendedErrorChecks] */

#define PDUR_METADATA_SUPPORT STD_OFF  /**< /ActiveEcuC/PduR/PduRGeneral[0:PduRMetaDataSupport] */
#define PDUR_VERSION_INFO_API STD_OFF  /**< /ActiveEcuC/PduR/PduRGeneral[0:PduRVersionInfoApi] */

#define PDUR_ERROR_NOTIFICATION STD_OFF

#define PDUR_MAIN_FUNCTION STD_OFF

#define PDUR_MULTICORE STD_OFF /**< /ActiveEcuC/PduR/PduRGeneral[0:PduRSupportMulticore] */

#define PduR_PBConfigIdType uint32

 
 /*  DET Error define list  */ 
#define PDUR_FCT_CANIFRXIND 0x01u 
#define PDUR_FCT_CANIFTX 0x09u 
#define PDUR_FCT_CANIFTXCFM 0x02u 
#define PDUR_FCT_CANNMRXIND 0x11u 
#define PDUR_FCT_CANNMTX 0x19u 
#define PDUR_FCT_CANNMTXCFM 0x12u 
#define PDUR_FCT_CANNMTT 0x13u 
#define PDUR_FCT_CANTPRXIND 0x05u 
#define PDUR_FCT_CANTPTX 0x09u 
#define PDUR_FCT_CANTPTXCFM 0x08u 
#define PDUR_FCT_CANTPSOR 0x06u 
#define PDUR_FCT_CANTPCPYRX 0x04u 
#define PDUR_FCT_CANTPCPYTX 0x07u 
#define PDUR_FCT_COMTX 0x89u 
#define PDUR_FCT_DCMTX 0x99u 
#define PDUR_FCT_DCMCTX 0x9Au 
#define PDUR_FCT_DCMCTX 0x9Au 
#define PDUR_FCT_FRIFRXIND 0x31u 
#define PDUR_FCT_FRIFTX 0x39u 
#define PDUR_FCT_FRIFTXCFM 0x32u 
#define PDUR_FCT_FRIFTT 0x33u 
#define PDUR_FCT_FRNMRXIND 0x41u 
#define PDUR_FCT_FRNMTX 0x49u 
#define PDUR_FCT_FRNMTXCFM 0x42u 
#define PDUR_FCT_FRNMTT 0x43u 
#define PDUR_FCT_LINIFRXIND 0x51u 
#define PDUR_FCT_LINIFTX 0x59u 
#define PDUR_FCT_LINIFTXCFM 0x52u 
#define PDUR_FCT_LINIFTT 0x53u 
#define PDUR_FCT_LINTPRXIND 0x55u 
#define PDUR_FCT_LINTPTX 0x59u 
#define PDUR_FCT_LINTPTXCFM 0x58u 
#define PDUR_FCT_LINTPSOR 0x56u 
#define PDUR_FCT_LINTPCPYRX 0x54u 
#define PDUR_FCT_LINTPCPYTX 0x57u 
#define PDUR_FCT_CDDLINTPSTUBTX 0xC9u 
 /*   PduR_CanIfIfRxIndication  PduR_CanIfTransmit  PduR_CanIfTxConfirmation  PduR_CanNmIfRxIndication  PduR_CanNmTransmit  PduR_CanNmTxConfirmation  PduR_CanNmTriggerTransmit  PduR_CanTpTpRxIndication  PduR_CanTpTransmit  PduR_CanTpTxConfirmation  PduR_CanTpStartOfReception  PduR_CanTpCopyRxData  PduR_CanTpCopyTxData  PduR_ComTransmit  PduR_DcmTransmit  PduR_DcmCancelTransmit  PduR_DcmCancelTransmit  PduR_FrIfIfRxIndication  PduR_FrIfTransmit  PduR_FrIfTxConfirmation  PduR_FrIfTriggerTransmit  PduR_FrNmIfRxIndication  PduR_FrNmTransmit  PduR_FrNmTxConfirmation  PduR_FrNmTriggerTransmit  PduR_LinIfIfRxIndication  PduR_LinIfTransmit  PduR_LinIfTxConfirmation  PduR_LinIfTriggerTransmit  PduR_LinTpTpRxIndication  PduR_LinTpTransmit  PduR_LinTpTxConfirmation  PduR_LinTpStartOfReception  PduR_LinTpCopyRxData  PduR_LinTpCopyTxData  PduR_CddLinTpStubTransmit  */ 



/**
 * \defgroup PduRHandleIdsIfRxDest Handle IDs of handle space IfRxDest.
 * \brief Communication interface Rx destination PDUs
 * \{
 */

/* Handle IDs active in all predefined variants (the application has not to take the active variant into account) */
/*      Symbolic Name                                                 Value   Active in predefined variant(s) */
#define PduRConf_PduRDestPdu_Frame_LinTr_RearECU_oLIN00_64640cc1_Rx_87852900_Rx 0u
#define PduRConf_PduRDestPdu_Frame_LinTr_Slave3_oLIN00_eb2bd0ab_Rx_8cf2ea35_Rx 1u
#define PduRConf_PduRDestPdu_PDU_Dummy_RearECU_9177c4f3_Rx_5699e50b_Rx 2u
#define PduRConf_PduRDestPdu_PDU_Fr_StartAppl_BothECU_RX_7a67d8c4_Rx_b2f650ce_Rx 3u
#define PduRConf_PduRDestPdu_msg_RxCycle100_0_oCAN_c71398f9_Rx_9e00b2d3_Rx 4u
#define PduRConf_PduRDestPdu_msg_RxCycle500_20_oCAN_266969e8_Rx_b1d1dd8a_Rx 5u
#define PduRConf_PduRDestPdu_msg_RxCycle_E2eProf1C_500_10_oCAN_23e9789c_Rx_01c8980f_Rx 6u
#define PduRConf_PduRDestPdu_msg_RxEvent_20_oCAN_13517c6b_Rx_37e6280b_Rx 7u
#define PduRConf_PduRDestPdu_msg_StartAppl_Rx_MyECU_oCAN_fe93a56e_Rx_7a86d966_Rx 8u
#define PduRConf_PduRDestPdu_pdu_RxDyn_64_600910d1_Rx_45b853db_Rx     9u
#define PduRConf_PduRDestPdu_pdu_RxStat_10_51092b83_Rx_2bf83137_Rx    10u
#define PduRConf_PduRDestPdu_pdu_RxStat_30_25bd2d72_Rx_9474dc2d_Rx    11u
/**\} */

/**
 * \defgroup PduRHandleIdsIfRxSrc Handle IDs of handle space IfRxSrc.
 * \brief Communication interface Rx source PDUs
 * \{
 */

/* Handle IDs active in all predefined variants (the application has not to take the active variant into account) */
/*      Symbolic Name                                                 Value   Active in predefined variant(s) */
#define PduRConf_PduRSrcPdu_PduRSrcPdu_2bf83137                       10u
#define PduRConf_PduRSrcPdu_PduRSrcPdu_7a86d966                       8u
#define PduRConf_PduRSrcPdu_PduRSrcPdu_8cf2ea35                       1u
#define PduRConf_PduRSrcPdu_PduRSrcPdu_9e00b2d3                       4u
#define PduRConf_PduRSrcPdu_PduRSrcPdu_01c8980f                       6u
#define PduRConf_PduRSrcPdu_PduRSrcPdu_37e6280b                       7u
#define PduRConf_PduRSrcPdu_PduRSrcPdu_45b853db                       9u
#define PduRConf_PduRSrcPdu_PduRSrcPdu_5699e50b                       2u
#define PduRConf_PduRSrcPdu_PduRSrcPdu_9474dc2d                       11u
#define PduRConf_PduRSrcPdu_PduRSrcPdu_87852900                       0u
#define PduRConf_PduRSrcPdu_PduRSrcPdu_b1d1dd8a                       5u
#define PduRConf_PduRSrcPdu_PduRSrcPdu_b2f650ce                       3u
/**\} */

/**
 * \defgroup PduRHandleIdsIfTpTxSrc Handle IDs of handle space IfTpTxSrc.
 * \brief Communication interface and transport protocol Tx PDUs
 * \{
 */

/* Handle IDs active in all predefined variants (the application has not to take the active variant into account) */
/*      Symbolic Name                                                 Value   Active in predefined variant(s) */
#define PduRConf_PduRSrcPdu_PduRSrcPdu_2e41d513                       9u
#define PduRConf_PduRSrcPdu_PduRSrcPdu_8c1e0786                       6u
#define PduRConf_PduRSrcPdu_PduRSrcPdu_15da8b7f                       19u
#define PduRConf_PduRSrcPdu_PduRSrcPdu_88fade16                       2u
#define PduRConf_PduRSrcPdu_PduRSrcPdu_616fda32                       15u
#define PduRConf_PduRSrcPdu_PduRSrcPdu_927d3065                       4u
#define PduRConf_PduRSrcPdu_PduRSrcPdu_3920f728                       16u
#define PduRConf_PduRSrcPdu_PduRSrcPdu_12782a39                       0u
#define PduRConf_PduRSrcPdu_PduRSrcPdu_704563f1                       10u
#define PduRConf_PduRSrcPdu_PduRSrcPdu_783591af                       1u
#define PduRConf_PduRSrcPdu_PduRSrcPdu_36788591                       12u
#define PduRConf_PduRSrcPdu_PduRSrcPdu_a7760f35                       18u
#define PduRConf_PduRSrcPdu_PduRSrcPdu_ab04820d                       17u
#define PduRConf_PduRSrcPdu_PduRSrcPdu_b376d1ba                       3u
#define PduRConf_PduRSrcPdu_PduRSrcPdu_c3d2922e                       11u
#define PduRConf_PduRSrcPdu_PduRSrcPdu_c844becc                       7u
#define PduRConf_PduRSrcPdu_PduRSrcPdu_cd5f4416                       8u
#define PduRConf_PduRSrcPdu_PduRSrcPdu_d1717db2                       5u
#define PduRConf_PduRSrcPdu_PduRSrcPdu_ec58e685                       14u
#define PduRConf_PduRSrcPdu_PduRSrcPdu_f3446321                       13u
/**\} */

/**
 * \defgroup PduRHandleIdsIfTxDest Handle IDs of handle space IfTxDest.
 * \brief Communication interface Tx destination PDUs
 * \{
 */

/* Handle IDs active in all predefined variants (the application has not to take the active variant into account) */
/*      Symbolic Name                                                 Value   Active in predefined variant(s) */
#define PduRConf_PduRDestPdu_Frame_LinTr_MyECU2_oLIN00_a05ec237_Tx    0u
#define PduRConf_PduRDestPdu_Frame_LinTr_MyECU_oLIN00_392a06a5_Tx     1u
#define PduRConf_PduRDestPdu_PDU_Fr_StartAppl_MyECU_TX_161591f9_Tx    2u
#define PduRConf_PduRDestPdu_PDU_Transmit_MyECU_dcbaa590_Tx           3u
#define PduRConf_PduRDestPdu_PDU_nm_MyECU_Fr_ae963333_Tx_c844becc_Tx  4u
#define PduRConf_PduRDestPdu_msg_StartAppl_Tx_MyECU_oCAN_c14d78df_Tx  5u
#define PduRConf_PduRDestPdu_msg_TxCycle10_0_oCAN_2d7b6a87_Tx         6u
#define PduRConf_PduRDestPdu_msg_TxCycle1000_10_oCAN_6dd6f284_Tx      7u
#define PduRConf_PduRDestPdu_msg_TxCycle_E2eProf1C_500_30_oCAN_995018f6_Tx 8u
#define PduRConf_PduRDestPdu_msg_TxEvent_10_oCAN_b9443fef_Tx          9u
#define PduRConf_PduRDestPdu_msg_diag_Uudt_Response_MyECU_oCAN_e0fa8c3a_Tx 10u
#define PduRConf_PduRDestPdu_msg_nm_MyECU_oCAN_c97b60cc_Tx_616fda32_Tx 11u
#define PduRConf_PduRDestPdu_pdu_TxDyn_16_44389cbf_Tx                 12u
#define PduRConf_PduRDestPdu_pdu_TxDyn_64_0bd27c77_Tx                 13u
#define PduRConf_PduRDestPdu_pdu_TxStat_40_505d7b88_Tx                14u
#define PduRConf_PduRDestPdu_pdu_TxStat_64_2f41aa7f_Tx                15u
/**\} */

/**
 * \defgroup PduRHandleIdsTpRxDest Handle IDs of handle space TpRxDest.
 * \brief Transport protocol Rx destination PDUs
 * \{
 */

/* Handle IDs active in all predefined variants (the application has not to take the active variant into account) */
/*      Symbolic Name                                                 Value   Active in predefined variant(s) */
#define PduRConf_PduRDestPdu_SlaveResp_RearECU_oLIN00_fa842351_Rx_e930d1d3_Rx 0u
#define PduRConf_PduRDestPdu_SlaveResp_Slave3_oLIN00_6f91e3ac_Rx_74fe1f87_Rx 1u
#define PduRConf_PduRDestPdu_msg_diag_RequestGlobal_oCAN_e06c7e2d_Rx_c958f031_Rx 2u
#define PduRConf_PduRDestPdu_msg_diag_Request_MyECU_oCAN_ca029ee7_Rx_9fd2cbe7_Rx 3u
/**\} */

/**
 * \defgroup PduRHandleIdsTpRxSrc Handle IDs of handle space TpRxSrc.
 * \brief Transport protocol Rx source PDUs
 * \{
 */

/* Handle IDs active in all predefined variants (the application has not to take the active variant into account) */
/*      Symbolic Name                                                 Value   Active in predefined variant(s) */
#define PduRConf_PduRSrcPdu_PduRSrcPdu_9fd2cbe7                       3u
#define PduRConf_PduRSrcPdu_PduRSrcPdu_74fe1f87                       1u
#define PduRConf_PduRSrcPdu_PduRSrcPdu_c958f031                       2u
#define PduRConf_PduRSrcPdu_PduRSrcPdu_e930d1d3                       0u
/**\} */

/**
 * \defgroup PduRHandleIdsTpTxDest Handle IDs of handle space TpTxDest.
 * \brief Transport protocol Tx PDUs
 * \{
 */

/* Handle IDs active in all predefined variants (the application has not to take the active variant into account) */
/*      Symbolic Name                                                 Value   Active in predefined variant(s) */
#define PduRConf_PduRDestPdu_MasterReq_RearECU_oLIN00_a4a697e2_Tx     0u
#define PduRConf_PduRDestPdu_MasterReq_Slave3_oLIN00_93dc5ed4_Tx      1u
#define PduRConf_PduRDestPdu_MasterReq_oLIN00_3234fe1b_Tx             2u
#define PduRConf_PduRDestPdu_msg_diag_Response_MyECU_oCAN_84acb98b_Tx 3u
/**\} */


/* User Config File Start */

/* User Config File End */


/**********************************************************************************************************************
 * GLOBAL FUNCTION MACROS
 *********************************************************************************************************************/

/**********************************************************************************************************************
 * GLOBAL DATA TYPES AND STRUCTURES
 *********************************************************************************************************************/

/**********************************************************************************************************************
 * GLOBAL FUNCTION PROTOTYPES
 *********************************************************************************************************************/

#endif  /* PDUR_CFG_H */
/**********************************************************************************************************************
 * END OF FILE: PduR_Cfg.h
 *********************************************************************************************************************/

