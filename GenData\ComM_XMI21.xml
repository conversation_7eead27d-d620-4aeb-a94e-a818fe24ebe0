<?xml version="1.0" encoding="UTF-8" standalone="no"?><xmi:XMI xmlns:xmi="http://schema.omg.org/spec/XMI/2.1" xmlns:uml="http://schema.omg.org/spec/UML/2.1" xmi:version="2.1"><xmi:Documentation exporter="Enterprise Architect" exporterVersion="6.5"/><xmi:Extension extender="Enterprise Architect" extenderID="6.5"><elements><element name="Channel:ConstStruct" scope="public" xmi:idref="EAID_dc5c080f59d4607dc71f56926d31cb9d" xmi:type="uml:Object"><properties documentation="Contains PreCompile configuration parameters of channels" isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><constraints/><attributes><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_9e3255b396941714806e7512536a8703"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute><attribute name="BusTypeOfChannel" scope="Private" xmi:idref="EAID_48e77396d7f1150eea7da9550b1f9694"><Constraints/><documentation value="The channel bus type"/><properties changeability="frozen" collection="true" type="ComM_BusType"/></attribute><attribute name="EnabledOfChannel" scope="Private" xmi:idref="EAID_40ff112048ce5439e3b9bc9974529c0a"><Constraints><Constraint name="CBoolArrayGenCondition" notes="deactivate the ComM_EnabledOfChannel if all values are true" type="Pre-condition"/></Constraints><documentation value="Decides if the channel is enabled in the variant"/><properties changeability="frozen" collection="true" type="ComM_EnabledOfChannelType"/></attribute><attribute name="GwTypeOfChannel" scope="Private" xmi:idref="EAID_274a85ae459e56cd1bf58036141c056b"><Constraints/><documentation value="The partial network gateway type, relevant for channels attached to coordinated partial networks"/><properties changeability="frozen" collection="true" type="ComM_GwTypeOfChannelType"/></attribute><attribute name="InhibitionInitValueOfChannel" scope="Private" xmi:idref="EAID_28e25c2377e855790147743c2c4d9b76"><Constraints/><documentation value="Initial value of the inhibition status of the channel"/><properties changeability="frozen" collection="true" type="ComM_InhibitionInitValueOfChannelType"/></attribute><attribute name="J1939SupportOfChannel" scope="Private" xmi:idref="EAID_23bc2e06081bb25a05e3bea5791ab83b"><Constraints><Constraint name="CBoolArrayGenCondition" notes="deactivate the ComM_J1939SupportOfChannel if all values are false" type="Pre-condition"/></Constraints><documentation value="Decides if the channel supports J1939"/><properties changeability="frozen" collection="true" type="ComM_J1939SupportOfChannelType"/></attribute><attribute name="ManagingChannelIdOfChannel" scope="Private" xmi:idref="EAID_83ce4fd49745799d76a111059ac6f46f"><Constraints/><documentation value="The channel Id of the referenced ComM channel (Managing Channel). If no reference is set, the value is set to 255"/><properties changeability="frozen" collection="true" type="ComM_ManagingChannelIdOfChannelType"/></attribute><attribute name="ManagingChannelOfChannel" scope="Private" xmi:idref="EAID_16d0c5050f492c00c7f73933ba96103e"><Constraints><Constraint name="CBoolArrayGenCondition" notes="deactivate the ComM_ManagingChannelOfChannel if all values are false" type="Pre-condition"/></Constraints><documentation value="TRUE if the channel is a managing channel, i.e. the channel is referenced by another channel (managed channel)"/><properties changeability="frozen" collection="true" type="ComM_ManagingChannelOfChannelType"/></attribute><attribute name="ManagingUserOfChannel" scope="Private" xmi:idref="EAID_402607febf214b7367899ee06dc368e9"><Constraints/><documentation value="Managing user handle generated for the channel if it is a managing channel (COMM_NO_MANAGINGUSEROFCHANNEL otherwise)"/><properties changeability="frozen" collection="true" type="ComM_ManagingUserOfChannelType"/></attribute><attribute name="MinFullComTimeOfChannel" scope="Private" xmi:idref="EAID_3b5dafcc2d352a3cdd58f21de372d4f5"><Constraints><Constraint name="CNumericalArrayGenCondition" notes="deactivate the ComM_MinFullComTimeOfChannel if all values are 0" type="Pre-condition"/></Constraints><documentation value="Minimal full communication time for the channel, relevant for NmTypes LIGHT and FULL"/><properties changeability="frozen" collection="true" type="ComM_MinFullComTimeOfChannelType"/></attribute><attribute name="NmLightDurationOfChannel" scope="Private" xmi:idref="EAID_682d9437827ccb1be14e3124bbe17a7b"><Constraints><Constraint name="CNumericalArrayGenCondition" notes="deactivate the ComM_NmLightDurationOfChannel if all values are 0" type="Pre-condition"/></Constraints><documentation value="Nm Light Timeout"/><properties changeability="frozen" collection="true" type="ComM_NmLightDurationOfChannelType"/></attribute><attribute name="NmLightSilentDurationOfChannel" scope="Private" xmi:idref="EAID_d196dab2db475d030253a38487a2685a"><Constraints><Constraint name="CNumericalArrayGenCondition" notes="deactivate the ComM_NmLightSilentDurationOfChannel if all values are 0" type="Pre-condition"/></Constraints><documentation value="Nm Light Silent Timeout"/><properties changeability="frozen" collection="true" type="ComM_NmLightSilentDurationOfChannelType"/></attribute><attribute name="NmSupportOfChannel" scope="Private" xmi:idref="EAID_9b4fd262daa6322e091302f17d3bbf1c"><Constraints><Constraint name="CBoolArrayGenCondition" notes="deactivate the ComM_NmSupportOfChannel if all values are false" type="Pre-condition"/></Constraints><documentation value="Decides if the channel has NmType FULL or PASSIVE"/><properties changeability="frozen" collection="true" type="ComM_NmSupportOfChannelType"/></attribute><attribute name="NmTypeOfChannel" scope="Private" xmi:idref="EAID_e876e023874664413918d69512577e11"><Constraints/><documentation value="The Network Management type of the channel"/><properties changeability="frozen" collection="true" type="ComM_NmTypeOfChannelType"/></attribute><attribute name="PassiveOrLinSlaveOfChannel" scope="Private" xmi:idref="EAID_858ad9eb938f360c58b0c8acde352dc2"><Constraints><Constraint name="CBoolArrayGenCondition" notes="deactivate the ComM_PassiveOrLinSlaveOfChannel if all values are false" type="Pre-condition"/></Constraints><documentation value="TRUE if the Nm Variant of the Channel is LINSLAVE or PASSIVE."/><properties changeability="frozen" collection="true" type="ComM_PassiveOrLinSlaveOfChannelType"/></attribute><attribute name="PncNmRequestOfChannel" scope="Private" xmi:idref="EAID_18a37ff5571ff41549bca268bdcde94c"><Constraints><Constraint name="CUserPreCompileDeactivationCondition" notes="PNC Support is disabled" type="Pre-condition"/><Constraint name="CBoolArrayGenCondition" notes="deactivate the ComM_PncNmRequestOfChannel if all values are false" type="Pre-condition"/></Constraints><documentation value="Decides if a Nm message shall be sent immediately after partial network state changes"/><properties changeability="frozen" collection="true" type="ComM_PncNmRequestOfChannelType"/></attribute><attribute name="ResetAfterForcingNoCommOfChannel" scope="Private" xmi:idref="EAID_0531c5cadd09a715413a33bd0612f545"><Constraints><Constraint name="CBoolArrayGenCondition" notes="deactivate the ComM_ResetAfterForcingNoCommOfChannel if all values are false" type="Pre-condition"/></Constraints><documentation value="Decides if ComM shall force an ECU reset after limited channel entered NO_COM"/><properties changeability="frozen" collection="true" type="ComM_ResetAfterForcingNoCommOfChannelType"/></attribute><attribute name="SilentSupportOfChannel" scope="Private" xmi:idref="EAID_9312cb252e24cd1b1031675d50f30b10"><Constraints><Constraint name="CBoolArrayGenCondition" notes="deactivate the ComM_SilentSupportOfChannel if all values are false" type="Pre-condition"/></Constraints><documentation value="Decides if the channel supports Silent mode (TRUE if ETH or CAN without J1939NM and Nm or NmLightSilentDuration)"/><properties changeability="frozen" collection="true" type="ComM_SilentSupportOfChannelType"/></attribute><attribute name="WakeupStateOfChannel" scope="Private" xmi:idref="EAID_30850d33aaad65cdd2fa03ff2be47cbb"><Constraints/><documentation value="Target channel state after a Passive Wake-up"/><properties changeability="frozen" collection="true" type="ComM_WakeupStateOfChannelType"/></attribute></attributes></element><element name="ChannelPb:ConstStruct" scope="public" xmi:idref="EAID_e924de3ac25b8403fe1b2f532e44eda8" xmi:type="uml:Object"><properties documentation="Contains PostBuild configuration parameters of channels" isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><constraints/><attributes><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_d81f55057d6edb0fe7e786e57d628b54"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute></attributes></element><element name="Pnc:ConstStruct" scope="public" xmi:idref="EAID_55106321ebefc0040c2879f6b1f74133" xmi:type="uml:Object"><properties documentation="Contains PreCompile configuration parameters of partial networks" isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><constraints/><attributes><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_8f999f7703460ca36934c5959ee34d78"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute><attribute name="ClearMaskOfPnc" scope="Private" xmi:idref="EAID_669da4ecef850c15229a450936b35db8"><Constraints/><documentation value="Clear mask for the partial network related signals (EIRA, ERA)"/><properties changeability="frozen" collection="true" type="ComM_ClearMaskOfPncType"/></attribute><attribute name="CoordinatedOfPnc" scope="Private" xmi:idref="EAID_a3592d2d4ffbe37c48e6cae7b9fb9d48"><Constraints><Constraint name="CBoolArrayGenCondition" notes="deactivate the ComM_CoordinatedOfPnc if all values are false" type="Pre-condition"/></Constraints><documentation value="TRUE if PNC is coordinated"/><properties changeability="frozen" collection="true" type="ComM_CoordinatedOfPncType"/></attribute><attribute name="PncIdOfPnc" scope="Private" xmi:idref="EAID_16dcd3c500eb4613ee5d395388f240bc"><Constraints/><documentation value="System ID of the partial network"/><properties changeability="frozen" collection="true" type="PNCHandleType"/></attribute><attribute name="SetMaskOfPnc" scope="Private" xmi:idref="EAID_0aae6e53038409a412585c6e0635df93"><Constraints/><documentation value="Set mask for the partial network related signals (EIRA, ERA)"/><properties changeability="frozen" collection="true" type="ComM_SetMaskOfPncType"/></attribute><attribute name="SystemUserOfPnc" scope="Private" xmi:idref="EAID_5e19d6bf0c0e8447b58f76415d153338"><Constraints><Constraint name="CNumericalArrayGenCondition" notes="deactivate the ComM_SystemUserOfPnc if all values are 65535" type="Pre-condition"/></Constraints><documentation value="System user handle generated for the PNC if it is coordinated (0xFFFF otherwise)"/><properties changeability="frozen" collection="true" type="ComM_SystemUserOfPncType"/></attribute><attribute name="WakeupEnabledOfPnc" scope="Private" xmi:idref="EAID_20ed681b5549cc7fc7a61c6301225f7e"><Constraints><Constraint name="CBoolArrayGenCondition" notes="deactivate the ComM_WakeupEnabledOfPnc if all values are false" type="Pre-condition"/></Constraints><documentation value="TRUE if PNC is referenced by at least one EcuM Wake-up Source and is mapped to at least one EthIf Switch Port Group"/><properties changeability="frozen" collection="true" type="ComM_WakeupEnabledOfPncType"/></attribute></attributes></element><element name="PncPb:ConstStruct" scope="public" xmi:idref="EAID_ee3ca0253d1c1982a122714edf2b0d9b" xmi:type="uml:Object"><properties documentation="Contains PostBuild configuration parameters of partial networks" isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><constraints/><attributes><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_67849bf01cce717c5aa859c5f94a62c2"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute><attribute name="SignalByteIndexOfPncPb" scope="Private" xmi:idref="EAID_0aa1889cfdfabe5675c5147cdefc7ada"><Constraints/><documentation value="Byte index in the partial network related signals (EIRA, ERA)"/><properties changeability="frozen" collection="true" type="ComM_SignalByteIndexOfPncPbType"/></attribute></attributes></element><element name="PncSignal:ConstStruct" scope="public" xmi:idref="EAID_185ff4347ce7dcfc8661eccee4657fa5" xmi:type="uml:Object"><properties documentation="Information about partial network signals (EIRA, ERA)" isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><constraints/><attributes><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_853cfff0059052e28b40a7c1618a698f"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute><attribute name="BusTypeIdOfPncSignal" scope="Private" xmi:idref="EAID_0420354a71e86cf14829e04452b14e4c"><Constraints/><documentation value="For EIRA RX Signals: An internal id for the BusType"/><properties changeability="frozen" collection="true" type="ComM_BusTypeIdOfPncSignalType"/></attribute><attribute name="TypeOfPncSignal" scope="Private" xmi:idref="EAID_ab0d4452e236ea9a4fd77449281b2486"><Constraints/><documentation value="Partial network signal type"/><properties changeability="frozen" collection="true" type="ComM_TypeOfPncSignalType"/></attribute></attributes></element><element name="User:ConstStruct" scope="public" xmi:idref="EAID_3f0fee0435e0fd385611b3a77d7788c4" xmi:type="uml:Object"><properties documentation="Information about ComM users" isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><constraints/><attributes><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_4bdb5ac1ce6cf9af1e809d0f82dfe261"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute><attribute name="PncUserOfUser" scope="Private" xmi:idref="EAID_595e7f54509fe780399d8cb70ce2379f"><Constraints/><documentation value="decides if a user is a partial network user or a direct channel user"/><properties changeability="frozen" collection="true" type="ComM_PncUserOfUserType"/></attribute></attributes></element><element name="UserByteMask:ConstStruct" scope="public" xmi:idref="EAID_edce6252e7d4d08ec6b22bd9cf751635" xmi:type="uml:Object"><properties documentation="Each user has N entries in this array (N = # channels attached to this user, directly or through PNC). Each entry describes a Byte Position and a Mask for storing/clearing the user request in UserReqFullCom" isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><constraints/><attributes><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_7cec1eb38b7fbd087cd1604a3a923a9f"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute><attribute name="ChannelOfUserByteMask" scope="Private" xmi:idref="EAID_2d61a0fef8abe6474b6bedab060a5b1f"><Constraints/><documentation value="ID of the channel which is requested by this entry."/><properties changeability="frozen" collection="true" type="NetworkHandleType"/></attribute><attribute name="ClearMaskOfUserByteMask" scope="Private" xmi:idref="EAID_a8acca63e83836de451fbe2662a4129f"><Constraints/><documentation value="Clear-mask for clearing the bit which corresponds to this user"/><properties changeability="frozen" collection="true" type="ComM_ClearMaskOfUserByteMaskType"/></attribute><attribute name="SetMaskOfUserByteMask" scope="Private" xmi:idref="EAID_68fd78a14802aacdb62658bae679a826"><Constraints/><documentation value="Set-mask for setting the bit which corresponds to this user"/><properties changeability="frozen" collection="true" type="ComM_SetMaskOfUserByteMaskType"/></attribute></attributes></element><element name="UserPncByteMask:ConstStruct" scope="public" xmi:idref="EAID_3e6e3abbf349922b36ae10dcc805f70f" xmi:type="uml:Object"><properties documentation="Each partial network user has N entries in this array (N = # pncs attached to this user). Each entry describes a Byte Position and a Mask for storing/clearing the user request in UserReqPncFullCom" isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><constraints/><attributes><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_60a2682ba1d46007d185aed98517a2f9"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute><attribute name="ClearMaskOfUserPncByteMask" scope="Private" xmi:idref="EAID_0d036fd173739d4cb0cba4ae5a0a0eac"><Constraints/><documentation value="Clear-mask for clearing the bit which corresponds to this user"/><properties changeability="frozen" collection="true" type="ComM_ClearMaskOfUserPncByteMaskType"/></attribute><attribute name="PncIDOfUserPncByteMask" scope="Private" xmi:idref="EAID_d23cfb86df3b7614be4e9d1c42cb3506"><Constraints/><documentation value="System Id of the partial network which is requested by this entry"/><properties changeability="frozen" collection="true" type="PNCHandleType"/></attribute><attribute name="SetMaskOfUserPncByteMask" scope="Private" xmi:idref="EAID_3b0316ced4a396c7b8c4240d7ab0952a"><Constraints/><documentation value="Set-mask for setting the bit which corresponds to this user"/><properties changeability="frozen" collection="true" type="ComM_SetMaskOfUserPncByteMaskType"/></attribute></attributes></element><element name="PncChannelMapping:ConstArray" scope="public" xmi:idref="EAID_1d65a113fe927d0401f52a2fef75429e" xmi:type="uml:Object"><attributes><attribute name="PncChannelMapping" scope="Private" xmi:idref="EAID_f7a82db69bb8ff870afa539b60415eba"><Constraints/><properties changeability="frozen" collection="true" type="ComM_PncChannelMappingType"/></attribute><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_0b175f148f9d74df7060b63d9230fdbb"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute></attributes></element><element name="UserModeNotiFunc:ConstArray" scope="public" xmi:idref="EAID_48c7e420aa45807a2bb0e94a6f797018" xmi:type="uml:Object"><properties documentation="User Mode Notification Functions (RTE Mode-Switch Interface)" isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><attributes><attribute name="UserModeNotiFunc" scope="Private" xmi:idref="EAID_2d12597acf91dd4a06a8c4ff851ebeeb"><Constraints><Constraint name="IndirectableFeatureDeactivationCondition" notes="No user mode Notification configured." type="Pre-condition"/></Constraints><documentation value="User Mode Notification Functions (RTE Mode-Switch Interface)"/><properties changeability="frozen" collection="true" type="ComM_StateNotiFuncPtrType"/></attribute><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_e3b870838fe8f3ed2fbb9d09ca9a4e6e"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute></attributes></element><element name="ActivePncComMode:VarArray" scope="public" xmi:idref="EAID_1be228193456239f9afbbe8be56f271e" xmi:type="uml:Object"><properties documentation="stores the current partial network state" isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><attributes><attribute name="ActivePncComMode" scope="Private" xmi:idref="EAID_b1ea91e48aa593cfdfb5326ce070bd95"><Constraints><Constraint name="CUserPreCompileDeactivationCondition" notes="PNC Support is disabled" type="Pre-condition"/></Constraints><documentation value="stores the current partial network state"/><properties collection="true"/></attribute><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_a884d433023bcb27e112fc9af638cd49"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute></attributes></element><element name="BusPncComModeReq:VarArray" scope="public" xmi:idref="EAID_70e08a0b97fd9b29806fb4ff1313b67b" xmi:type="uml:Object"><properties documentation="Stores the partial network state requested by external requests" isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><attributes><attribute name="BusPncComModeReq" scope="Private" xmi:idref="EAID_a6c2beafb79c73ec7b779ad612c9c44b"><Constraints/><documentation value="Stores the partial network state requested by external requests"/><properties collection="true"/></attribute><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_36463d8a229c9d4560e727b3bd7ffa31"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute></attributes></element><element name="BusSleepModeIndicated:VarArray" scope="public" xmi:idref="EAID_b9de19103e15de4759e27fe448c4ce2e" xmi:type="uml:Object"><properties documentation="Bus Sleep mode indicated by the BusSM, TRUE if indicated, FALSE otherwise" isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><attributes><attribute name="BusSleepModeIndicated" scope="Private" xmi:idref="EAID_589527a41167e30414ffe64d9ea4537a"><Constraints><Constraint name="IndirectableFeatureDeactivationCondition" notes="NM variant LINSLAVE is not present" type="Pre-condition"/></Constraints><documentation value="Bus Sleep mode indicated by the BusSM, TRUE if indicated, FALSE otherwise"/><properties collection="true"/></attribute><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_b64164c0d5088f64c5595882b7c642ff"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute></attributes></element><element name="DcmRequestActive:VarArray" scope="public" xmi:idref="EAID_d0306faa28faf77e5a0a0b45115c9cd4" xmi:type="uml:Object"><properties documentation="Status of Dcm active diagnostic request, TRUE if requested, FALSE otherwise" isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><attributes><attribute name="DcmRequestActive" scope="Private" xmi:idref="EAID_1f796f0c668a9e5acc111b6bc6b04b46"><Constraints><Constraint name="IndirectableFeatureDeactivationCondition" notes="Keep Awake Channel support or Dcm support is disabled" type="Pre-condition"/></Constraints><documentation value="Status of Dcm active diagnostic request, TRUE if requested, FALSE otherwise"/><properties collection="true"/></attribute><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_74bbd861a0c80abf4af7c39f4b1e6c92"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute></attributes></element><element name="FullComRequesters:VarArray" scope="public" xmi:idref="EAID_7315fb7b8323f1adad3b70682c5785bb" xmi:type="uml:Object"><properties documentation="Temporarily stores the IDs of users currently requesting FullComm for a channel. It is used as a pseudo-parameter between ComM_CurrentChannelRequestUpdate and the generated ComM_CurrentChannelRequestNotification." isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><attributes><attribute name="FullComRequesters" scope="Private" xmi:idref="EAID_037d020c4a5c405c53eb330ea6455f2a"><Constraints><Constraint name="IndirectableFeatureDeactivationCondition" notes="Deactivated because no channel had ComMFullCommRequestNoficiation enabled" type="Pre-condition"/></Constraints><documentation value="Temporarily stores the IDs of users currently requesting FullComm for a channel. It is used as a pseudo-parameter between ComM_CurrentChannelRequestUpdate and the generated ComM_CurrentChannelRequestNotification."/><properties collection="true"/></attribute><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_024515bf6439b94a052462118a9c1432"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute></attributes></element><element name="LastStateChange:VarArray" scope="public" xmi:idref="EAID_a4541417e91d5ab7da61695ceb094243" xmi:type="uml:Object"><properties documentation="Variables for storing the last notified user mode" isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><attributes><attribute name="LastStateChange" scope="Private" xmi:idref="EAID_385947a4755abc4bdfa726a20ff2cadd"><Constraints><Constraint name="IndirectableFeatureDeactivationCondition" notes="No user mode Notification configured." type="Pre-condition"/></Constraints><documentation value="Variables for storing the last notified user mode"/><properties collection="true"/></attribute><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_03b8d766f23855c5cc429886d8b5e331"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute></attributes></element><element name="MinFullComModeTimer:VarArray" scope="public" xmi:idref="EAID_54062dcd3617f9674453ca3a9da48a69" xmi:type="uml:Object"><properties documentation="The current value of Min Full Com Mode timer" isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><attributes><attribute name="MinFullComModeTimer" scope="Private" xmi:idref="EAID_9f7ad2edafcecc88d4d898986caeec77"><Constraints><Constraint name="IndirectableFeatureDeactivationCondition" notes="Min Full Com Timer is disabled" type="Pre-condition"/></Constraints><documentation value="The current value of Min Full Com Mode timer"/><properties collection="true"/></attribute><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_3985ba2d6ac63ce1f7d2d61b401de4de"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute></attributes></element><element name="NmLightTimer:VarArray" scope="public" xmi:idref="EAID_c77d321b871ef38c6217abd7a01946af" xmi:type="uml:Object"><properties documentation="The current value of Nm Light or Nm Light Silent timer" isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><attributes><attribute name="NmLightTimer" scope="Private" xmi:idref="EAID_2316cbaa567d34cefec74eb31869462a"><Constraints><Constraint name="IndirectableFeatureDeactivationCondition" notes="Nm Light and Nm Light Silent Timers are disabled" type="Pre-condition"/></Constraints><documentation value="The current value of Nm Light or Nm Light Silent timer"/><properties collection="true"/></attribute><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_0b845491708a33ec00831112dd1005cb"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute></attributes></element><element name="PncPSleepTimer:VarArray" scope="public" xmi:idref="EAID_66cd903be4717d479ff657bc2c9003d7" xmi:type="uml:Object"><properties documentation="partial network prepare sleep timer" isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><attributes><attribute name="PncPSleepTimer" scope="Private" xmi:idref="EAID_85995282838aa43794ca1a46ea91f87c"><Constraints><Constraint name="CUserPreCompileDeactivationCondition" notes="PNC Support is disabled" type="Pre-condition"/></Constraints><documentation value="partial network prepare sleep timer"/><properties collection="true"/></attribute><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_6b6649517f2b4d90b2649502cf95fb8a"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute></attributes></element><element name="PncSignalValues:VarArray" scope="public" xmi:idref="EAID_43d350dcbc20ae9e7f8710e87d1f3d5e" xmi:type="uml:Object"><attributes><attribute name="PncSignalValues" scope="Private" xmi:idref="EAID_7265477cc8af10fb0a7831c4649302bb"><Constraints/><properties collection="true"/></attribute><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_3c3b7db4d31d4e3fb7caea001b642ec3"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute></attributes></element><element name="UserReqFullCom:VarArray" scope="public" xmi:idref="EAID_9a98b427de242badd4b2e8d988706a27" xmi:type="uml:Object"><properties documentation="RAM array used to store user requests for channels as bitmasks. Each channel 'owns' 1..n bytes in this array, depending on the number of users assigned to it." isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><attributes><attribute name="UserReqFullCom" scope="Private" xmi:idref="EAID_79b88d29a5f0e1a6548892d83b23839e"><Constraints/><documentation value="RAM array used to store user requests for channels as bitmasks. Each channel 'owns' 1..n bytes in this array, depending on the number of users assigned to it."/><properties collection="true"/></attribute><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_17f2fef6c96bfc6c6fc0965dad3870c4"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute></attributes></element><element name="UserReqPncFullCom:VarArray" scope="public" xmi:idref="EAID_f4c959a5f2e36926e6b1f0346a4012e0" xmi:type="uml:Object"><properties documentation="RAM array used to store user requests for PNCs as bitmasks. Each PNC 'owns' 1..n bytes in this array, depending on the number of users assigned to it." isAbstract="false" isSpecification="false" nType="0" sType="Object" scope="public"/><attributes><attribute name="UserReqPncFullCom" scope="Private" xmi:idref="EAID_61b61064c9b70428f7d54c1fe7016383"><Constraints/><documentation value="RAM array used to store user requests for PNCs as bitmasks. Each PNC 'owns' 1..n bytes in this array, depending on the number of users assigned to it."/><properties collection="true"/></attribute><attribute name="isOutOfBoundsWriteProtectionProhibited" scope="Public" xmi:idref="EAID_cff588f1c5a84c30a1374cb7720c406c"><initial body="false"/><properties changeability="frozen" collection="false" derived="0" duplicates="0" type="boolean"/><bounds lower="1" upper="1"/></attribute></attributes></element></elements><diagrams><diagram xmi:id="EAID_a4f78d9fdc7dff859ecc7de1ed1b5d14"><elements><element subject="EAID_dc5c080f59d4607dc71f56926d31cb9d"/><element subject="EAID_e924de3ac25b8403fe1b2f532e44eda8"/><element subject="EAID_d29c9e774d5669e09542cb0454bfb396"/><element subject="EAID_6f3746e4d170e1b776c08012abba538c"/><element subject="EAID_84f41182393acfc0da1d01b3dfe0bdef"/><element subject="EAID_55106321ebefc0040c2879f6b1f74133"/><element subject="EAID_ee3ca0253d1c1982a122714edf2b0d9b"/><element subject="EAID_d532643c77e3ed631d33b8000e5b3b0e"/><element subject="EAID_f99087a9d7327c975ba6e4b7c90867f0"/><element subject="EAID_4cbb7e983dffc1fba0ba418ea1f31bf7"/><element subject="EAID_995c654f6ce7c0a05cc1e81790c22e75"/><element subject="EAID_185ff4347ce7dcfc8661eccee4657fa5"/><element subject="EAID_d3389586478ca6860e35ec0cacc748de"/><element subject="EAID_154216b1aa9047646295d4e379b692cf"/><element subject="EAID_3f0fee0435e0fd385611b3a77d7788c4"/><element subject="EAID_71051b943953a3a13f159103cf95935d"/><element subject="EAID_a2ae8ae10e2a6f2f7967ad143b56b8f6"/><element subject="EAID_edce6252e7d4d08ec6b22bd9cf751635"/><element subject="EAID_c801cdc2d2a134d27a67dafc9007c293"/><element subject="EAID_3e6e3abbf349922b36ae10dcc805f70f"/><element subject="EAID_cc529bad959797ad11eb53bc95de26a5"/><element subject="EAID_1d65a113fe927d0401f52a2fef75429e"/><element subject="EAID_48c7e420aa45807a2bb0e94a6f797018"/><element subject="EAID_1be228193456239f9afbbe8be56f271e"/><element subject="EAID_67d5a7a73ed803a4d6d62cf243e9b124"/><element subject="EAID_70e08a0b97fd9b29806fb4ff1313b67b"/><element subject="EAID_3c15b09f8e573166ad2df34318d26dfc"/><element subject="EAID_b9de19103e15de4759e27fe448c4ce2e"/><element subject="EAID_f293276c19a37ef88e573708decee42e"/><element subject="EAID_d0306faa28faf77e5a0a0b45115c9cd4"/><element subject="EAID_43b846c8d766ab2ee17e20f548d4b3a1"/><element subject="EAID_7315fb7b8323f1adad3b70682c5785bb"/><element subject="EAID_a4541417e91d5ab7da61695ceb094243"/><element subject="EAID_4c3a938e278b2e87e8ddb92b1ca97d06"/><element subject="EAID_54062dcd3617f9674453ca3a9da48a69"/><element subject="EAID_16bd24ccb35d208c75e956a564a9d4b8"/><element subject="EAID_c77d321b871ef38c6217abd7a01946af"/><element subject="EAID_4aa8fd532b783dd431c72f5671c77063"/><element subject="EAID_66cd903be4717d479ff657bc2c9003d7"/><element subject="EAID_c2c18b9bc30c22134f2f12515a33be3f"/><element subject="EAID_43d350dcbc20ae9e7f8710e87d1f3d5e"/><element subject="EAID_9a98b427de242badd4b2e8d988706a27"/><element subject="EAID_f4c959a5f2e36926e6b1f0346a4012e0"/></elements><model owner="EAPKcc66b7eb0e6cd2d072b0d06421b873cf" package="EAPKcc66b7eb0e6cd2d072b0d06421b873cf"/><properties name="All Data and Relations" type="Object"/><style1 value="ShowPrivate=1;ShowProtected=1;ShowPublic=1;HideRelationships=0;Locked=0;Border=1;HighlightForeign=1;PackageContents=1;SequenceNotes=0;ScalePrintImage=0;PPgs.cx=0;PPgs.cy=0;DocSize.cx=791;DocSize.cy=1134;ShowDetails=0;Orientation=P;Zoom=100;ShowTags=0;OpParams=1;VisibleAttributeDetail=0;ShowOpRetType=1;ShowIcons=1;CollabNums=0;HideProps=0;ShowReqs=0;ShowCons=0;PaperSize=9;HideParents=0;UseAlias=0;HideAtts=0;HideOps=0;HideStereo=0;HideElemStereo=0;ShowTests=0;ShowMaint=0;ConnectorNotation=UML 2.1;ExplicitNavigability=0;AdvancedElementProps=1;AdvancedFeatureProps=1;AdvancedConnectorProps=1;ShowNotes=0;SuppressBrackets=0;SuppConnectorLabels=0;PrintPageHeadFoot=0;ShowAsList=0;"/><style2 value="ExcludeRTF=0;DocAll=0;HideQuals=0;AttPkg=1;ShowTests=0;ShowMaint=0;SuppressFOC=1;MatrixActive=0;SwimlanesActive=0;MatrixLineWidth=1;MatrixLocked=0;TConnectorNotation=UML 2.1;TExplicitNavigability=0;AdvancedElementProps=1;AdvancedFeatureProps=1;AdvancedConnectorProps=1;ProfileData=;MDGDgm=;STBLDgm=;ShowNotes=0;VisibleAttributeDetail=0;ShowOpRetType=1;SuppressBrackets=0;SuppConnectorLabels=0;PrintPageHeadFoot=0;ShowAsList=0;"/><swimlanes value="locked=false;orientation=0;width=0;inbar=false;names=false;color=0;bold=false;fcol=0;;cls=0;"/><matrixitems value="locked=false;matrixactive=false;swimlanesactive=false;width=1;"/><extendedProperties/></diagram><diagram xmi:id="EAID_c66bfa3617b738db5eba4f57f524c4cf"><elements><element subject="EAID_dc5c080f59d4607dc71f56926d31cb9d"/><element subject="EAID_e924de3ac25b8403fe1b2f532e44eda8"/><element subject="EAID_d29c9e774d5669e09542cb0454bfb396"/><element subject="EAID_6f3746e4d170e1b776c08012abba538c"/><element subject="EAID_84f41182393acfc0da1d01b3dfe0bdef"/><element subject="EAID_55106321ebefc0040c2879f6b1f74133"/><element subject="EAID_ee3ca0253d1c1982a122714edf2b0d9b"/><element subject="EAID_d532643c77e3ed631d33b8000e5b3b0e"/><element subject="EAID_f99087a9d7327c975ba6e4b7c90867f0"/><element subject="EAID_4cbb7e983dffc1fba0ba418ea1f31bf7"/><element subject="EAID_995c654f6ce7c0a05cc1e81790c22e75"/><element subject="EAID_185ff4347ce7dcfc8661eccee4657fa5"/><element subject="EAID_d3389586478ca6860e35ec0cacc748de"/><element subject="EAID_154216b1aa9047646295d4e379b692cf"/><element subject="EAID_3f0fee0435e0fd385611b3a77d7788c4"/><element subject="EAID_71051b943953a3a13f159103cf95935d"/><element subject="EAID_a2ae8ae10e2a6f2f7967ad143b56b8f6"/><element subject="EAID_edce6252e7d4d08ec6b22bd9cf751635"/><element subject="EAID_c801cdc2d2a134d27a67dafc9007c293"/><element subject="EAID_3e6e3abbf349922b36ae10dcc805f70f"/><element subject="EAID_cc529bad959797ad11eb53bc95de26a5"/><element subject="EAID_1d65a113fe927d0401f52a2fef75429e"/><element subject="EAID_48c7e420aa45807a2bb0e94a6f797018"/></elements><model owner="EAPKcc66b7eb0e6cd2d072b0d06421b873cf" package="EAPKcc66b7eb0e6cd2d072b0d06421b873cf"/><properties name="CONST with Struct Elements" type="Object"/><style1 value="ShowPrivate=1;ShowProtected=1;ShowPublic=1;HideRelationships=0;Locked=0;Border=1;HighlightForeign=1;PackageContents=1;SequenceNotes=0;ScalePrintImage=0;PPgs.cx=0;PPgs.cy=0;DocSize.cx=791;DocSize.cy=1134;ShowDetails=0;Orientation=P;Zoom=100;ShowTags=0;OpParams=1;VisibleAttributeDetail=0;ShowOpRetType=1;ShowIcons=1;CollabNums=0;HideProps=0;ShowReqs=0;ShowCons=0;PaperSize=9;HideParents=0;UseAlias=0;HideAtts=0;HideOps=0;HideStereo=0;HideElemStereo=0;ShowTests=0;ShowMaint=0;ConnectorNotation=UML 2.1;ExplicitNavigability=0;AdvancedElementProps=1;AdvancedFeatureProps=1;AdvancedConnectorProps=1;ShowNotes=0;SuppressBrackets=0;SuppConnectorLabels=0;PrintPageHeadFoot=0;ShowAsList=0;"/><style2 value="ExcludeRTF=0;DocAll=0;HideQuals=0;AttPkg=1;ShowTests=0;ShowMaint=0;SuppressFOC=1;MatrixActive=0;SwimlanesActive=0;MatrixLineWidth=1;MatrixLocked=0;TConnectorNotation=UML 2.1;TExplicitNavigability=0;AdvancedElementProps=1;AdvancedFeatureProps=1;AdvancedConnectorProps=1;ProfileData=;MDGDgm=;STBLDgm=;ShowNotes=0;VisibleAttributeDetail=0;ShowOpRetType=1;SuppressBrackets=0;SuppConnectorLabels=0;PrintPageHeadFoot=0;ShowAsList=0;"/><swimlanes value="locked=false;orientation=0;width=0;inbar=false;names=false;color=0;bold=false;fcol=0;;cls=0;"/><matrixitems value="locked=false;matrixactive=false;swimlanesactive=false;width=1;"/><extendedProperties/></diagram><diagram xmi:id="EAID_3a1d2926ed64071ce1ca9c3e4c1aa97d"><elements><element subject="EAID_dc5c080f59d4607dc71f56926d31cb9d"/><element subject="EAID_e924de3ac25b8403fe1b2f532e44eda8"/><element subject="EAID_d29c9e774d5669e09542cb0454bfb396"/><element subject="EAID_6f3746e4d170e1b776c08012abba538c"/><element subject="EAID_84f41182393acfc0da1d01b3dfe0bdef"/><element subject="EAID_55106321ebefc0040c2879f6b1f74133"/><element subject="EAID_ee3ca0253d1c1982a122714edf2b0d9b"/><element subject="EAID_d532643c77e3ed631d33b8000e5b3b0e"/><element subject="EAID_f99087a9d7327c975ba6e4b7c90867f0"/><element subject="EAID_4cbb7e983dffc1fba0ba418ea1f31bf7"/><element subject="EAID_995c654f6ce7c0a05cc1e81790c22e75"/><element subject="EAID_185ff4347ce7dcfc8661eccee4657fa5"/><element subject="EAID_d3389586478ca6860e35ec0cacc748de"/><element subject="EAID_154216b1aa9047646295d4e379b692cf"/><element subject="EAID_3f0fee0435e0fd385611b3a77d7788c4"/><element subject="EAID_71051b943953a3a13f159103cf95935d"/><element subject="EAID_a2ae8ae10e2a6f2f7967ad143b56b8f6"/><element subject="EAID_edce6252e7d4d08ec6b22bd9cf751635"/><element subject="EAID_c801cdc2d2a134d27a67dafc9007c293"/><element subject="EAID_3e6e3abbf349922b36ae10dcc805f70f"/><element subject="EAID_cc529bad959797ad11eb53bc95de26a5"/><element subject="EAID_1d65a113fe927d0401f52a2fef75429e"/><element subject="EAID_48c7e420aa45807a2bb0e94a6f797018"/></elements><model owner="EAPKcc66b7eb0e6cd2d072b0d06421b873cf" package="EAPKcc66b7eb0e6cd2d072b0d06421b873cf"/><properties name="CONST without Struct Elements" type="Object"/><style1 value="ShowPrivate=1;ShowProtected=1;ShowPublic=1;HideRelationships=0;Locked=0;Border=1;HighlightForeign=1;PackageContents=1;SequenceNotes=0;ScalePrintImage=0;PPgs.cx=0;PPgs.cy=0;DocSize.cx=791;DocSize.cy=1134;ShowDetails=0;Orientation=P;Zoom=100;ShowTags=0;OpParams=1;VisibleAttributeDetail=0;ShowOpRetType=1;ShowIcons=1;CollabNums=0;HideProps=0;ShowReqs=0;ShowCons=0;PaperSize=9;HideParents=0;UseAlias=0;HideAtts=1;HideOps=1;HideStereo=0;HideElemStereo=0;ShowTests=0;ShowMaint=0;ConnectorNotation=UML 2.1;ExplicitNavigability=0;AdvancedElementProps=1;AdvancedFeatureProps=1;AdvancedConnectorProps=1;ShowNotes=0;SuppressBrackets=0;SuppConnectorLabels=0;PrintPageHeadFoot=0;ShowAsList=0;"/><style2 value="ExcludeRTF=0;DocAll=0;HideQuals=0;AttPkg=1;ShowTests=0;ShowMaint=0;SuppressFOC=1;MatrixActive=0;SwimlanesActive=0;MatrixLineWidth=1;MatrixLocked=0;TConnectorNotation=UML 2.1;TExplicitNavigability=0;AdvancedElementProps=1;AdvancedFeatureProps=1;AdvancedConnectorProps=1;ProfileData=;MDGDgm=;STBLDgm=;ShowNotes=1;VisibleAttributeDetail=0;ShowOpRetType=1;SuppressBrackets=0;SuppConnectorLabels=0;PrintPageHeadFoot=0;ShowAsList=0;"/><swimlanes value="locked=false;orientation=0;width=0;inbar=false;names=false;color=0;bold=false;fcol=0;;cls=0;"/><matrixitems value="locked=false;matrixactive=false;swimlanesactive=false;width=1;"/><extendedProperties/></diagram><diagram xmi:id="EAID_20961643b2dcea68006c83a7e6b03c90"><elements><element subject="EAID_1be228193456239f9afbbe8be56f271e"/><element subject="EAID_55106321ebefc0040c2879f6b1f74133"/><element subject="EAID_70e08a0b97fd9b29806fb4ff1313b67b"/><element subject="EAID_55106321ebefc0040c2879f6b1f74133"/><element subject="EAID_b9de19103e15de4759e27fe448c4ce2e"/><element subject="EAID_dc5c080f59d4607dc71f56926d31cb9d"/><element subject="EAID_d0306faa28faf77e5a0a0b45115c9cd4"/><element subject="EAID_dc5c080f59d4607dc71f56926d31cb9d"/><element subject="EAID_7315fb7b8323f1adad3b70682c5785bb"/><element subject="EAID_a4541417e91d5ab7da61695ceb094243"/><element subject="EAID_3f0fee0435e0fd385611b3a77d7788c4"/><element subject="EAID_54062dcd3617f9674453ca3a9da48a69"/><element subject="EAID_dc5c080f59d4607dc71f56926d31cb9d"/><element subject="EAID_c77d321b871ef38c6217abd7a01946af"/><element subject="EAID_dc5c080f59d4607dc71f56926d31cb9d"/><element subject="EAID_66cd903be4717d479ff657bc2c9003d7"/><element subject="EAID_55106321ebefc0040c2879f6b1f74133"/><element subject="EAID_43d350dcbc20ae9e7f8710e87d1f3d5e"/><element subject="EAID_9a98b427de242badd4b2e8d988706a27"/><element subject="EAID_f4c959a5f2e36926e6b1f0346a4012e0"/></elements><model owner="EAPKcc66b7eb0e6cd2d072b0d06421b873cf" package="EAPKcc66b7eb0e6cd2d072b0d06421b873cf"/><properties name="VAR and Relations" type="Object"/><style1 value="ShowPrivate=1;ShowProtected=1;ShowPublic=1;HideRelationships=0;Locked=0;Border=1;HighlightForeign=1;PackageContents=1;SequenceNotes=0;ScalePrintImage=0;PPgs.cx=0;PPgs.cy=0;DocSize.cx=791;DocSize.cy=1134;ShowDetails=0;Orientation=P;Zoom=100;ShowTags=0;OpParams=1;VisibleAttributeDetail=0;ShowOpRetType=1;ShowIcons=1;CollabNums=0;HideProps=0;ShowReqs=0;ShowCons=0;PaperSize=9;HideParents=0;UseAlias=0;HideAtts=0;HideOps=0;HideStereo=0;HideElemStereo=0;ShowTests=0;ShowMaint=0;ConnectorNotation=UML 2.1;ExplicitNavigability=0;AdvancedElementProps=1;AdvancedFeatureProps=1;AdvancedConnectorProps=1;ShowNotes=0;SuppressBrackets=0;SuppConnectorLabels=0;PrintPageHeadFoot=0;ShowAsList=0;"/><style2 value="ExcludeRTF=0;DocAll=0;HideQuals=0;AttPkg=1;ShowTests=0;ShowMaint=0;SuppressFOC=1;MatrixActive=0;SwimlanesActive=0;MatrixLineWidth=1;MatrixLocked=0;TConnectorNotation=UML 2.1;TExplicitNavigability=0;AdvancedElementProps=1;AdvancedFeatureProps=1;AdvancedConnectorProps=1;ProfileData=;MDGDgm=;STBLDgm=;ShowNotes=1;VisibleAttributeDetail=0;ShowOpRetType=1;SuppressBrackets=0;SuppConnectorLabels=0;PrintPageHeadFoot=0;ShowAsList=0;"/><swimlanes value="locked=false;orientation=0;width=0;inbar=false;names=false;color=0;bold=false;fcol=0;;cls=0;"/><matrixitems value="locked=false;matrixactive=false;swimlanesactive=false;width=1;"/><extendedProperties/></diagram><diagram xmi:id="EAID_5b82f4552ac27cbfba80ea7f1fe028c2"><elements><element subject="EAID_43d350dcbc20ae9e7f8710e87d1f3d5e"/></elements><model owner="EAPKcc66b7eb0e6cd2d072b0d06421b873cf" package="EAPKcc66b7eb0e6cd2d072b0d06421b873cf"/><properties name="Data Accessed by Adress Operator" type="Object"/><style1 value="ShowPrivate=1;ShowProtected=1;ShowPublic=1;HideRelationships=0;Locked=0;Border=1;HighlightForeign=1;PackageContents=1;SequenceNotes=0;ScalePrintImage=0;PPgs.cx=0;PPgs.cy=0;DocSize.cx=791;DocSize.cy=1134;ShowDetails=0;Orientation=P;Zoom=100;ShowTags=0;OpParams=1;VisibleAttributeDetail=0;ShowOpRetType=1;ShowIcons=1;CollabNums=0;HideProps=0;ShowReqs=0;ShowCons=0;PaperSize=9;HideParents=0;UseAlias=0;HideAtts=1;HideOps=1;HideStereo=0;HideElemStereo=0;ShowTests=0;ShowMaint=0;ConnectorNotation=UML 2.1;ExplicitNavigability=0;AdvancedElementProps=1;AdvancedFeatureProps=1;AdvancedConnectorProps=1;ShowNotes=0;SuppressBrackets=0;SuppConnectorLabels=0;PrintPageHeadFoot=0;ShowAsList=0;"/><style2 value="ExcludeRTF=0;DocAll=0;HideQuals=0;AttPkg=1;ShowTests=0;ShowMaint=0;SuppressFOC=1;MatrixActive=0;SwimlanesActive=0;MatrixLineWidth=1;MatrixLocked=0;TConnectorNotation=UML 2.1;TExplicitNavigability=0;AdvancedElementProps=1;AdvancedFeatureProps=1;AdvancedConnectorProps=1;ProfileData=;MDGDgm=;STBLDgm=;ShowNotes=1;VisibleAttributeDetail=0;ShowOpRetType=1;SuppressBrackets=0;SuppConnectorLabels=0;PrintPageHeadFoot=0;ShowAsList=0;"/><swimlanes value="locked=false;orientation=0;width=0;inbar=false;names=false;color=0;bold=false;fcol=0;;cls=0;"/><matrixitems value="locked=false;matrixactive=false;swimlanesactive=false;width=1;"/><extendedProperties/></diagram><diagram xmi:id="EAID_9f3f6a328f8f201ffb5d604110bc32c2"><elements/><model owner="EAPKcc66b7eb0e6cd2d072b0d06421b873cf" package="EAPKcc66b7eb0e6cd2d072b0d06421b873cf"/><properties name="Data Accessed by Interface Handles" type="Object"/><style1 value="ShowPrivate=1;ShowProtected=1;ShowPublic=1;HideRelationships=0;Locked=0;Border=1;HighlightForeign=1;PackageContents=1;SequenceNotes=0;ScalePrintImage=0;PPgs.cx=0;PPgs.cy=0;DocSize.cx=791;DocSize.cy=1134;ShowDetails=0;Orientation=P;Zoom=100;ShowTags=0;OpParams=1;VisibleAttributeDetail=0;ShowOpRetType=1;ShowIcons=1;CollabNums=0;HideProps=0;ShowReqs=0;ShowCons=0;PaperSize=9;HideParents=0;UseAlias=0;HideAtts=1;HideOps=1;HideStereo=0;HideElemStereo=0;ShowTests=0;ShowMaint=0;ConnectorNotation=UML 2.1;ExplicitNavigability=0;AdvancedElementProps=1;AdvancedFeatureProps=1;AdvancedConnectorProps=1;ShowNotes=0;SuppressBrackets=0;SuppConnectorLabels=0;PrintPageHeadFoot=0;ShowAsList=0;"/><style2 value="ExcludeRTF=0;DocAll=0;HideQuals=0;AttPkg=1;ShowTests=0;ShowMaint=0;SuppressFOC=1;MatrixActive=0;SwimlanesActive=0;MatrixLineWidth=1;MatrixLocked=0;TConnectorNotation=UML 2.1;TExplicitNavigability=0;AdvancedElementProps=1;AdvancedFeatureProps=1;AdvancedConnectorProps=1;ProfileData=;MDGDgm=;STBLDgm=;ShowNotes=1;VisibleAttributeDetail=0;ShowOpRetType=1;SuppressBrackets=0;SuppConnectorLabels=0;PrintPageHeadFoot=0;ShowAsList=0;"/><swimlanes value="locked=false;orientation=0;width=0;inbar=false;names=false;color=0;bold=false;fcol=0;;cls=0;"/><matrixitems value="locked=false;matrixactive=false;swimlanesactive=false;width=1;"/><extendedProperties/></diagram><diagram xmi:id="EAID_0f7c773cf33b29407ea716400047c1af"><elements><element subject="EAID_dc5c080f59d4607dc71f56926d31cb9d"/><element subject="EAID_55106321ebefc0040c2879f6b1f74133"/><element subject="EAID_48c7e420aa45807a2bb0e94a6f797018"/><element subject="EAID_1be228193456239f9afbbe8be56f271e"/><element subject="EAID_70e08a0b97fd9b29806fb4ff1313b67b"/><element subject="EAID_b9de19103e15de4759e27fe448c4ce2e"/><element subject="EAID_d0306faa28faf77e5a0a0b45115c9cd4"/><element subject="EAID_7315fb7b8323f1adad3b70682c5785bb"/><element subject="EAID_a4541417e91d5ab7da61695ceb094243"/><element subject="EAID_54062dcd3617f9674453ca3a9da48a69"/><element subject="EAID_c77d321b871ef38c6217abd7a01946af"/><element subject="EAID_66cd903be4717d479ff657bc2c9003d7"/></elements><model owner="EAPKcc66b7eb0e6cd2d072b0d06421b873cf" package="EAPKcc66b7eb0e6cd2d072b0d06421b873cf"/><properties name="Max Precompile Configurable Data" type="Object"/><style1 value="ShowPrivate=1;ShowProtected=1;ShowPublic=1;HideRelationships=0;Locked=0;Border=1;HighlightForeign=1;PackageContents=1;SequenceNotes=0;ScalePrintImage=0;PPgs.cx=0;PPgs.cy=0;DocSize.cx=791;DocSize.cy=1134;ShowDetails=0;Orientation=P;Zoom=100;ShowTags=0;OpParams=1;VisibleAttributeDetail=0;ShowOpRetType=1;ShowIcons=1;CollabNums=0;HideProps=0;ShowReqs=0;ShowCons=0;PaperSize=9;HideParents=0;UseAlias=0;HideAtts=1;HideOps=1;HideStereo=0;HideElemStereo=0;ShowTests=0;ShowMaint=0;ConnectorNotation=UML 2.1;ExplicitNavigability=0;AdvancedElementProps=1;AdvancedFeatureProps=1;AdvancedConnectorProps=1;ShowNotes=0;SuppressBrackets=0;SuppConnectorLabels=0;PrintPageHeadFoot=0;ShowAsList=0;"/><style2 value="ExcludeRTF=0;DocAll=0;HideQuals=0;AttPkg=1;ShowTests=0;ShowMaint=0;SuppressFOC=1;MatrixActive=0;SwimlanesActive=0;MatrixLineWidth=1;MatrixLocked=0;TConnectorNotation=UML 2.1;TExplicitNavigability=0;AdvancedElementProps=1;AdvancedFeatureProps=1;AdvancedConnectorProps=1;ProfileData=;MDGDgm=;STBLDgm=;ShowNotes=1;VisibleAttributeDetail=0;ShowOpRetType=1;SuppressBrackets=0;SuppConnectorLabels=0;PrintPageHeadFoot=0;ShowAsList=0;"/><swimlanes value="locked=false;orientation=0;width=0;inbar=false;names=false;color=0;bold=false;fcol=0;;cls=0;"/><matrixitems value="locked=false;matrixactive=false;swimlanesactive=false;width=1;"/><extendedProperties/></diagram><diagram xmi:id="EAID_970030cbafbc3048158e013323402682"><elements/><model owner="EAPKcc66b7eb0e6cd2d072b0d06421b873cf" package="EAPKcc66b7eb0e6cd2d072b0d06421b873cf"/><properties name="Max Linktime Configurable Data" type="Object"/><style1 value="ShowPrivate=1;ShowProtected=1;ShowPublic=1;HideRelationships=0;Locked=0;Border=1;HighlightForeign=1;PackageContents=1;SequenceNotes=0;ScalePrintImage=0;PPgs.cx=0;PPgs.cy=0;DocSize.cx=791;DocSize.cy=1134;ShowDetails=0;Orientation=P;Zoom=100;ShowTags=0;OpParams=1;VisibleAttributeDetail=0;ShowOpRetType=1;ShowIcons=1;CollabNums=0;HideProps=0;ShowReqs=0;ShowCons=0;PaperSize=9;HideParents=0;UseAlias=0;HideAtts=1;HideOps=1;HideStereo=0;HideElemStereo=0;ShowTests=0;ShowMaint=0;ConnectorNotation=UML 2.1;ExplicitNavigability=0;AdvancedElementProps=1;AdvancedFeatureProps=1;AdvancedConnectorProps=1;ShowNotes=0;SuppressBrackets=0;SuppConnectorLabels=0;PrintPageHeadFoot=0;ShowAsList=0;"/><style2 value="ExcludeRTF=0;DocAll=0;HideQuals=0;AttPkg=1;ShowTests=0;ShowMaint=0;SuppressFOC=1;MatrixActive=0;SwimlanesActive=0;MatrixLineWidth=1;MatrixLocked=0;TConnectorNotation=UML 2.1;TExplicitNavigability=0;AdvancedElementProps=1;AdvancedFeatureProps=1;AdvancedConnectorProps=1;ProfileData=;MDGDgm=;STBLDgm=;ShowNotes=1;VisibleAttributeDetail=0;ShowOpRetType=1;SuppressBrackets=0;SuppConnectorLabels=0;PrintPageHeadFoot=0;ShowAsList=0;"/><swimlanes value="locked=false;orientation=0;width=0;inbar=false;names=false;color=0;bold=false;fcol=0;;cls=0;"/><matrixitems value="locked=false;matrixactive=false;swimlanesactive=false;width=1;"/><extendedProperties/></diagram><diagram xmi:id="EAID_2ee5c3e8678e0713cd1e8c989a35d3dc"><elements><element subject="EAID_e924de3ac25b8403fe1b2f532e44eda8"/><element subject="EAID_ee3ca0253d1c1982a122714edf2b0d9b"/><element subject="EAID_185ff4347ce7dcfc8661eccee4657fa5"/><element subject="EAID_3f0fee0435e0fd385611b3a77d7788c4"/><element subject="EAID_edce6252e7d4d08ec6b22bd9cf751635"/><element subject="EAID_3e6e3abbf349922b36ae10dcc805f70f"/><element subject="EAID_1d65a113fe927d0401f52a2fef75429e"/><element subject="EAID_43d350dcbc20ae9e7f8710e87d1f3d5e"/><element subject="EAID_9a98b427de242badd4b2e8d988706a27"/><element subject="EAID_f4c959a5f2e36926e6b1f0346a4012e0"/></elements><model owner="EAPKcc66b7eb0e6cd2d072b0d06421b873cf" package="EAPKcc66b7eb0e6cd2d072b0d06421b873cf"/><properties name="Max Postbuild Configurable Data" type="Object"/><style1 value="ShowPrivate=1;ShowProtected=1;ShowPublic=1;HideRelationships=0;Locked=0;Border=1;HighlightForeign=1;PackageContents=1;SequenceNotes=0;ScalePrintImage=0;PPgs.cx=0;PPgs.cy=0;DocSize.cx=791;DocSize.cy=1134;ShowDetails=0;Orientation=P;Zoom=100;ShowTags=0;OpParams=1;VisibleAttributeDetail=0;ShowOpRetType=1;ShowIcons=1;CollabNums=0;HideProps=0;ShowReqs=0;ShowCons=0;PaperSize=9;HideParents=0;UseAlias=0;HideAtts=1;HideOps=1;HideStereo=0;HideElemStereo=0;ShowTests=0;ShowMaint=0;ConnectorNotation=UML 2.1;ExplicitNavigability=0;AdvancedElementProps=1;AdvancedFeatureProps=1;AdvancedConnectorProps=1;ShowNotes=0;SuppressBrackets=0;SuppConnectorLabels=0;PrintPageHeadFoot=0;ShowAsList=0;"/><style2 value="ExcludeRTF=0;DocAll=0;HideQuals=0;AttPkg=1;ShowTests=0;ShowMaint=0;SuppressFOC=1;MatrixActive=0;SwimlanesActive=0;MatrixLineWidth=1;MatrixLocked=0;TConnectorNotation=UML 2.1;TExplicitNavigability=0;AdvancedElementProps=1;AdvancedFeatureProps=1;AdvancedConnectorProps=1;ProfileData=;MDGDgm=;STBLDgm=;ShowNotes=1;VisibleAttributeDetail=0;ShowOpRetType=1;SuppressBrackets=0;SuppConnectorLabels=0;PrintPageHeadFoot=0;ShowAsList=0;"/><swimlanes value="locked=false;orientation=0;width=0;inbar=false;names=false;color=0;bold=false;fcol=0;;cls=0;"/><matrixitems value="locked=false;matrixactive=false;swimlanesactive=false;width=1;"/><extendedProperties/></diagram><diagram xmi:id="EAID_3f8f53cda1a8e54019b3ca64356d3a76"><elements/><model owner="EAPKcc66b7eb0e6cd2d072b0d06421b873cf" package="EAPKcc66b7eb0e6cd2d072b0d06421b873cf"/><properties name="Calibration Lite Data" type="Object"/><style1 value="ShowPrivate=1;ShowProtected=1;ShowPublic=1;HideRelationships=0;Locked=0;Border=1;HighlightForeign=1;PackageContents=1;SequenceNotes=0;ScalePrintImage=0;PPgs.cx=0;PPgs.cy=0;DocSize.cx=791;DocSize.cy=1134;ShowDetails=0;Orientation=P;Zoom=100;ShowTags=0;OpParams=1;VisibleAttributeDetail=0;ShowOpRetType=1;ShowIcons=1;CollabNums=0;HideProps=0;ShowReqs=0;ShowCons=0;PaperSize=9;HideParents=0;UseAlias=0;HideAtts=1;HideOps=1;HideStereo=0;HideElemStereo=0;ShowTests=0;ShowMaint=0;ConnectorNotation=UML 2.1;ExplicitNavigability=0;AdvancedElementProps=1;AdvancedFeatureProps=1;AdvancedConnectorProps=1;ShowNotes=0;SuppressBrackets=0;SuppConnectorLabels=0;PrintPageHeadFoot=0;ShowAsList=0;"/><style2 value="ExcludeRTF=0;DocAll=0;HideQuals=0;AttPkg=1;ShowTests=0;ShowMaint=0;SuppressFOC=1;MatrixActive=0;SwimlanesActive=0;MatrixLineWidth=1;MatrixLocked=0;TConnectorNotation=UML 2.1;TExplicitNavigability=0;AdvancedElementProps=1;AdvancedFeatureProps=1;AdvancedConnectorProps=1;ProfileData=;MDGDgm=;STBLDgm=;ShowNotes=1;VisibleAttributeDetail=0;ShowOpRetType=1;SuppressBrackets=0;SuppConnectorLabels=0;PrintPageHeadFoot=0;ShowAsList=0;"/><swimlanes value="locked=false;orientation=0;width=0;inbar=false;names=false;color=0;bold=false;fcol=0;;cls=0;"/><matrixitems value="locked=false;matrixactive=false;swimlanesactive=false;width=1;"/><extendedProperties/></diagram><diagram xmi:id="EAID_0b655dae9a7f21ae74cb37a294187188"><elements/><model owner="EAPKcc66b7eb0e6cd2d072b0d06421b873cf" package="EAPKcc66b7eb0e6cd2d072b0d06421b873cf"/><properties name="Sandbox with Details" type="Object"/><style1 value="ShowPrivate=1;ShowProtected=1;ShowPublic=1;HideRelationships=0;Locked=0;Border=1;HighlightForeign=1;PackageContents=1;SequenceNotes=0;ScalePrintImage=0;PPgs.cx=0;PPgs.cy=0;DocSize.cx=791;DocSize.cy=1134;ShowDetails=0;Orientation=P;Zoom=100;ShowTags=0;OpParams=1;VisibleAttributeDetail=0;ShowOpRetType=1;ShowIcons=1;CollabNums=0;HideProps=0;ShowReqs=0;ShowCons=0;PaperSize=9;HideParents=0;UseAlias=0;HideAtts=0;HideOps=0;HideStereo=0;HideElemStereo=0;ShowTests=0;ShowMaint=0;ConnectorNotation=UML 2.1;ExplicitNavigability=0;AdvancedElementProps=1;AdvancedFeatureProps=1;AdvancedConnectorProps=1;ShowNotes=0;SuppressBrackets=0;SuppConnectorLabels=0;PrintPageHeadFoot=0;ShowAsList=0;"/><style2 value="ExcludeRTF=0;DocAll=0;HideQuals=0;AttPkg=1;ShowTests=0;ShowMaint=0;SuppressFOC=1;MatrixActive=0;SwimlanesActive=0;MatrixLineWidth=1;MatrixLocked=0;TConnectorNotation=UML 2.1;TExplicitNavigability=0;AdvancedElementProps=1;AdvancedFeatureProps=1;AdvancedConnectorProps=1;ProfileData=;MDGDgm=;STBLDgm=;ShowNotes=1;VisibleAttributeDetail=0;ShowOpRetType=1;SuppressBrackets=0;SuppConnectorLabels=0;PrintPageHeadFoot=0;ShowAsList=0;"/><swimlanes value="locked=false;orientation=0;width=0;inbar=false;names=false;color=0;bold=false;fcol=0;;cls=0;"/><matrixitems value="locked=false;matrixactive=false;swimlanesactive=false;width=1;"/><extendedProperties/></diagram><diagram xmi:id="EAID_23bc5eae579b3da40e260970b9f132ba"><elements/><model owner="EAPKcc66b7eb0e6cd2d072b0d06421b873cf" package="EAPKcc66b7eb0e6cd2d072b0d06421b873cf"/><properties name="Sandbox without Details" type="Object"/><style1 value="ShowPrivate=1;ShowProtected=1;ShowPublic=1;HideRelationships=0;Locked=0;Border=1;HighlightForeign=1;PackageContents=1;SequenceNotes=0;ScalePrintImage=0;PPgs.cx=0;PPgs.cy=0;DocSize.cx=791;DocSize.cy=1134;ShowDetails=0;Orientation=P;Zoom=100;ShowTags=0;OpParams=1;VisibleAttributeDetail=0;ShowOpRetType=1;ShowIcons=1;CollabNums=0;HideProps=0;ShowReqs=0;ShowCons=0;PaperSize=9;HideParents=0;UseAlias=0;HideAtts=1;HideOps=1;HideStereo=0;HideElemStereo=0;ShowTests=0;ShowMaint=0;ConnectorNotation=UML 2.1;ExplicitNavigability=0;AdvancedElementProps=1;AdvancedFeatureProps=1;AdvancedConnectorProps=1;ShowNotes=0;SuppressBrackets=0;SuppConnectorLabels=0;PrintPageHeadFoot=0;ShowAsList=0;"/><style2 value="ExcludeRTF=0;DocAll=0;HideQuals=0;AttPkg=1;ShowTests=0;ShowMaint=0;SuppressFOC=1;MatrixActive=0;SwimlanesActive=0;MatrixLineWidth=1;MatrixLocked=0;TConnectorNotation=UML 2.1;TExplicitNavigability=0;AdvancedElementProps=1;AdvancedFeatureProps=1;AdvancedConnectorProps=1;ProfileData=;MDGDgm=;STBLDgm=;ShowNotes=0;VisibleAttributeDetail=0;ShowOpRetType=1;SuppressBrackets=0;SuppConnectorLabels=0;PrintPageHeadFoot=0;ShowAsList=0;"/><swimlanes value="locked=false;orientation=0;width=0;inbar=false;names=false;color=0;bold=false;fcol=0;;cls=0;"/><matrixitems value="locked=false;matrixactive=false;swimlanesactive=false;width=1;"/><extendedProperties/></diagram></diagrams></xmi:Extension><uml:Model name="ComM Abstract Data Model" visibility="public" xmi:id="EAPKcc66b7eb0e6cd2d072b0d06421b873cf" xmi:type="uml:Package"><packagedElement name="Channel:ConstStruct" visibility="public" xmi:id="EAID_dc5c080f59d4607dc71f56926d31cb9d" xmi:type="uml:InstanceSpecification"/><packagedElement name="ChannelPb:ConstStruct" visibility="public" xmi:id="EAID_e924de3ac25b8403fe1b2f532e44eda8" xmi:type="uml:InstanceSpecification"><ownedAttribute aggregation="none" association="EAID_d29c9e774d5669e09542cb0454bfb396" isDerived="false" isDerivedUnion="false" isOrdered="true" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_dstd29c9e774d5669e09542cb0454bfb396" xmi:type="uml:Property"><lowerValue value="0" xmi:id="EAID_8b1b53636c6060b78d57260ca308bf37" xmi:type="uml:LiteralInteger"/><upperValue value="-1" xmi:id="EAID_0cd2358eddd3ca4e05884767ee8d681d" xmi:type="uml:LiteralUnlimitedNatural"/><type xmi:idref="EAID_9a98b427de242badd4b2e8d988706a27"/></ownedAttribute><ownedAttribute aggregation="none" association="EAID_6f3746e4d170e1b776c08012abba538c" isDerived="false" isDerivedUnion="false" isOrdered="true" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_dst6f3746e4d170e1b776c08012abba538c" xmi:type="uml:Property"><lowerValue value="0" xmi:id="EAID_d6c9d3f86041c6fae7ac6162f44db8d1" xmi:type="uml:LiteralInteger"/><upperValue value="-1" xmi:id="EAID_7db8e15bd71bf2608b46d4dd6740ed98" xmi:type="uml:LiteralUnlimitedNatural"/><type xmi:idref="EAID_49e30bdc56645cfdcfc3b4a35fab693a"/></ownedAttribute><ownedAttribute aggregation="none" association="EAID_84f41182393acfc0da1d01b3dfe0bdef" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_dst84f41182393acfc0da1d01b3dfe0bdef" xmi:type="uml:Property"><lowerValue value="1" xmi:id="EAID_3b11781412745a639f24b24cb94cbe9d" xmi:type="uml:LiteralInteger"/><upperValue value="-1" xmi:id="EAID_254007a3669761d6f36212ce1d50d5f4" xmi:type="uml:LiteralUnlimitedNatural"/><type xmi:idref="EAID_ee3ca0253d1c1982a122714edf2b0d9b"/></ownedAttribute></packagedElement><packagedElement name="indirection" visibility="public" xmi:id="EAID_d29c9e774d5669e09542cb0454bfb396" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dstd29c9e774d5669e09542cb0454bfb396"/><memberEnd xmi:idref="EAID_srcd29c9e774d5669e09542cb0454bfb396"/><ownedEnd aggregation="none" association="EAID_d29c9e774d5669e09542cb0454bfb396" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_srcd29c9e774d5669e09542cb0454bfb396" xmi:type="uml:Property"><type xmi:idref="EAID_e924de3ac25b8403fe1b2f532e44eda8"/></ownedEnd></packagedElement><packagedElement name="indirection" visibility="public" xmi:id="EAID_6f3746e4d170e1b776c08012abba538c" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dst6f3746e4d170e1b776c08012abba538c"/><memberEnd xmi:idref="EAID_src6f3746e4d170e1b776c08012abba538c"/><ownedEnd aggregation="none" association="EAID_6f3746e4d170e1b776c08012abba538c" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_src6f3746e4d170e1b776c08012abba538c" xmi:type="uml:Property"><type xmi:idref="EAID_e924de3ac25b8403fe1b2f532e44eda8"/></ownedEnd></packagedElement><packagedElement name="indirection" visibility="public" xmi:id="EAID_84f41182393acfc0da1d01b3dfe0bdef" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dst84f41182393acfc0da1d01b3dfe0bdef"/><memberEnd xmi:idref="EAID_src84f41182393acfc0da1d01b3dfe0bdef"/><ownedEnd aggregation="none" association="EAID_84f41182393acfc0da1d01b3dfe0bdef" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_src84f41182393acfc0da1d01b3dfe0bdef" xmi:type="uml:Property"><type xmi:idref="EAID_e924de3ac25b8403fe1b2f532e44eda8"/></ownedEnd></packagedElement><packagedElement name="Pnc:ConstStruct" visibility="public" xmi:id="EAID_55106321ebefc0040c2879f6b1f74133" xmi:type="uml:InstanceSpecification"/><packagedElement name="PncPb:ConstStruct" visibility="public" xmi:id="EAID_ee3ca0253d1c1982a122714edf2b0d9b" xmi:type="uml:InstanceSpecification"><ownedAttribute aggregation="none" association="EAID_d532643c77e3ed631d33b8000e5b3b0e" isDerived="false" isDerivedUnion="false" isOrdered="true" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_dstd532643c77e3ed631d33b8000e5b3b0e" xmi:type="uml:Property"><lowerValue value="1" xmi:id="EAID_6f7deec252b3bd5402de39c881f9efd3" xmi:type="uml:LiteralInteger"/><upperValue value="-1" xmi:id="EAID_cf1aac38d94b040aa1eceeb5cf63ff54" xmi:type="uml:LiteralUnlimitedNatural"/><type xmi:idref="EAID_f4c959a5f2e36926e6b1f0346a4012e0"/></ownedAttribute><ownedAttribute aggregation="none" association="EAID_f99087a9d7327c975ba6e4b7c90867f0" isDerived="false" isDerivedUnion="false" isOrdered="true" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_dstf99087a9d7327c975ba6e4b7c90867f0" xmi:type="uml:Property"><lowerValue value="0" xmi:id="EAID_06e19c130e4ee5fe1a8e99015003e2f7" xmi:type="uml:LiteralInteger"/><upperValue value="-1" xmi:id="EAID_16369e0171a09f22b9dc90cbffd32175" xmi:type="uml:LiteralUnlimitedNatural"/><type xmi:idref="EAID_1d65a113fe927d0401f52a2fef75429e"/></ownedAttribute><ownedAttribute aggregation="none" association="EAID_4cbb7e983dffc1fba0ba418ea1f31bf7" isDerived="false" isDerivedUnion="false" isOrdered="true" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_dst4cbb7e983dffc1fba0ba418ea1f31bf7" xmi:type="uml:Property"><lowerValue value="0" xmi:id="EAID_469f327616a4313371d4d44083b7a0e6" xmi:type="uml:LiteralInteger"/><upperValue value="-1" xmi:id="EAID_6547418ff4f90e026e066458575d6659" xmi:type="uml:LiteralUnlimitedNatural"/><type xmi:idref="EAID_66e30dab79d877ed3563381892eac6ec"/></ownedAttribute><ownedAttribute aggregation="none" association="EAID_995c654f6ce7c0a05cc1e81790c22e75" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_dst995c654f6ce7c0a05cc1e81790c22e75" xmi:type="uml:Property"><lowerValue value="1" xmi:id="EAID_fdecc0ea1887284de70556a17cfafa5c" xmi:type="uml:LiteralInteger"/><upperValue value="-1" xmi:id="EAID_3430728e8b3cebb5c42b40ccaf26225b" xmi:type="uml:LiteralUnlimitedNatural"/><type xmi:idref="EAID_185ff4347ce7dcfc8661eccee4657fa5"/></ownedAttribute></packagedElement><packagedElement name="indirection" visibility="public" xmi:id="EAID_d532643c77e3ed631d33b8000e5b3b0e" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dstd532643c77e3ed631d33b8000e5b3b0e"/><memberEnd xmi:idref="EAID_srcd532643c77e3ed631d33b8000e5b3b0e"/><ownedEnd aggregation="none" association="EAID_d532643c77e3ed631d33b8000e5b3b0e" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_srcd532643c77e3ed631d33b8000e5b3b0e" xmi:type="uml:Property"><type xmi:idref="EAID_ee3ca0253d1c1982a122714edf2b0d9b"/></ownedEnd></packagedElement><packagedElement name="indirection" visibility="public" xmi:id="EAID_f99087a9d7327c975ba6e4b7c90867f0" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dstf99087a9d7327c975ba6e4b7c90867f0"/><memberEnd xmi:idref="EAID_srcf99087a9d7327c975ba6e4b7c90867f0"/><ownedEnd aggregation="none" association="EAID_f99087a9d7327c975ba6e4b7c90867f0" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_srcf99087a9d7327c975ba6e4b7c90867f0" xmi:type="uml:Property"><type xmi:idref="EAID_ee3ca0253d1c1982a122714edf2b0d9b"/></ownedEnd></packagedElement><packagedElement name="indirection" visibility="public" xmi:id="EAID_4cbb7e983dffc1fba0ba418ea1f31bf7" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dst4cbb7e983dffc1fba0ba418ea1f31bf7"/><memberEnd xmi:idref="EAID_src4cbb7e983dffc1fba0ba418ea1f31bf7"/><ownedEnd aggregation="none" association="EAID_4cbb7e983dffc1fba0ba418ea1f31bf7" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_src4cbb7e983dffc1fba0ba418ea1f31bf7" xmi:type="uml:Property"><type xmi:idref="EAID_ee3ca0253d1c1982a122714edf2b0d9b"/></ownedEnd></packagedElement><packagedElement name="indirection" visibility="public" xmi:id="EAID_995c654f6ce7c0a05cc1e81790c22e75" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dst995c654f6ce7c0a05cc1e81790c22e75"/><memberEnd xmi:idref="EAID_src995c654f6ce7c0a05cc1e81790c22e75"/><ownedEnd aggregation="none" association="EAID_995c654f6ce7c0a05cc1e81790c22e75" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_src995c654f6ce7c0a05cc1e81790c22e75" xmi:type="uml:Property"><type xmi:idref="EAID_ee3ca0253d1c1982a122714edf2b0d9b"/></ownedEnd></packagedElement><packagedElement name="PncSignal:ConstStruct" visibility="public" xmi:id="EAID_185ff4347ce7dcfc8661eccee4657fa5" xmi:type="uml:InstanceSpecification"><ownedAttribute aggregation="none" association="EAID_d3389586478ca6860e35ec0cacc748de" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_dstd3389586478ca6860e35ec0cacc748de" xmi:type="uml:Property"><lowerValue value="0" xmi:id="EAID_638cf2387c56a7bb1ff70ad6f7c5d217" xmi:type="uml:LiteralInteger"/><upperValue value="1" xmi:id="EAID_532a0ac52c42bc19aa05adb5399a5c3c" xmi:type="uml:LiteralInteger"/><type xmi:idref="EAID_dc5c080f59d4607dc71f56926d31cb9d"/></ownedAttribute><ownedAttribute aggregation="none" association="EAID_154216b1aa9047646295d4e379b692cf" isDerived="false" isDerivedUnion="false" isOrdered="true" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_dst154216b1aa9047646295d4e379b692cf" xmi:type="uml:Property"><lowerValue value="0" xmi:id="EAID_d50a35397d199c4bfdac9a3f40c80133" xmi:type="uml:LiteralInteger"/><upperValue value="-1" xmi:id="EAID_987d90f531ec7857ab2c9c16ca5599a8" xmi:type="uml:LiteralUnlimitedNatural"/><type xmi:idref="EAID_43d350dcbc20ae9e7f8710e87d1f3d5e"/></ownedAttribute></packagedElement><packagedElement name="indirection" visibility="public" xmi:id="EAID_d3389586478ca6860e35ec0cacc748de" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dstd3389586478ca6860e35ec0cacc748de"/><memberEnd xmi:idref="EAID_srcd3389586478ca6860e35ec0cacc748de"/><ownedEnd aggregation="none" association="EAID_d3389586478ca6860e35ec0cacc748de" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_srcd3389586478ca6860e35ec0cacc748de" xmi:type="uml:Property"><type xmi:idref="EAID_185ff4347ce7dcfc8661eccee4657fa5"/></ownedEnd></packagedElement><packagedElement name="indirection" visibility="public" xmi:id="EAID_154216b1aa9047646295d4e379b692cf" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dst154216b1aa9047646295d4e379b692cf"/><memberEnd xmi:idref="EAID_src154216b1aa9047646295d4e379b692cf"/><ownedEnd aggregation="none" association="EAID_154216b1aa9047646295d4e379b692cf" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_src154216b1aa9047646295d4e379b692cf" xmi:type="uml:Property"><type xmi:idref="EAID_185ff4347ce7dcfc8661eccee4657fa5"/></ownedEnd></packagedElement><packagedElement name="User:ConstStruct" visibility="public" xmi:id="EAID_3f0fee0435e0fd385611b3a77d7788c4" xmi:type="uml:InstanceSpecification"><ownedAttribute aggregation="none" association="EAID_71051b943953a3a13f159103cf95935d" isDerived="false" isDerivedUnion="false" isOrdered="true" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_dst71051b943953a3a13f159103cf95935d" xmi:type="uml:Property"><lowerValue value="0" xmi:id="EAID_0fd201b73720bb0f8225ab0aa3ba5a2a" xmi:type="uml:LiteralInteger"/><upperValue value="-1" xmi:id="EAID_b46a9741677566baf93a51ebf5d8ff2d" xmi:type="uml:LiteralUnlimitedNatural"/><type xmi:idref="EAID_edce6252e7d4d08ec6b22bd9cf751635"/></ownedAttribute><ownedAttribute aggregation="none" association="EAID_a2ae8ae10e2a6f2f7967ad143b56b8f6" isDerived="false" isDerivedUnion="false" isOrdered="true" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_dsta2ae8ae10e2a6f2f7967ad143b56b8f6" xmi:type="uml:Property"><lowerValue value="0" xmi:id="EAID_f9b3ca78a1ba2cbcc274194331d25a9d" xmi:type="uml:LiteralInteger"/><upperValue value="-1" xmi:id="EAID_e0d9a85edac1e8b05e8d220ec1b868e1" xmi:type="uml:LiteralUnlimitedNatural"/><type xmi:idref="EAID_3e6e3abbf349922b36ae10dcc805f70f"/></ownedAttribute></packagedElement><packagedElement name="indirection" visibility="public" xmi:id="EAID_71051b943953a3a13f159103cf95935d" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dst71051b943953a3a13f159103cf95935d"/><memberEnd xmi:idref="EAID_src71051b943953a3a13f159103cf95935d"/><ownedEnd aggregation="none" association="EAID_71051b943953a3a13f159103cf95935d" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_src71051b943953a3a13f159103cf95935d" xmi:type="uml:Property"><type xmi:idref="EAID_3f0fee0435e0fd385611b3a77d7788c4"/></ownedEnd></packagedElement><packagedElement name="indirection" visibility="public" xmi:id="EAID_a2ae8ae10e2a6f2f7967ad143b56b8f6" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dsta2ae8ae10e2a6f2f7967ad143b56b8f6"/><memberEnd xmi:idref="EAID_srca2ae8ae10e2a6f2f7967ad143b56b8f6"/><ownedEnd aggregation="none" association="EAID_a2ae8ae10e2a6f2f7967ad143b56b8f6" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_srca2ae8ae10e2a6f2f7967ad143b56b8f6" xmi:type="uml:Property"><type xmi:idref="EAID_3f0fee0435e0fd385611b3a77d7788c4"/></ownedEnd></packagedElement><packagedElement name="UserByteMask:ConstStruct" visibility="public" xmi:id="EAID_edce6252e7d4d08ec6b22bd9cf751635" xmi:type="uml:InstanceSpecification"><ownedAttribute aggregation="none" association="EAID_c801cdc2d2a134d27a67dafc9007c293" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_dstc801cdc2d2a134d27a67dafc9007c293" xmi:type="uml:Property"><lowerValue value="1" xmi:id="EAID_fbe9d106454711357a810536201d1078" xmi:type="uml:LiteralInteger"/><upperValue value="1" xmi:id="EAID_22882005e568cfff785921899e50b060" xmi:type="uml:LiteralInteger"/><type xmi:idref="EAID_9a98b427de242badd4b2e8d988706a27"/></ownedAttribute></packagedElement><packagedElement name="indirection" visibility="public" xmi:id="EAID_c801cdc2d2a134d27a67dafc9007c293" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dstc801cdc2d2a134d27a67dafc9007c293"/><memberEnd xmi:idref="EAID_srcc801cdc2d2a134d27a67dafc9007c293"/><ownedEnd aggregation="none" association="EAID_c801cdc2d2a134d27a67dafc9007c293" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_srcc801cdc2d2a134d27a67dafc9007c293" xmi:type="uml:Property"><type xmi:idref="EAID_edce6252e7d4d08ec6b22bd9cf751635"/></ownedEnd></packagedElement><packagedElement name="UserPncByteMask:ConstStruct" visibility="public" xmi:id="EAID_3e6e3abbf349922b36ae10dcc805f70f" xmi:type="uml:InstanceSpecification"><ownedAttribute aggregation="none" association="EAID_cc529bad959797ad11eb53bc95de26a5" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_dstcc529bad959797ad11eb53bc95de26a5" xmi:type="uml:Property"><lowerValue value="1" xmi:id="EAID_8c618babe52175c4cdd8e03eaf63ff75" xmi:type="uml:LiteralInteger"/><upperValue value="1" xmi:id="EAID_8f3489b5e500562c873862c753724bd5" xmi:type="uml:LiteralInteger"/><type xmi:idref="EAID_f4c959a5f2e36926e6b1f0346a4012e0"/></ownedAttribute></packagedElement><packagedElement name="indirection" visibility="public" xmi:id="EAID_cc529bad959797ad11eb53bc95de26a5" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dstcc529bad959797ad11eb53bc95de26a5"/><memberEnd xmi:idref="EAID_srccc529bad959797ad11eb53bc95de26a5"/><ownedEnd aggregation="none" association="EAID_cc529bad959797ad11eb53bc95de26a5" isDerived="false" isDerivedUnion="false" isOrdered="false" isReadOnly="false" isStatic="false" isUnique="true" visibility="public" xmi:id="EAID_srccc529bad959797ad11eb53bc95de26a5" xmi:type="uml:Property"><type xmi:idref="EAID_3e6e3abbf349922b36ae10dcc805f70f"/></ownedEnd></packagedElement><packagedElement name="PncChannelMapping:ConstArray" visibility="public" xmi:id="EAID_1d65a113fe927d0401f52a2fef75429e" xmi:type="uml:InstanceSpecification"/><packagedElement name="UserModeNotiFunc:ConstArray" visibility="public" xmi:id="EAID_48c7e420aa45807a2bb0e94a6f797018" xmi:type="uml:InstanceSpecification"/><packagedElement name="ActivePncComMode:VarArray" visibility="public" xmi:id="EAID_1be228193456239f9afbbe8be56f271e" xmi:type="uml:InstanceSpecification"/><packagedElement name="VarIndirectableSizeByConstIndirecableRelation" visibility="public" xmi:id="EAID_67d5a7a73ed803a4d6d62cf243e9b124" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dst67d5a7a73ed803a4d6d62cf243e9b124"/><ownedEnd aggregation="none" association="EAID_67d5a7a73ed803a4d6d62cf243e9b124" visibility="public" xmi:id="EAID_dst67d5a7a73ed803a4d6d62cf243e9b124" xmi:type="uml:Property"><type xmi:idref="EAID_55106321ebefc0040c2879f6b1f74133"/></ownedEnd><memberEnd xmi:idref="EAID_src67d5a7a73ed803a4d6d62cf243e9b124"/><ownedEnd aggregation="none" association="EAID_67d5a7a73ed803a4d6d62cf243e9b124" visibility="public" xmi:id="EAID_src67d5a7a73ed803a4d6d62cf243e9b124" xmi:type="uml:Property"><type xmi:idref="EAID_1be228193456239f9afbbe8be56f271e"/></ownedEnd></packagedElement><packagedElement name="BusPncComModeReq:VarArray" visibility="public" xmi:id="EAID_70e08a0b97fd9b29806fb4ff1313b67b" xmi:type="uml:InstanceSpecification"/><packagedElement name="VarIndirectableSizeByConstIndirecableRelation" visibility="public" xmi:id="EAID_3c15b09f8e573166ad2df34318d26dfc" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dst3c15b09f8e573166ad2df34318d26dfc"/><ownedEnd aggregation="none" association="EAID_3c15b09f8e573166ad2df34318d26dfc" visibility="public" xmi:id="EAID_dst3c15b09f8e573166ad2df34318d26dfc" xmi:type="uml:Property"><type xmi:idref="EAID_55106321ebefc0040c2879f6b1f74133"/></ownedEnd><memberEnd xmi:idref="EAID_src3c15b09f8e573166ad2df34318d26dfc"/><ownedEnd aggregation="none" association="EAID_3c15b09f8e573166ad2df34318d26dfc" visibility="public" xmi:id="EAID_src3c15b09f8e573166ad2df34318d26dfc" xmi:type="uml:Property"><type xmi:idref="EAID_70e08a0b97fd9b29806fb4ff1313b67b"/></ownedEnd></packagedElement><packagedElement name="BusSleepModeIndicated:VarArray" visibility="public" xmi:id="EAID_b9de19103e15de4759e27fe448c4ce2e" xmi:type="uml:InstanceSpecification"/><packagedElement name="VarIndirectableSizeByConstIndirecableRelation" visibility="public" xmi:id="EAID_f293276c19a37ef88e573708decee42e" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dstf293276c19a37ef88e573708decee42e"/><ownedEnd aggregation="none" association="EAID_f293276c19a37ef88e573708decee42e" visibility="public" xmi:id="EAID_dstf293276c19a37ef88e573708decee42e" xmi:type="uml:Property"><type xmi:idref="EAID_dc5c080f59d4607dc71f56926d31cb9d"/></ownedEnd><memberEnd xmi:idref="EAID_srcf293276c19a37ef88e573708decee42e"/><ownedEnd aggregation="none" association="EAID_f293276c19a37ef88e573708decee42e" visibility="public" xmi:id="EAID_srcf293276c19a37ef88e573708decee42e" xmi:type="uml:Property"><type xmi:idref="EAID_b9de19103e15de4759e27fe448c4ce2e"/></ownedEnd></packagedElement><packagedElement name="DcmRequestActive:VarArray" visibility="public" xmi:id="EAID_d0306faa28faf77e5a0a0b45115c9cd4" xmi:type="uml:InstanceSpecification"/><packagedElement name="VarIndirectableSizeByConstIndirecableRelation" visibility="public" xmi:id="EAID_43b846c8d766ab2ee17e20f548d4b3a1" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dst43b846c8d766ab2ee17e20f548d4b3a1"/><ownedEnd aggregation="none" association="EAID_43b846c8d766ab2ee17e20f548d4b3a1" visibility="public" xmi:id="EAID_dst43b846c8d766ab2ee17e20f548d4b3a1" xmi:type="uml:Property"><type xmi:idref="EAID_dc5c080f59d4607dc71f56926d31cb9d"/></ownedEnd><memberEnd xmi:idref="EAID_src43b846c8d766ab2ee17e20f548d4b3a1"/><ownedEnd aggregation="none" association="EAID_43b846c8d766ab2ee17e20f548d4b3a1" visibility="public" xmi:id="EAID_src43b846c8d766ab2ee17e20f548d4b3a1" xmi:type="uml:Property"><type xmi:idref="EAID_d0306faa28faf77e5a0a0b45115c9cd4"/></ownedEnd></packagedElement><packagedElement name="FullComRequesters:VarArray" visibility="public" xmi:id="EAID_7315fb7b8323f1adad3b70682c5785bb" xmi:type="uml:InstanceSpecification"/><packagedElement name="LastStateChange:VarArray" visibility="public" xmi:id="EAID_a4541417e91d5ab7da61695ceb094243" xmi:type="uml:InstanceSpecification"/><packagedElement name="VarIndirectableSizeByConstIndirecableRelation" visibility="public" xmi:id="EAID_4c3a938e278b2e87e8ddb92b1ca97d06" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dst4c3a938e278b2e87e8ddb92b1ca97d06"/><ownedEnd aggregation="none" association="EAID_4c3a938e278b2e87e8ddb92b1ca97d06" visibility="public" xmi:id="EAID_dst4c3a938e278b2e87e8ddb92b1ca97d06" xmi:type="uml:Property"><type xmi:idref="EAID_3f0fee0435e0fd385611b3a77d7788c4"/></ownedEnd><memberEnd xmi:idref="EAID_src4c3a938e278b2e87e8ddb92b1ca97d06"/><ownedEnd aggregation="none" association="EAID_4c3a938e278b2e87e8ddb92b1ca97d06" visibility="public" xmi:id="EAID_src4c3a938e278b2e87e8ddb92b1ca97d06" xmi:type="uml:Property"><type xmi:idref="EAID_a4541417e91d5ab7da61695ceb094243"/></ownedEnd></packagedElement><packagedElement name="MinFullComModeTimer:VarArray" visibility="public" xmi:id="EAID_54062dcd3617f9674453ca3a9da48a69" xmi:type="uml:InstanceSpecification"/><packagedElement name="VarIndirectableSizeByConstIndirecableRelation" visibility="public" xmi:id="EAID_16bd24ccb35d208c75e956a564a9d4b8" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dst16bd24ccb35d208c75e956a564a9d4b8"/><ownedEnd aggregation="none" association="EAID_16bd24ccb35d208c75e956a564a9d4b8" visibility="public" xmi:id="EAID_dst16bd24ccb35d208c75e956a564a9d4b8" xmi:type="uml:Property"><type xmi:idref="EAID_dc5c080f59d4607dc71f56926d31cb9d"/></ownedEnd><memberEnd xmi:idref="EAID_src16bd24ccb35d208c75e956a564a9d4b8"/><ownedEnd aggregation="none" association="EAID_16bd24ccb35d208c75e956a564a9d4b8" visibility="public" xmi:id="EAID_src16bd24ccb35d208c75e956a564a9d4b8" xmi:type="uml:Property"><type xmi:idref="EAID_54062dcd3617f9674453ca3a9da48a69"/></ownedEnd></packagedElement><packagedElement name="NmLightTimer:VarArray" visibility="public" xmi:id="EAID_c77d321b871ef38c6217abd7a01946af" xmi:type="uml:InstanceSpecification"/><packagedElement name="VarIndirectableSizeByConstIndirecableRelation" visibility="public" xmi:id="EAID_4aa8fd532b783dd431c72f5671c77063" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dst4aa8fd532b783dd431c72f5671c77063"/><ownedEnd aggregation="none" association="EAID_4aa8fd532b783dd431c72f5671c77063" visibility="public" xmi:id="EAID_dst4aa8fd532b783dd431c72f5671c77063" xmi:type="uml:Property"><type xmi:idref="EAID_dc5c080f59d4607dc71f56926d31cb9d"/></ownedEnd><memberEnd xmi:idref="EAID_src4aa8fd532b783dd431c72f5671c77063"/><ownedEnd aggregation="none" association="EAID_4aa8fd532b783dd431c72f5671c77063" visibility="public" xmi:id="EAID_src4aa8fd532b783dd431c72f5671c77063" xmi:type="uml:Property"><type xmi:idref="EAID_c77d321b871ef38c6217abd7a01946af"/></ownedEnd></packagedElement><packagedElement name="PncPSleepTimer:VarArray" visibility="public" xmi:id="EAID_66cd903be4717d479ff657bc2c9003d7" xmi:type="uml:InstanceSpecification"/><packagedElement name="VarIndirectableSizeByConstIndirecableRelation" visibility="public" xmi:id="EAID_c2c18b9bc30c22134f2f12515a33be3f" xmi:type="uml:Association"><memberEnd xmi:idref="EAID_dstc2c18b9bc30c22134f2f12515a33be3f"/><ownedEnd aggregation="none" association="EAID_c2c18b9bc30c22134f2f12515a33be3f" visibility="public" xmi:id="EAID_dstc2c18b9bc30c22134f2f12515a33be3f" xmi:type="uml:Property"><type xmi:idref="EAID_55106321ebefc0040c2879f6b1f74133"/></ownedEnd><memberEnd xmi:idref="EAID_srcc2c18b9bc30c22134f2f12515a33be3f"/><ownedEnd aggregation="none" association="EAID_c2c18b9bc30c22134f2f12515a33be3f" visibility="public" xmi:id="EAID_srcc2c18b9bc30c22134f2f12515a33be3f" xmi:type="uml:Property"><type xmi:idref="EAID_66cd903be4717d479ff657bc2c9003d7"/></ownedEnd></packagedElement><packagedElement name="PncSignalValues:VarArray" visibility="public" xmi:id="EAID_43d350dcbc20ae9e7f8710e87d1f3d5e" xmi:type="uml:InstanceSpecification"/><packagedElement name="UserReqFullCom:VarArray" visibility="public" xmi:id="EAID_9a98b427de242badd4b2e8d988706a27" xmi:type="uml:InstanceSpecification"/><packagedElement name="UserReqPncFullCom:VarArray" visibility="public" xmi:id="EAID_f4c959a5f2e36926e6b1f0346a4012e0" xmi:type="uml:InstanceSpecification"/></uml:Model></xmi:XMI>
