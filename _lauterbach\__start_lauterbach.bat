@echo off
REM ############################ User Section! ##################################
REM set here the path to your T32 root folder!
set T32_ROOT_PATH=unknown
REM set T32_ROOT_PATH=D:\Uti\T32

REM Set here your architecture
set ARCHITECTURE=unknown
REM set ARCHITECTURE=powerpc
REM set ARCHITECTURE=tricore
REM set ARCHITECTURE=arm
REM #############################################################################


REM ###################### DO NOT EDIT THIS LINES! ##############################
REM set the corresponding executable of Trace32 for the choosen architecture
set EXEC=unknown

if "%ARCHITECTURE%" == "powerpc" (
  set EXEC=t32mppc.exe
)
if "%ARCHITECTURE%" == "tricore" (
  set EXEC=t32mtc.exe
)
if "%ARCHITECTURE%" == "arm" (
  set EXEC=t32marm.exe
)

if "%T32_ROOT_PATH%" == "unknown" (
  COLOR CF
  echo.
  echo ################## User action necessary! #####################
  echo.
  echo The T32_ROOT_PATH variable is not set!
  echo Read the Manual in the _lauterbach folder!
  echo.
  echo ###############################################################
  echo.
  pause
  GOTO END
)
if "%ARCHITECTURE%" == "unknown" (
  COLOR CF
  echo.
  echo ################## User action necessary! #####################
  echo.
  echo The ARCHITECTURE variable is not set!
  echo Read the Manual in the _lauterbach folder!
  echo.
  echo ###############################################################
  echo.
  pause
  GOTO END
)
if "%EXEC%" == "unknown" (
  COLOR CF
  echo.
  echo ################## User action necessary! #####################
  echo.
  echo Your choosen architecture os not yet supportet by this batch file or wrong!
  echo Read the Manual in the _lauterbach folder!
  echo.
  echo ###############################################################
  echo.
  pause
  GOTO END
)

REM Start the T32 Tool with the start.cmm script
start %T32_ROOT_PATH%\bin\windows64\%EXEC% -c .\config.t32 10000 LauterbachEnvironment USB CORE=1 -s _start.cmm ARCHITECTURE=%ARCHITECTURE%

:END
REM #############################################################################
