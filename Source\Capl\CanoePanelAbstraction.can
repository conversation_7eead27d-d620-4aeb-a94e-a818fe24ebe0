/**********************************************************************************************************************
 *  COPYRIGHT
 *  -------------------------------------------------------------------------------------------------------------------
 *  \verbatim
 *  Copyright (c) 2020 by Vector Informatik GmbH.                                                  All rights reserved.
 *
 *                 This software is copyright protected and proprietary to Vector Informatik GmbH.
 *                 Vector Informatik GmbH grants to you only those rights as set out in the license conditions.
 *                 All other rights remain with Vector Informatik GmbH.
 *  \endverbatim
 *  -------------------------------------------------------------------------------------------------------------------
 *  FILE DESCRIPTION
 *  -------------------------------------------------------------------------------------------------------------------
 *              File: CanoePanelAbstraction.can
 *       Description: Provides database abstraction for the user panels
 *********************************************************************************************************************/
includes
{

}

variables
{

  // Invalid Values
  const byte  GLOBAL_MAX_INVALID_BYTE_VALUE  = 255;
  const word  GLOBAL_MAX_INVALID_WORD_VALUE  = 65535;
  const long  GLOBAL_MAX_INVALID_LONG_VALUE  = **********;
  const dword GLOBAL_MAX_INVALID_DWORD_VALUE = **********;
  const byte true = 1;
  const byte false = 0;


  const dword CAN_ID_VITA_CAN0_1087 = 1087;
  const dword CAN_ID_VITA_CAN0_MSG_NM_MYECU_OCAN_1024 = 1024;
  const dword CAN_ID_VITA_CAN0_MSG_RXCYCLE500_20_OCAN_272 = 272;
  const dword CAN_ID_VITA_CAN0_MSG_RXCYCLE_E2EPROF1C_500_10_OCAN_288 = 288;
  const dword CAN_ID_VITA_CAN0_MSG_TXCYCLE1000_10_OCAN_872 = 872;
  const dword CAN_ID_VITA_CAN0_MSG_TXCYCLE10_0_OCAN_616 = 616;
  const dword MAX_PAYLOAD_SIZE = 256;
  const dword MAX_SIGNAL_IDS_COUNT = 8;
  const dword PDU_ID_VITA_CAN0_1087 = 1;
  const dword PDU_ID_VITA_CAN0_MSG_NM_MYECU_OCAN_1024 = 0;
  const dword PDU_ID_VITA_CAN0_MSG_RXCYCLE500_20_OCAN_272 = 8;
  const dword PDU_ID_VITA_CAN0_MSG_RXCYCLE_E2EPROF1C_500_10_OCAN_288 = 10;
  const dword PDU_ID_VITA_CAN0_MSG_TXCYCLE1000_10_OCAN_872 = 9;
  const dword PDU_ID_VITA_CAN0_MSG_TXCYCLE10_0_OCAN_616 = 11;
  const dword PDU_ID_VITA_FR0_PDU_FR_STARTAPPL_BOTHECU_RX = 4;
  const dword PDU_ID_VITA_FR0_PDU_NM_MYECU_FR = 3;
  const dword PDU_ID_VITA_FR0_PDU_NM_REARECU_FR = 2;
  const dword PDU_ID_VITA_FR0_PDU_TRANSMIT_MYECU = 5;
  const dword PDU_ID_VITA_LIN0_FRAME_LINTR_MYECU = 7;
  const dword PDU_ID_VITA_LIN0_FRAME_LINTR_REARECU = 6;
  const dword SIGNAL_ID_VITA_CAN0_MSG_RXCYCLE500_20_OCAN_SIGNAL_RX20BIT_CYCLIC_OMSG_RXCYCLE500_20_OCAN = 0;
  const dword SIGNAL_ID_VITA_CAN0_MSG_RXCYCLE_E2EPROF1C_500_10_OCAN_SIGRX_PROF1C_SIG32BIT_OMSG_RXCYCLE_E2EPROF1C_500_10_OCAN = 1;
  const dword SIGNAL_ID_VITA_CAN0_MSG_TXCYCLE1000_10_OCAN_SIGNAL_TX10BIT_CYCLIC_OMSG_TXCYCLE1000_10_OCAN = 2;
  const dword SIGNAL_ID_VITA_CAN0_MSG_TXCYCLE10_0_OCAN_SIGNAL_TX24BIT_CYCLIC_OMSG_TXCYCLE10_0_OCAN = 3;
  const dword SIGNAL_ID_VITA_FR0_PDU_FR_STARTAPPL_BOTHECU_RX_STARTAPPL_FR_BOTHECU_RX_CTRL_OPDU_FR_STARTAPPL_BOTHECU_RX = 4;
  const dword SIGNAL_ID_VITA_FR0_PDU_TRANSMIT_MYECU_SIGNAL_SOMETXSIGNAL_OPDU_TRANSMIT_MYECU = 5;
  const dword SIGNAL_ID_VITA_LIN0_FRAME_LINTR_MYECU_SIG_STARTAPPL_LINDATA_TX = 7;
  const dword SIGNAL_ID_VITA_LIN0_FRAME_LINTR_REARECU_SIG_STARTAPPL_LINDATA_RX = 6;
  byte pduPayload [MAX_PAYLOAD_SIZE];
  float signalTransmissionTimeStamp [MAX_SIGNAL_IDS_COUNT];
  frPdu   Vita_FR0::PDU_Fr_StartAppl_BothECU_RX Vita_FR0_PDU_Fr_StartAppl_BothECU_RX;
  frPdu   Vita_FR0::PDU_nm_RearECU_Fr    Vita_FR0_PDU_nm_RearECU_Fr;
  linMessage Vita_LIN0::Frame_LinTr_RearECU Vita_LIN0_Frame_LinTr_RearECU;
  message Vita_CAN0.1087                 Vita_CAN0_1087;
#if TOOL_MAJOR_VERSION >= 9
  pdu   Vita_CAN0::msg_RxCycle500_20_oCAN Vita_CAN0_msg_RxCycle500_20_oCAN;
#else
  message Vita_CAN0::msg_RxCycle500_20_oCAN Vita_CAN0_msg_RxCycle500_20_oCAN;
#endif
#if TOOL_MAJOR_VERSION >= 9
  pdu   Vita_CAN0::msg_RxCycle_E2eProf1C_500_10_oCAN Vita_CAN0_msg_RxCycle_E2eProf1C_500_10_oCAN;
#else
  message Vita_CAN0::msg_RxCycle_E2eProf1C_500_10_oCAN Vita_CAN0_msg_RxCycle_E2eProf1C_500_10_oCAN;
#endif

  const dword GLOBAL_MAX_CAN_CHANNELS = 1;
  const dword GLOBAL_MAX_CHANNELS = 3;
  const dword GLOBAL_MAX_ETHERNET_CHANNELS = 0;
  const dword GLOBAL_MAX_FLEXRAY_CHANNELS = 1;
  const dword GLOBAL_MAX_J1939_CHANNELS = 0;
  const dword GLOBAL_MAX_LIN_CHANNELS = 1;

  const dword CANOE_MAX_LIN_CHANNELS = 32;
  const dword CANOE_MAX_FLEXRAY_CHANNELS = 32;
  const dword MAX_BUS_TYPES = 5;
  dword channelToCanoeChannel[GLOBAL_MAX_CHANNELS];
  dword canoeFrChannelsToChannels[CANOE_MAX_FLEXRAY_CHANNELS];
  dword canoeLinChannelsToChannels[CANOE_MAX_LIN_CHANNELS];
  byte channelOnlineState[GLOBAL_MAX_CHANNELS];
  byte busActivityNotificationStatus [GLOBAL_MAX_CHANNELS];
  frConfiguration gFRParams;

  struct FrWUPStatistic
  {
    float timestamp;
    word symbol; // 0 unknown, 1  CAS, 2 MTS, 3 Wakeup, 4 Undefined
    byte frChannel; // 1 - A, 2 - B
  };
  struct FrWUPStatistic  FrWUPStatistics[GLOBAL_MAX_FLEXRAY_CHANNELS];
  /*********************************************************
  * CONFIG DATA TYPES
  **********************************************************/

  enum EBusType
  {
    E_CAN = 0,
    E_FLEXRAY = 1,
    E_LIN = 2,
    E_ETHERNET = 3,
    E_J1939 = 4
  };
  struct CAL_ConfigData
  {
    char  canoeChannelName [200];
  };
  struct CAL_Data
  {
    dword busContext;
  };
  struct GlobalConfigData
  {
    dword busSpecificIndex;
    enum  EBusType busType;
  };

  dword msgCounter[GLOBAL_MAX_CHANNELS];
  float msgCounterStartTime[GLOBAL_MAX_CHANNELS];
  struct CAL_ConfigData CAL_ConfigurationData [GLOBAL_MAX_CHANNELS] =
  {
    {
      canoeChannelName = "Vita_CAN0"
    },
    {
      canoeChannelName = "Vita_LIN0"
    },
    {
      canoeChannelName = "Vita_FR0"
    }
  };
  struct CAL_Data CAL_Data [GLOBAL_MAX_CHANNELS] =
  {
    {
      busContext = GLOBAL_MAX_INVALID_DWORD_VALUE
    },
    {
      busContext = GLOBAL_MAX_INVALID_DWORD_VALUE
    },
    {
      busContext = GLOBAL_MAX_INVALID_DWORD_VALUE
    }
  };
  struct GlobalConfigData globalConfigData [GLOBAL_MAX_CHANNELS] =
  {
    {
      busSpecificIndex = 0,
      busType = E_CAN
    },
    {
      busSpecificIndex = 0,
      busType = E_LIN
    },
    {
      busSpecificIndex = 0,
      busType = E_FLEXRAY
    }
  };



  const dword CHAR_10 = 10;
  const dword CHAR_100 = 100;
  const dword CHAR_200 = 200;

  // Properties of user interface elements like buttons, labels, trackbars etc.
  struct UiElementType
  {
    char name[CHAR_100];
    char text[CHAR_100];
    char hint[CHAR_200];
    byte enabled;
    byte visible;
  };
  struct UiElementLineTagsType
  {
    char tagStart[12];
    char tagEnd[13];
    char tagNameStart[7];
    char tagNameEnd[8];
    char tagHintStart[7];
    char tagHintEnd[8];
    char tagTextStart[7];
    char tagTextEnd[8];
    char tagEnabledStart[10];
    char tagEnabledEnd[11];
    char tagVisibleStart[10];
    char tagVisibleEnd[11];
  };
  struct UiElementLineTagsType uiElementLineTags =
  {
    tagStart = "<UiElement>",
    tagEnd = "</UiElement>",
    tagNameStart = "<name>",
    tagNameEnd = "</name>",
    tagHintStart = "<hint>",
    tagHintEnd = "</hint>",
    tagTextStart = "<text>",
    tagTextEnd = "</text>",
    tagEnabledStart = "<enabled>",
    tagEnabledEnd = "</enabled>",
    tagVisibleStart = "<visible>",
    tagVisibleEnd = "</visible>"
  };
  struct UiElementGlobalTagsType
  {
    char tagStart[213];
    char tagEnd[31];
  };
  struct UiElementGlobalTagsType uiElementGlobalTags =
  {
    tagStart = "<?xml version=\"1.0\" encoding=\"utf-8\"?><PanelContentData xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\" xmlns=\"http://vector.com/StartApplication/1.0\"><mControl>",
    tagEnd = "</mControl></PanelContentData>"
  };
  char uiSubElementDoNotSend[38] = "DO_NOT_UPDATE_THIS_VALUE_IN_THE_PANEL";
  const byte UI_MAX_LINES = 20;
  const dword UI_MAX_LINE_ARRAY_SIZE = __size_of(struct UiElementType) + __size_of(struct UiElementLineTagsType);
  const dword UI_MAX_FORMATTER_ARRAY_SIZE = (UI_MAX_LINE_ARRAY_SIZE * UI_MAX_LINES) + __size_of(struct UiElementGlobalTagsType);

  /*********************************************************
   * COM PANEL
   *********************************************************/

  enum ComGridColumnIndex
  {
    COM_COL_NAME = 0,
    COM_COL_RX = 1,
    COM_COL_TX = 2,
    COM_COL_MAX
  };

  enum ComGridRowIndex
  {
    COM_ROW_COM_PATH = 0,
    COM_ROW_SIGNAL = 1,
    COM_ROW_VALUE = 2,
    COM_ROW_TRANSMITTED = 3,
    COM_ROW_J1939_REQUESTED = 4,
    COM_ROW_J1939_TP_TYPE = 5,
    COM_ROW_SECOC_FRESHNESS = 6,
    COM_ROW_SECOC_AUTH = 7,
    COM_ROW_MAX
  };

  enum ComUiElementIndex
  {
    COM_UI_SCALE_RX = 0,
    COM_UI_CALCULATE = 1,
    COM_UI_SCALE_TX = 2,
    COM_UI_PLAY_BTN = 3,
    COM_UI_PAUSE_BTN = 4,
    COM_UI_PATH_SELECTOR = 5,
    COM_UI_TRACKBAR = 6,
    COM_UI_MAX
  };

  enum ComGrid
  {
    COM_DATA_GRID_VIEW = 0,
    COM_PANEL_GRID_MAX
  };

  // Representation of a single cell and the related properties.
  struct GridCell
  {
    char backgroundColor[CHAR_10];
    byte columnSpacing;
    char hint[CHAR_200];
    char text[CHAR_200];
    char textAlignment[CHAR_10];
    char textColor[CHAR_10];
  };

  // A single row is represented by the GridCell properties and a visibility flag.
  struct SingleRow
  {
    struct GridCell cell[COM_COL_MAX];
    byte isVisible;
  };

  // A Grid contains a header row and the content rows.
  struct ComGrid
  {
    char name[CHAR_100];
    struct GridCell header[COM_COL_MAX];
    struct SingleRow row[COM_ROW_MAX];
  };

  struct UiElementType comUiElement[COM_UI_MAX];
  struct ComGrid comGrid[COM_PANEL_GRID_MAX];

  mstimer timerForSensorValueChange;
  byte autoSensorValueChange;
  dword comIdOfRxSignalUnderTest = GLOBAL_MAX_INVALID_DWORD_VALUE;
  dword comIdOfTxSignalUnderTest = GLOBAL_MAX_INVALID_DWORD_VALUE;

  /*********************************************************
   * MEM VAR SECTION
   *********************************************************/

  const byte MEM_STATE_UNKNOWN        = 0x00;
  const byte MEM_STATE_WRITE_PENDING  = 0x01;
  const byte MEM_STATE_WRITE_FINISHED = 0x02;
  const byte MEM_STATE_READ_PENDING   = 0x03;
  const byte MEM_STATE_READ_FINISHED  = 0x04;
  const byte MEM_STATE_WRITE_FAILED   = 0x05;
  const byte MEM_STATE_READ_FAILED    = 0x06;

  int activeUseCase = 0;

  dword buscontext_Vita_CAN0 = 0;
  dword buscontext_Vita_FR0 = 0;
  dword buscontext_Vita_LIN0 = 0;
  // DcmDslConnection 'DcmDslProtocolRow_29812fbf_DC_0xE001_oMyECU_oCAN_9c85e87b' on channel 'Vita_CAN0'
  const dword dcmConnectionTxIdentifier_DcmDslProtocolRow_29812fbf_DC_0xE001_oMyECU_oCAN_9c85e87b = 0x610;
  const dword dcmConnectionRxIdentifier_DcmDslProtocolRow_29812fbf_DC_0xE001_oMyECU_oCAN_9c85e87b = 0x612;
  long dcmCanTpConnectionhandle_DcmDslProtocolRow_29812fbf_DC_0xE001_oMyECU_oCAN_9c85e87b;
  byte dcmCanTpConnectionOpen_DcmDslProtocolRow_29812fbf_DC_0xE001_oMyECU_oCAN_9c85e87b = 0;


  const long MAX_BUFFER_LENGTH = 1000;
  struct dcmBuffer
  {
    byte buffer[MAX_BUFFER_LENGTH];
    long bufferSize;
  };
  struct dcmBuffer doIpUdsBuffer;
  struct dcmBuffer doIPRxBuffer;
  struct dcmBuffer doIPTxBuffer;

  int diagRxDataCounter;

  const byte dcmDidIdHigh                  = 0x00;
  const byte dcmDidIdLow                   = 0x55;
  const byte dcmDTCHighByte                = 0x48;
  const byte dcmDTCMiddleByte              = 0x2B;
  const byte dcmDTCLowByte                 = 0xFF;
  const byte dcmDTCSnapshotRecordNumber    = 0x01;
  const byte dcmServiceIdDSC               = 0x10;
  const byte dcmServiceIdWDBI              = 0x2e;
  const byte dcmServiceIdRDBI              = 0x22;
  const byte dcmServiceIdCDTCI             = 0x14;
  const byte dcmServiceIdRDTCI             = 0x19;
  const byte dcmRDTCIsubFunctionRDTCSSBDTC = 0x04;

  const byte DIAG_UDS_RESPONSE_INDEX_SID               = 0;
  const byte DIAG_UDS_RESPONSE_INDEX_RDBI_DATA         = 3;
  const byte DIAG_UDS_RESPONSE_INDEX_RDTCSSBDTC_STATUS = 5;
  const byte DIAG_UDS_RESPONSE_INDEX_RDTCSSBDTC_DATA   = 10;

  const int DIAG_UDS_MAX_CHANNELS = 1;
  const int DIAG_UDS_REQUEST_QUEUE_LENGTH = 5;
  const int DIAG_UDS_REQUEST_TIMEOUT = 10000;
  const int DIAG_UDS_CHANNEL_CHANGE_TIME = 100;

  enum DiagRequestState
  {
    DIAG_QUEUE_EMPTY_REQUEST_NOT_ACTIVE = 0,
    DIAG_QUEUE_EMPTY_REQUEST_ACTIVE = 1,
    DIAG_QUEUE_NOT_EMPTY_REQUEST_NOT_ACTIVE = 2,
    DIAG_QUEUE_NOT_EMPTY_REQUEST_ACTIVE = 3
  };

  enum DiagUdsChannelChangeTimerState
  {
    DIAG_CHANNEL_CHANGE_TIMER_NOT_RUNNING = 0,
    DIAG_CHANNEL_CHANGE_TIMER_RUNNING = 1,
    DIAG_CHANNEL_CHANGE_TIMER_EXPIRED = 2
  };

  struct DiagUdsRequestQueueEntry
  {
    struct dcmBuffer request;
    int connectionId;
  };

  struct DiagUdsRequestQueue
  {
    struct DiagUdsRequestQueueEntry queue[DIAG_UDS_REQUEST_QUEUE_LENGTH];
    int head;
    int size;
    int requestInProgress;
  };

  struct DiagUdsRequestQueue diagUdsRequestQueue;

  struct DiagUdsServiceConfigData
  {
    byte rdbiSessionLevel;
    byte wdbiSessionLevel;
    byte cdtciSessionLevel;
    byte rdtcssbdtcSessionLevel;
  };

  struct DiagUdsServiceConfigData diagUdsServiceConfigData [DIAG_UDS_MAX_CHANNELS] =
  {
    { /* Vita_CAN0 */
      rdbiSessionLevel = 0,
      wdbiSessionLevel = 0,
      cdtciSessionLevel = 0,
      rdtcssbdtcSessionLevel = 0
    }
  };

  msTimer diagUdsRequestTimeoutTimer;
  msTimer diagUdsChannelChangeTimer;

  enum DiagUdsChannelChangeTimerState diagUdsChannelChangeTimerState;

  struct DiagUcBusLogDataText
  {
    char time[50];
    char type[50];
    char service[50];
    char status[50];
  };
  struct DiagUcBusLogDataText diagUcBusLogDataText;

  struct DiagUcInfoDisplayDataType
  {
    float time;
    char type[20];
    char service[20];
    char status[20];
  };
  struct DiagUcInfoDisplayDataType diagDataGridRequest;
  struct DiagUcInfoDisplayDataType diagDataGridResponse;

  dword   RxCtrlConfCounter;
  dword   RxDataConfCounter;
  msTimer RxCmdSignalConfTimer;
  dword   RxCmdSignalConfTimeout = 200;
  byte    RXCMD_CONF_COUNTER_INIT_VALUE = 5;
  int     LastRxCtrlSignalValue = -1;
  int     LastRxDataSignalValue = -1;

  //write window
  dword startApplWriteWindow;
}

/**
 * Before CANoe measurement starts
 */
on preStart
{
  startApplWriteWindow = writeCreate("StartApplication");

  //Com use case
  comUc_Initialize();
  comUc_UpdatePanelGridHeaderContent();
  comUc_UpdateUiContent();

  //Reset vars
  @sysvar::StartApplication::ComActualOutput = 0;
  @sysvar::StartApplication::ComExpectedOutput = 0;
  @sysvar::StartApplication::ComInput = 0;
  @sysvar::StartApplication::ComSendCtrl = 1;
  autoSensorValueChange = 1;

  //General
  @sysvar::StartApplication::UseCaseActivator = sysvar::StartApplication::UseCaseActivator::Invalid;

  //IL
  frSetSendPDU(Vita_FR0_PDU_Fr_StartAppl_BothECU_RX);
  frSetSendPDU(Vita_FR0_PDU_nm_RearECU_Fr);
  initSignalTransmissionTimeStamps();

  //AL
  CAL_OnPreStart();

  //Diag use case
  diagRxDataCounter = 0;
  diagUdsChannelChangeTimerState = DIAG_CHANNEL_CHANGE_TIMER_NOT_RUNNING;
  diagUdsRequestQueueReset();

  //CANoe version handling
  saveCANoeVersionInfo();
  verifyCANoeVersion();
}

/**
 * CANoe measurement started
 */
on start
{
  deactivatePanel(sysvar::StartApplication::UseCaseActivator::Com_RxTx);
  CAL_OnStart();

  deactivatePanel(sysvar::StartApplication::UseCaseActivator::Mem);

  deactivatePanel(sysvar::StartApplication::UseCaseActivator::Diag);
  diagInitializeBuscontext();
}

/**
 * CANoe measurement stopped
 */
on preStop
{
  CAL_OnPreStop();
  diagOnPrestop();
}

/**
 * CANoe measurement stopped
 */
on stopMeasurement
{
  writeDestroy(startApplWriteWindow);
}

/**
 * Check whether the transmission of RxCtrl or RxData needs to be repeated
 */
on timer RxCmdSignalConfTimer
{
  if (RxCtrlConfCounter > 0 && LastRxCtrlSignalValue > -1)
  {
    writeRxCtrlSig(LastRxCtrlSignalValue);
    setTimer(RxCmdSignalConfTimer, RxCmdSignalConfTimeout);
  }
  else
  {
    if (RxDataConfCounter > 0 && LastRxDataSignalValue > -1)
    {
      writeRxDataSig(LastRxDataSignalValue);
      setTimer(RxCmdSignalConfTimer, RxCmdSignalConfTimeout);
    }
  }
}





/*********************************************************
 * BusLoadInfo API
 *********************************************************/

/**
 * Initialize the bus load statistics
 **/
void CAL_ResetBusLoadInfo()
{
  int i;
  float timestampNow;
  timestampNow = getCurrentTimeInSeconds();
  for (i=0;i<elcount(msgCounterStartTime);i++)
  {
    msgCounterStartTime[i] = timestampNow;
    msgCounter[i] = 0;
  }
}

/*********************************************************
 * FrWUPStatistics access API
 *********************************************************/

/**
 * Initialize the FlexRay Wakeup Symbol statistics
 **/
void CAL_ResetFrWUPStatistics()
{
  word i;
  for (i = 0; i < GLOBAL_MAX_FLEXRAY_CHANNELS; ++i)
  {
    FrWUPStatistics[i].timestamp = 0;
  }
}
/**
 * Get the point in time when the wakeup symbol was detected
 **/
float CAL_GetFrWUPTimestamp(dword channel)
{
  return FrWUPStatistics[cfg_GetBusSpecificIndex(channel)].timestamp;
}

/**
 * Store the point in time when the wakeup symbol was detected
 **/
void CAL_SetFrWUPTimestamp(dword channel, float value)
{
  FrWUPStatistics[cfg_GetBusSpecificIndex(channel)].timestamp = value;
}

/**
 * Get which wakeup symbol was detected
 **/
word CAL_GetFrWUPSymbol(dword channel)
{
  return FrWUPStatistics[cfg_GetBusSpecificIndex(channel)].symbol;
}

/**
 * Store which wakeup symbol was detected
 **/
void CAL_SetFrWUPSymbol(dword channel, word value)
{
  FrWUPStatistics[cfg_GetBusSpecificIndex(channel)].symbol = value;
}

/**
 * Get on which channel the wakeup symbol was detected
 **/
byte CAL_GetFrWUPFrChannel(dword channel)
{
  return FrWUPStatistics[cfg_GetBusSpecificIndex(channel)].frChannel;
}

/**
 * Store on which channel the wakeup symbol was detected
 **/
void CAL_SetFrWUPFrChannel(dword channel, byte value)
{
  FrWUPStatistics[cfg_GetBusSpecificIndex(channel)].frChannel = value;
}

/*********************************************************
 * CAL_DataConfig access API
 *********************************************************/

/**
 * Get the bus type (e.g. CAN, FlexRay, etc.) for the channel
 **/
enum EBusType cfg_GetBusType(dword channel)
{
  return globalConfigData[channel].busType;
}

/**
 * Get the CANoe channel index for the channel
 **/
dword cfg_GetBusSpecificIndex(dword channel)
{
  return globalConfigData[channel].busSpecificIndex;
}

/**
 * Get the name of the channel in the CANoe configuration
 **/
void cfg_GetCanoeChannelName(dword channel, char channelName[])
{
  strncpy(channelName, CAL_ConfigurationData[ channel ].canoeChannelName, 200);
}
/**
 * Determine the channel ID based on the channel index of CANoe
 * @return GLOBAL_MAX_INVALID_DWORD_VALUE if channel is unknown for the given canoe channel
 **/
dword CAL_GetChannelFromCanoeFlexRayChannel(dword frChannel)
{
  if (frChannel >= CANOE_MAX_FLEXRAY_CHANNELS)
  {
    return GLOBAL_MAX_INVALID_DWORD_VALUE;
  }
  return canoeFrChannelsToChannels[frChannel];
}

/**
 * Determine the CANoe channel index based on the channel ID
 * @return 0 if CANoe Fr channel is unknown for the given 'channel'
 **/
dword CAL_GetCanoeFlexRayChannelFromChannel(dword channel)
{
  if (channel >= GLOBAL_MAX_CHANNELS)
  {
    return 0;
  }
  if ( channelToCanoeChannel[channel] != GLOBAL_MAX_INVALID_DWORD_VALUE )
  {
    return channelToCanoeChannel[channel];
  }
  else
  {
    return 0; // canoe invalid channel
  }
}
/**
 * Determine the channel ID based on the channel index of CANoe
 * @return GLOBAL_MAX_INVALID_DWORD_VALUE if channel is unknown for the given canoe channel
 **/
dword CAL_GetChannelFromCanoeLinChannel(dword linChannel)
{
  if (linChannel >= CANOE_MAX_LIN_CHANNELS)
  {
    return GLOBAL_MAX_INVALID_DWORD_VALUE;
  }
  return canoeLinChannelsToChannels[linChannel];
}
/*********************************************************
 * CAL_Data access API
 *********************************************************/
dword CAL_GetDataBusContext(dword channel)
{
  return CAL_Data[ channel ].busContext;
}

void CAL_SetDataBusContext(dword channel, dword value)
{
  CAL_Data[ channel ].busContext = value;
}

/*********************************************************
 * CAL_Data internal functions
 *********************************************************/

/**
 * Determine whether bus activity detection is enabled for the channel.
 **/
byte CAL_GetBusActivityDetectionEnabled(dword channel)
{
  return busActivityNotificationStatus[channel];
}

/**
 * Enable or disable bus activity detection for the channel.
 **/
void CAL_SetBusActivityDetectionEnabled(dword channel, byte value)
{
  busActivityNotificationStatus[channel] = value;
}

/*********************************************************
 * CAL API
 *********************************************************/

/**
 * Notify about the "on preStart" procedure.
 **/
void CAL_OnPreStart()
{
  char channelName[200];
  dword busContext;
  dword channel;
  byte value;
  for (channel=0; channel< CANOE_MAX_FLEXRAY_CHANNELS; ++channel)
  {
    canoeFrChannelsToChannels[channel] = GLOBAL_MAX_INVALID_DWORD_VALUE;
  }
  for (channel=0; channel< CANOE_MAX_LIN_CHANNELS; ++channel)
  {
    canoeLinChannelsToChannels[channel] = GLOBAL_MAX_INVALID_DWORD_VALUE;
  }
  for (channel=0; channel<GLOBAL_MAX_CHANNELS; channel++)
  {
    cfg_GetCanoeChannelName(channel, channelName);
    busContext = GetBusNameContext(channelName);
    channelToCanoeChannel[channel] = GLOBAL_MAX_INVALID_DWORD_VALUE;
    CAL_SetChannelOnlineState(channel, true);
    if (0 != busContext)
    {
      value = busContext;
      CAL_SetDataBusContext(channel, busContext);
      if ( E_FLEXRAY == cfg_GetBusType(channel) )
      {
        canoeFrChannelsToChannels[value] = channel;
        channelToCanoeChannel[channel] = value;
      }
      if (E_LIN == cfg_GetBusType(channel))
      {
        canoeLinChannelsToChannels[value] = channel;
      }
    }
  }
}

/**
 * Notify about the "on start" procedure.
 **/
void CAL_OnStart()
{
}

/**
 * Notify about the "on preStop" procedure.
 **/
void CAL_OnPreStop()
{
}

/**
 * Set the channel online status.
 * @param channel: channel ID
 * @param state: channel state to be set. 1 if the channel is online else 0.
 **/
void CAL_SetChannelOnlineState(dword channel, byte state)
{
  if (channel >= GLOBAL_MAX_CHANNELS)
  {
    return;
  }
  channelOnlineState[channel] = state;
}

/**
 * Get the channel online status.
 * @param channel: channel ID
 * @return 1 if the channel is online else 0.
 **/
byte CAL_GetChannelOnlineState(dword channel)
{
  if (channel >= GLOBAL_MAX_CHANNELS)
  {
    return false;
  }
  return channelOnlineState[channel];
}

/**
 * Disable the detection of bus activity like a wakeup.
 * It should be called before full communication is requested from the BUSSM to prevent detection of own wakeup on the bus
 * so that in this case ComM_BusActivityIndication() is not called.
 * @param channel: Identification of the channel.
 **/
void CAL_DisableBusActivityDetection(dword channel) {
  CAL_SetBusActivityDetectionEnabled(channel, false);
}

/**
 * Enable the detection of bus activity like a wakeup.
 * It should be called after the BUSSM reports bus sleep so that a further bus activity of the currently sleeping bus can be detected again.
 * @param channel: Identification of the channel.
 **/
void CAL_EnableBusActivityDetection(dword channel) {
  CAL_SetBusActivityDetectionEnabled(channel, true);
}

/**
 * Activate the can bus for the tester.
 * @param channel: Identification of the channel.
 **/
void CAL_CanGoOnline(dword channel)
{
  setBusContext(CAL_GetDataBusContext(channel));
  canOnline(3);
  CAL_SetChannelOnlineState(channel, true);
}

/**
 * Deactivate the can bus for the tester.
 * @param channel: Identification of the channel.
 **/
void CAL_CanGoOffline(dword channel)
{
  setBusContext(CAL_GetDataBusContext(channel));
  canOffline(3);
  CAL_SetChannelOnlineState(channel, false);
}

/**
 * Sends a Lin wakeup frame.
 * @param channel: Identification of the channel.
 **/
void CAL_LinSendWakeup(dword channel)
{
  setBusContext(CAL_GetDataBusContext(channel));
#if ( (TOOL_MAJOR_VERSION == 8) && (TOOL_MINOR_VERSION < 5) )
  linSendWakeup();
#else
  linWakeup();
#endif
}


/**
 * Handle the detection of a FLEXRAY wakeup symbol
 **/
on frSymbol
{
  dword channel;
  if ( (dword)this.msgChannel > GLOBAL_MAX_FLEXRAY_CHANNELS )
  {
    return;
  }
  channel = CAL_GetChannelFromCanoeFlexRayChannel((dword)this.msgChannel);
  if (GLOBAL_MAX_INVALID_DWORD_VALUE == channel)
  {
    return; // unsupported
  }
  /* store statistic for symbols != CAS */
  if (this.fr_symbol != 1)
  {
    CAL_SetFrWUPTimestamp(channel, getCurrentTimeInSeconds());
    CAL_SetFrWUPSymbol(channel, this.fr_symbol);
    CAL_SetFrWUPFrChannel(channel, this.fr_channelMask);
  }
  CAL_OnFrSymbol(channel);
}
/**
 * Handle the start of a FlexRay cycle
 **/
on frStartCycle *
{
  dword channel;
  if ( (dword)this.msgChannel > GLOBAL_MAX_FLEXRAY_CHANNELS )
  {
    return;
  }
  channel = CAL_GetChannelFromCanoeFlexRayChannel((dword)this.msgChannel);
  if (GLOBAL_MAX_INVALID_DWORD_VALUE == channel)
  {
    return;
  }
  CAL_OnFrStartCycle(channel, this.fr_cycle);
}

/**
 * Handle the change of the FlexRay POC state
 **/
on frPocState
{
  dword channel;
  if ( (dword)this.msgChannel > GLOBAL_MAX_FLEXRAY_CHANNELS )
  {
    return;
  }
  channel = CAL_GetChannelFromCanoeFlexRayChannel((dword)this.msgChannel);
  if ( GLOBAL_MAX_INVALID_DWORD_VALUE == channel)
  {
    return;
  }
  CAL_OnFrPocState(channel, this.fr_POCState);
}



/**
 * Deactivate all controls of the panel for the given use case
 **/
void deactivatePanel(int useCaseId)
{
  switch(useCaseId)
  {
    case 0:
    case 3:
      setComPanelStatus(0);
      break;
    case 1:
      setMemPanelStatus(0);
      break;
    case 2:
      setDiagPanelStatus(0);
      break;
  }
}


/**
 * Enable/disable the controls of the panel for the MEM use case
 **/
void setMemPanelStatus(byte isEnabled)
{
  enableControl("StartApplication.Memory", "memInputBox", isEnabled);
  enableControl("StartApplication.Memory", "memBlockSelector", isEnabled);
  enableControl("StartApplication.Memory", "memWriteButton", isEnabled);
  enableControl("StartApplication.Memory", "memOperationPendingLED", isEnabled);
}

/**
 * Activate all controls of the panel for the given use case
 **/
void activatePanel(int useCaseId)
{
  switch (useCaseId)
  {
    case 0:
    case 3:
      writeLineEx(startApplWriteWindow,4,"Activate Com use case");
      setComPanelStatus(1);
      break;
    case 1:
      writeLineEx(startApplWriteWindow,4,"Activate Mem use case");
      setMemPanelStatus(1);
      break;
    case 2:
      writeLineEx(startApplWriteWindow,4,"Activate Diag use case");
      setDiagPanelStatus(1);
      break;
  }
}

/**
 * Initiate the transmission of RxCtrl with the given value
 **/
void writeRxCtrlSig(int data)
{
  updateSignal(SIGNAL_ID_VITA_CAN0_MSG_RXCYCLE500_20_OCAN_SIGNAL_RX20BIT_CYCLIC_OMSG_RXCYCLE500_20_OCAN, data);
  writeLineEx(startApplWriteWindow,4," - CtrlSignal: Send msg_RxCycle500_20_oCAN on Vita_CAN0");
  outputPdu(PDU_ID_VITA_CAN0_MSG_RXCYCLE500_20_OCAN_272);
  LastRxCtrlSignalValue = data;
  setRxCtrlConfCounter();
}

/**
 * Initiate the transmission of RxData with the given value
 **/
void writeRxDataSig(int data)
{
  updateSignal(SIGNAL_ID_VITA_CAN0_MSG_RXCYCLE_E2EPROF1C_500_10_OCAN_SIGRX_PROF1C_SIG32BIT_OMSG_RXCYCLE_E2EPROF1C_500_10_OCAN, data);
  writeLineEx(startApplWriteWindow,4," - DataSignal: Send msg_RxCycle_E2eProf1C_500_10_oCAN on Vita_CAN0");
  outputPdu(PDU_ID_VITA_CAN0_MSG_RXCYCLE_E2EPROF1C_500_10_OCAN_288);
  LastRxDataSignalValue = data;
  setRxDataConfCounter();
}


/**
 * In the COM use case, calculate the new signal values by scaling the current sensor value
 * and trigger the transmission of the corresponding messages to the ECU.
 **/
void sendComRxSignals(byte sensorValue)
{
  int64 scaledValue;
  dword signalLength;
  scaledValue = sensorValue*0x1000000;                            /* Scale uint8 [0 ... 0xff] to uint32 [0 ... 0xffffffff] */
  updateSignal(SIGNAL_ID_VITA_CAN0_MSG_RXCYCLE_E2EPROF1C_500_10_OCAN_SIGRX_PROF1C_SIG32BIT_OMSG_RXCYCLE_E2EPROF1C_500_10_OCAN, scaledValue);
  scaledValue = sensorValue;                                      /* Scale uint8 [0 ... 0xff] to uint8 [0 ... 0xff] */
  updateSignal(SIGNAL_ID_VITA_LIN0_FRAME_LINTR_REARECU_SIG_STARTAPPL_LINDATA_RX, scaledValue);
  scaledValue = sensorValue*0x100;                                /* Scale uint8 [0 ... 0xff] to uint16 [0 ... 0xffff] */
  updateSignal(SIGNAL_ID_VITA_FR0_PDU_FR_STARTAPPL_BOTHECU_RX_STARTAPPL_FR_BOTHECU_RX_CTRL_OPDU_FR_STARTAPPL_BOTHECU_RX, scaledValue);
  outputPdu(PDU_ID_VITA_CAN0_MSG_RXCYCLE_E2EPROF1C_500_10_OCAN_288 );
  outputPdu(PDU_ID_VITA_FR0_PDU_FR_STARTAPPL_BOTHECU_RX );
  outputPdu(PDU_ID_VITA_LIN0_FRAME_LINTR_REARECU );
}

/**
 * The selected communication path in the COM use case has changed
 **/
on sysvar_update StartApplication::ComSignalPairSelector
{
  byte value;
  switch(sysGetVariableInt(this))
  {
    case 0:
      comGrid[COM_DATA_GRID_VIEW].row[COM_ROW_J1939_TP_TYPE].isVisible = false;
      comGrid[COM_DATA_GRID_VIEW].row[COM_ROW_J1939_REQUESTED].isVisible = false;
      comGrid[COM_DATA_GRID_VIEW].row[COM_ROW_SECOC_FRESHNESS].isVisible = false;
      comGrid[COM_DATA_GRID_VIEW].row[COM_ROW_SECOC_AUTH].isVisible = false;
      comIdOfRxSignalUnderTest = SIGNAL_ID_VITA_CAN0_MSG_RXCYCLE_E2EPROF1C_500_10_OCAN_SIGRX_PROF1C_SIG32BIT_OMSG_RXCYCLE_E2EPROF1C_500_10_OCAN;
      comIdOfTxSignalUnderTest = SIGNAL_ID_VITA_CAN0_MSG_TXCYCLE10_0_OCAN_SIGNAL_TX24BIT_CYCLIC_OMSG_TXCYCLE10_0_OCAN;
      snprintf(comGrid[COM_DATA_GRID_VIEW].row[COM_ROW_COM_PATH].cell[COM_COL_RX].text, CHAR_200, "Vita_CAN0 (CAN)");
      snprintf(comGrid[COM_DATA_GRID_VIEW].row[COM_ROW_SIGNAL].cell[COM_COL_RX].text, CHAR_200, "SigRx_Prof1C_Sig32Bit_omsg_RxCycle_E2eProf1C_500_10_oCAN");
      snprintf(comGrid[COM_DATA_GRID_VIEW].row[COM_ROW_COM_PATH].cell[COM_COL_TX].text, CHAR_200, "Vita_CAN0 (CAN)");
      snprintf(comGrid[COM_DATA_GRID_VIEW].row[COM_ROW_SIGNAL].cell[COM_COL_TX].text, CHAR_200, "Signal_Tx24bit_Cyclic_omsg_TxCycle10_0_oCAN");
      snprintf(comGrid[COM_DATA_GRID_VIEW].row[COM_ROW_TRANSMITTED].cell[COM_COL_RX].text, CHAR_200, "%.3f [s]", getSignalTransmissionTimeStamp(SIGNAL_ID_VITA_CAN0_MSG_RXCYCLE_E2EPROF1C_500_10_OCAN_SIGRX_PROF1C_SIG32BIT_OMSG_RXCYCLE_E2EPROF1C_500_10_OCAN));
      snprintf(comGrid[COM_DATA_GRID_VIEW].row[COM_ROW_TRANSMITTED].cell[COM_COL_TX].text, CHAR_200, "%.3f [s]", getSignalTransmissionTimeStamp(SIGNAL_ID_VITA_CAN0_MSG_TXCYCLE10_0_OCAN_SIGNAL_TX24BIT_CYCLIC_OMSG_TXCYCLE10_0_OCAN));
      snprintf(comUiElement[COM_UI_SCALE_RX].text, CHAR_100, "y(x) = x*0x1000000");
      snprintf(comUiElement[COM_UI_SCALE_RX].hint, CHAR_200, "");
      snprintf(comUiElement[COM_UI_CALCULATE].text, CHAR_100, "u(y) = y/0x100");
      snprintf(comUiElement[COM_UI_CALCULATE].hint, CHAR_200, "");
      snprintf(comUiElement[COM_UI_SCALE_TX].text, CHAR_100, "v(u) = u/0x10000");
      snprintf(comUiElement[COM_UI_SCALE_TX].hint, CHAR_200, "");
      comUc_UpdateUiContent();
    break;
    case 1:
      comGrid[COM_DATA_GRID_VIEW].row[COM_ROW_J1939_TP_TYPE].isVisible = false;
      comGrid[COM_DATA_GRID_VIEW].row[COM_ROW_J1939_REQUESTED].isVisible = false;
      comGrid[COM_DATA_GRID_VIEW].row[COM_ROW_SECOC_FRESHNESS].isVisible = false;
      comGrid[COM_DATA_GRID_VIEW].row[COM_ROW_SECOC_AUTH].isVisible = false;
      comIdOfRxSignalUnderTest = SIGNAL_ID_VITA_LIN0_FRAME_LINTR_REARECU_SIG_STARTAPPL_LINDATA_RX;
      comIdOfTxSignalUnderTest = SIGNAL_ID_VITA_LIN0_FRAME_LINTR_MYECU_SIG_STARTAPPL_LINDATA_TX;
      snprintf(comGrid[COM_DATA_GRID_VIEW].row[COM_ROW_COM_PATH].cell[COM_COL_RX].text, CHAR_200, "Vita_LIN0 (LIN)");
      snprintf(comGrid[COM_DATA_GRID_VIEW].row[COM_ROW_SIGNAL].cell[COM_COL_RX].text, CHAR_200, "Sig_StartAppl_LinData_Rx_oFrame_LinTr_RearECU_oLIN00");
      snprintf(comGrid[COM_DATA_GRID_VIEW].row[COM_ROW_COM_PATH].cell[COM_COL_TX].text, CHAR_200, "Vita_LIN0 (LIN)");
      snprintf(comGrid[COM_DATA_GRID_VIEW].row[COM_ROW_SIGNAL].cell[COM_COL_TX].text, CHAR_200, "Sig_StartAppl_LinData_Tx_oFrame_LinTr_MyECU_oLIN00");
      snprintf(comGrid[COM_DATA_GRID_VIEW].row[COM_ROW_TRANSMITTED].cell[COM_COL_RX].text, CHAR_200, "%.3f [s]", getSignalTransmissionTimeStamp(SIGNAL_ID_VITA_LIN0_FRAME_LINTR_REARECU_SIG_STARTAPPL_LINDATA_RX));
      snprintf(comGrid[COM_DATA_GRID_VIEW].row[COM_ROW_TRANSMITTED].cell[COM_COL_TX].text, CHAR_200, "%.3f [s]", getSignalTransmissionTimeStamp(SIGNAL_ID_VITA_LIN0_FRAME_LINTR_MYECU_SIG_STARTAPPL_LINDATA_TX));
      snprintf(comUiElement[COM_UI_SCALE_RX].text, CHAR_100, "y(x) = x");
      snprintf(comUiElement[COM_UI_SCALE_RX].hint, CHAR_200, "");
      snprintf(comUiElement[COM_UI_CALCULATE].text, CHAR_100, "u(y) = y");
      snprintf(comUiElement[COM_UI_CALCULATE].hint, CHAR_200, "");
      snprintf(comUiElement[COM_UI_SCALE_TX].text, CHAR_100, "v(u) = u");
      snprintf(comUiElement[COM_UI_SCALE_TX].hint, CHAR_200, "");
      comUc_UpdateUiContent();
    break;
    case 2:
      comGrid[COM_DATA_GRID_VIEW].row[COM_ROW_J1939_TP_TYPE].isVisible = false;
      comGrid[COM_DATA_GRID_VIEW].row[COM_ROW_J1939_REQUESTED].isVisible = false;
      comGrid[COM_DATA_GRID_VIEW].row[COM_ROW_SECOC_FRESHNESS].isVisible = false;
      comGrid[COM_DATA_GRID_VIEW].row[COM_ROW_SECOC_AUTH].isVisible = false;
      comIdOfRxSignalUnderTest = SIGNAL_ID_VITA_FR0_PDU_FR_STARTAPPL_BOTHECU_RX_STARTAPPL_FR_BOTHECU_RX_CTRL_OPDU_FR_STARTAPPL_BOTHECU_RX;
      comIdOfTxSignalUnderTest = SIGNAL_ID_VITA_FR0_PDU_TRANSMIT_MYECU_SIGNAL_SOMETXSIGNAL_OPDU_TRANSMIT_MYECU;
      snprintf(comGrid[COM_DATA_GRID_VIEW].row[COM_ROW_COM_PATH].cell[COM_COL_RX].text, CHAR_200, "Vita_FR0 (FlexRay)");
      snprintf(comGrid[COM_DATA_GRID_VIEW].row[COM_ROW_SIGNAL].cell[COM_COL_RX].text, CHAR_200, "StartAppl_Fr_BothECU_RX_Ctrl_oPDU_Fr_StartAppl_BothECU_RX");
      snprintf(comGrid[COM_DATA_GRID_VIEW].row[COM_ROW_COM_PATH].cell[COM_COL_TX].text, CHAR_200, "Vita_FR0 (FlexRay)");
      snprintf(comGrid[COM_DATA_GRID_VIEW].row[COM_ROW_SIGNAL].cell[COM_COL_TX].text, CHAR_200, "Signal_SomeTxSignal_oPDU_Transmit_MyECU");
      snprintf(comGrid[COM_DATA_GRID_VIEW].row[COM_ROW_TRANSMITTED].cell[COM_COL_RX].text, CHAR_200, "%.3f [s]", getSignalTransmissionTimeStamp(SIGNAL_ID_VITA_FR0_PDU_FR_STARTAPPL_BOTHECU_RX_STARTAPPL_FR_BOTHECU_RX_CTRL_OPDU_FR_STARTAPPL_BOTHECU_RX));
      snprintf(comGrid[COM_DATA_GRID_VIEW].row[COM_ROW_TRANSMITTED].cell[COM_COL_TX].text, CHAR_200, "%.3f [s]", getSignalTransmissionTimeStamp(SIGNAL_ID_VITA_FR0_PDU_TRANSMIT_MYECU_SIGNAL_SOMETXSIGNAL_OPDU_TRANSMIT_MYECU));
      snprintf(comUiElement[COM_UI_SCALE_RX].text, CHAR_100, "y(x) = x*0x100");
      snprintf(comUiElement[COM_UI_SCALE_RX].hint, CHAR_200, "");
      snprintf(comUiElement[COM_UI_CALCULATE].text, CHAR_100, "u(y) = y*0x10000");
      snprintf(comUiElement[COM_UI_CALCULATE].hint, CHAR_200, "");
      snprintf(comUiElement[COM_UI_SCALE_TX].text, CHAR_100, "v(u) = u/0x1000000");
      snprintf(comUiElement[COM_UI_SCALE_TX].hint, CHAR_200, "");
      comUc_UpdateUiContent();
    break;
  }
  // Update the Rx Signal to show the new scaled value.
  if (activeUseCase == sysvar::StartApplication::UseCaseActivator::Com_RxTx || activeUseCase == sysvar::StartApplication::UseCaseActivator::Com_TxOnly)
  {
    // Set the value entries to '---', in case tx/rx direction does not work the value will not be updated
    // otherwise the new value will overwrite the '---'.
    snprintf(comGrid[COM_DATA_GRID_VIEW].row[COM_ROW_VALUE].cell[COM_COL_TX].text, CHAR_200, "---");
    snprintf(comGrid[COM_DATA_GRID_VIEW].row[COM_ROW_VALUE].cell[COM_COL_RX].text, CHAR_200, "---");
    snprintf(comGrid[COM_DATA_GRID_VIEW].row[COM_ROW_SECOC_FRESHNESS].cell[COM_COL_TX].text, CHAR_200, "---");
    snprintf(comGrid[COM_DATA_GRID_VIEW].row[COM_ROW_SECOC_FRESHNESS].cell[COM_COL_RX].text, CHAR_200, "---");
    snprintf(comGrid[COM_DATA_GRID_VIEW].row[COM_ROW_SECOC_AUTH].cell[COM_COL_TX].text, CHAR_200, "---");
    snprintf(comGrid[COM_DATA_GRID_VIEW].row[COM_ROW_SECOC_AUTH].cell[COM_COL_RX].text, CHAR_200, "---");
    comUc_UpdatePanelGridRowContent();
    value = @sysvar::StartApplication::ComInput;
    sendComRxSignals(value);
  }
  // Set the rx signal under test to the Control RxData signal in case of COM use case TxOnly to get data grid information updates
  if (activeUseCase == sysvar::StartApplication::UseCaseActivator::Com_TxOnly)
  {
    comIdOfRxSignalUnderTest = SIGNAL_ID_VITA_CAN0_MSG_RXCYCLE_E2EPROF1C_500_10_OCAN_SIGRX_PROF1C_SIG32BIT_OMSG_RXCYCLE_E2EPROF1C_500_10_OCAN;
  }
}

/**
 * This function is called when a signal transmitted by the ECU changes.
 * @param signalId: unique identifier of signal
 * @param value: new signal value
 **/
void StartApplication_OnTxSignalUpdate(dword signalId, int64 value)
{
  int64 actualValue;
  switch(signalId)
  {
    case SIGNAL_ID_VITA_CAN0_MSG_TXCYCLE10_0_OCAN_SIGNAL_TX24BIT_CYCLIC_OMSG_TXCYCLE10_0_OCAN:
      StartApplication_OnTxData(value);
      break;
    case SIGNAL_ID_VITA_CAN0_MSG_TXCYCLE1000_10_OCAN_SIGNAL_TX10BIT_CYCLIC_OMSG_TXCYCLE1000_10_OCAN:
      StartApplication_OnTxCtrl(value);
      break;
  }
  if (activeUseCase != sysvar::StartApplication::UseCaseActivator::Com_RxTx && activeUseCase != sysvar::StartApplication::UseCaseActivator::Com_TxOnly)
  {
    return;
  }
  if (signalId == comIdOfTxSignalUnderTest && value != 0xFF)  /* Is the signal the Tx signal of the currently selected Rx-Tx signal pair and value is valid ?*/
  {
    snprintf(comGrid[COM_DATA_GRID_VIEW].row[COM_ROW_VALUE].cell[COM_COL_TX].text, CHAR_200, "%d", value);
    /* Calculate the speed value by scaling the Tx signal to the value range [0..254]  */
    switch(signalId)
    {
      case SIGNAL_ID_VITA_CAN0_MSG_TXCYCLE10_0_OCAN_SIGNAL_TX24BIT_CYCLIC_OMSG_TXCYCLE10_0_OCAN:
        snprintf(comGrid[COM_DATA_GRID_VIEW].row[COM_ROW_TRANSMITTED].cell[COM_COL_TX].text, CHAR_200, "%.3f [s]", getSignalTransmissionTimeStamp(SIGNAL_ID_VITA_CAN0_MSG_TXCYCLE10_0_OCAN_SIGNAL_TX24BIT_CYCLIC_OMSG_TXCYCLE10_0_OCAN));
        actualValue = value/0x10000;                                    /* Scale uint24 [0 ... 0xffffff] to uint8 [0 ... 0xff] */
        break;
      case SIGNAL_ID_VITA_LIN0_FRAME_LINTR_MYECU_SIG_STARTAPPL_LINDATA_TX:
        snprintf(comGrid[COM_DATA_GRID_VIEW].row[COM_ROW_TRANSMITTED].cell[COM_COL_TX].text, CHAR_200, "%.3f [s]", getSignalTransmissionTimeStamp(SIGNAL_ID_VITA_LIN0_FRAME_LINTR_MYECU_SIG_STARTAPPL_LINDATA_TX));
        actualValue = value;                                            /* Scale uint8 [0 ... 0xff] to uint8 [0 ... 0xff] */
        break;
      case SIGNAL_ID_VITA_FR0_PDU_TRANSMIT_MYECU_SIGNAL_SOMETXSIGNAL_OPDU_TRANSMIT_MYECU:
        snprintf(comGrid[COM_DATA_GRID_VIEW].row[COM_ROW_TRANSMITTED].cell[COM_COL_TX].text, CHAR_200, "%.3f [s]", getSignalTransmissionTimeStamp(SIGNAL_ID_VITA_FR0_PDU_TRANSMIT_MYECU_SIGNAL_SOMETXSIGNAL_OPDU_TRANSMIT_MYECU));
        actualValue = value/0x1000000;                                  /* Scale uint32 [0 ... 0xffffffff] to uint8 [0 ... 0xff] */
        break;
    }
    comUc_UpdatePanelGridRowContent();
    @sysvar::StartApplication::ComActualOutput = actualValue;
  }
}

/**
 * This function is called when a signal transmitted by the Tester changes.
 * @param signalId: unique identifier of signal
 * @param value: new signal value
 **/
void StartApplication_OnRxSignalUpdate(dword signalId, int64 value)
{
  int64 expectedValue = 0;
  switch(signalId)
  {
    case SIGNAL_ID_VITA_CAN0_MSG_RXCYCLE_E2EPROF1C_500_10_OCAN_SIGRX_PROF1C_SIG32BIT_OMSG_RXCYCLE_E2EPROF1C_500_10_OCAN:
      StartApplication_OnRxData(value);
      break;
    case SIGNAL_ID_VITA_CAN0_MSG_RXCYCLE500_20_OCAN_SIGNAL_RX20BIT_CYCLIC_OMSG_RXCYCLE500_20_OCAN:
      StartApplication_OnRxCtrl(value);
      break;
  }
  if (activeUseCase != sysvar::StartApplication::UseCaseActivator::Com_RxTx && activeUseCase != sysvar::StartApplication::UseCaseActivator::Com_TxOnly)
  {
    return;
  }
  if (signalId == comIdOfRxSignalUnderTest && value != 0xFF) /* Is the signal the Rx signal of the currently selected Rx-Tx signal pair and has a valid value?*/
  {
    snprintf(comGrid[COM_DATA_GRID_VIEW].row[COM_ROW_VALUE].cell[COM_COL_RX].text, CHAR_200, "%d", value);
    switch(signalId)
    {
      case SIGNAL_ID_VITA_CAN0_MSG_RXCYCLE_E2EPROF1C_500_10_OCAN_SIGRX_PROF1C_SIG32BIT_OMSG_RXCYCLE_E2EPROF1C_500_10_OCAN:
        snprintf(comGrid[COM_DATA_GRID_VIEW].row[COM_ROW_TRANSMITTED].cell[COM_COL_RX].text, CHAR_200, "%.3f [s]", getSignalTransmissionTimeStamp(SIGNAL_ID_VITA_CAN0_MSG_RXCYCLE_E2EPROF1C_500_10_OCAN_SIGRX_PROF1C_SIG32BIT_OMSG_RXCYCLE_E2EPROF1C_500_10_OCAN));
        expectedValue = value/0x1000000;                                  /* Scale uint32 [0 ... 0xffffffff] to uint8 [0 ... 0xff] */
        break;
      case SIGNAL_ID_VITA_LIN0_FRAME_LINTR_REARECU_SIG_STARTAPPL_LINDATA_RX:
        snprintf(comGrid[COM_DATA_GRID_VIEW].row[COM_ROW_TRANSMITTED].cell[COM_COL_RX].text, CHAR_200, "%.3f [s]", getSignalTransmissionTimeStamp(SIGNAL_ID_VITA_LIN0_FRAME_LINTR_REARECU_SIG_STARTAPPL_LINDATA_RX));
        expectedValue = value;                                            /* Scale uint8 [0 ... 0xff] to uint8 [0 ... 0xff] */
        break;
      case SIGNAL_ID_VITA_FR0_PDU_FR_STARTAPPL_BOTHECU_RX_STARTAPPL_FR_BOTHECU_RX_CTRL_OPDU_FR_STARTAPPL_BOTHECU_RX:
        snprintf(comGrid[COM_DATA_GRID_VIEW].row[COM_ROW_TRANSMITTED].cell[COM_COL_RX].text, CHAR_200, "%.3f [s]", getSignalTransmissionTimeStamp(SIGNAL_ID_VITA_FR0_PDU_FR_STARTAPPL_BOTHECU_RX_STARTAPPL_FR_BOTHECU_RX_CTRL_OPDU_FR_STARTAPPL_BOTHECU_RX));
        expectedValue = value/0x100;                                      /* Scale uint16 [0 ... 0xffff] to uint8 [0 ... 0xff] */
        break;
      default:
        expectedValue = value;
        break;
    }
    comUc_UpdatePanelGridRowContent();
    @sysvar::StartApplication::ComExpectedOutput = expectedValue;
  }
}

/**
 * This function is called when a signal with array data type transmitted by the ECU changes.
 * @param signalId: unique identifier of signal
 * @param signalPayload: new signal payload
 * @param length: signal length
 **/
void StartApplication_OnTxSignalUpdate(dword signalId, byte signalPayload[], dword length)
{
  int64 actualValue = 0;
  word index;
  dword i;
  if (activeUseCase != sysvar::StartApplication::UseCaseActivator::Com_RxTx && activeUseCase != sysvar::StartApplication::UseCaseActivator::Com_TxOnly)
  {
    return;
  }
  if (signalId == comIdOfTxSignalUnderTest)
  {
    actualValue = 0;
    switch(signalId)
    {
    }
    comUc_UpdatePanelGridRowContent();
    @sysvar::StartApplication::ComActualOutput = actualValue;
  }
}

/**
 * This function is called when a signal with array data type transmitted by the Tester changes.
 * @param signalId: unique identifier of signal
 * @param signalPayload: new signal payload
 * @param length: signal length
 **/
void StartApplication_OnRxSignalUpdate(dword signalId, byte signalPayload[], dword length)
{
  byte expectedValue;
  word index;
  dword i;
  if (activeUseCase != sysvar::StartApplication::UseCaseActivator::Com_RxTx && activeUseCase != sysvar::StartApplication::UseCaseActivator::Com_TxOnly)
  {
    return;
  }
  if (signalId == comIdOfRxSignalUnderTest)
  {
    expectedValue = 0;
    switch(signalId)
    {
    }
    comUc_UpdatePanelGridRowContent();
    @sysvar::StartApplication::ComExpectedOutput = expectedValue;
  }
}


/**
 * Determine whether the signal has an update bit
 **/
byte hasUpdateBit(dword signalId)
{
  byte hasUpdateBit;
  hasUpdateBit = 0;
  switch ( signalId )
  {
    case SIGNAL_ID_VITA_CAN0_MSG_RXCYCLE500_20_OCAN_SIGNAL_RX20BIT_CYCLIC_OMSG_RXCYCLE500_20_OCAN :
      hasUpdateBit = 0;
      break;
    case SIGNAL_ID_VITA_CAN0_MSG_RXCYCLE_E2EPROF1C_500_10_OCAN_SIGRX_PROF1C_SIG32BIT_OMSG_RXCYCLE_E2EPROF1C_500_10_OCAN :
      hasUpdateBit = 0;
      break;
    case SIGNAL_ID_VITA_FR0_PDU_FR_STARTAPPL_BOTHECU_RX_STARTAPPL_FR_BOTHECU_RX_CTRL_OPDU_FR_STARTAPPL_BOTHECU_RX :
      hasUpdateBit = 0;
      break;
    case SIGNAL_ID_VITA_LIN0_FRAME_LINTR_REARECU_SIG_STARTAPPL_LINDATA_RX :
      hasUpdateBit = 0;
      break;
  }
  return hasUpdateBit;
}

/**
 * Get the ID of the PDU which contains the signal
 **/
dword getPduIdFromSignalId(dword signalId)
{
  switch ( signalId )
  {
    case SIGNAL_ID_VITA_CAN0_MSG_RXCYCLE500_20_OCAN_SIGNAL_RX20BIT_CYCLIC_OMSG_RXCYCLE500_20_OCAN : return PDU_ID_VITA_CAN0_MSG_RXCYCLE500_20_OCAN_272;
    case SIGNAL_ID_VITA_CAN0_MSG_RXCYCLE_E2EPROF1C_500_10_OCAN_SIGRX_PROF1C_SIG32BIT_OMSG_RXCYCLE_E2EPROF1C_500_10_OCAN : return PDU_ID_VITA_CAN0_MSG_RXCYCLE_E2EPROF1C_500_10_OCAN_288;
    case SIGNAL_ID_VITA_CAN0_MSG_TXCYCLE1000_10_OCAN_SIGNAL_TX10BIT_CYCLIC_OMSG_TXCYCLE1000_10_OCAN : return PDU_ID_VITA_CAN0_MSG_TXCYCLE1000_10_OCAN_872;
    case SIGNAL_ID_VITA_CAN0_MSG_TXCYCLE10_0_OCAN_SIGNAL_TX24BIT_CYCLIC_OMSG_TXCYCLE10_0_OCAN : return PDU_ID_VITA_CAN0_MSG_TXCYCLE10_0_OCAN_616;
    case SIGNAL_ID_VITA_FR0_PDU_FR_STARTAPPL_BOTHECU_RX_STARTAPPL_FR_BOTHECU_RX_CTRL_OPDU_FR_STARTAPPL_BOTHECU_RX : return PDU_ID_VITA_FR0_PDU_FR_STARTAPPL_BOTHECU_RX;
    case SIGNAL_ID_VITA_FR0_PDU_TRANSMIT_MYECU_SIGNAL_SOMETXSIGNAL_OPDU_TRANSMIT_MYECU : return PDU_ID_VITA_FR0_PDU_TRANSMIT_MYECU;
    case SIGNAL_ID_VITA_LIN0_FRAME_LINTR_MYECU_SIG_STARTAPPL_LINDATA_TX : return PDU_ID_VITA_LIN0_FRAME_LINTR_MYECU;
    case SIGNAL_ID_VITA_LIN0_FRAME_LINTR_REARECU_SIG_STARTAPPL_LINDATA_RX : return PDU_ID_VITA_LIN0_FRAME_LINTR_REARECU;
  }
  writeLineEx(startApplWriteWindow, 3, "Unknown signal id is used, id: %d", signalId);
  return GLOBAL_MAX_INVALID_DWORD_VALUE;
}

/**
 * Output the PDU with the current PDU buffer content as payload
 **/
void outputPdu(dword pduId)
{
  dword channel;
  channel = getChannelIdFromPduId(pduId);
  if (channel != GLOBAL_MAX_INVALID_DWORD_VALUE)
  {
    if (CAL_GetChannelOnlineState(channel) != true)
    {
      return;
    }
  }
  switch ( pduId )
  {
    case PDU_ID_VITA_CAN0_1087 :
      output(Vita_CAN0_1087);
      break;
    case PDU_ID_VITA_CAN0_MSG_RXCYCLE500_20_OCAN_272 :
#if TOOL_MAJOR_VERSION >= 9
      triggerPDU(Vita_CAN0_msg_RxCycle500_20_oCAN);
#else
      output(Vita_CAN0_msg_RxCycle500_20_oCAN);
#endif
      break;
    case PDU_ID_VITA_CAN0_MSG_RXCYCLE_E2EPROF1C_500_10_OCAN_288 :
#if TOOL_MAJOR_VERSION >= 9
      triggerPDU(Vita_CAN0_msg_RxCycle_E2eProf1C_500_10_oCAN);
#else
      output(Vita_CAN0_msg_RxCycle_E2eProf1C_500_10_oCAN);
#endif
      break;
    case PDU_ID_VITA_FR0_PDU_FR_STARTAPPL_BOTHECU_RX :
      frUpdatePdu(Vita_FR0_PDU_Fr_StartAppl_BothECU_RX, 1, /* Update bit is set on transmission */ 1 /* The PDU is transmitted one time. */);
      break;
    case PDU_ID_VITA_FR0_PDU_NM_REARECU_FR :
      frUpdatePdu(Vita_FR0_PDU_nm_RearECU_Fr, 1, /* Update bit is set on transmission */ 1 /* The PDU is transmitted one time. */);
      break;
    case PDU_ID_VITA_LIN0_FRAME_LINTR_REARECU :
      output(Vita_LIN0_Frame_LinTr_RearECU);
      break;
    default:
      writeLineEx(startApplWriteWindow, 3, "Unknown pdu id is used, id: %d", pduId);
  }
}
/**
 * Update the PDU buffer with the given payload and output the PDU
 **/
void outputPdu(dword pduId, byte payload[], dword pduLength)
{
  updatePdu(pduId, payload, pduLength);
  outputPdu(pduId);
}

/**
 * Return the ID of the PDU which corresponds to the given canId on the given channel.
 * Return GLOBAL_MAX_INVALID_DWORD_VALUE if the given canId is unknown.
 **/
dword getPduIdFromCanId(dword canId, dword channel)
{
  switch ( channel )
  {
    case 0:
      switch ( canId )
      {
        case CAN_ID_VITA_CAN0_1087: return PDU_ID_VITA_CAN0_1087;
        case CAN_ID_VITA_CAN0_MSG_NM_MYECU_OCAN_1024: return PDU_ID_VITA_CAN0_MSG_NM_MYECU_OCAN_1024;
        case CAN_ID_VITA_CAN0_MSG_RXCYCLE500_20_OCAN_272: return PDU_ID_VITA_CAN0_MSG_RXCYCLE500_20_OCAN_272;
        case CAN_ID_VITA_CAN0_MSG_RXCYCLE_E2EPROF1C_500_10_OCAN_288: return PDU_ID_VITA_CAN0_MSG_RXCYCLE_E2EPROF1C_500_10_OCAN_288;
        case CAN_ID_VITA_CAN0_MSG_TXCYCLE1000_10_OCAN_872: return PDU_ID_VITA_CAN0_MSG_TXCYCLE1000_10_OCAN_872;
        case CAN_ID_VITA_CAN0_MSG_TXCYCLE10_0_OCAN_616: return PDU_ID_VITA_CAN0_MSG_TXCYCLE10_0_OCAN_616;
        default: return GLOBAL_MAX_INVALID_DWORD_VALUE;
      }
    default: return GLOBAL_MAX_INVALID_DWORD_VALUE;
  }
}

/**
 * Return the id of the channel on which the pdu is transmitted.
 * @param pduId: id of the pdu which channel id is requested
 * @return channel id of the given pduId
 **/
dword getChannelIdFromPduId(dword pduId)
{
  switch ( pduId )
  {
    case PDU_ID_VITA_CAN0_1087:
    case PDU_ID_VITA_CAN0_MSG_NM_MYECU_OCAN_1024:
    case PDU_ID_VITA_CAN0_MSG_RXCYCLE500_20_OCAN_272:
    case PDU_ID_VITA_CAN0_MSG_RXCYCLE_E2EPROF1C_500_10_OCAN_288:
    case PDU_ID_VITA_CAN0_MSG_TXCYCLE1000_10_OCAN_872:
    case PDU_ID_VITA_CAN0_MSG_TXCYCLE10_0_OCAN_616:
      return 0;
    case PDU_ID_VITA_LIN0_FRAME_LINTR_MYECU:
    case PDU_ID_VITA_LIN0_FRAME_LINTR_REARECU:
      return 1;
    case PDU_ID_VITA_FR0_PDU_FR_STARTAPPL_BOTHECU_RX:
    case PDU_ID_VITA_FR0_PDU_NM_MYECU_FR:
    case PDU_ID_VITA_FR0_PDU_NM_REARECU_FR:
    case PDU_ID_VITA_FR0_PDU_TRANSMIT_MYECU:
      return 2;
    default: return GLOBAL_MAX_INVALID_DWORD_VALUE;
  }
}

/**
 * Return true if the given pduId is Rx Pdu, false otherwise.
 **/
dword getIsRxPdu(dword pduId)
{
  switch ( pduId )
  {
    case PDU_ID_VITA_CAN0_1087:
    case PDU_ID_VITA_CAN0_MSG_RXCYCLE500_20_OCAN_272:
    case PDU_ID_VITA_CAN0_MSG_RXCYCLE_E2EPROF1C_500_10_OCAN_288:
    case PDU_ID_VITA_FR0_PDU_FR_STARTAPPL_BOTHECU_RX:
    case PDU_ID_VITA_FR0_PDU_NM_REARECU_FR:
    case PDU_ID_VITA_LIN0_FRAME_LINTR_REARECU:
      return true;
    default:
      return false;
  }
}

/**
 * Update the PDU buffer with the given payload without outputting the PDU
 **/
void updatePdu(dword pduId, byte payload[], dword pduLength)
{
  dword i;
  switch ( pduId )
  {
    case PDU_ID_VITA_CAN0_1087:
    {
      Vita_CAN0_1087.DataLength = pduLength;
      for ( i=0; i < pduLength; ++i)
      {
        Vita_CAN0_1087.byte(i) = payload[i];
      }
      break;
    }
    case PDU_ID_VITA_CAN0_MSG_RXCYCLE500_20_OCAN_272:
    {
#if TOOL_MAJOR_VERSION >= 9

      for ( i = 0; i < pduLength && i < Vita_CAN0_msg_RxCycle500_20_oCAN.PDULength; ++i)
#else

      for ( i = 0; i < pduLength && i < Vita_CAN0_msg_RxCycle500_20_oCAN.DataLength; ++i)
#endif
      {
        Vita_CAN0_msg_RxCycle500_20_oCAN.byte(i) = payload[i];
      }
      break;
    }
    case PDU_ID_VITA_CAN0_MSG_RXCYCLE_E2EPROF1C_500_10_OCAN_288:
    {
#if TOOL_MAJOR_VERSION >= 9

      for ( i = 0; i < pduLength && i < Vita_CAN0_msg_RxCycle_E2eProf1C_500_10_oCAN.PDULength; ++i)
#else

      for ( i = 0; i < pduLength && i < Vita_CAN0_msg_RxCycle_E2eProf1C_500_10_oCAN.DataLength; ++i)
#endif
      {
        Vita_CAN0_msg_RxCycle_E2eProf1C_500_10_oCAN.byte(i) = payload[i];
      }
      break;
    }
    case PDU_ID_VITA_FR0_PDU_FR_STARTAPPL_BOTHECU_RX:
    {
      for ( i = 0; i < pduLength && i < Vita_FR0_PDU_Fr_StartAppl_BothECU_RX.FR_PDULength; ++i)
      {
        Vita_FR0_PDU_Fr_StartAppl_BothECU_RX.byte(i) = payload[i];
      }
      break;
    }
    case PDU_ID_VITA_FR0_PDU_NM_REARECU_FR:
    {
      for ( i = 0; i < pduLength && i < Vita_FR0_PDU_nm_RearECU_Fr.FR_PDULength; ++i)
      {
        Vita_FR0_PDU_nm_RearECU_Fr.byte(i) = payload[i];
      }
      break;
    }
    case PDU_ID_VITA_LIN0_FRAME_LINTR_REARECU:
    {
      for ( i = 0; i < pduLength && i < Vita_LIN0_Frame_LinTr_RearECU.dlc; ++i)
      {
        Vita_LIN0_Frame_LinTr_RearECU.byte(i) = payload[i];
      }
      break;
    }
    default:
      writeLineEx(startApplWriteWindow, 3, "Unknown pdu id is used by updatePdu, id: %d", pduId);
  }
}

/**
 * Update the signal in the PDU buffer and output the corresponding PDU
 **/
void sendSignal(dword signalId, int64 value)
{
  switch ( signalId )
  {
    case SIGNAL_ID_VITA_CAN0_MSG_RXCYCLE500_20_OCAN_SIGNAL_RX20BIT_CYCLIC_OMSG_RXCYCLE500_20_OCAN :
      updateSignal(signalId, value);
      outputPdu( getPduIdFromSignalId(signalId) );
      break;
    case SIGNAL_ID_VITA_CAN0_MSG_RXCYCLE_E2EPROF1C_500_10_OCAN_SIGRX_PROF1C_SIG32BIT_OMSG_RXCYCLE_E2EPROF1C_500_10_OCAN :
      updateSignal(signalId, value);
      outputPdu( getPduIdFromSignalId(signalId) );
      break;
    case SIGNAL_ID_VITA_FR0_PDU_FR_STARTAPPL_BOTHECU_RX_STARTAPPL_FR_BOTHECU_RX_CTRL_OPDU_FR_STARTAPPL_BOTHECU_RX :
      updateSignal(signalId, value);
      outputPdu( getPduIdFromSignalId(signalId) );
      break;
    case SIGNAL_ID_VITA_LIN0_FRAME_LINTR_REARECU_SIG_STARTAPPL_LINDATA_RX :
      updateSignal(signalId, value);
      outputPdu( getPduIdFromSignalId(signalId) );
      break;
    default:
      writeLineEx(startApplWriteWindow, 3, "Unknown signal id is used, id: %d", signalId);
  }
}

/**
 * Update the signal in the PDU buffer without transmission triggering
 **/
void updateSignal(dword signalId, int64 value)
{
  switch ( signalId )
  {
    case SIGNAL_ID_VITA_CAN0_MSG_RXCYCLE500_20_OCAN_SIGNAL_RX20BIT_CYCLIC_OMSG_RXCYCLE500_20_OCAN :
      Vita_CAN0_msg_RxCycle500_20_oCAN.Signal_Rx20bit_Cyclic_omsg_RxCycle500_20_oCAN = value;
      break;
    case SIGNAL_ID_VITA_CAN0_MSG_RXCYCLE_E2EPROF1C_500_10_OCAN_SIGRX_PROF1C_SIG32BIT_OMSG_RXCYCLE_E2EPROF1C_500_10_OCAN :
      Vita_CAN0_msg_RxCycle_E2eProf1C_500_10_oCAN.SigRx_Prof1C_Sig32Bit_omsg_RxCycle_E2eProf1C_500_10_oCAN = value;
      break;
    case SIGNAL_ID_VITA_FR0_PDU_FR_STARTAPPL_BOTHECU_RX_STARTAPPL_FR_BOTHECU_RX_CTRL_OPDU_FR_STARTAPPL_BOTHECU_RX :
      Vita_FR0_PDU_Fr_StartAppl_BothECU_RX.StartAppl_Fr_BothECU_RX_Ctrl_oPDU_Fr_StartAppl_BothECU_RX = value;
      break;
    case SIGNAL_ID_VITA_LIN0_FRAME_LINTR_REARECU_SIG_STARTAPPL_LINDATA_RX :
      Vita_LIN0_Frame_LinTr_RearECU.Sig_StartAppl_LinData_Rx = value;
      break;
    default:
      writeLineEx(startApplWriteWindow, 3, "Unknown signal id is used, id: %d", signalId);
  }
}

/**
 * Update the signal with type array in the PDU buffer without transmission triggering
 **/
void updateSignal(dword signalId, byte signalPayLoad[], dword length)
{
  dword i;
  switch ( signalId )
  {
    default:
      writeLineEx(startApplWriteWindow, 3, "Unknown signal id is used, id: %d", signalId);
  }
}

/**
 * Update the update bit signal corresponding to the given by signalId signal with the given value
 **/
void updateSignalUpdateBit(dword signalId, byte value)
{
  switch ( signalId )
  {
    case SIGNAL_ID_VITA_CAN0_MSG_RXCYCLE500_20_OCAN_SIGNAL_RX20BIT_CYCLIC_OMSG_RXCYCLE500_20_OCAN : /* no update bit signal */ break;
    case SIGNAL_ID_VITA_CAN0_MSG_RXCYCLE_E2EPROF1C_500_10_OCAN_SIGRX_PROF1C_SIG32BIT_OMSG_RXCYCLE_E2EPROF1C_500_10_OCAN : /* no update bit signal */ break;
    case SIGNAL_ID_VITA_FR0_PDU_FR_STARTAPPL_BOTHECU_RX_STARTAPPL_FR_BOTHECU_RX_CTRL_OPDU_FR_STARTAPPL_BOTHECU_RX : /* no update bit signal */ break;
    case SIGNAL_ID_VITA_LIN0_FRAME_LINTR_REARECU_SIG_STARTAPPL_LINDATA_RX : /* no update bit signal */ break;
    default:
      writeLineEx(startApplWriteWindow, 3, "Unknown signal id is used, id: %d", signalId);
  }
}

float getSignalTransmissionTimeStamp(dword signalId)
{
  return signalTransmissionTimeStamp[signalId];
}
void setSignalTransmissionTimeStamp(dword signalId, float value)
{
  signalTransmissionTimeStamp[signalId] = value;
}
void initSignalTransmissionTimeStamps()
{
  dword i;
  for (i = 0; i < MAX_SIGNAL_IDS_COUNT; ++i)
  {
    setSignalTransmissionTimeStamp(i,  0.0);
  }
}



/**
 * Handle CAN messages occurrence on the channel Vita_CAN0
 **/
on message Vita_CAN0.*
{
  dword pduId;
  word i;
  CAL_OnFrameIndication( 0 );
  pduId = getPduIdFromCanId( this.id, 0 );
  if ( pduId != PDU_ID_VITA_CAN0_MSG_RXCYCLE_E2EPROF1C_500_10_OCAN_288 && pduId != PDU_ID_VITA_CAN0_MSG_RXCYCLE500_20_OCAN_272 )
  {
    CAL_OnCanMessageIndication( 0 );
  }
  if ( pduId == GLOBAL_MAX_INVALID_DWORD_VALUE )
  {
    return;
  }
  if ( getIsRxPdu( pduId ) )
  {
    onRxPduUpdate( pduId );
  }
  else
  {
    for (i = 0; i < this.DataLength; ++i)
    {
      pduPayload[i] = this.byte(i);
    }
    onTxPduUpdate( pduId, pduPayload, this.DataLength );
  }
}
on linMessage Vita_LIN0.*
{
  if (this.id == 0x3C && this.dlc == 8) /* MasterReq('Go-to-Sleep') */
  {
    if (this.byte(0) == 0x00 && this.byte(1) == 0xFF && this.byte(2) == 0xFF && this.byte(3) == 0xFF &&
        this.byte(4) == 0xFF && this.byte(5) == 0xFF && this.byte(6) == 0xFF && this.byte(7) == 0xFF)
    {
      return;
    }
  }
  CAL_OnFrameIndication(1);
  CAL_OnLinMessageIndication(1);
}
on frPDU Vita_FR0.*
{
  CAL_OnFrameIndication(2);
  CAL_OnFrPduIndication(2);
}

on frPDU Vita_FR0::PDU_Fr_StartAppl_BothECU_RX
{
  CAL_OnFrameIndication( 2 );
  CAL_OnFrPduIndication( 2 );
  onRxPduUpdate( PDU_ID_VITA_FR0_PDU_FR_STARTAPPL_BOTHECU_RX );
}

on frPDU Vita_FR0::PDU_Transmit_MyECU
{
  CAL_OnFrameIndication( 2 );
  CAL_OnFrPduIndication( 2 );
  onTxPduUpdate( PDU_ID_VITA_FR0_PDU_TRANSMIT_MYECU, this.fr_Payload, this.fr_PayloadLength );
}

on frPDU Vita_FR0::PDU_nm_MyECU_Fr
{
  CAL_OnFrameIndication( 2 );
  CAL_OnFrPduIndication( 2 );
  onTxPduUpdate( PDU_ID_VITA_FR0_PDU_NM_MYECU_FR, this.fr_Payload, this.fr_PayloadLength );
}

on frPDU Vita_FR0::PDU_nm_RearECU_Fr
{
  CAL_OnFrameIndication( 2 );
  CAL_OnFrPduIndication( 2 );
  onRxPduUpdate( PDU_ID_VITA_FR0_PDU_NM_REARECU_FR );
}

on linMessage Vita_LIN0::Frame_LinTr_MyECU
{
  word i;
  for (i = 0; i < this.dlc; ++i)
  {
    pduPayload[i] = this.byte(i);
  }
  CAL_OnFrameIndication( 1 );
  CAL_OnLinMessageIndication( 1 );
  onTxPduUpdate( PDU_ID_VITA_LIN0_FRAME_LINTR_MYECU, pduPayload, this.dlc );
}

on linMessage Vita_LIN0::Frame_LinTr_RearECU
{
  CAL_OnFrameIndication( 1 );
  CAL_OnLinMessageIndication( 1 );
  onRxPduUpdate( PDU_ID_VITA_LIN0_FRAME_LINTR_REARECU );
}

on signal_update Vita_CAN0::msg_RxCycle500_20_oCAN::Signal_Rx20bit_Cyclic_omsg_RxCycle500_20_oCAN
{
  onRxSignalUpdate( SIGNAL_ID_VITA_CAN0_MSG_RXCYCLE500_20_OCAN_SIGNAL_RX20BIT_CYCLIC_OMSG_RXCYCLE500_20_OCAN, this.raw );
}

on signal_update Vita_CAN0::msg_RxCycle_E2eProf1C_500_10_oCAN::SigRx_Prof1C_Sig32Bit_omsg_RxCycle_E2eProf1C_500_10_oCAN
{
  onRxSignalUpdate( SIGNAL_ID_VITA_CAN0_MSG_RXCYCLE_E2EPROF1C_500_10_OCAN_SIGRX_PROF1C_SIG32BIT_OMSG_RXCYCLE_E2EPROF1C_500_10_OCAN, this.raw );
}

on signal_update Vita_CAN0::msg_TxCycle1000_10_oCAN::Signal_Tx10bit_Cyclic_omsg_TxCycle1000_10_oCAN
{
  onTxSignalUpdate( SIGNAL_ID_VITA_CAN0_MSG_TXCYCLE1000_10_OCAN_SIGNAL_TX10BIT_CYCLIC_OMSG_TXCYCLE1000_10_OCAN, this.raw );
}

on signal_update Vita_CAN0::msg_TxCycle10_0_oCAN::Signal_Tx24bit_Cyclic_omsg_TxCycle10_0_oCAN
{
  onTxSignalUpdate( SIGNAL_ID_VITA_CAN0_MSG_TXCYCLE10_0_OCAN_SIGNAL_TX24BIT_CYCLIC_OMSG_TXCYCLE10_0_OCAN, this.raw );
}

on signal_update Vita_FR0::PDU_Fr_StartAppl_BothECU_RX::StartAppl_Fr_BothECU_RX_Ctrl_oPDU_Fr_StartAppl_BothECU_RX
{
  onRxSignalUpdate( SIGNAL_ID_VITA_FR0_PDU_FR_STARTAPPL_BOTHECU_RX_STARTAPPL_FR_BOTHECU_RX_CTRL_OPDU_FR_STARTAPPL_BOTHECU_RX, this.raw );
}

on signal_update Vita_FR0::PDU_Transmit_MyECU::Signal_SomeTxSignal_oPDU_Transmit_MyECU
{
  onTxSignalUpdate( SIGNAL_ID_VITA_FR0_PDU_TRANSMIT_MYECU_SIGNAL_SOMETXSIGNAL_OPDU_TRANSMIT_MYECU, this.raw );
}

on signal_update Vita_LIN0::Frame_LinTr_MyECU::Sig_StartAppl_LinData_Tx
{
  onTxSignalUpdate( SIGNAL_ID_VITA_LIN0_FRAME_LINTR_MYECU_SIG_STARTAPPL_LINDATA_TX, this.raw );
}

on signal_update Vita_LIN0::Frame_LinTr_RearECU::Sig_StartAppl_LinData_Rx
{
  onRxSignalUpdate( SIGNAL_ID_VITA_LIN0_FRAME_LINTR_REARECU_SIG_STARTAPPL_LINDATA_RX, this.raw );
}


/**
 * This function is called when a pdu transmitted by the ECU changes
 **/
void onTxPduUpdate(dword pduId, byte payload[], dword length)
{
  onTxPayloadUpdate(pduId, payload, length);
}

/**
 * This function is called when a pdu transmitted by the Tester changes
 **/
void onRxPduUpdate(dword pduId)
{
  onRxPayloadUpdate(pduId);
}

/**
 * This function is called when a signal of type array transmitted by the ECU changes
 **/
void onTxSignalUpdate(dword signalId, byte signalPayload[], dword length)
{
  setSignalTransmissionTimeStamp(signalId, getCurrentTimeInSeconds());
  StartApplication_OnTxSignalUpdate(signalId, signalPayload, length);
}

/**
 * This function is called when a signal of type array transmitted by the Tester changes
 **/
void onRxSignalUpdate(dword signalId, byte signalPayload[], dword length)
{
  setSignalTransmissionTimeStamp(signalId, getCurrentTimeInSeconds());
  StartApplication_OnRxSignalUpdate(signalId, signalPayload, length);
}

/**
 * This function is called when a signal transmitted by the ECU changes
 **/
void onTxSignalUpdate(dword signalId, int64 value)
{
  setSignalTransmissionTimeStamp(signalId, getCurrentTimeInSeconds());
  StartApplication_OnTxSignalUpdate(signalId, value);
}

/**
 * This function is called when a signal transmitted by the Tester changes
 **/
void onRxSignalUpdate(dword signalId, int64 value)
{
  setSignalTransmissionTimeStamp(signalId, getCurrentTimeInSeconds());
  StartApplication_OnRxSignalUpdate(signalId, value);
}

/**
 * This function is called when the payload of a pdu transmitted by the ECU changes
 **/
void onTxPayloadUpdate(dword pduId, byte payload[], dword length)
{
}

/**
 * This function is called when the payload of a pdu transmitted by the Tester changes
 **/
void onRxPayloadUpdate(dword pduId)
{
}




/**
 * Notify about the CAN message occurrence on the CAN channel
 * @param channel: channel ID
 **/
void CAL_OnCanMessageIndication(dword channel)
{
  switch( channel )
  {
    case 0 :
      break;
    default: break;
  }
}

/**
 * Notify about the FR symbol occurrence on the given FR channel
 * @param channel: FlexRay channel ID
 **/
void CAL_OnFrSymbol(dword channel)
{
}

/**
 * Notify about the FlexRay pdu occurrence on the channel
 * @param channel: channel ID
 **/
void CAL_OnFrPduIndication(dword channel)
{
  switch( channel )
  {
    case 2 :
      break;
    default: break;
  }
}

/**
 * Notify about the LIN message occurrence on the LIN channel
 * @param channel: channel ID
 **/
void CAL_OnLinMessageIndication(dword channel)
{
  switch( channel )
  {
    case 1 :
      break;
    default: break;
  }
}

/**
 * Notify about the frame occurrence on the channel
 * @param channel: channel ID
 **/
void CAL_OnFrameIndication(dword channel)
{
  msgCounter[channel]++;
}

/**
 * Notify about the poc state event on the given FR channel
 * @param channel: FlexRay channel ID
 * @param pocState: new poc state value
 **/
void CAL_OnFrPocState(dword channel, long pocState)
{
  if (isSimulated())
  {
    if (2 /*NORMAL ACTIVE*/ == pocState)
    {
      CAL_SetChannelOnlineState(channel, true);
    }
    else
    {
      CAL_SetChannelOnlineState(channel, false);
    }
  }
}

/**
 * Notify about the sleep mode on the given LIN channel
 * @param channel: LIN channel ID
 **/
void CAL_OnLinSleepModeEvent(dword channel)
{
}

/**
 * Notify about the start cycle on the given FR channel
 * @param channel: FlexRay channel ID
 * @param cycle: new cycle value
 **/
void CAL_OnFrStartCycle(dword channel, word cycle)
{
}

/**
 * Notify about the wakeup frame on the given LIN channel
 * @param channel: LIN channel ID
 **/
void CAL_OnLinWakeupFrame(dword channel)
{
}



/**
 * A different use case was selected.
 **/
on sysvar StartApplication::UseCaseActivator
{
  int newUseCase;
  newUseCase = @sysvar::StartApplication::UseCaseActivator;

  if(newUseCase != sysvar::StartApplication::UseCaseActivator::Invalid)
  {
    deactivatePanel(activeUseCase);
    activatePanel(newUseCase);

    if (newUseCase == sysvar::StartApplication::UseCaseActivator::Com_RxTx || newUseCase == sysvar::StartApplication::UseCaseActivator::Com_TxOnly)
    {
      timerForSensorValueChange.setCyclic(150);
      if (@sysvar::StartApplication::ComSendCtrl < 2 )
      {
        autoSensorValueChange = 1;
      }
      if (@sysvar::StartApplication::ComInput > 254 || @sysvar::StartApplication::ComInput < 0)
      {
        @sysvar::StartApplication::ComInput = 0;
      }
      @StartApplication::ComSignalPairSelector = @StartApplication::ComSignalPairSelector;
    }
    startRxCmdSignalConfTimer(1);
    activeUseCase = newUseCase;
    writeRxDataSig(0xFF);
    writeRxCtrlSig(activeUseCase);
  }
}

/**
 * The RxCtrl signal was transmitted on the bus, update the retransmission counter
 **/
void StartApplication_OnRxCtrl (int data)
{
  if (data != 0xFF && RxCtrlConfCounter > 0)
  {
    RxCtrlConfCounter--;
  }
}

/**
 * The RxData signal was transmitted on the bus, update the retransmission counter
 **/
void StartApplication_OnRxData (int data)
{
  if (data != 0xFF && RxCtrlConfCounter == 0 && RxDataConfCounter > 0)
  {
    RxDataConfCounter--;
  }
}

/**
 * The TxData signal was transmitted by the ECU, update the system variable of the active use case
 **/
void StartApplication_OnTxData (int data)
{
  switch(activeUseCase)
  {
    case 1:
      memUc_OnTxData(data);
      break;
    case 2:
      @sysvar::StartApplication::DiagCounterValueFromTxData = data;
      break;
  }
}

/**
 * The TxCtrl signal was transmitted by the ECU, update the system variable of the active use case
 **/
void StartApplication_OnTxCtrl(int data)
{
  switch(activeUseCase)
  {
    case 1:
      memUc_OnTxCtrl(data);
      break;
  }
}

void setRxCtrlConfCounter()
{
  if (LastRxCtrlSignalValue != 0xFF && isTimerActive(RxCmdSignalConfTimer) == 1)
  {
    RxCtrlConfCounter += RXCMD_CONF_COUNTER_INIT_VALUE;
  }
}

void setRxDataConfCounter()
{
  if (LastRxDataSignalValue != 0xFF && isTimerActive(RxCmdSignalConfTimer) == 1)
  {
    RxDataConfCounter += RXCMD_CONF_COUNTER_INIT_VALUE;
  }
}

void startRxCmdSignalConfTimer(byte isRxCtrlSignal)
{
  if (isRxCtrlSignal)
  {
    RxCtrlConfCounter = 0;
  }
  RxDataConfCounter = 0;
  if (isTimerActive(RxCmdSignalConfTimer) == 0)
  {
    setTimer(RxCmdSignalConfTimer, RxCmdSignalConfTimeout);
  }
}

/************************/
/*** MEM ***/
/************************/

on sysvar StartApplication::MemNvBlockSelector
{
  memUc_SendRxData(0, 0);
}

on sysvar StartApplication::MemNvStore
{
  if (sysGetVariableInt(this) == 1)
  {
    memUc_SendRxData(@sysvar::StartApplication::MemNvStoreValue, 1);
  }
}

on sysvar StartApplication::MemActivator
{
  if (sysGetVariableInt(this) == 1)
  {
    @sysvar::StartApplication::UseCaseActivator = sysvar::StartApplication::UseCaseActivator::Mem;
  }
}

on sysvar StartApplication::MemNvPendingExtended
{
    if(@this == MEM_STATE_WRITE_PENDING || @this == MEM_STATE_READ_PENDING)
    {
      @sysvar::StartApplication::MemNvPending = 1;
      writeRxDataSig(0xFF);
    }
    else
    {
      @sysvar::StartApplication::MemNvPending = 0;
    }
}

/**
 * Send the data value and block via RxData signal to write and read the given block.
 * Following data is sent:
 *
 * Bit       0     1     2     3     4     5     6     7
 *        +-----+-----+-----+-----+-----+-----+-----+-----+
 * RxData | DataValue                   |Write|Block|  -  |
 *        +-----+-----+-----+-----+-----+-----+-----+-----+
 *
 * DataValue: The value which will be written to the active block.
 * Write:     If set the data value will be written to the active block.
 * Block:     Sets the active block.
 *
 *
 * @param dataValue: The value which should be written to the active block.
 * @param write: 0: only change block (value is ignored but must be a valid value, e.g. 0), 1: write the data value.
 **/
void memUc_SendRxData(byte dataValue, byte write)
{
  byte value;
  if(dataValue >= 0 && dataValue <= 0x1F)
  {
    value = dataValue;
    value |= (write & 0x01) << 5;
    value |= (@sysvar::StartApplication::MemNvBlockSelector & 0x01) << 6;
    if(write == 1)
    {
      writeLineEx(startApplWriteWindow, 4, "MEM use case: Set active NvM Block to %d and write value %d", @sysvar::StartApplication::MemNvBlockSelector, dataValue);
    }
    else
    {
      writeLineEx(startApplWriteWindow, 4, "MEM use case: Set active NvM Block to %d", @sysvar::StartApplication::MemNvBlockSelector);
    }
    writeRxDataSig(value);
  }
  else
  {
    writeLineEx(startApplWriteWindow, 4, "MEM use case: Value %d is outside the range 0 ... 31.", @sysvar::StartApplication::MemNvStoreValue);
  }
}

/**
 * Called after TxCtrl is received with the current block state.
 * Following data is received:
 *
 * Bit       0     1     2     3     4     5     6     7
 *        +-----+-----+-----+-----+-----+-----+-----+-----+
 * TxCtrl | BlockState      | -                     |Block|
 *        +-----+-----+-----+-----+-----+-----+-----+-----+
 *
 * BlockState: The block state of the currently active block.
 * Block:      The currently active block.
 **/
void memUc_OnTxCtrl(int data)
{
  byte blockId;
  byte pendingState;
  blockId = (data >> 7) & 0x01;
  // only update the data if the received data is for the currently active block
  if(@sysvar::StartApplication::MemNvBlockSelector == blockId)
  {
    pendingState = data & 0x7;
    if(pendingState <= 6)
    {
      @sysvar::StartApplication::MemNvPendingExtended = pendingState;
    }
  }
}

/**
 * Called after TxData is received with the value which was read from the currently active block.
 * Following data is received:
 *
 * Bit       0     1     2     3     4     5     6     7
 *        +-----+-----+-----+-----+-----+-----+-----+-----+
 * TxData | DataValue                   | -         |Block|
 *        +-----+-----+-----+-----+-----+-----+-----+-----+
 *
 * DataValue: The value which was read from the currently active block.
 * Block:     The currently active block.
 **/
void memUc_OnTxData(int data)
{
  byte blockId;
  blockId = (data >> 7) & 0x01;
  // only update the data if the received data is for the currently active block
  if(@sysvar::StartApplication::MemNvBlockSelector == blockId)
  {
    @sysvar::StartApplication::MemNvReadCurrValue = data & 0x1F;
  }
}

/**
 * Enable/disable the controls of the panel for the DIAG use case
 **/
void setDiagPanelStatus(byte isEnabled)
{
  enableControl("StartApplication.Diagnostic", "diagSetEventFailedButton", isEnabled);
  enableControl("StartApplication.Diagnostic", "diagSetEventPassedButton", isEnabled);
  enableControl("StartApplication.Diagnostic", "diagWDBIButton", isEnabled);
  enableControl("StartApplication.Diagnostic", "diagRDBIButton", isEnabled);
  enableControl("StartApplication.Diagnostic", "diagCDTCIButton", isEnabled);
  enableControl("StartApplication.Diagnostic", "diagRDTCSSBDTCButton", isEnabled);
  enableControl("StartApplication.Diagnostic", "diagChannelSelector", isEnabled);
  if(!isEnabled)
  {
    putValueToControl("StartApplication.Diagnostic","BusLog_Time", "");
    putValueToControl("StartApplication.Diagnostic","BusLog_Type", "");
    putValueToControl("StartApplication.Diagnostic","BusLog_Service", "");
    putValueToControl("StartApplication.Diagnostic","BusLog_Status", "");
  }
}

on sysvar StartApplication::DiagActivator
{
  if (sysGetVariableInt(this) == 1)
  {
    @sysvar::StartApplication::UseCaseActivator = sysvar::StartApplication::UseCaseActivator::Diag;
  }
}

on sysvar StartApplication::DiagSetEventStatusFailed
{
  if (sysGetVariableInt(this) == 1)
  {
    writeLineEx(startApplWriteWindow, 4, "DiagUsecase: Set DemEvent to Failed and increment event counter");
    if(diagRxDataCounter > 63)
    {
      diagRxDataCounter = 0; //reset counter when reaching its max value (6 bits)
    }
    //send out rxData counter (bit 2-7) and event status failed command (0x1 in bit 0 and 1) in RxData signal
    writeRxDataSig( (diagRxDataCounter << 2) | 1);
    diagRxDataCounter++;
  }
}

on sysvar StartApplication::DiagSetEventStatusPassed
{
  if (sysGetVariableInt(this) == 1)
  {
    writeLineEx(startApplWriteWindow, 4, "DiagUsecase: Set DemEvent to Passed and increment event counter");
    if(diagRxDataCounter > 63)
    {
      diagRxDataCounter = 0; //reset counter when reaching its max value (6 bits)
    }
    //send out rxData counter (bit 2-7) and event status passed command (0x0 in bit 0 and 1) in RxData signal
    writeRxDataSig(diagRxDataCounter << 2);
    diagRxDataCounter++;
  }
}

on sysvar StartApplication::DiagResetCounterWithRxData
{
  if (sysGetVariableInt(this) == 1)
  {
    writeLineEx(startApplWriteWindow, 4, "DiagUsecase: Send RxData to reset event counter to 0");
    if(diagRxDataCounter > 63)
    {
      diagRxDataCounter = 0; //reset counter when reaching its max value (6 bits)
    }
    //send out event counter (bit 2-7) and reset counter command (0x2 in bit 0 and 1) in RxData signal
    writeRxDataSig(diagRxDataCounter << 2 | 2);
    diagRxDataCounter++;
  }
}

on sysvar StartApplication::DiagGetDTCSnapshot
{
  BYTE data[6] = {dcmServiceIdRDTCI, dcmRDTCIsubFunctionRDTCSSBDTC, dcmDTCHighByte, dcmDTCMiddleByte, dcmDTCLowByte, dcmDTCSnapshotRecordNumber};
  if (@this == 1)
  {
    if (diagUdsServiceConfigData[@sysvar::StartApplication::DiagChannel].rdtcssbdtcSessionLevel != 0) {
      diagUdsSendDscRequest(@sysvar::StartApplication::DiagChannel, diagUdsServiceConfigData[@sysvar::StartApplication::DiagChannel].rdtcssbdtcSessionLevel);
    }
    writeLineEx(startApplWriteWindow,4,"DiagUsecase: Send RDTCSSBDTC to get DTC Snapshot");
    sendUDSRequest(@sysvar::StartApplication::DiagChannel, data, elCount(data));
  }
}

on sysvar StartApplication::DiagClearDTC
{
  BYTE data[4] = {dcmServiceIdCDTCI, dcmDTCHighByte, dcmDTCMiddleByte, dcmDTCLowByte};
  if (@this == 1)
  {
    if (diagUdsServiceConfigData[@sysvar::StartApplication::DiagChannel].cdtciSessionLevel != 0) {
      diagUdsSendDscRequest(@sysvar::StartApplication::DiagChannel, diagUdsServiceConfigData[@sysvar::StartApplication::DiagChannel].cdtciSessionLevel);
    }
    writeLineEx(startApplWriteWindow,4,"DiagUsecase: Send CDTCI to clear DTC");
    sendUDSRequest(@sysvar::StartApplication::DiagChannel, data, elCount(data));
  }
}

on sysvar StartApplication::DiagActivateDefaultSession
{
  if (@this == 1)
  {
    writeLineEx(startApplWriteWindow,4,"DiagUsecase: Request Default Session");
    diagUdsSendDscRequest(@sysvar::StartApplication::DiagChannel, 0x01);
  }
}

on sysvar StartApplication::DiagGetCounter
{
  BYTE data[3] = {dcmServiceIdRDBI, dcmDidIdHigh, dcmDidIdLow};
  if (@this == 1)
  {
    if (diagUdsServiceConfigData[@sysvar::StartApplication::DiagChannel].rdbiSessionLevel != 0) {
      diagUdsSendDscRequest(@sysvar::StartApplication::DiagChannel, diagUdsServiceConfigData[@sysvar::StartApplication::DiagChannel].rdbiSessionLevel);
    }
    writeLineEx(startApplWriteWindow,4,"DiagUsecase: Send RDBI to query current value of event counter");
    sendUDSRequest(@sysvar::StartApplication::DiagChannel, data, elCount(data));
  }
}

on sysvar StartApplication::DiagResetCounter
{
  if (@this == 1)
  {
    writeLineEx(startApplWriteWindow,4,"DiagUsecase: Send WDBI to reset event counter to 0");
    @sysvar::StartApplication::DiagSetCounter = 0;
  }
}

on sysvar_update StartApplication::DiagSetCounter
{
  BYTE data[5] = {dcmServiceIdWDBI, dcmDidIdHigh, dcmDidIdLow, 0x00, 0x00};
  data[3] = @sysvar::StartApplication::DiagSetCounter >> 8;
  data[4] = @sysvar::StartApplication::DiagSetCounter & 0xFF;

  if (diagUdsServiceConfigData[@sysvar::StartApplication::DiagChannel].wdbiSessionLevel != 0) {
    diagUdsSendDscRequest(@sysvar::StartApplication::DiagChannel, diagUdsServiceConfigData[@sysvar::StartApplication::DiagChannel].wdbiSessionLevel);
  }
  sendUDSRequest(@sysvar::StartApplication::DiagChannel, data, elCount(data));
}

/**
 * Called if the currently active channel of the DIAG use case is changed.
 * Sends a UDS session request for the default channel on the previously active channel.
 */
on sysvar StartApplication::DiagChannel
{
  int oldChannel = 0;
  diagUdsRequestQueueClear();
  writeLineEx(startApplWriteWindow,4,"DiagUsecase: Channel was changed");
  diagUdsSendDscRequest(oldChannel, 0x01);
  oldChannel = @this;
}

/**
 * Send a DiagnosticSessionControl UDS request.
 * @param diagChannel: The channel over which the request should be sent
 * @param sessionLevel: The session to request
 */
void diagUdsSendDscRequest(int diagChannel, byte sessionLevel)
{
  BYTE data[2] = {dcmServiceIdDSC, 0x00};
  data[1] = sessionLevel;
  writeLineEx(startApplWriteWindow,4,"DiagUsecase: Send DSC Request");
  sendUDSRequest(diagChannel, data, elCount(data));
}

/**
 * Check if a diagnostic request is currently in progress, i.e. the request was sent but the response was not received yet.
 */
int diagUdsRequestQueueIsRequestInProgress()
{
  return diagUdsRequestQueue.requestInProgress != 0;
}

/**
 * Set that a diagnostic request is now in progress, i.e. the request was sent.
 */
void diagUdsRequestQueueSetInProgress()
{
  diagUdsRequestQueue.requestInProgress = 1;
  if (diagUdsRequestQueueIsEmpty())
  {
    @StartApplication::DiagRequestState = DIAG_QUEUE_EMPTY_REQUEST_ACTIVE;
  }
  else
  {
    @StartApplication::DiagRequestState = DIAG_QUEUE_NOT_EMPTY_REQUEST_ACTIVE;
  }
}

/**
 * Set that a diagnostic request is currently not in progress, i.e. the response for the previously active request was received.
 */
void  diagUdsRequestQueueSetNotInProgress()
{
  diagUdsRequestQueue.requestInProgress = 0;
  if (diagUdsRequestQueueIsEmpty())
  {
    @StartApplication::DiagRequestState = DIAG_QUEUE_EMPTY_REQUEST_NOT_ACTIVE;
  }
  else
  {
    @StartApplication::DiagRequestState = DIAG_QUEUE_NOT_EMPTY_REQUEST_NOT_ACTIVE;
  }
}

/**
 * Reset the diagnostic request queue. This clears all entries and the current request state.
 */
void diagUdsRequestQueueReset()
{
  diagUdsRequestQueue.head = 0;
  diagUdsRequestQueue.size = 0;
  diagUdsRequestQueue.requestInProgress = 0;
  @StartApplication::DiagRequestState = DIAG_QUEUE_EMPTY_REQUEST_NOT_ACTIVE;
}

/**
 * Clear the diagnostic request queue. This clears all entries and keeps current request state.
 */
void diagUdsRequestQueueClear()
{
  diagUdsRequestQueue.head = 0;
  diagUdsRequestQueue.size = 0;
  if (diagUdsRequestQueueIsRequestInProgress())
  {
    @StartApplication::DiagRequestState = DIAG_QUEUE_EMPTY_REQUEST_ACTIVE;
  }
  else
  {
    @StartApplication::DiagRequestState = DIAG_QUEUE_EMPTY_REQUEST_NOT_ACTIVE;
  }
}

/**
 * Check if the diagnostic request queue is empty.
 */
int diagUdsRequestQueueIsEmpty()
{
  return diagUdsRequestQueue.size == 0;
}

/**
 * Check if the diagnostic request queue is full.
 */
int diagUdsRequestQueueIsFull()
{
  return diagUdsRequestQueue.size == DIAG_UDS_REQUEST_QUEUE_LENGTH;
}

/**
 * Add an entry to the diagnostic request queue (FIFO). In case the queue is full the entry is ignored.
 * @param entry: The UDS request to add to the queue
 */
void diagUdsRequestEnqueue(struct DiagUdsRequestQueueEntry entry)
{
  if (!diagUdsRequestQueueIsFull())
  {
    diagUdsRequestQueue.head++;
    if(diagUdsRequestQueue.head >= DIAG_UDS_REQUEST_QUEUE_LENGTH)
    {
      diagUdsRequestQueue.head = 0;
    }
    diagUdsRequestQueue.size++;
    memcpy(diagUdsRequestQueue.queue[diagUdsRequestQueue.head], entry);
    if (diagUdsRequestQueueIsRequestInProgress())
    {
      @StartApplication::DiagRequestState = DIAG_QUEUE_NOT_EMPTY_REQUEST_ACTIVE;
    }
    else
    {
      @StartApplication::DiagRequestState = DIAG_QUEUE_NOT_EMPTY_REQUEST_NOT_ACTIVE;
    }
  }
  else
  {
    logError("DiagUseCase: DiagUdsRequestQueue is full, UDS request is lost");
  }
}

/**
 * Get the next entry of the diagnostic request queue (FIFO). In case the queue is empty the out parameter is not changed.
 * @param entry: Output parameter which contains the next entry
 */
void diagUdsRequestDequeue(struct DiagUdsRequestQueueEntry entry)
{
  int tailPosition;
  if (!diagUdsRequestQueueIsEmpty())
  {
    tailPosition = (diagUdsRequestQueue.head + DIAG_UDS_REQUEST_QUEUE_LENGTH - diagUdsRequestQueue.size + 1) % DIAG_UDS_REQUEST_QUEUE_LENGTH;
    memcpy(entry, diagUdsRequestQueue.queue[tailPosition]);
    diagUdsRequestQueue.size--;
    if (diagUdsRequestQueueIsEmpty())
    {
      if (diagUdsRequestQueueIsRequestInProgress())
      {
        @StartApplication::DiagRequestState = DIAG_QUEUE_EMPTY_REQUEST_ACTIVE;
      }
      else
      {
        @StartApplication::DiagRequestState = DIAG_QUEUE_EMPTY_REQUEST_NOT_ACTIVE;
      }
    }
    else
    {
      if (diagUdsRequestQueueIsRequestInProgress())
      {
        @StartApplication::DiagRequestState = DIAG_QUEUE_NOT_EMPTY_REQUEST_ACTIVE;
      }
      else
      {
        @StartApplication::DiagRequestState = DIAG_QUEUE_NOT_EMPTY_REQUEST_NOT_ACTIVE;
      }
    }
  }
}

/**
 * Called in case there is no response for a request after a given timeout.
 * Clears the inProgress flag of the queue and continues with diagnostic request queue execution.
 */
on timer diagUdsRequestTimeoutTimer
{
  logError("DiagUseCase: UdsResponse timeout has occurred, timeout [s]", DIAG_UDS_REQUEST_TIMEOUT/1000);
  diagUdsRequestQueueSetNotInProgress();
  diagHandleUdsRequest();
}

/**
 * This timer is started if the current request which shall be sent is on a different channel than the previous one.
 * Triggers the diagHandleUdsRequest method to send the new request after the given wait time.
 */
on timer diagUdsChannelChangeTimer
{
  diagUdsChannelChangeTimerState = DIAG_CHANNEL_CHANGE_TIMER_EXPIRED;
  diagHandleUdsRequest();
}

/**
 * Send a UDS request on the given connection.
 * The request is added the diagnostic request queue and the queue execution is triggered.
 * @param connId: The diag channel id over which the request should be sent
 * @param request: The request as byte array
 * @param requestLength: The length of the request in bytes
 */
void sendUDSRequest(int connId, byte request[], long requestLength)
{
  long payloadLength;
  int msgLength;
  struct DiagUdsRequestQueueEntry queueEntry;
  queueEntry.connectionId = connId;
  queueEntry.request.bufferSize = requestLength;
  memcpy(queueEntry.request.buffer, request, requestLength);
  diagUdsRequestEnqueue(queueEntry);
  diagHandleUdsRequest();
}

void diagOnPrestop()
{
}

void diagInitializeBuscontext()
{
  buscontext_Vita_CAN0 = GetBusNameContext("Vita_CAN0");
  buscontext_Vita_FR0 = GetBusNameContext("Vita_FR0");
  buscontext_Vita_LIN0 = GetBusNameContext("Vita_LIN0");
}

/**
 * Handle the diagnostic request queue execution.
 * This includes the creation of the connections and sending of the request.
 */
void diagHandleUdsRequest()
{
  struct dcmBuffer dcmUdsBuffer;
  struct DiagUdsRequestQueueEntry queueEntry;
  int connId;
  int oldConnId = -1;

  if (DIAG_CHANNEL_CHANGE_TIMER_RUNNING == diagUdsChannelChangeTimerState)
  {
    return;
  }
  else if (DIAG_CHANNEL_CHANGE_TIMER_EXPIRED == diagUdsChannelChangeTimerState)
  {
    diagUdsChannelChangeTimerState = DIAG_CHANNEL_CHANGE_TIMER_NOT_RUNNING;
    // do not dequeue a new request, the request for which the timer was started is still in the variable queueEntry
  }
  else if (DIAG_CHANNEL_CHANGE_TIMER_NOT_RUNNING == diagUdsChannelChangeTimerState)
  {
    if (diagUdsRequestQueueIsEmpty())
    {
      return;
    }
    if (diagUdsRequestQueueIsRequestInProgress())
    {
      return;
    }
    diagUdsRequestDequeue(queueEntry);
    connId = queueEntry.connectionId;
    // channel was changed, do not send request immediately -> start a timer for sending the request
    if ( (oldConnId >= 0) && (oldConnId != connId) )
    {
      diagUdsChannelChangeTimerState = DIAG_CHANNEL_CHANGE_TIMER_RUNNING;
      diagUdsChannelChangeTimer.set(DIAG_UDS_CHANNEL_CHANGE_TIME);
      return;
    }
  }
  oldConnId = connId;
  memcpy(dcmUdsBuffer, queueEntry.request);
  diagUdsRequestQueueSetInProgress();

  switch(connId)
  {
    // DcmDslConnection 'DcmDslProtocolRow_29812fbf_DC_0xE001_oMyECU_oCAN_9c85e87b' on channel 'Vita_CAN0'
    case(0):
      SetBusContext(buscontext_Vita_CAN0);
      if(!dcmCanTpConnectionOpen_DcmDslProtocolRow_29812fbf_DC_0xE001_oMyECU_oCAN_9c85e87b)
      {
        createDcmConnection_DcmDslProtocolRow_29812fbf_DC_0xE001_oMyECU_oCAN_9c85e87b();
        dcmCanTpConnectionOpen_DcmDslProtocolRow_29812fbf_DC_0xE001_oMyECU_oCAN_9c85e87b = 1;
      }
      CanTpSendData(dcmCanTpConnectionhandle_DcmDslProtocolRow_29812fbf_DC_0xE001_oMyECU_oCAN_9c85e87b, dcmUdsBuffer.buffer, dcmUdsBuffer.bufferSize);
      break;
    default:
      writeLineEx(startApplWriteWindow,4,"DiagUsecase: Unknown bus system: %d", connId);
  }
  setTimer(diagUdsRequestTimeoutTimer, DIAG_UDS_REQUEST_TIMEOUT);
  updateDiagDataGridRequestInfo(dcmUdsBuffer.buffer);
}

void CanTp_ReceptionInd(long connHandle, byte data[])
{
  // DcmDslConnection 'DcmDslProtocolRow_29812fbf_DC_0xE001_oMyECU_oCAN_9c85e87b' on channel 'Vita_CAN0'
  if (connHandle == dcmCanTpConnectionhandle_DcmDslProtocolRow_29812fbf_DC_0xE001_oMyECU_oCAN_9c85e87b)
  {
    writeLineEx(startApplWriteWindow,4,"DiagUsecase: Received data on CanTp connection %d", connHandle);
    handleUDSResponse(data, elcount(data));
  }
}

void CanTp_ErrorInd(long connHandle, long error)
{
  logError("DiagUsecase ERROR: CanTp_ErrorInd was called for the connection", connHandle);
  logError("DiagUsecase ERROR: CanTp_ErrorInd was called with the error code", error);
}

/**
 * Create TpConnection for DcmDslConnection 'DcmDslProtocolRow_29812fbf_DC_0xE001_oMyECU_oCAN_9c85e87b' on channel 'Vita_CAN0'
 **/
void createDcmConnection_DcmDslProtocolRow_29812fbf_DC_0xE001_oMyECU_oCAN_9c85e87b()
{
  writeLineEx(startApplWriteWindow,4,"DiagUsecase: Create CanTpConnectionhandle_DcmDslProtocolRow_29812fbf_DC_0xE001_oMyECU_oCAN_9c85e87b");
  dcmCanTpConnectionhandle_DcmDslProtocolRow_29812fbf_DC_0xE001_oMyECU_oCAN_9c85e87b = CanTpCreateConnection(0); // Normal mode
  if(dcmCanTpConnectionhandle_DcmDslProtocolRow_29812fbf_DC_0xE001_oMyECU_oCAN_9c85e87b <= 0)
  {
    logError("DiagUsecase ERROR: CanTpCreateConnection returned an invalid  connection handle for DcmDslProtocolRow_29812fbf_DC_0xE001_oMyECU_oCAN_9c85e87b", dcmCanTpConnectionhandle_DcmDslProtocolRow_29812fbf_DC_0xE001_oMyECU_oCAN_9c85e87b);
  }
  CanTpSetTxIdentifier(dcmCanTpConnectionhandle_DcmDslProtocolRow_29812fbf_DC_0xE001_oMyECU_oCAN_9c85e87b, dcmConnectionTxIdentifier_DcmDslProtocolRow_29812fbf_DC_0xE001_oMyECU_oCAN_9c85e87b);
  CanTpSetRxIdentifier(dcmCanTpConnectionhandle_DcmDslProtocolRow_29812fbf_DC_0xE001_oMyECU_oCAN_9c85e87b, dcmConnectionRxIdentifier_DcmDslProtocolRow_29812fbf_DC_0xE001_oMyECU_oCAN_9c85e87b);
}

/**
 * Clear buffer
 **/
void clearBuffer(byte buffer[])
{
  long empty;
  clearBuffer(buffer, empty);
}
/**
 * Clear buffer
 **/
void clearBuffer(byte buffer[], long& bufferSize)
{
  int i;
  for(i=0;i<elCount(buffer);i++)
  {
    buffer[i] = 0x00;
  }
  bufferSize = 0;
}

void clearBuffer(struct dcmBuffer bufferStruct)
{
  clearBuffer(bufferStruct.buffer, bufferStruct.bufferSize);
}



/**
 * Called if a response for a UDS request was received.
 * Triggers the further execution of the diagnostic request queue.
 * @param data: The response as byte array
 * @param dataLength: The length of the response in bytes
 **/
void handleUDSResponse(byte data[], int dataLength )
{
  writeLineEx(startApplWriteWindow,4,"DiagUsecase: Received %d bytes [%02x] ..." , dataLength, data[0]);
  sysSetVariableInt(sysvar::StartApplication::DiagResponseCode,data[0]);
  if (dataLength == DIAG_UDS_RESPONSE_INDEX_RDBI_DATA + 2 && data[DIAG_UDS_RESPONSE_INDEX_SID] == (dcmServiceIdRDBI + 0x40))
  {
    writeLineEx(startApplWriteWindow,4," - Positive Response to RDBI");
    writeLineEx(startApplWriteWindow,4,"   - byte[DIAG_UDS_RESPONSE_INDEX_RDBI_DATA] = %d", data[DIAG_UDS_RESPONSE_INDEX_RDBI_DATA]);
    writeLineEx(startApplWriteWindow,4,"   - byte[DIAG_UDS_RESPONSE_INDEX_RDBI_DATA + 1] = %d", data[DIAG_UDS_RESPONSE_INDEX_RDBI_DATA + 1]);
    sysSetVariableInt(sysvar::StartApplication::DiagCounterValue,(data[DIAG_UDS_RESPONSE_INDEX_RDBI_DATA] << 8) | data[DIAG_UDS_RESPONSE_INDEX_RDBI_DATA + 1]);
  }
  if (dataLength == 3 && data[DIAG_UDS_RESPONSE_INDEX_SID] == (dcmServiceIdWDBI + 0x40))
  {
    writeLineEx(startApplWriteWindow,4," - Positive Response to WDBI");
  }
  if (dataLength == 3 && data[DIAG_UDS_RESPONSE_INDEX_SID] == (dcmServiceIdDSC + 0x40))
  {
    writeLineEx(startApplWriteWindow,4," - Positive Response to DSC");
  }
  if(dataLength > DIAG_UDS_RESPONSE_INDEX_RDTCSSBDTC_STATUS && data[DIAG_UDS_RESPONSE_INDEX_SID] == (dcmServiceIdRDTCI + 0x40))
  {
    sysSetVariableInt(sysvar::StartApplication::DiagDTCStatusByteValue, data[DIAG_UDS_RESPONSE_INDEX_RDTCSSBDTC_STATUS]);
    if(dataLength > DIAG_UDS_RESPONSE_INDEX_RDTCSSBDTC_DATA + 1)
    {
      sysSetVariableInt(sysvar::StartApplication::DiagSnapshotDataValue, (data[DIAG_UDS_RESPONSE_INDEX_RDTCSSBDTC_DATA] << 8) | data[DIAG_UDS_RESPONSE_INDEX_RDTCSSBDTC_DATA + 1]);
    }
    else
    {
      sysSetVariableInt(sysvar::StartApplication::DiagSnapshotDataValue, -1);
    }
    writeLineEx(startApplWriteWindow,4," - Positive Response to RDTCI");
  }
  if(dataLength == 1 && data[DIAG_UDS_RESPONSE_INDEX_SID] == (dcmServiceIdCDTCI + 0x40))
  {
    writeLineEx(startApplWriteWindow,4," - Positive Response to CDTCI");
  }
  updateDiagDataGridResponseInfo(data);
  // handle pending response
  if (!(data[DIAG_UDS_RESPONSE_INDEX_SID] == 0x7F && data[2] == 0x78))
  {
    cancelTimer(diagUdsRequestTimeoutTimer);
    diagUdsRequestQueueSetNotInProgress();
    diagHandleUdsRequest();
  }
}

/**
 * Write diagnostic response info to DIAG panel
 **/
void updateDiagDataGridResponseInfo(byte buffer[])
{
  char formatter[50];
  diagDataGridResponse.time = getCurrentTimeInSeconds();
  snprintf(diagDataGridResponse.type, elcount(diagDataGridResponse.type), "Response");
  switch(buffer[0])
  {
    case dcmServiceIdDSC + 0x40:
      snprintf(diagDataGridResponse.service,elcount(diagDataGridResponse.service),"DSC");
      snprintf(diagDataGridResponse.status,elcount(diagDataGridResponse.status),"positive");
      break;
    case dcmServiceIdRDBI + 0x40:
      snprintf(diagDataGridResponse.service,elcount(diagDataGridResponse.service),"RDBI");
      snprintf(diagDataGridResponse.status,elcount(diagDataGridResponse.status),"positive");
      break;
    case dcmServiceIdWDBI + 0x40:
      snprintf(diagDataGridResponse.service,elcount(diagDataGridResponse.service),"WDBI");
      snprintf(diagDataGridResponse.status,elcount(diagDataGridResponse.status),"positive");
      break;
    case dcmServiceIdCDTCI + 0x40:
      snprintf(diagDataGridResponse.service,elcount(diagDataGridResponse.service),"CDTCI");
      snprintf(diagDataGridResponse.status,elcount(diagDataGridResponse.status),"positive");
      break;
    case dcmServiceIdRDTCI + 0x40:
      snprintf(diagDataGridResponse.service,elcount(diagDataGridResponse.service),"RDTCI");
      snprintf(diagDataGridResponse.status,elcount(diagDataGridResponse.status),"positive");
      break;
    case dcmRDTCIsubFunctionRDTCSSBDTC + 0x40:
      snprintf(diagDataGridResponse.service,elcount(diagDataGridResponse.service),"RDTCSSBDTC");
      snprintf(diagDataGridResponse.status,elcount(diagDataGridResponse.status),"positive");
      break;
    case 0x7F:
      snprintf(diagDataGridResponse.status,elcount(diagDataGridResponse.status),"negative (NRC 0x%x)", buffer[2]);
      switch(buffer[1])
      {
        case dcmServiceIdDSC: snprintf(diagDataGridResponse.service,elcount(diagDataGridResponse.service),"DSC"); break;
        case dcmServiceIdRDBI: snprintf(diagDataGridResponse.service,elcount(diagDataGridResponse.service),"RDBI"); break;
        case dcmServiceIdWDBI: snprintf(diagDataGridResponse.service,elcount(diagDataGridResponse.service),"WDBI"); break;
        case dcmServiceIdCDTCI: snprintf(diagDataGridResponse.service,elcount(diagDataGridResponse.service),"CDTCI"); break;
        case dcmServiceIdRDTCI: snprintf(diagDataGridResponse.service,elcount(diagDataGridResponse.service),"RDTCI"); break;
        case dcmRDTCIsubFunctionRDTCSSBDTC: snprintf(diagDataGridRequest.service,elcount(diagDataGridRequest.service),"RDTCSSBDTC"); break;
        default: snprintf(diagDataGridResponse.service,elcount(diagDataGridResponse.service), ""); break;
      }
      logError("DiagUseCase: Negative response received for DCM service", diagDataGridResponse.service);
      snprintf(formatter, 5, "0x%x", buffer[2]);
      logError("DiagUseCase: Negative response received with NRC", formatter);
      break;
    default:
      snprintf(diagDataGridResponse.service,elcount(diagDataGridResponse.service), "");
      snprintf(diagDataGridResponse.status,elcount(diagDataGridResponse.status), "");
      break;
  }
  snprintf(formatter, 10, "%.3f", diagDataGridResponse.time);
  strncat(diagUcBusLogDataText.time, formatter, 50);
  strncat(diagUcBusLogDataText.type, diagDataGridResponse.type, 50);
  strncat(diagUcBusLogDataText.service, diagDataGridResponse.service, 50);
  strncat(diagUcBusLogDataText.status, diagDataGridResponse.status, 50);
  putValueToControl("StartApplication.Diagnostic", "BusLog_Time", diagUcBusLogDataText.time);
  putValueToControl("StartApplication.Diagnostic", "BusLog_Type", diagUcBusLogDataText.type);
  putValueToControl("StartApplication.Diagnostic", "BusLog_Service", diagUcBusLogDataText.service);
  putValueToControl("StartApplication.Diagnostic", "BusLog_Status", diagUcBusLogDataText.status);
}

/**
 * Write diagnostic request info to DIAG panel
 **/
void updateDiagDataGridRequestInfo(byte buffer[])
{
  char formatter[50];
  diagDataGridRequest.time = getCurrentTimeInSeconds();
  snprintf(diagDataGridRequest.type, elcount(diagDataGridRequest.type), "Request");
  snprintf(diagDataGridRequest.status,elcount(diagDataGridRequest.service),"---");
  switch(buffer[0])
  {
    case dcmServiceIdDSC: snprintf(diagDataGridRequest.service,elcount(diagDataGridRequest.service),"DSC"); break;
    case dcmServiceIdRDBI: snprintf(diagDataGridRequest.service,elcount(diagDataGridRequest.service),"RDBI"); break;
    case dcmServiceIdWDBI: snprintf(diagDataGridRequest.service,elcount(diagDataGridRequest.service),"WDBI"); break;
    case dcmServiceIdCDTCI: snprintf(diagDataGridRequest.service,elcount(diagDataGridRequest.service),"CDTCI"); break;
    case dcmServiceIdRDTCI: snprintf(diagDataGridRequest.service,elcount(diagDataGridRequest.service),"RDTCI"); break;
    case dcmRDTCIsubFunctionRDTCSSBDTC: snprintf(diagDataGridRequest.service,elcount(diagDataGridRequest.service),"RDTCSSBDTC"); break;
    default: snprintf(diagDataGridRequest.service,elcount(diagDataGridRequest.service),""); break;
  }
  strncpy(diagUcBusLogDataText.time, "", 1);
  strncpy(diagUcBusLogDataText.type, "", 1);
  strncpy(diagUcBusLogDataText.service, "", 1);
  strncpy(diagUcBusLogDataText.status, "", 1);
  snprintf(formatter, 10, "%.3f\n", diagDataGridRequest.time);
  strncat(diagUcBusLogDataText.time, formatter, 50);
  snprintf(formatter, elcount(diagDataGridRequest.type), "%s\n", diagDataGridRequest.type);
  strncat(diagUcBusLogDataText.type, formatter, 50);
  snprintf(formatter, elcount(diagDataGridRequest.service), "%s\n", diagDataGridRequest.service);
  strncat(diagUcBusLogDataText.service, formatter, 50);
  snprintf(formatter, elcount(diagDataGridRequest.status), "%s\n", diagDataGridRequest.status);
  strncat(diagUcBusLogDataText.status, formatter, 50);
  putValueToControl("StartApplication.Diagnostic", "BusLog_Time", diagUcBusLogDataText.time);
  putValueToControl("StartApplication.Diagnostic", "BusLog_Type", diagUcBusLogDataText.type);
  putValueToControl("StartApplication.Diagnostic", "BusLog_Service", diagUcBusLogDataText.service);
  putValueToControl("StartApplication.Diagnostic", "BusLog_Status", diagUcBusLogDataText.status);
}


/**
 * Initialize COM Panel data.
 */
void comUc_Initialize()
{
  byte h, i, j;
  snprintf(comGrid[COM_DATA_GRID_VIEW].name, CHAR_100, "comDataGridView");
  for (h = 0; h < COM_PANEL_GRID_MAX; h++)
  {
    for(i = 0; i < COM_COL_MAX; i++)
    {
      snprintf(comGrid[h].header[i].backgroundColor, CHAR_10, "");
      snprintf(comGrid[h].header[i].textColor, CHAR_10, "");
    }

    snprintf(comGrid[h].header[COM_COL_NAME].text, CHAR_200, "Name");
    snprintf(comGrid[h].header[COM_COL_NAME].hint, CHAR_200, "Content Description");
    snprintf(comGrid[h].header[COM_COL_NAME].textAlignment, CHAR_10, "right");
    comGrid[h].header[COM_COL_NAME].columnSpacing = 12;

    snprintf(comGrid[h].header[COM_COL_RX].text, CHAR_200, "Rx");
    snprintf(comGrid[h].header[COM_COL_RX].hint, CHAR_200, "Rx message(s)");
    snprintf(comGrid[h].header[COM_COL_RX].textAlignment, CHAR_10, "center");
    comGrid[h].header[COM_COL_RX].columnSpacing = 44;

    snprintf(comGrid[h].header[COM_COL_TX].text, CHAR_200, "Tx");
    snprintf(comGrid[h].header[COM_COL_TX].hint, CHAR_200, "Tx message(s)");
    snprintf(comGrid[h].header[COM_COL_TX].textAlignment, CHAR_10, "center");
    comGrid[h].header[COM_COL_TX].columnSpacing = 44;

    for(i = 0; i < COM_ROW_MAX; i++)
    {
      for(j = 0; j < COM_COL_MAX; j++)
      {
        snprintf(comGrid[h].row[i].cell[j].backgroundColor, CHAR_10, "");
        snprintf(comGrid[h].row[i].cell[j].hint, CHAR_200, "");
        snprintf(comGrid[h].row[i].cell[j].textColor, CHAR_10, "");
        comGrid[h].row[i].cell[j].columnSpacing = 0;
      }
    }

    // first row
    comGrid[h].row[COM_ROW_COM_PATH].isVisible = true;
    snprintf(comGrid[h].row[COM_ROW_COM_PATH].cell[COM_COL_NAME].text, CHAR_200, "Com Path");
    snprintf(comGrid[h].row[COM_ROW_COM_PATH].cell[COM_COL_NAME].textAlignment, CHAR_10, "right");   // 1. col
    snprintf(comGrid[h].row[COM_ROW_COM_PATH].cell[COM_COL_NAME].hint, CHAR_200, "This row shows the selected communication path");
    snprintf(comGrid[h].row[COM_ROW_COM_PATH].cell[COM_COL_RX].textAlignment, CHAR_10, "center");    // 2. col
    snprintf(comGrid[h].row[COM_ROW_COM_PATH].cell[COM_COL_TX].textAlignment, CHAR_10, "center");    // 3. col
    // second row
    comGrid[h].row[COM_ROW_SIGNAL].isVisible = true;
    snprintf(comGrid[h].row[COM_ROW_SIGNAL].cell[COM_COL_NAME].text, CHAR_200, "Sig. Name");
    snprintf(comGrid[h].row[COM_ROW_SIGNAL].cell[COM_COL_NAME].textAlignment, CHAR_10, "right");    // 1. col
    snprintf(comGrid[h].row[COM_ROW_SIGNAL].cell[COM_COL_NAME].hint, CHAR_200, "This row shows the used signal names");
    snprintf(comGrid[h].row[COM_ROW_SIGNAL].cell[COM_COL_RX].textAlignment, CHAR_10, "center");     // 2. col
    snprintf(comGrid[h].row[COM_ROW_SIGNAL].cell[COM_COL_TX].textAlignment, CHAR_10, "center");     // 3. col
    // third row
    comGrid[h].row[COM_ROW_VALUE].isVisible = true;
    snprintf(comGrid[h].row[COM_ROW_VALUE].cell[COM_COL_NAME].text, CHAR_200, "Sig. Value");
    snprintf(comGrid[h].row[COM_ROW_VALUE].cell[COM_COL_NAME].textAlignment, CHAR_10, "right");     // 1. col
    snprintf(comGrid[h].row[COM_ROW_VALUE].cell[COM_COL_NAME].hint, CHAR_200, "This row shows the scaled signal values");
    snprintf(comGrid[h].row[COM_ROW_VALUE].cell[COM_COL_RX].textAlignment, CHAR_10, "center");      // 2. col
    snprintf(comGrid[h].row[COM_ROW_VALUE].cell[COM_COL_TX].textAlignment, CHAR_10, "center");      // 3. col
    // fourth row
    comGrid[h].row[COM_ROW_TRANSMITTED].isVisible = true;
    snprintf(comGrid[h].row[COM_ROW_TRANSMITTED].cell[COM_COL_NAME].text, CHAR_200, "Transmitted");
    snprintf(comGrid[h].row[COM_ROW_TRANSMITTED].cell[COM_COL_NAME].textAlignment, CHAR_10, "right");     // 1. col
    snprintf(comGrid[h].row[COM_ROW_TRANSMITTED].cell[COM_COL_NAME].hint, CHAR_200, "This row shows the timestamp of the signal transmission");
    snprintf(comGrid[h].row[COM_ROW_TRANSMITTED].cell[COM_COL_RX].textAlignment, CHAR_10, "center");      // 2. col
    snprintf(comGrid[h].row[COM_ROW_TRANSMITTED].cell[COM_COL_TX].textAlignment, CHAR_10, "center");      // 3. col
    // fifth row
    comGrid[h].row[COM_ROW_J1939_TP_TYPE].isVisible = false;
    snprintf(comGrid[h].row[COM_ROW_J1939_REQUESTED].cell[COM_COL_NAME].text, CHAR_200, "Requested");
    snprintf(comGrid[h].row[COM_ROW_J1939_REQUESTED].cell[COM_COL_NAME].textAlignment, CHAR_10, "right");  // 1. col
    snprintf(comGrid[h].row[COM_ROW_J1939_REQUESTED].cell[COM_COL_NAME].hint, CHAR_200, "This row shows the timestamp of the PGN EA00p request");
    snprintf(comGrid[h].row[COM_ROW_J1939_REQUESTED].cell[COM_COL_RX].textAlignment, CHAR_10, "center");   // 2. col
    snprintf(comGrid[h].row[COM_ROW_J1939_REQUESTED].cell[COM_COL_TX].textAlignment, CHAR_10, "center");   // 3. col
    // sixth row
    comGrid[h].row[COM_ROW_J1939_TP_TYPE].isVisible = false;
    snprintf(comGrid[h].row[COM_ROW_J1939_TP_TYPE].cell[COM_COL_NAME].text, CHAR_200, "TP Type");
    snprintf(comGrid[h].row[COM_ROW_J1939_TP_TYPE].cell[COM_COL_NAME].textAlignment, CHAR_10, "right");  // 1. col
    snprintf(comGrid[h].row[COM_ROW_J1939_TP_TYPE].cell[COM_COL_NAME].hint, CHAR_200, "This row shows the transport message type of J1939");
    snprintf(comGrid[h].row[COM_ROW_J1939_TP_TYPE].cell[COM_COL_RX].textAlignment, CHAR_10, "center");   // 2. col
    snprintf(comGrid[h].row[COM_ROW_J1939_TP_TYPE].cell[COM_COL_TX].textAlignment, CHAR_10, "center");   // 3. col
    // seventh row
    comGrid[h].row[COM_ROW_SECOC_FRESHNESS].isVisible = false;
    snprintf(comGrid[h].row[COM_ROW_SECOC_FRESHNESS].cell[COM_COL_NAME].text, CHAR_200, "Freshness");
    snprintf(comGrid[h].row[COM_ROW_SECOC_FRESHNESS].cell[COM_COL_NAME].textAlignment, CHAR_10, "right");  // 1. col
    snprintf(comGrid[h].row[COM_ROW_SECOC_FRESHNESS].cell[COM_COL_NAME].hint, CHAR_200, "This row shows the freshness value of SecOC PDUs");
    snprintf(comGrid[h].row[COM_ROW_SECOC_FRESHNESS].cell[COM_COL_RX].textAlignment, CHAR_10, "center");   // 2. col
    snprintf(comGrid[h].row[COM_ROW_SECOC_FRESHNESS].cell[COM_COL_TX].textAlignment, CHAR_10, "center");   // 3. col
    // eighth row
    comGrid[h].row[COM_ROW_SECOC_AUTH].isVisible = false;
    snprintf(comGrid[h].row[COM_ROW_SECOC_AUTH].cell[COM_COL_NAME].text, CHAR_200, "AuthInfo");
    snprintf(comGrid[h].row[COM_ROW_SECOC_AUTH].cell[COM_COL_NAME].textAlignment, CHAR_10, "right");  // 1. col
    snprintf(comGrid[h].row[COM_ROW_SECOC_AUTH].cell[COM_COL_NAME].hint, CHAR_200, "This row shows the authentication information of SecOC PDUs");
    snprintf(comGrid[h].row[COM_ROW_SECOC_AUTH].cell[COM_COL_RX].textAlignment, CHAR_10, "center");   // 2. col
    snprintf(comGrid[h].row[COM_ROW_SECOC_AUTH].cell[COM_COL_TX].textAlignment, CHAR_10, "center");   // 3. col
  }

  for(i = 0; i < COM_UI_MAX; i++)
  {
    snprintf(comUiElement[i].text, CHAR_100, "");
    comUiElement[i].enabled = true;
    comUiElement[i].visible = true;
  }
  snprintf(comUiElement[COM_UI_SCALE_RX].name, CHAR_100, "comScaleRx");
  snprintf(comUiElement[COM_UI_CALCULATE].name, CHAR_100, "comCalculate");
  snprintf(comUiElement[COM_UI_SCALE_TX].name, CHAR_100, "comScaleTx");
  snprintf(comUiElement[COM_UI_PLAY_BTN].name, CHAR_100, "comPlayBtn");
  snprintf(comUiElement[COM_UI_PLAY_BTN].text, CHAR_100, "4"); // play sign
  snprintf(comUiElement[COM_UI_PAUSE_BTN].name, CHAR_100, "comPauseBtn");
  snprintf(comUiElement[COM_UI_PAUSE_BTN].text, CHAR_100, ";"); // pause sign
  snprintf(comUiElement[COM_UI_PATH_SELECTOR].name, CHAR_100, "comPathComboBox");
  snprintf(comUiElement[COM_UI_TRACKBAR].name, CHAR_100, "comTrackBar");
}

/**
 * Send Com Panel Grid header data.
 */
void comUc_UpdatePanelGridHeaderContent()
{
  char formatter[__size_of(struct ComGrid) * COM_PANEL_GRID_MAX];
  snprintf(formatter, elcount(formatter),
    "<?xml version=\"1.0\" encoding=\"utf-8\"?>"
    "<PanelContentData xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\" xmlns=\"http://vector.com/StartApplication/1.0\">"
      "<mGrid>"
        "<PanelGrid>"
          "<name>%s</name>"
          "<header>"
            "<GridCell>"
              "<text>%s</text><columnSpacing>%d</columnSpacing><hint>%s</hint><textAlignment>%s</textAlignment><textColor>%s</textColor><backgroundColor>%s</backgroundColor>"
            "</GridCell>"
            "<GridCell>"
              "<text>%s</text><columnSpacing>%d</columnSpacing><hint>%s</hint><textAlignment>%s</textAlignment><textColor>%s</textColor><backgroundColor>%s</backgroundColor>"
            "</GridCell>"
            "<GridCell>"
              "<text>%s</text><columnSpacing>%d</columnSpacing><hint>%s</hint><textAlignment>%s</textAlignment><textColor>%s</textColor><backgroundColor>%s</backgroundColor>"
            "</GridCell>"
          "</header>"
        "</PanelGrid>"
      "</mGrid>"
    "</PanelContentData>",
    comGrid[COM_DATA_GRID_VIEW].name,
    comGrid[COM_DATA_GRID_VIEW].header[COM_COL_NAME].text, comGrid[COM_DATA_GRID_VIEW].header[COM_COL_NAME].columnSpacing, comGrid[COM_DATA_GRID_VIEW].header[COM_COL_NAME].hint, comGrid[COM_DATA_GRID_VIEW].header[COM_COL_NAME].textAlignment, comGrid[COM_DATA_GRID_VIEW].header[COM_COL_NAME].textColor, comGrid[COM_DATA_GRID_VIEW].header[COM_COL_NAME].backgroundColor,
    comGrid[COM_DATA_GRID_VIEW].header[COM_COL_RX].text, comGrid[COM_DATA_GRID_VIEW].header[COM_COL_RX].columnSpacing, comGrid[COM_DATA_GRID_VIEW].header[COM_COL_RX].hint, comGrid[COM_DATA_GRID_VIEW].header[COM_COL_RX].textAlignment, comGrid[COM_DATA_GRID_VIEW].header[COM_COL_RX].textColor, comGrid[COM_DATA_GRID_VIEW].header[COM_COL_RX].backgroundColor,
    comGrid[COM_DATA_GRID_VIEW].header[COM_COL_TX].text, comGrid[COM_DATA_GRID_VIEW].header[COM_COL_TX].columnSpacing, comGrid[COM_DATA_GRID_VIEW].header[COM_COL_TX].hint, comGrid[COM_DATA_GRID_VIEW].header[COM_COL_TX].textAlignment, comGrid[COM_DATA_GRID_VIEW].header[COM_COL_TX].textColor, comGrid[COM_DATA_GRID_VIEW].header[COM_COL_TX].backgroundColor);

    sysSetVariableString(sysvar::StartApplication::ComPanelGridHeaderContent, formatter);
}

/**
 * Send Com Panel Grid row data.
 */
void comUc_UpdatePanelGridRowContent()
{
  char formatter[__size_of(struct ComGrid) * COM_PANEL_GRID_MAX];
  char subFormatter[(__size_of(struct GridCell) * COM_COL_MAX) + 580];
  byte i;

  if ((sysvar::StartApplication::UseCaseActivator::Com_RxTx == @sysvar::StartApplication::UseCaseActivator) || (sysvar::StartApplication::UseCaseActivator::Com_TxOnly == @sysvar::StartApplication::UseCaseActivator))
  {
    // Add the header of the XML message incl. target grid name.
    snprintf(formatter, elcount(formatter),
      "<?xml version=\"1.0\" encoding=\"utf-8\"?>"
      "<PanelContentData xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\" xmlns=\"http://vector.com/StartApplication/1.0\">"
        "<mGrid>"
          "<PanelGrid>"
            "<name>%s</name>"
            "<row>",
      comGrid[COM_DATA_GRID_VIEW].name);

    // Add the single rows to the formatter string.
    for (i = 0; i < COM_ROW_MAX; i++)
    {
      if (1 == comGrid[COM_DATA_GRID_VIEW].row[i].isVisible)
      {
        snprintf(subFormatter, elcount(subFormatter),
          "<ArrayOfGridCell>"
            "<GridCell>"
              "<text>%s</text><columnSpacing>%d</columnSpacing><hint>%s</hint><textAlignment>%s</textAlignment><textColor>%s</textColor><backgroundColor>%s</backgroundColor>"
            "</GridCell>"
            "<GridCell>"
              "<text>%s</text><columnSpacing>%d</columnSpacing><hint>%s</hint><textAlignment>%s</textAlignment><textColor>%s</textColor><backgroundColor>%s</backgroundColor>"
            "</GridCell>"
            "<GridCell>"
              "<text>%s</text><columnSpacing>%d</columnSpacing><hint>%s</hint><textAlignment>%s</textAlignment><textColor>%s</textColor><backgroundColor>%s</backgroundColor>"
            "</GridCell>"
          "</ArrayOfGridCell>",
          // One single row.
          comGrid[COM_DATA_GRID_VIEW].row[i].cell[COM_COL_NAME].text, comGrid[COM_DATA_GRID_VIEW].row[i].cell[COM_COL_NAME].columnSpacing, comGrid[COM_DATA_GRID_VIEW].row[i].cell[COM_COL_NAME].hint, comGrid[COM_DATA_GRID_VIEW].row[i].cell[COM_COL_NAME].textAlignment, comGrid[COM_DATA_GRID_VIEW].row[i].cell[COM_COL_NAME].textColor, comGrid[COM_DATA_GRID_VIEW].row[i].cell[COM_COL_NAME].backgroundColor,
          comGrid[COM_DATA_GRID_VIEW].row[i].cell[COM_COL_RX].text, comGrid[COM_DATA_GRID_VIEW].row[i].cell[COM_COL_RX].columnSpacing, comGrid[COM_DATA_GRID_VIEW].row[i].cell[COM_COL_RX].hint, comGrid[COM_DATA_GRID_VIEW].row[i].cell[COM_COL_RX].textAlignment, comGrid[COM_DATA_GRID_VIEW].row[i].cell[COM_COL_RX].textColor, comGrid[COM_DATA_GRID_VIEW].row[i].cell[COM_COL_RX].backgroundColor,
          comGrid[COM_DATA_GRID_VIEW].row[i].cell[COM_COL_TX].text, comGrid[COM_DATA_GRID_VIEW].row[i].cell[COM_COL_TX].columnSpacing, comGrid[COM_DATA_GRID_VIEW].row[i].cell[COM_COL_TX].hint, comGrid[COM_DATA_GRID_VIEW].row[i].cell[COM_COL_TX].textAlignment, comGrid[COM_DATA_GRID_VIEW].row[i].cell[COM_COL_TX].textColor, comGrid[COM_DATA_GRID_VIEW].row[i].cell[COM_COL_TX].backgroundColor);

        strncat(formatter, subFormatter, elcount(formatter));
      }
    }

    // Add the footer to the formatter string.
    snprintf(subFormatter, elcount(subFormatter),
      "</row>"
          "</PanelGrid>"
        "</mGrid>"
      "</PanelContentData>");
    strncat(formatter, subFormatter, elcount(formatter));

    sysSetVariableString(sysvar::StartApplication::ComPanelGridRowContent, formatter);
  }
}

/**
 * Send Com Panel user interface data.
 */
void comUc_UpdateUiContent()
{
  char formatter[__size_of(struct UiElementType) * COM_UI_MAX];
  snprintf(formatter, elcount(formatter),
      "<?xml version=\"1.0\" encoding=\"utf-8\"?>"
      "<PanelContentData xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\" xmlns=\"http://vector.com/StartApplication/1.0\">"
        "<mControl>"
          "<UiElement><name>%s</name><text>%s</text><hint>%s</hint><enabled>%d</enabled><visible>%d</visible></UiElement>"
          "<UiElement><name>%s</name><text>%s</text><hint>%s</hint><enabled>%d</enabled><visible>%d</visible></UiElement>"
          "<UiElement><name>%s</name><text>%s</text><hint>%s</hint><enabled>%d</enabled><visible>%d</visible></UiElement>"
          "<UiElement><name>%s</name><text>%s</text><hint>%s</hint><enabled>%d</enabled><visible>%d</visible></UiElement>"
          "<UiElement><name>%s</name><text>%s</text><hint>%s</hint><enabled>%d</enabled><visible>%d</visible></UiElement>"
          "<UiElement><name>%s</name><text>%s</text><hint>%s</hint><enabled>%d</enabled><visible>%d</visible></UiElement>"
          "<UiElement><name>%s</name><text>%s</text><hint>%s</hint><enabled>%d</enabled><visible>%d</visible></UiElement>"
        "</mControl>"
      "</PanelContentData>",
      comUiElement[COM_UI_SCALE_RX].name, comUiElement[COM_UI_SCALE_RX].text, comUiElement[COM_UI_SCALE_RX].hint, comUiElement[COM_UI_SCALE_RX].enabled, comUiElement[COM_UI_SCALE_RX].visible,
      comUiElement[COM_UI_CALCULATE].name, comUiElement[COM_UI_CALCULATE].text, comUiElement[COM_UI_CALCULATE].hint, comUiElement[COM_UI_CALCULATE].enabled, comUiElement[COM_UI_CALCULATE].visible,
      comUiElement[COM_UI_SCALE_TX].name, comUiElement[COM_UI_SCALE_TX].text, comUiElement[COM_UI_SCALE_TX].hint, comUiElement[COM_UI_SCALE_TX].enabled, comUiElement[COM_UI_SCALE_TX].visible,
      comUiElement[COM_UI_PLAY_BTN].name, comUiElement[COM_UI_PLAY_BTN].text, comUiElement[COM_UI_PLAY_BTN].hint, comUiElement[COM_UI_PLAY_BTN].enabled, comUiElement[COM_UI_PLAY_BTN].visible,
      comUiElement[COM_UI_PAUSE_BTN].name, comUiElement[COM_UI_PAUSE_BTN].text, comUiElement[COM_UI_PAUSE_BTN].hint, comUiElement[COM_UI_PAUSE_BTN].enabled, comUiElement[COM_UI_PAUSE_BTN].visible,
      comUiElement[COM_UI_PATH_SELECTOR].name, comUiElement[COM_UI_PATH_SELECTOR].text, comUiElement[COM_UI_PATH_SELECTOR].hint, comUiElement[COM_UI_PATH_SELECTOR].enabled, comUiElement[COM_UI_PATH_SELECTOR].visible,
      comUiElement[COM_UI_TRACKBAR].name, comUiElement[COM_UI_TRACKBAR].text, comUiElement[COM_UI_TRACKBAR].hint, comUiElement[COM_UI_TRACKBAR].enabled, comUiElement[COM_UI_TRACKBAR].visible);

  sysSetVariableString(sysvar::StartApplication::ComPanelUiContent, formatter);
}

/**
 * Fill byte array signal with sensor value.
 */
void comUc_FillByteArray(byte sensorValue, byte signalData[], dword length)
{
  dword i;
  for (i = 0; i < elCount(signalData); i++)
  {
    signalData[i] = 0;
  }

  signalData[sensorValue % length] = sensorValue;
}

/**
 * Enable/disable the controls of the panel for the COM use case
 **/
void setComPanelStatus (byte isEnabled)
{
  if (isEnabled)
  {
    comUiElement[COM_UI_PLAY_BTN].enabled = !autoSensorValueChange;
    comUiElement[COM_UI_PAUSE_BTN].enabled = autoSensorValueChange;
  }
  else
  {
    comUiElement[COM_UI_PLAY_BTN].enabled = 0;
    comUiElement[COM_UI_PAUSE_BTN].enabled = 0;
  }
  comUiElement[COM_UI_PATH_SELECTOR].enabled = isEnabled;
  comUiElement[COM_UI_TRACKBAR].enabled = isEnabled;
  comUc_UpdateUiContent();
}

/**
 * The sensor value has changed, calculate the new signal values and send the corresponding messages to the ECU
 **/
on sysvar StartApplication::ComInput
{
    byte value;
    if (activeUseCase == sysvar::StartApplication::UseCaseActivator::Com_RxTx || activeUseCase == sysvar::StartApplication::UseCaseActivator::Com_TxOnly)
    {
      value =  @sysvar::StartApplication::ComInput;
      sendComRxSignals(value);
    }
}

/**
 * Increase or decrease value if Play is active
 **/
on timer timerForSensorValueChange
{
  int i;
  if (activeUseCase == sysvar::StartApplication::UseCaseActivator::Com_RxTx || activeUseCase == sysvar::StartApplication::UseCaseActivator::Com_TxOnly)
  {
    if ( autoSensorValueChange == 1 )
    {
      /* Play button is pressed --> Change sensor value */
      if ( @sysvar::StartApplication::ComValueShapeKind == 0 )
      {
        /* The sensor value is incremented periodically */
        if ( @sysvar::StartApplication::ComInput < 254 )
        {
          @sysvar::StartApplication::ComInput = @sysvar::StartApplication::ComInput + 1;
        }
        if ( @sysvar::StartApplication::ComInput >= 254 )
        {
          /* sensor value has reached upper boundary --> switch to decrement */
          @sysvar::StartApplication::ComValueShapeKind = 1;
        }
      }
      else
      {
        /* The sensor value is decremented periodically */
        if ( @sysvar::StartApplication::ComInput > 0 )
        {
          @sysvar::StartApplication::ComInput = @sysvar::StartApplication::ComInput - 1;
        }
        if ( @sysvar::StartApplication::ComInput <= 0 )
        {
          /* sensor value has reached lower boundary --> switch to increment */
          @sysvar::StartApplication::ComValueShapeKind = 0;
        }
      }
    }
  }
}

/**
 * Handle Play and Pause button
 **/
on sysvar StartApplication::ComSendCtrl
{
  int value;
  value = @sysvar::StartApplication::ComSendCtrl;
  if (@sysvar::StartApplication::ComSendCtrl > 1)
  {
    autoSensorValueChange = 0; //deactivate automatic change of sensor value
  }
  else
  {
    autoSensorValueChange = 1;
  }
  comUiElement[COM_UI_PLAY_BTN].enabled = !autoSensorValueChange;
  comUiElement[COM_UI_PAUSE_BTN].enabled = autoSensorValueChange;
  comUc_UpdateUiContent();
}

on sysvar StartApplication::ComExpectedOutput
{
  @sysvar::StartApplication::ComCmpOutputs = @sysvar::StartApplication::ComActualOutput - @sysvar::StartApplication::ComExpectedOutput;
}

on sysvar StartApplication::ComActualOutput
{
  @sysvar::StartApplication::ComCmpOutputs = @sysvar::StartApplication::ComActualOutput - @sysvar::StartApplication::ComExpectedOutput;
}

/**
 * Returns the length of a null-terminated string.
 * @param input: a null terminated char array
 * @return length of the string
 */
dword getStringLength(char input[])
{
  dword length;
  dword i;

  length = 0;
  for (i = 0; i < elcount(input); i++)
  {
    if('\0' == input[i]) {
      break;
    }
    length++;
  }
  return length;
}

/**
 * Initialize a byte array with the given value.
 * @param array: byte array to be initialized
 * @param initValue: value to be used to initialize each byte
 **/
void initByteArray(byte array[], byte initValue)
{
  int i;
  for (i = 0; i < elCount(array); i++)
  {
    array[i] = initValue;
  }
}

/**
 * Compare two byte arrays to have the same content.
 * @param array1: first array to compare
 * @param array2: second array to compare
 * @return 1 if the arrays are the same, 0 if the arrays are different
 **/
byte cmpByteArrays(byte array1[], byte array2[])
{
  byte returnValue;
  int i;
  if (elCount(array1) != elCount(array2))
  {
    returnValue = false;
  }
  else
  {
    returnValue = true;
    for(i = 0; i < elCount(array1); i++)
    {
      returnValue &= (array1[i] == array2[i]);
    }
  }
  return returnValue;
}

/**
 * Concatenates an UI tag to a 'line'.
 *
 * @param line: input parameter to which the UI tag shall be concatenated
 * @param element: element/content of the UI tag which shall be added
 * @param tagStart: start tag symbol for the element
 * @param tagEnd: end tag symbol for the element
 **/
void addUiTag(char line[], char element[], char tagStart[], char tagEnd[])
{
  if (0 != strncmp(uiSubElementDoNotSend, element, elcount(uiSubElementDoNotSend)))
  {
    strncat(line, tagStart, elcount(line));
    strncat(line, element, elcount(line));
    strncat(line, tagEnd, elcount(line));
  }
}

/**
 * Concatenates an UI tag to a 'line'.
 *
 * @param line: input parameter to which the UI tag shall be concatenated
 * @param element: element/content of the UI tag which shall be added, interpreted as string, 0 means '0', otherwise '1'
 * @param tagStart: start tag symbol for the element
 * @param tagEnd: end tag symbol for the element
 **/
void addUiTag(char line[], byte element, char tagStart[], char tagEnd[])
{
  if (GLOBAL_MAX_INVALID_BYTE_VALUE != element)
  {
    strncat(line, tagStart, elcount(line));
    if (0 == element)
    {
      strncat(line, "0", elcount(line));
    }
    else
    {
      strncat(line, "1", elcount(line));
    }
    strncat(line, tagEnd, elcount(line));
  }
}

/**
 * Send use case panel user interface data.
 */
void updateUiContent(struct UiElementType uiElement[], int numberOfUiSubElements, sysvarString* uiSysVar)
{
  byte i;
  char xmlCompleteObject[UI_MAX_FORMATTER_ARRAY_SIZE];

  // Verify if internal constant was chosen big enough
  if (numberOfUiSubElements > UI_MAX_LINES)
  {
    logError("MAX_UI_LINES constant was chosen with a too small value as reserved buffer size, panel cannot be updated");
    logError("MAX_UI_LINES is", UI_MAX_LINES);
    logError("numberOfSubUiElements is", (long)numberOfUiSubElements);
  }
  else
  {
    xmlCompleteObject[0] = '\0';
    strncat(xmlCompleteObject, uiElementGlobalTags.tagStart, elcount(xmlCompleteObject));
    for (i = 0; i < numberOfUiSubElements; i++)
    {
      char xmlSingleLine[__size_of(struct UiElementType)+__size_of(struct UiElementLineTagsType)];
      xmlSingleLine[0] = '\0';
      strncat(xmlSingleLine, uiElementLineTags.tagStart, elcount(xmlSingleLine));

      addUiTag(xmlSingleLine, uiElement[i].name, uiElementLineTags.tagNameStart, uiElementLineTags.tagNameEnd);
      addUiTag(xmlSingleLine, uiElement[i].text, uiElementLineTags.tagTextStart, uiElementLineTags.tagTextEnd);
      addUiTag(xmlSingleLine, uiElement[i].hint, uiElementLineTags.tagHintStart, uiElementLineTags.tagHintEnd);
      addUiTag(xmlSingleLine, uiElement[i].enabled, uiElementLineTags.tagEnabledStart, uiElementLineTags.tagEnabledEnd);
      addUiTag(xmlSingleLine, uiElement[i].visible, uiElementLineTags.tagVisibleStart, uiElementLineTags.tagVisibleEnd);

      strncat(xmlSingleLine, uiElementLineTags.tagEnd, elcount(xmlSingleLine));
      strncat(xmlCompleteObject, xmlSingleLine, elcount(xmlCompleteObject));
    }
    strncat(xmlCompleteObject, uiElementGlobalTags.tagEnd, elcount(xmlCompleteObject));
    sysSetVariableString(uiSysVar, xmlCompleteObject);
  }
}

/**
 * Add an entry with the current time stamp to the error log and print the same message to the write window.
 * @param errorMessage: the error message
 **/
void logError(char errorMessage[])
{
  char buffer[11000];
  char bufferOld[10000];
  if (0 == sysGetVariableString(sysvar::StartApplication::ErrorLog, bufferOld, elCount(bufferOld)))
  {
    snprintf(buffer, elcount(buffer), "%s\n%10.6f: %s", bufferOld, getCurrentTimeInSeconds(), errorMessage);
    sysSetVariableString(sysvar::StartApplication::ErrorLog, buffer);
  }
  writeLineEx(startApplWriteWindow, 3, "%10.6f: %s", getCurrentTimeInSeconds(), errorMessage);
}

/**
 * Add an entry with the current time stamp to the error log and print the same message to the write window.
 * @param errorMessage: the error message
 * @param value: the value to add to the end of the error message
 **/
void logError(char errorMessage[], long value)
{
  char buffer[11000];
  char bufferOld[10000];
  if (0 == sysGetVariableString(sysvar::StartApplication::ErrorLog, bufferOld, elCount(bufferOld)))
  {
    snprintf(buffer, elcount(buffer), "%s\n%10.6f: %s: %d", bufferOld, getCurrentTimeInSeconds(), errorMessage, value);
    sysSetVariableString(sysvar::StartApplication::ErrorLog, buffer);
  }
  writeLineEx(startApplWriteWindow, 3, "%10.6f: %s: %d", getCurrentTimeInSeconds(), errorMessage, value);
}

/**
 * Add an entry with the current time stamp to the error log and print the same message to the write window.
 * @param errorMessage: the error message
 * @param value: the value to add to the end of the error message
 **/
void logError(char errorMessage[], byte value)
{
  char buffer[11000];
  char bufferOld[10000];
  if (0 == sysGetVariableString(sysvar::StartApplication::ErrorLog, bufferOld, elCount(bufferOld)))
  {
    snprintf(buffer, elcount(buffer), "%s\n%10.6f: %s: %d", bufferOld, getCurrentTimeInSeconds(), errorMessage, value);
    sysSetVariableString(sysvar::StartApplication::ErrorLog, buffer);
  }
  writeLineEx(startApplWriteWindow, 3, "%10.6f: %s: %d", getCurrentTimeInSeconds(), errorMessage, value);
}

/**
 * Add an entry with the current time stamp to the error log and print the same message to the write window.
 * @param errorMessage: the error message
 * @param value: the value to add to the end of the error message
 **/
void logError(char errorMessage[], dword value)
{
  char buffer[11000];
  char bufferOld[10000];
  if (0 == sysGetVariableString(sysvar::StartApplication::ErrorLog, bufferOld, elCount(bufferOld)))
  {
    snprintf(buffer, elcount(buffer), "%s\n%10.6f: %s: %d", bufferOld, getCurrentTimeInSeconds(), errorMessage, value);
    sysSetVariableString(sysvar::StartApplication::ErrorLog, buffer);
  }
  writeLineEx(startApplWriteWindow, 3, "%10.6f: %s: %d", getCurrentTimeInSeconds(), errorMessage, value);
}

/**
 * Add an entry with the current time stamp to the error log and print the same message to the write window.
 * @param errorMessage: the error message
 * @param value: the value to add to the end of the error message
 **/
void logError(char errorMessage[], char value[])
{
  char buffer[11000];
  char bufferOld[10000];
  if (0 == sysGetVariableString(sysvar::StartApplication::ErrorLog, bufferOld, elCount(bufferOld)))
  {
    snprintf(buffer, elcount(buffer), "%s\n%10.6f: %s: %s", bufferOld, getCurrentTimeInSeconds(), errorMessage, value);
    sysSetVariableString(sysvar::StartApplication::ErrorLog, buffer);
  }
  writeLineEx(startApplWriteWindow, 3, "%10.6f: %s: %s", getCurrentTimeInSeconds(), errorMessage, value);
}

float getCurrentTimeInSeconds()
{
  return timeNowFloat()/100000.0;
}


/**
 * Verify CANoe version and log error if current CANoe version is not supported.
 **/
void verifyCANoeVersion()
{
}
/**
 * Get current CANoe version and write in sysvar CANoeVersionInfo.
 **/
void saveCANoeVersionInfo()
{
  int majorVersion = 0;
  int minorVersion = 0;
  int servicePack = 0;
#if TOOL_MAJOR_VERSION == 8
  majorVersion = 8;
#elif TOOL_MAJOR_VERSION == 9
  majorVersion = 9;
#elif TOOL_MAJOR_VERSION == 10
  majorVersion = 10;
#elif TOOL_MAJOR_VERSION == 11
  majorVersion = 11;
#elif TOOL_MAJOR_VERSION == 12
  majorVersion = 12;
#elif TOOL_MAJOR_VERSION > 12
  majorVersion = 99;
#endif

#if TOOL_MINOR_VERSION == 0
  minorVersion = 0;
#elif TOOL_MINOR_VERSION == 1
  minorVersion = 1;
#elif TOOL_MINOR_VERSION == 2
  minorVersion = 2;
#elif TOOL_MINOR_VERSION == 3
  minorVersion = 3;
#elif TOOL_MINOR_VERSION == 4
  minorVersion = 4;
#elif TOOL_MINOR_VERSION == 5
  minorVersion = 5;
#elif TOOL_MINOR_VERSION == 6
  minorVersion = 6;
#elif TOOL_MINOR_VERSION == 7
  minorVersion = 7;
#elif TOOL_MINOR_VERSION == 8
  minorVersion = 8;
#elif TOOL_MINOR_VERSION == 9
  minorVersion = 9;
#elif TOOL_MINOR_VERSION > 9
  minorVersion = 99;
#endif

#if TOOL_SERVICE_PACK == 0
  servicePack = 0;
#elif TOOL_SERVICE_PACK == 1
  servicePack = 1;
#elif TOOL_SERVICE_PACK == 2
  servicePack = 2;
#elif TOOL_SERVICE_PACK == 3
  servicePack = 3;
#elif TOOL_SERVICE_PACK == 4
  servicePack = 4;
#elif TOOL_SERVICE_PACK == 5
  servicePack = 5;
#elif TOOL_SERVICE_PACK == 6
  servicePack = 6;
#elif TOOL_SERVICE_PACK == 7
  servicePack = 7;
#elif TOOL_SERVICE_PACK == 8
  servicePack = 8;
#elif TOOL_SERVICE_PACK == 9
  servicePack = 9;
#elif TOOL_SERVICE_PACK > 9
  servicePack = 99;
#endif

  @sysvar::StartApplication::CANoeVersionInfo = majorVersion * 10000 + minorVersion * 100 + servicePack;
}
